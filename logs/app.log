{"timestamp":"2024-12-25T14:59:30.175Z","level":"info","message":"Starting seeding process for entity: bodies","data":{"config":{"numUsers":50,"numBodies":150,"numTeams":5,"numFridges":150,"numIncidents":20,"numMaintenanceLogs":100,"numNotifications":200,"numSchedules":50,"numShifts":100,"numAdmissions":100,"numReferrals":75,"numReleases":50,"numPathologyReports":100,"numLabTests":200,"numImageStudies":150,"numGeneticTests":50,"numToxicologyReports":75,"numHistologyReports":100,"numProcedures":150,"numTissueSpecimens":200,"numQualityControls":300,"numChainOfCustody":500,"startDate":"2024-02-01","endDate":"2024-08-22"},"entity":"bodies","timestamp":"2024-12-25T14:59:30.175Z"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T14:59:30.176Z","level":"debug","message":"Fetching dependencies for bodies","data":{"entity":"bodies","dependencies":["users"]},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T14:59:36.298Z","level":"info","message":"Executing seeder function for bodies","data":{"entity":"bodies","config":{"numUsers":50,"numBodies":150,"numTeams":5,"numFridges":150,"numIncidents":20,"numMaintenanceLogs":100,"numNotifications":200,"numSchedules":50,"numShifts":100,"numAdmissions":100,"numReferrals":75,"numReleases":50,"numPathologyReports":100,"numLabTests":200,"numImageStudies":150,"numGeneticTests":50,"numToxicologyReports":75,"numHistologyReports":100,"numProcedures":150,"numTissueSpecimens":200,"numQualityControls":300,"numChainOfCustody":500,"startDate":"2024-02-01","endDate":"2024-08-22"},"dependenciesCount":[14,6]},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T14:59:36.299Z","level":"info","message":"Starting body generation","data":{"config":[{"id":"cm53zr64g00gu97jbizy0p9fn","name":"Yolanda Metz","email":"<EMAIL>","password":"$2a$10$HgYicQg7qSDQhLmgnHNfsOB4Mu6cAy5C/JenQkoMqH9B0ImR6QwbG","role":"PATHOLOGIST","emailVerified":"2024-01-22T14:33:36.039Z","image":"https://avatars.githubusercontent.com/u/89829458","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"54056800","employeeId":"O0f2VMb04x","institution":"Rau - Skiles","department":"Jewelry","vehicleReg":"EB96HIE","phoneNumber":"************** x3505","supervisor":null,"status":"ACTIVE","permissions":["read"],"createdAt":"2024-12-25T14:29:34.528Z","updatedAt":"2024-12-25T14:29:34.528Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zr96e00h197jbdmmh3tfp","name":"Alberta Bogan","email":"<EMAIL>","password":"$2a$10$RdlI617p4uihpf6nNKaPUuuYY81kP04b6sSUJlta1cNCKBJkyJuCO","role":"PATHOLOGIST","emailVerified":"2024-11-04T03:52:09.772Z","image":"https://avatars.githubusercontent.com/u/51274369","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"40866309","employeeId":"r45IBKblTz","institution":"Kuhn and Sons","department":"Books","vehicleReg":"ON00QSV","phoneNumber":"************ x5745","supervisor":null,"status":"ACTIVE","permissions":["read"],"createdAt":"2024-12-25T14:29:38.486Z","updatedAt":"2024-12-25T14:29:38.486Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zrav600h697jbtuiiirxf","name":"Maureen Legros","email":"<EMAIL>","password":"$2a$10$LKTlyHd47pG7uJ7SyDgGe.V6M6D0fS7/CjKwBDSBZE2ACXoX1yyNy","role":"PATHOLOGIST","emailVerified":"2024-04-04T15:11:18.202Z","image":"https://avatars.githubusercontent.com/u/20243135","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"12611945","employeeId":"f8yCrQSsgr","institution":"Crist Inc","department":"Jewelry","vehicleReg":"HU18UFJ","phoneNumber":"************ x57974","supervisor":null,"status":"ACTIVE","permissions":["write"],"createdAt":"2024-12-25T14:29:40.674Z","updatedAt":"2024-12-25T14:29:40.674Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zrg0200hj97jbbz6dlxyg","name":"Jackie Littel-Pfeffer","email":"<EMAIL>","password":"$2a$10$JfZZKCkVGxWlJ1GIRShaA..Kb9sKNTVWZU4UDdwhE9LB0bzYN3Fb.","role":"PATHOLOGIST","emailVerified":"2024-06-11T11:13:54.360Z","image":"https://avatars.githubusercontent.com/u/13042929","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"57889935","employeeId":"HyWAqAZVdh","institution":"Marvin, Kerluke and Fritsch","department":"Toys","vehicleReg":"QA83FOO","phoneNumber":"************","supervisor":null,"status":"ACTIVE","permissions":["read"],"createdAt":"2024-12-25T14:29:47.330Z","updatedAt":"2024-12-25T14:29:47.330Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zrhkx00hn97jb65rabu49","name":"Thelma Wolff","email":"<EMAIL>","password":"$2a$10$2LAESWSbpV6chjI57Z0ee.luZbsuAkZU1.Oop91mQyoFZQuo2MP8q","role":"PATHOLOGIST","emailVerified":"2024-01-01T03:09:11.969Z","image":"https://avatars.githubusercontent.com/u/98206886","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"34755474","employeeId":"TB6s8UCfMX","institution":"Nitzsche LLC","department":"Outdoors","vehicleReg":"WO60OKI","phoneNumber":"(************* x010","supervisor":null,"status":"ACTIVE","permissions":["write"],"createdAt":"2024-12-25T14:29:49.377Z","updatedAt":"2024-12-25T14:29:49.377Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zrhz500ho97jbtdhhwnwk","name":"Gwen Braun","email":"<EMAIL>","password":"$2a$10$xXprlwOE0nN8Lg2jRqQH4eehcIF3NoNbVoxgZ41PPzc.BcWssDOgq","role":"PATHOLOGIST","emailVerified":"2024-11-04T15:51:02.169Z","image":"https://avatars.githubusercontent.com/u/34406504","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"85014038","employeeId":"M0tKgZjgVs","institution":"Monahan Inc","department":"Beauty","vehicleReg":"CT87GOM","phoneNumber":"************** x52611","supervisor":null,"status":"ACTIVE","permissions":["admin"],"createdAt":"2024-12-25T14:29:49.889Z","updatedAt":"2024-12-25T14:29:49.889Z","assignedBodies":[],"pathologistReports":[]}],"usersCount":14,"userRoles":["ADMIN","FIELD_EMPLOYEE","PATHOLOGIST","MORGUE_STAFF","ADMIN","PATHOLOGIST","FIELD_EMPLOYEE","PATHOLOGIST","PATHOLOGIST","PATHOLOGIST","PATHOLOGIST","ADMIN","MORGUE_STAFF","FIELD_EMPLOYEE"]},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T14:59:36.299Z","level":"debug","message":"Staff validation","data":{"pathologistsCount":6,"morgueStaffCount":2,"requiredPathologists":1,"requiredMorgueStaff":1},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T14:59:36.300Z","level":"error","message":"Failed to create any bodies. Please check the error logs.","data":{"pathologistsCount":6,"morgueStaffCount":2},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T14:59:36.301Z","level":"error","message":"Error seeding bodies","data":{"entity":"bodies","error":{"message":"Failed to create any bodies. Please check the error logs.","stack":"Error: Failed to create any bodies. Please check the error logs.\n    at Object.generateBodies [as seedFunction] (webpack-internal:///(rsc)/./src/lib/database/seeder.ts:293:15)\n    at POST (webpack-internal:///(rsc)/./src/app/api/seed/[entity]/route.ts:428:47)\n    at async /home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:55753\n    at async eO.execute (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46523)\n    at async eO.handle (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57007)\n    at async doRender (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1359:42)\n    at async cacheEntry.responseCache.get.routeKind (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1569:40)\n    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1489:28)\n    at async DevServer.renderPageComponent (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1913:24)\n    at async DevServer.renderToResponseImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1951:32)\n    at async DevServer.pipeImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:917:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/next-server.js:272:17)\n    at async DevServer.handleRequestImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:813:17)\n    at async /home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/dev/next-dev-server.js:339:20\n    at async Span.traceAsyncFn (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/trace/trace.js:154:20)\n    at async DevServer.handleRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/dev/next-dev-server.js:336:24)\n    at async invokeRender (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:173:21)\n    at async handleRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:350:24)\n    at async requestHandlerImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:374:13)\n    at async Server.requestListener (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/start-server.js:141:13)","name":"Error"},"config":{"numUsers":50,"numBodies":150,"numTeams":5,"numFridges":150,"numIncidents":20,"numMaintenanceLogs":100,"numNotifications":200,"numSchedules":50,"numShifts":100,"numAdmissions":100,"numReferrals":75,"numReleases":50,"numPathologyReports":100,"numLabTests":200,"numImageStudies":150,"numGeneticTests":50,"numToxicologyReports":75,"numHistologyReports":100,"numProcedures":150,"numTissueSpecimens":200,"numQualityControls":300,"numChainOfCustody":500,"startDate":"2024-02-01","endDate":"2024-08-22"}},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:00:42.784Z","level":"info","message":"Starting seeding process for entity: collections","data":{"config":{"numUsers":50,"numBodies":150,"numTeams":5,"numFridges":150,"numIncidents":20,"numMaintenanceLogs":100,"numNotifications":200,"numSchedules":50,"numShifts":100,"numAdmissions":100,"numReferrals":75,"numReleases":50,"numPathologyReports":100,"numLabTests":200,"numImageStudies":150,"numGeneticTests":50,"numToxicologyReports":75,"numHistologyReports":100,"numProcedures":150,"numTissueSpecimens":200,"numQualityControls":300,"numChainOfCustody":500,"startDate":"2024-02-01","endDate":"2024-08-22"},"entity":"collections","timestamp":"2024-12-25T15:00:42.784Z"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:00:42.784Z","level":"debug","message":"Fetching dependencies for collections","data":{"entity":"collections","dependencies":["bodies","users"]},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:00:45.404Z","level":"error","message":"Error seeding collections","data":{"entity":"collections","error":{"message":"No eligible bodies found for collections. Please ensure bodies exist with the correct status.","stack":"Error: No eligible bodies found for collections. Please ensure bodies exist with the correct status.\n    at getDependencies (webpack-internal:///(rsc)/./src/app/api/seed/[entity]/route.ts:326:19)\n    at async POST (webpack-internal:///(rsc)/./src/app/api/seed/[entity]/route.ts:409:34)\n    at async /home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:55753\n    at async eO.execute (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46523)\n    at async eO.handle (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57007)\n    at async doRender (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1359:42)\n    at async cacheEntry.responseCache.get.routeKind (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1569:40)\n    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1489:28)\n    at async DevServer.renderPageComponent (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1913:24)\n    at async DevServer.renderToResponseImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1951:32)\n    at async DevServer.pipeImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:917:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/next-server.js:272:17)\n    at async DevServer.handleRequestImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:813:17)\n    at async /home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/dev/next-dev-server.js:339:20\n    at async Span.traceAsyncFn (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/trace/trace.js:154:20)\n    at async DevServer.handleRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/dev/next-dev-server.js:336:24)\n    at async invokeRender (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:173:21)\n    at async handleRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:350:24)\n    at async requestHandlerImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:374:13)\n    at async Server.requestListener (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/start-server.js:141:13)","name":"Error"},"config":{"numUsers":50,"numBodies":150,"numTeams":5,"numFridges":150,"numIncidents":20,"numMaintenanceLogs":100,"numNotifications":200,"numSchedules":50,"numShifts":100,"numAdmissions":100,"numReferrals":75,"numReleases":50,"numPathologyReports":100,"numLabTests":200,"numImageStudies":150,"numGeneticTests":50,"numToxicologyReports":75,"numHistologyReports":100,"numProcedures":150,"numTissueSpecimens":200,"numQualityControls":300,"numChainOfCustody":500,"startDate":"2024-02-01","endDate":"2024-08-22"}},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:21:22.769Z","level":"info","message":"Starting seeding process for entity: bodies","data":{"config":{"numUsers":50,"numBodies":150,"numTeams":5,"numFridges":150,"numIncidents":20,"numMaintenanceLogs":100,"numNotifications":200,"numSchedules":50,"numShifts":100,"numAdmissions":100,"numReferrals":75,"numReleases":50,"numPathologyReports":100,"numLabTests":200,"numImageStudies":150,"numGeneticTests":50,"numToxicologyReports":75,"numHistologyReports":100,"numProcedures":150,"numTissueSpecimens":200,"numQualityControls":300,"numChainOfCustody":500,"startDate":"2024-02-01","endDate":"2024-08-22"},"entity":"bodies","timestamp":"2024-12-25T15:21:22.768Z"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:21:22.774Z","level":"debug","message":"Fetching dependencies for bodies","data":{"entity":"bodies","dependencies":["users"]},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:21:27.920Z","level":"info","message":"Executing seeder function for bodies","data":{"entity":"bodies","config":{"numUsers":50,"numBodies":150,"numTeams":5,"numFridges":150,"numIncidents":20,"numMaintenanceLogs":100,"numNotifications":200,"numSchedules":50,"numShifts":100,"numAdmissions":100,"numReferrals":75,"numReleases":50,"numPathologyReports":100,"numLabTests":200,"numImageStudies":150,"numGeneticTests":50,"numToxicologyReports":75,"numHistologyReports":100,"numProcedures":150,"numTissueSpecimens":200,"numQualityControls":300,"numChainOfCustody":500,"startDate":"2024-02-01","endDate":"2024-08-22"},"dependenciesCount":[14,6]},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:21:27.921Z","level":"info","message":"Starting body generation","data":{"config":[{"id":"cm53zr64g00gu97jbizy0p9fn","name":"Yolanda Metz","email":"<EMAIL>","password":"$2a$10$HgYicQg7qSDQhLmgnHNfsOB4Mu6cAy5C/JenQkoMqH9B0ImR6QwbG","role":"PATHOLOGIST","emailVerified":"2024-01-22T14:33:36.039Z","image":"https://avatars.githubusercontent.com/u/89829458","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"54056800","employeeId":"O0f2VMb04x","institution":"Rau - Skiles","department":"Jewelry","vehicleReg":"EB96HIE","phoneNumber":"************** x3505","supervisor":null,"status":"ACTIVE","permissions":["read"],"createdAt":"2024-12-25T14:29:34.528Z","updatedAt":"2024-12-25T14:29:34.528Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zr96e00h197jbdmmh3tfp","name":"Alberta Bogan","email":"<EMAIL>","password":"$2a$10$RdlI617p4uihpf6nNKaPUuuYY81kP04b6sSUJlta1cNCKBJkyJuCO","role":"PATHOLOGIST","emailVerified":"2024-11-04T03:52:09.772Z","image":"https://avatars.githubusercontent.com/u/51274369","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"40866309","employeeId":"r45IBKblTz","institution":"Kuhn and Sons","department":"Books","vehicleReg":"ON00QSV","phoneNumber":"************ x5745","supervisor":null,"status":"ACTIVE","permissions":["read"],"createdAt":"2024-12-25T14:29:38.486Z","updatedAt":"2024-12-25T14:29:38.486Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zrav600h697jbtuiiirxf","name":"Maureen Legros","email":"<EMAIL>","password":"$2a$10$LKTlyHd47pG7uJ7SyDgGe.V6M6D0fS7/CjKwBDSBZE2ACXoX1yyNy","role":"PATHOLOGIST","emailVerified":"2024-04-04T15:11:18.202Z","image":"https://avatars.githubusercontent.com/u/20243135","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"12611945","employeeId":"f8yCrQSsgr","institution":"Crist Inc","department":"Jewelry","vehicleReg":"HU18UFJ","phoneNumber":"************ x57974","supervisor":null,"status":"ACTIVE","permissions":["write"],"createdAt":"2024-12-25T14:29:40.674Z","updatedAt":"2024-12-25T14:29:40.674Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zrg0200hj97jbbz6dlxyg","name":"Jackie Littel-Pfeffer","email":"<EMAIL>","password":"$2a$10$JfZZKCkVGxWlJ1GIRShaA..Kb9sKNTVWZU4UDdwhE9LB0bzYN3Fb.","role":"PATHOLOGIST","emailVerified":"2024-06-11T11:13:54.360Z","image":"https://avatars.githubusercontent.com/u/13042929","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"57889935","employeeId":"HyWAqAZVdh","institution":"Marvin, Kerluke and Fritsch","department":"Toys","vehicleReg":"QA83FOO","phoneNumber":"************","supervisor":null,"status":"ACTIVE","permissions":["read"],"createdAt":"2024-12-25T14:29:47.330Z","updatedAt":"2024-12-25T14:29:47.330Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zrhkx00hn97jb65rabu49","name":"Thelma Wolff","email":"<EMAIL>","password":"$2a$10$2LAESWSbpV6chjI57Z0ee.luZbsuAkZU1.Oop91mQyoFZQuo2MP8q","role":"PATHOLOGIST","emailVerified":"2024-01-01T03:09:11.969Z","image":"https://avatars.githubusercontent.com/u/98206886","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"34755474","employeeId":"TB6s8UCfMX","institution":"Nitzsche LLC","department":"Outdoors","vehicleReg":"WO60OKI","phoneNumber":"(************* x010","supervisor":null,"status":"ACTIVE","permissions":["write"],"createdAt":"2024-12-25T14:29:49.377Z","updatedAt":"2024-12-25T14:29:49.377Z","assignedBodies":[],"pathologistReports":[]},{"id":"cm53zrhz500ho97jbtdhhwnwk","name":"Gwen Braun","email":"<EMAIL>","password":"$2a$10$xXprlwOE0nN8Lg2jRqQH4eehcIF3NoNbVoxgZ41PPzc.BcWssDOgq","role":"PATHOLOGIST","emailVerified":"2024-11-04T15:51:02.169Z","image":"https://avatars.githubusercontent.com/u/34406504","resetToken":null,"resetTokenExpiry":null,"verificationToken":null,"failedAttempts":0,"lockoutUntil":null,"lastLogin":null,"persalNumber":"85014038","employeeId":"M0tKgZjgVs","institution":"Monahan Inc","department":"Beauty","vehicleReg":"CT87GOM","phoneNumber":"************** x52611","supervisor":null,"status":"ACTIVE","permissions":["admin"],"createdAt":"2024-12-25T14:29:49.889Z","updatedAt":"2024-12-25T14:29:49.889Z","assignedBodies":[],"pathologistReports":[]}],"usersCount":14,"userRoles":["ADMIN","FIELD_EMPLOYEE","PATHOLOGIST","MORGUE_STAFF","ADMIN","PATHOLOGIST","FIELD_EMPLOYEE","PATHOLOGIST","PATHOLOGIST","PATHOLOGIST","PATHOLOGIST","ADMIN","MORGUE_STAFF","FIELD_EMPLOYEE"]},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:21:27.921Z","level":"debug","message":"Staff validation","data":{"pathologistsCount":6,"morgueStaffCount":2,"requiredPathologists":1,"requiredMorgueStaff":1},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:21:27.922Z","level":"error","message":"Failed to create any bodies. Please check the error logs.","data":{"pathologistsCount":6,"morgueStaffCount":2},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-25T15:21:27.923Z","level":"error","message":"Error seeding bodies","data":{"entity":"bodies","error":{"message":"Failed to create any bodies. Please check the error logs.","stack":"Error: Failed to create any bodies. Please check the error logs.\n    at Object.generateBodies [as seedFunction] (webpack-internal:///(rsc)/./src/lib/database/seeder.ts:293:15)\n    at POST (webpack-internal:///(rsc)/./src/app/api/seed/[entity]/route.ts:428:47)\n    at async /home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:55753\n    at async eO.execute (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46523)\n    at async eO.handle (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57007)\n    at async doRender (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1359:42)\n    at async cacheEntry.responseCache.get.routeKind (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1569:40)\n    at async DevServer.renderToResponseWithComponentsImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1489:28)\n    at async DevServer.renderPageComponent (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1913:24)\n    at async DevServer.renderToResponseImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:1951:32)\n    at async DevServer.pipeImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:917:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/next-server.js:272:17)\n    at async DevServer.handleRequestImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/base-server.js:813:17)\n    at async /home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/dev/next-dev-server.js:339:20\n    at async Span.traceAsyncFn (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/trace/trace.js:154:20)\n    at async DevServer.handleRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/dev/next-dev-server.js:336:24)\n    at async invokeRender (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:173:21)\n    at async handleRequest (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:350:24)\n    at async requestHandlerImpl (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/router-server.js:374:13)\n    at async Server.requestListener (/home/<USER>/Documents/Webdev/gp-pathology/node_modules/next/dist/server/lib/start-server.js:141:13)","name":"Error"},"config":{"numUsers":50,"numBodies":150,"numTeams":5,"numFridges":150,"numIncidents":20,"numMaintenanceLogs":100,"numNotifications":200,"numSchedules":50,"numShifts":100,"numAdmissions":100,"numReferrals":75,"numReleases":50,"numPathologyReports":100,"numLabTests":200,"numImageStudies":150,"numGeneticTests":50,"numToxicologyReports":75,"numHistologyReports":100,"numProcedures":150,"numTissueSpecimens":200,"numQualityControls":300,"numChainOfCustody":500,"startDate":"2024-02-01","endDate":"2024-08-22"}},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:45:47.115Z","level":"error","message":"Error fetching body details:","data":{"name":"PrismaClientValidationError","clientVersion":"6.1.0"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:45:47.222Z","level":"error","message":"Error fetching body details:","data":{"name":"PrismaClientValidationError","clientVersion":"6.1.0"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:48:25.900Z","level":"error","message":"Error fetching body details:","data":{"name":"PrismaClientValidationError","clientVersion":"6.1.0"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:48:25.903Z","level":"error","message":"Error fetching body details:","data":{"name":"PrismaClientValidationError","clientVersion":"6.1.0"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:51:45.298Z","level":"error","message":"Error fetching body details","data":{"errorMessage":"\nInvalid `prisma.body.findUnique()` invocation:\n\n\nThe column `BodyRelease.releaseTime` does not exist in the current database."},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:51:45.299Z","level":"error","message":"Error fetching body details","data":{"errorMessage":"\nInvalid `prisma.body.findUnique()` invocation:\n\n\nThe column `BodyRelease.releaseTime` does not exist in the current database."},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:51:55.750Z","level":"error","message":"Error fetching body details","data":{"errorMessage":"\nInvalid `prisma.body.findUnique()` invocation:\n\n\nThe column `BodyRelease.releaseTime` does not exist in the current database."},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:54:32.868Z","level":"error","message":"Error fetching body details","data":{"errorMessage":"\nInvalid `prisma.body.findUnique()` invocation:\n\n\nThe column `BodyRelease.releaseTime` does not exist in the current database."},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:54:48.807Z","level":"error","message":"Error fetching body details","data":{"errorMessage":"\nInvalid `prisma.body.findUnique()` invocation:\n\n\nThe column `BodyRelease.releaseTime` does not exist in the current database."},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:54:48.807Z","level":"error","message":"Error fetching body details","data":{"errorMessage":"\nInvalid `prisma.body.findUnique()` invocation:\n\n\nThe column `BodyRelease.releaseTime` does not exist in the current database."},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T15:59:55.491Z","level":"error","message":"Error fetching body details","data":{"errorMessage":"\nInvalid `prisma.body.findUnique()` invocation:\n\n\nThe column `BodyRelease.releaseTime` does not exist in the current database."},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T17:26:36.410Z","level":"warn","message":"Body not found","data":{"bodyId":"cm54diizj000c97p9aqgjai4h"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T17:26:36.420Z","level":"warn","message":"Body not found","data":{"bodyId":"cm54diizj000c97p9aqgjai4h"},"environment":"development","source":"gp-pathology-system"}
{"timestamp":"2024-12-26T17:26:56.889Z","level":"warn","message":"Body not found","data":{"bodyId":"cm54diizj000c97p9aqgjai4h"},"environment":"development","source":"gp-pathology-system"}
