-- CreateEnum
CREATE TYPE "TransitionType" AS ENUM ('ADMISSION_TO_REFERRAL_IN', 'REFERRAL_IN_TO_REFERRAL_OUT', 'REFERRAL_OUT_TO_PENDING_RELEASE', 'AUTO_OVERDUE_DETECTION');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "EscalationLevel" AS ENUM ('SUPERVISOR', 'MANAGER', 'DIRECTOR', 'EXTERNAL');

-- CreateEnum
CREATE TYPE "PriorityLevel" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- CreateEnum
CREATE TYPE "UrgencyLevel" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

-- CreateEnum
CREATE TYPE "TransportMethod" AS ENUM ('AMBULANCE', 'HEARSE', 'PRIVATE_VEHICLE', 'OTHER');

-- CreateEnum
CREATE TYPE "TransferReason" AS ENUM ('SPECIALIST_CONSULTATION', 'AUTOPSY_REQUIRED', 'CAPACITY_CONSTRAINTS', 'FAMILY_REQUEST', 'LEGAL_REQUIREMENT', 'OTHER');

-- CreateEnum
CREATE TYPE "ReleaseRelation" AS ENUM ('FAMILY_MEMBER', 'LEGAL_GUARDIAN', 'FUNERAL_DIRECTOR', 'LEGAL_REPRESENTATIVE', 'OTHER');

-- CreateEnum
CREATE TYPE "OverdueReason" AS ENUM ('DOCUMENTATION_INCOMPLETE', 'FAMILY_UNAVAILABLE', 'LEGAL_PROCEEDINGS', 'INVESTIGATION_ONGOING', 'FACILITY_CAPACITY', 'TRANSPORT_DELAYS', 'OTHER');

-- CreateTable
CREATE TABLE "status_transition_audit" (
    "id" TEXT NOT NULL,
    "bodyTagId" TEXT NOT NULL,
    "fromStatus" TEXT NOT NULL,
    "toStatus" TEXT NOT NULL,
    "transitionType" "TransitionType" NOT NULL,
    "performedBy" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" JSONB,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "status_transition_audit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral_in" (
    "id" TEXT NOT NULL,
    "bodyTagId" TEXT NOT NULL,
    "sourceFacilityId" TEXT NOT NULL,
    "referralNumber" TEXT NOT NULL,
    "receivingStaffId" TEXT NOT NULL,
    "transportMethod" "TransportMethod",
    "transportNotes" TEXT,
    "processedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "referral_in_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral_out" (
    "id" TEXT NOT NULL,
    "bodyTagId" TEXT NOT NULL,
    "destinationFacilityId" TEXT NOT NULL,
    "transferReason" "TransferReason" NOT NULL,
    "transferReasonOther" TEXT,
    "expectedArrivalDate" TIMESTAMP(3),
    "transportArrangements" TEXT,
    "urgencyLevel" "UrgencyLevel" NOT NULL DEFAULT 'MEDIUM',
    "initiatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "initiatedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "referral_out_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pending_release" (
    "id" TEXT NOT NULL,
    "bodyTagId" TEXT NOT NULL,
    "releaseAuthorizationNumber" TEXT NOT NULL,
    "releaseDate" TIMESTAMP(3) NOT NULL,
    "releaseToPersonName" TEXT NOT NULL,
    "releaseToPersonRelation" "ReleaseRelation" NOT NULL,
    "releaseToPersonContact" TEXT NOT NULL,
    "releaseConditions" TEXT,
    "authorizedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "authorizedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "pending_release_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "overdue_record" (
    "id" TEXT NOT NULL,
    "bodyTagId" TEXT NOT NULL,
    "overdueReason" "OverdueReason" NOT NULL,
    "overdueReasonOther" TEXT,
    "escalationLevel" "EscalationLevel" NOT NULL DEFAULT 'SUPERVISOR',
    "escalationNotes" TEXT NOT NULL,
    "priorityLevel" "PriorityLevel" NOT NULL DEFAULT 'HIGH',
    "expectedResolutionDate" TIMESTAMP(3),
    "actionRequired" TEXT NOT NULL,
    "detectedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "detectedBy" TEXT NOT NULL,
    "resolvedAt" TIMESTAMP(3),
    "resolvedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "overdue_record_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "referral_in_bodyTagId_key" ON "referral_in"("bodyTagId");

-- CreateIndex
CREATE UNIQUE INDEX "referral_out_bodyTagId_key" ON "referral_out"("bodyTagId");

-- CreateIndex
CREATE UNIQUE INDEX "pending_release_bodyTagId_key" ON "pending_release"("bodyTagId");

-- CreateIndex
CREATE UNIQUE INDEX "overdue_record_bodyTagId_key" ON "overdue_record"("bodyTagId");

-- AddForeignKey
ALTER TABLE "status_transition_audit" ADD CONSTRAINT "status_transition_audit_bodyTagId_fkey" FOREIGN KEY ("bodyTagId") REFERENCES "BodyTag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "status_transition_audit" ADD CONSTRAINT "status_transition_audit_performedBy_fkey" FOREIGN KEY ("performedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_in" ADD CONSTRAINT "referral_in_bodyTagId_fkey" FOREIGN KEY ("bodyTagId") REFERENCES "BodyTag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_in" ADD CONSTRAINT "referral_in_sourceFacilityId_fkey" FOREIGN KEY ("sourceFacilityId") REFERENCES "Facility"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_in" ADD CONSTRAINT "referral_in_receivingStaffId_fkey" FOREIGN KEY ("receivingStaffId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_in" ADD CONSTRAINT "referral_in_processedBy_fkey" FOREIGN KEY ("processedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_out" ADD CONSTRAINT "referral_out_bodyTagId_fkey" FOREIGN KEY ("bodyTagId") REFERENCES "BodyTag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_out" ADD CONSTRAINT "referral_out_destinationFacilityId_fkey" FOREIGN KEY ("destinationFacilityId") REFERENCES "Facility"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral_out" ADD CONSTRAINT "referral_out_initiatedBy_fkey" FOREIGN KEY ("initiatedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pending_release" ADD CONSTRAINT "pending_release_bodyTagId_fkey" FOREIGN KEY ("bodyTagId") REFERENCES "BodyTag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pending_release" ADD CONSTRAINT "pending_release_authorizedBy_fkey" FOREIGN KEY ("authorizedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "overdue_record" ADD CONSTRAINT "overdue_record_bodyTagId_fkey" FOREIGN KEY ("bodyTagId") REFERENCES "BodyTag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "overdue_record" ADD CONSTRAINT "overdue_record_detectedBy_fkey" FOREIGN KEY ("detectedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "overdue_record" ADD CONSTRAINT "overdue_record_resolvedBy_fkey" FOREIGN KEY ("resolvedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
