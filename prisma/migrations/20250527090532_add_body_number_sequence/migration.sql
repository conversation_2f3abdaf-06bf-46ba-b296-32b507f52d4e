/*
  Warnings:

  - You are about to drop the column `invitationType` on the `InvitationLog` table. All the data in the column will be lost.
  - You are about to drop the column `ipAddress` on the `InvitationLog` table. All the data in the column will be lost.
  - You are about to drop the column `messageId` on the `InvitationLog` table. All the data in the column will be lost.
  - You are about to drop the column `metadata` on the `InvitationLog` table. All the data in the column will be lost.
  - You are about to drop the column `userAgent` on the `InvitationLog` table. All the data in the column will be lost.
  - You are about to drop the `EmailMetric` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `EmailTemplate` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Task` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "EmailMetric" DROP CONSTRAINT "EmailMetric_templateId_fkey";

-- DropForeignKey
ALTER TABLE "Task" DROP CONSTRAINT "Task_facilityId_fkey";

-- DropIndex
DROP INDEX "InvitationLog_messageId_idx";

-- AlterTable
ALTER TABLE "InvitationLog" DROP COLUMN "invitationType",
DROP COLUMN "ipAddress",
DROP COLUMN "messageId",
DROP COLUMN "metadata",
DROP COLUMN "userAgent";

-- DropTable
DROP TABLE "EmailMetric";

-- DropTable
DROP TABLE "EmailTemplate";

-- DropTable
DROP TABLE "Task";

-- DropEnum
DROP TYPE "EmailStatus";

-- DropEnum
DROP TYPE "EmailTemplateType";

-- CreateTable
CREATE TABLE "BodyNumberSequence" (
    "id" TEXT NOT NULL,
    "facilityId" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "lastNumber" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BodyNumberSequence_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "BodyNumberSequence_facilityId_idx" ON "BodyNumberSequence"("facilityId");

-- CreateIndex
CREATE INDEX "BodyNumberSequence_year_idx" ON "BodyNumberSequence"("year");

-- CreateIndex
CREATE UNIQUE INDEX "BodyNumberSequence_facilityId_year_key" ON "BodyNumberSequence"("facilityId", "year");

-- AddForeignKey
ALTER TABLE "BodyNumberSequence" ADD CONSTRAINT "BodyNumberSequence_facilityId_fkey" FOREIGN KEY ("facilityId") REFERENCES "Facility"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
