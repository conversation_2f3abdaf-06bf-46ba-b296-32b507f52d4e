import { Gender } from '@/types/body-management'
import { faker } from '@faker-js/faker/locale/zu_ZA'
import {
  BodyStatus,
  UserRole,
  CollectionType,
  Prisma,
  CustodyRecordType,
  NotificationType,
  UserStatus,
  StorageLocation,
  User
} from '@prisma/client'
import _ from 'lodash'
import { BodyMeasurements, SecurityScanResults } from './types'

// Time-based generators
export function generatePastDate(daysAgo: number): Date {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date
}

export function generateTimeSlots(startDate: Date, endDate: Date, count: number): Date[] {
  const timeSlots: Date[] = []
  const interval = (endDate.getTime() - startDate.getTime()) / count

  for (let i = 0; i < count; i++) {
    timeSlots.push(new Date(startDate.getTime() + interval * i))
  }

  return timeSlots
}

// Body-related generators
export function generateBodyMeasurements(): BodyMeasurements
 {
  const height = faker.number.int({ min: 150, max: 190 })
  const weight = faker.number.int({ min: 45, max: 100 })
  const bmi = Number((weight / Math.pow(height / 100, 2)).toFixed(1))
  return { height, weight, bmi }
}

export function generateCauseOfDeath(): string {
  const naturalCauses = [
    'Myocardial Infarction',
    'Cerebrovascular Accident',
    'Respiratory Failure',
    'Septic Shock',
    'Cancer',
    'Organ Failure'
  ]

  const traumaticCauses = [
    'Multiple Trauma',
    'Gunshot Wound',
    'Motor Vehicle Accident',
    'Fall from Height',
    'Drowning'
  ]

  const undeterminedCauses = [
    'Pending Investigation',
    'Under Review',
    'Awaiting Toxicology'
  ]

  const distribution = Math.random()
  if (distribution < 0.6) {
    return faker.helpers.arrayElement(naturalCauses)
  } else if (distribution < 0.9) {
    return faker.helpers.arrayElement(traumaticCauses)
  } else {
    return faker.helpers.arrayElement(undeterminedCauses)
  }
}

export function generateBodyStatus(admissionDate: Date): BodyStatus {
  const daysSinceAdmission = Math.floor((new Date().getTime() - admissionDate.getTime()) / (1000 * 60 * 60 * 24))

  if (daysSinceAdmission < 2) return BodyStatus.ADMITTED
  if (daysSinceAdmission < 5) return BodyStatus.IN_STORAGE
  if (daysSinceAdmission < 7) return BodyStatus.PENDING_RELEASE
  if (daysSinceAdmission < 8) return BodyStatus.SECURITY_VERIFIED
  if (daysSinceAdmission < 10) return BodyStatus.RELEASED
  return BodyStatus.REFERRED
}

export function generateCollectionDetails(date: Date) {
  const isBusinessHours = date.getHours() >= 8 && date.getHours() <= 17
  const collectionTypes = [
    { type: CollectionType.HOSPITAL, weight: isBusinessHours ? 0.6 : 0.3 },
    { type: CollectionType.CRIME_SCENE, weight: isBusinessHours ? 0.3 : 0.5 },
    { type: CollectionType.OTHER, weight: isBusinessHours ? 0.1 : 0.2 }
  ]

  const randomValue = Math.random()
  let cumulativeWeight = 0
  const selectedType = collectionTypes.find(ct => {
    cumulativeWeight += ct.weight
    return randomValue <= cumulativeWeight
  })?.type || CollectionType.OTHER

  return {
    type: selectedType,
    location: selectedType === CollectionType.HOSPITAL
      ? faker.helpers.arrayElement([
          'Emergency Department',
          'Intensive Care Unit',
          'General Ward',
          'Operating Theater'
        ])
      : faker.location.streetAddress(),
    institution: selectedType === CollectionType.HOSPITAL
      ? faker.company.name() + ' Hospital'
      : selectedType === CollectionType.CRIME_SCENE
        ? 'Crime Scene'
        : 'Other Location'
  }
}

// Temperature and maintenance generators
export function generateTemperatureLog(baseTemp: number, timestamp: Date): number {
  const hourOfDay = timestamp.getHours()
  const isNight = hourOfDay >= 22 || hourOfDay <= 4
  const randomVariation = (Math.random() - 0.5) * 0.4
  const timeBasedVariation = isNight ? -0.2 : 0.2

  return Number((baseTemp + randomVariation + timeBasedVariation).toFixed(1))
}

export function generateMaintenanceSchedule(startDate: Date, endDate: Date) {
  const schedule = []
  const currentDate = new Date(startDate)

  while (currentDate <= endDate) {
    if (currentDate.getDay() === 1) { // Monday
      schedule.push({
        date: new Date(currentDate),
        type: 'routine',
        description: 'Weekly maintenance check'
      })
    }

    // Random emergency maintenance (10% chance per week)
    if (Math.random() < 0.1 && currentDate.getDay() === 4) { // Thursday
      schedule.push({
        date: new Date(currentDate),
        type: 'emergency',
        description: faker.helpers.arrayElement([
          'Temperature fluctuation detected',
          'Door seal inspection',
          'Cooling system check',
          'Power supply issue'
        ])
      })
    }

    currentDate.setDate(currentDate.getDate() + 1)
  }

  return schedule
}

// Security and audit generators
export function generateSecurityScan(): SecurityScanResults {
  return {
    scanDate: new Date(),
    operator: faker.person.fullName(),
    findings: faker.helpers.arrayElement([
      'All clear',
      'Minor discrepancy noted',
      'Requires secondary inspection',
      'Documentation incomplete'
    ]),
    cleared: Math.random() > 0.1
  }
}

export function generateAuditEvent(userId: string, timestamp: Date) {
  return {
    action: faker.helpers.arrayElement(['login', 'view_record', 'update_record', 'create_report', 'access_denied']),
    userId,
    timestamp,
    details: {
      ip: faker.internet.ip(),
      userAgent: faker.internet.userAgent(),
      success: Math.random() > 0.05
    } as Prisma.JsonObject
  }
}

// Staff schedule generator
export function generateStaffSchedule(users: { id: string; role: UserRole }[], startDate: Date, endDate: Date) {
  const schedule: Array<{
    userId: string
    role: UserRole
    shift: 'morning' | 'afternoon' | 'night'
    date: Date
  }> = []

  const currentDate = new Date(startDate)
  while (currentDate <= endDate) {
    const shifts = ['morning', 'afternoon', 'night'] as const

    shifts.forEach(shift => {
      // Filter users by role requirements for each shift
      const availableUsers = users.filter(user => {
        if (shift === 'night') {
          return user.role === UserRole.MORGUE_STAFF || user.role === UserRole.SECURITY_STAFF
        }
        return true
      })

      // Assign users to shifts
      const selectedUser = faker.helpers.arrayElement(availableUsers)
      schedule.push({
        userId: selectedUser.id,
        role: selectedUser.role,
        shift,
        date: new Date(currentDate)
      })
    })

    currentDate.setDate(currentDate.getDate() + 1)
  }

  return schedule
}

// ID Generators
export function generateId(prefix: string, length: number): string {
  return `${prefix}-${faker.string.alphanumeric(length).toUpperCase()}`
}

export function generateBodyTag(): string {
  const year = new Date().getFullYear();
  const sequence = faker.number.int({ min: 1, max: 9999 });
  const facilityCode = faker.helpers.arrayElement(['PTA', 'JHB', 'CPT', 'DBN']);
  return `GP/${facilityCode}/${sequence}/${year}`;
}

export function generatePhoneNumber(): string {
  return `+27${faker.string.numeric(9)}`
}

export function generateFloat(min: number, max: number, precision: number): number {
  return Number(faker.number.float({ min, max, multipleOf: precision }))
}

// User Generators
export function generateFirstName() {
  return faker.person.firstName()
}

export function generateLastName() {
  return faker.person.lastName()
}

export function generateEmail(firstName: string, lastName: string, domain: string = 'example.com') {
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`
}

export function generateUser(role: UserRole): User {
  return {
    id: generateId('USER', 8),
    name: faker.person.fullName(),
    email: faker.internet.email(),
    password: faker.internet.password(),
    role: faker.helpers.arrayElement(Object.values(UserRole)),
    employeeId: generateId('EMP', 6),
    persalNumber: faker.string.numeric(8),
    department: faker.helpers.arrayElement(['Pathology', 'Morgue', 'Security', 'Field']),
    phoneNumber: generatePhoneNumber(),
    status: UserStatus.ACTIVE,
    image: faker.image.url(),
    emailVerified: faker.date.recent(),
    resetToken: faker.string.alphanumeric(10),
    resetTokenExpiry: faker.date.soon({ days: 1 }),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    institution: 'SoImagine',
    vehicleReg: `${faker.string.alpha(3).toUpperCase()}${faker.number.int({ min: 100, max: 999 })}GP`,
    supervisor: faker.person.fullName(),
    lastLogin: faker.date.recent(),
    permissions: ['all'],
  }
}

export function generateCollectionRequest(fieldWorkers: { id: string }[]) {
  const priority = faker.helpers.arrayElement(['HIGH', 'MEDIUM', 'LOW'])
  const now = new Date()
  const expectedPickupTime = faker.date.soon({ days: 1, refDate: now })

  return {
    caseNumber: generateId('CR', 8),
    collectionType: faker.helpers.arrayElement(['HOSPITAL', 'CRIME_SCENE', 'OTHER']),
    locationName: faker.company.name(),
    address: faker.location.streetAddress(),
    coordinates: {
      lat: faker.location.latitude(),
      lng: faker.location.longitude()
    },
    priority,
    status: faker.helpers.arrayElement(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']),
    assignedToId: faker.helpers.arrayElement(fieldWorkers).id,
    expectedPickupTime,
    details: faker.lorem.paragraph(),
    attachments: Array(faker.number.int({ min: 0, max: 3 }))
      .fill(null)
      .map(() => faker.image.url())
  }
}

export function generateBodyCollection(
  body: { id: string },
  fieldWorker: { id: string },
  collectionRequest?: { id: string }
) {
  const collectionTypes = ['HOSPITAL', 'CRIME_SCENE', 'OTHER'] as const
  const type = faker.helpers.arrayElement(collectionTypes)

  return {
    bodyId: body.id,
    userId: fieldWorker.id,
    name: faker.company.name(),
    institution: type === 'HOSPITAL' ? faker.company.name() : undefined,
    vehicleReg: `${faker.string.alpha(3).toUpperCase()}${faker.number.int({ min: 100, max: 999 })}GP`,
    arrivalTime: faker.date.recent(),
    gpsCoords: {
      lat: faker.location.latitude(),
      lng: faker.location.longitude()
    },
    collectionType: type,
    caseNumber: faker.helpers.maybe(() => generateId('CASE', 8)),
    hospitalRef: type === 'HOSPITAL' ? generateId('HR', 8) : undefined,
    collectionPoint: type === 'HOSPITAL'
      ? 'Hospital Morgue'
      : faker.location.streetAddress(),
    bodyDescription: faker.lorem.paragraph(),
    weatherConditions: faker.lorem.sentence(),
    temperature: generateFloat(15, 35, 1),
    identificationMarks: faker.helpers.maybe(() => faker.lorem.sentences()),
    personalEffects: faker.helpers.maybe(() => faker.lorem.paragraph()),
    witnessStatements: faker.helpers.maybe(() => faker.lorem.paragraphs(2)),
    photos: Array(faker.number.int({ min: 1, max: 5 }))
      .fill(null)
      .map(() => faker.image.url()),
    status: 'COLLECTED',
    barcodeValue: generateId('BC', 10),
    documents: faker.helpers.maybe(() => ({
      deathCertificate: faker.system.filePath(),
      policeReport: faker.system.filePath(),
      hospitalRecords: faker.system.filePath()
    })),
    collectedBy: faker.person.fullName(),
    collectionNotes: faker.helpers.maybe(() => faker.lorem.paragraph()),
    isVerified: faker.datatype.boolean(),
    verifiedBy: faker.helpers.maybe(() => fieldWorker.id),
    verifiedAt: faker.helpers.maybe(() => faker.date.recent()),
    collectionRequest: collectionRequest ? { connect: { id: collectionRequest.id } } : undefined
  }
}

// Add these body-related generators

export function generateBodyAdmission(bodyId: string, fridgeId: string) {
  return {
    bodyId,
    admissionDate: faker.date.recent().toISOString(),
    fridgeBarCode: `FRG-${fridgeId}`,
    deathRegisterNumber: generateId('DR', 10),
    estimatedTimeOfDeath: faker.helpers.maybe(() => faker.date.recent().toISOString()),
    bodyCondition: faker.helpers.arrayElement([
      'Good',
      'Fair',
      'Poor',
      'Decomposed',
      'Severely Decomposed'
    ]),
    personalBelongings: faker.helpers.maybe(() =>
      JSON.stringify(Array(faker.number.int({ min: 0, max: 5 }))
        .fill(null)
        .map(() => ({
          type: faker.helpers.arrayElement(['Clothing', 'Jewelry', 'Documents', 'Other']),
          description: faker.lorem.sentence(),
          condition: faker.helpers.arrayElement(['Good', 'Damaged', 'Poor'])
        }))
      )
    ),
    notes: faker.helpers.maybe(() => faker.lorem.paragraph()),
    photo: faker.helpers.maybe(() => faker.image.url()),
    assignedFridge: fridgeId,
    temperature: faker.number.float({ min: 2, max: 6, multipleOf: 0.1 }).toString(),
    barcodeValue: generateId('ADM', 12)
  }
}

export function generateBodyReferral(bodyId: string, userId: string) {
  const activities = Array(faker.number.int({ min: 1, max: 5 }))
    .fill(null)
    .map(() => ({
      type: faker.helpers.arrayElement(['status_change', 'security_check', 'custody_change', 'note_added']),
      timestamp: faker.date.recent(),
      details: faker.lorem.sentence(),
      metadata: {
        location: faker.helpers.arrayElement([
          'eGoli', 'eKapa', 'eThekwini', 'uTshwane', 'Bloemfontein', 'Kimberley',
          'Polokwane', 'Mbombela', 'Rustenburg', 'iBhayi', 'eMonti', 'Stellenbosch'
        ]),
        performedBy: faker.person.fullName()
      },
      user: userId
    }))

  return {
    type: faker.helpers.arrayElement(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'RETURNED']),
    securityScanResults: {
      scanDate: faker.date.recent(),
      operator: faker.person.fullName(),
      findings: faker.helpers.arrayElement(['Clear', 'Suspicious Items Detected', 'Technical Issues', 'Requires Further Investigation']),
      notes: faker.lorem.sentence()
    },
    documents: faker.helpers.maybe(() => ({
      referralForm: faker.system.filePath(),
      authorizationDoc: faker.system.filePath(),
      scanResults: faker.system.filePath()
    })),
    status: faker.helpers.arrayElement(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'IN_TRANSIT']),
    user: { id: userId },
    activities
  }
}

export function generateBodyRelease(bodyId: string, userId: string) {
  return {
    releaseDate: faker.date.recent().toISOString(),
    deathRegisterNumber: generateId('DR', 10),
    undertakerName: faker.company.name(),
    undertakerLicense: generateId('UL', 8),
    undertakerVehicle: faker.vehicle.vrm(),
    relativeDetails: {
      name: faker.person.fullName(),
      relationship: faker.helpers.arrayElement(['Parent', 'Child', 'Spouse', 'Sibling']),
      idNumber: faker.string.numeric(13),
      contactNumber: faker.phone.number(),
      address: faker.location.streetAddress()
    },
    securityOfficer: faker.person.fullName(),
    notes: faker.helpers.maybe(() => faker.lorem.paragraph()),
    photos: Array(faker.number.int({ min: 1, max: 5 }))
      .fill(null)
      .map(() => faker.image.url()),
    checklist: {
      identityVerified: true,
      documentsComplete: true,
      belongingsReturned: true,
      paymentConfirmed: true,
      authorizedRelease: true,
      conditionChecked: true
    },
    barcodeValue: generateId('REL', 12)
  }
}

export function generateBodyState(bodyId: string, fridgeId: string) {
  return {
    bodyId,
    temperature: generateFloat(2, 6, 1),
    humidity: generateFloat(30, 70, 1),
    fridgeBarCode: `FRG-${fridgeId}`,
    fridgeStatus: faker.helpers.arrayElement(['Normal', 'Warning', 'Critical']),
    fridgeTemperature: faker.number.float({ min: 2, max: 6, multipleOf: 0.1 }).toString(),
    currentLocation: faker.helpers.arrayElement(['Main Storage', 'Examination Room', 'Transit']),
    lastMovedTimestamp: faker.date.recent(),
    decompositionStage: faker.helpers.arrayElement([
      'Fresh',
      'Early Decomposition',
      'Advanced Decomposition'
    ]),
    visibleInjuries: Array(faker.number.int({ min: 0, max: 3 }))
      .fill(null)
      .map(() => faker.lorem.sentence()),
    lastHandledBy: faker.person.fullName(),
    lastHandledTimestamp: faker.date.recent(),
    lastUpdated: faker.date.recent(),
    notes: faker.lorem.paragraph()
  }
}

export function generateBodyIdentification(bodyId: string) {
  const gender = faker.helpers.arrayElement(['male', 'female']) as Gender
  const age = faker.number.int({ min: 18, max: 90 })

  return {
    bodyId,
    fullName: faker.person.fullName({ sex: _.random(0, 1) ? 'male' : 'female' }),
    age,
    gender,
    nationality: faker.location.country(),
    identificationNumber: faker.string.numeric(13),
    estimatedAgeRange: `${age - 5}-${age + 5}`,
    physicalDescription: faker.lorem.paragraph(),
    distinguishingMarks: faker.helpers.maybe(() => faker.lorem.sentences()),
    tattoos: faker.helpers.maybe(() => faker.lorem.sentences()),
    scars: faker.helpers.maybe(() => faker.lorem.sentences()),
    birthmarks: faker.helpers.maybe(() => faker.lorem.sentences()),
    additionalNotes: faker.helpers.maybe(() => faker.lorem.paragraph())
  }
}

// Helper function to generate a complete body record with all related data
export function generateCompleteBody(
  pathologistId: string,
  fieldWorkerId: string,
  fridgeId: string
) {
  const bodyId = faker.string.uuid()
  const collection = generateBodyCollection(
    { id: bodyId },
    { id: fieldWorkerId },
    undefined
  )
  const admission = generateBodyAdmission(bodyId, fridgeId)
  const state = generateBodyState(bodyId, fridgeId)
  const identification = generateBodyIdentification(bodyId)
  const referrals = Array(faker.number.int({ min: 0, max: 2 }))
    .fill(null)
    .map(() => generateBodyReferral(bodyId, pathologistId))
  const release = Math.random() > 0.5 ? generateBodyRelease(bodyId, fieldWorkerId) : null

  return {
    body: {
      id: bodyId,
      trackingNumber: generateId('TN', 8),
      bodyTag: generateId('BT', 8),
      bodyTagContent: faker.lorem.sentence(),
      status: faker.helpers.arrayElement(Object.values(BodyStatus)),
      deathRegistration: faker.string.uuid(),
    },
    collection: {
      ...collection,
      institution: faker.company.name(),
      collectionRequest: null // Remove the connect attempt completely
    },
    admission,
    state,
    identification,
    referrals,
    release
  }
}

// Pathology-related generators
export function generatePathologyReport(bodyId: string, pathologistId: string, reviewerId?: string) {
  const reportTypes = ["INITIAL", "SUPPLEMENTARY", "FINAL", "AMENDMENT"]
  const statuses = ['DRAFT', 'PENDING_REVIEW', 'REVIEWED', 'FINALIZED', 'AMENDED']

  return {
    bodyId,
    reportType: faker.helpers.arrayElement(reportTypes),
    findings: faker.lorem.paragraphs(3),
    conclusion: faker.lorem.paragraph(),
    pathologistUserId: pathologistId,
    dateCreated: faker.date.recent(),
    lastModified: faker.date.recent(),
    status: faker.helpers.arrayElement(statuses),
    reviewedByUserId: reviewerId,
    reviewDate: reviewerId ? faker.date.recent() : null,
    attachments: Array(faker.number.int({ min: 1, max: 4 }))
      .fill(null)
      .map(() => faker.system.filePath())
  }
}

export function generateLabTest(bodyId: string, requestedByUserId: string) {
    const testTypes = ['BLOOD_ANALYSIS', 'TISSUE_ANALYSIS', 'DNA_ANALYSIS', 'TOXICOLOGY_SCREEN']
    const statuses = ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED']
    const priorities = ['ROUTINE', 'URGENT', 'STAT']

  return {
    bodyId,
    testType: faker.helpers.arrayElement(testTypes),
    requestedByUserId,
    dateRequested: faker.date.recent(),
    dateCompleted: faker.helpers.maybe(() => faker.date.recent()),
    results: {
      findings: faker.lorem.paragraph(),
      values: Array(faker.number.int({ min: 2, max: 5 }))
        .fill(null)
        .map(() => ({
          parameter: faker.lorem.word(),
          value: faker.number.float({ min: 0, max: 100, fractionDigits: 2 }),
          unit: faker.helpers.arrayElement(['mg/dL', 'mmol/L', 'g/L']),
          referenceRange: `${faker.number.float({ min: 0, max: 50 })} - ${faker.number.float({ min: 51, max: 100 })}`
        }))
    },
    status: faker.helpers.arrayElement(statuses),
    priority: faker.helpers.arrayElement(priorities)
  }
}

// Security and audit generators
export function generateSecurityLog(userId?: string) {
  const types = [
    'LOGIN_ATTEMPT',
    'ACCESS_GRANTED',
    'ACCESS_DENIED',
    'PASSWORD_CHANGE',
    'PROFILE_UPDATE',
    'SYSTEM_ACCESS',
    'DATA_EXPORT',
    'CONFIGURATION_CHANGE'
  ]

  return {
    type: faker.helpers.arrayElement(types),
    userId,
    details: {
      ip: faker.internet.ip(),
      location: faker.location.city(),
      device: faker.helpers.arrayElement(['Desktop', 'Mobile', 'Tablet']),
      browser: faker.helpers.arrayElement(['Chrome', 'Firefox', 'Safari']),
      success: faker.datatype.boolean(),
      reason: faker.helpers.maybe(() => faker.lorem.sentence())
    },
    timestamp: faker.date.recent()
  }
}

export function generateChainOfCustody(
  bodyId: string,
  fromPerson: string,
  toPerson: string,
  type: CustodyRecordType
) {
  return {
    bodyId,
    fromPerson,
    toPerson,
    transferTime: faker.date.recent(),
    reason: faker.lorem.sentence(),
    notes: faker.helpers.maybe(() => faker.lorem.paragraph()),
    type,
    status: faker.helpers.arrayElement(['INITIATED', 'IN_PROGRESS', 'COMPLETED', 'VERIFIED']),
    metadata: {
      location: faker.location.city(),
      temperature: faker.number.float({ min: 2, max: 8, fractionDigits: 1 }),
      humidity: faker.number.float({ min: 30, max: 70, fractionDigits: 1 }),
      transportMethod: faker.helpers.arrayElement(['Vehicle', 'Walking', 'Elevator']),
      verifiedBy: faker.helpers.maybe(() => faker.person.fullName())
    }
  }
}

// Notification generators
export function generateNotification(
  userId: string,
  bodyId?: string,
  type: NotificationType = faker.helpers.arrayElement([
    'SYSTEM',
    'COLLECTION',
    'ASSIGNMENT',
    'DOCUMENT',
    'STATUS_UPDATE'
  ])
) {
  return {
    title: faker.lorem.sentence(),
    body: faker.lorem.paragraph(),
    type,
    priority: faker.helpers.arrayElement(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
    status: 'PENDING',
    metadata: {
      source: faker.helpers.arrayElement(['system', 'user', 'automated']),
      category: faker.helpers.arrayElement(['alert', 'info', 'action_required']),
      link: faker.helpers.maybe(() => faker.internet.url()),
      additionalInfo: faker.helpers.maybe(() => ({ key: faker.lorem.word() }))
    },
    userId,
    bodyId,
    expiresAt: faker.date.future(),
    readAt: null
  }
}

//EXTRAS
export function generateFacilitySettings() {
  return {
    facilityName: faker.company.name(),
    facilityCode: faker.string.alphanumeric(8).toUpperCase(),
    address: faker.location.streetAddress(),
    contactPerson: faker.person.fullName(),
    contactEmail: faker.internet.email(),
    contactPhone: faker.phone.number(),
    operatingHours: {
      start: '09:00',
      end: '17:00'
    },
    timezone: faker.location.timeZone(),
    language: faker.helpers.arrayElement(['en', 'es', 'fr']),
    facilityType: faker.helpers.arrayElement(['HOSPITAL', 'CLINIC', 'LABORATORY']),
    maxCapacity: faker.number.int({ min: 50, max: 500 }),
    emergencyContact: faker.phone.number(),
    accreditationNumber: faker.string.alphanumeric(12).toUpperCase(),
    accreditationExpiry: faker.date.future().toISOString(),
    facilityLicense: faker.string.alphanumeric(10).toUpperCase(),
    isEmergencyFacility: faker.datatype.boolean(),
    lastUpdated: new Date()
  }
}

export function generateStorageLocation(): StorageLocation {
  return {
    id: faker.string.uuid(),
    location: faker.location.city(),
    status: faker.helpers.arrayElement(['ACTIVE', 'MAINTENANCE', 'INACTIVE']),
    currentBody: faker.string.uuid(),
    capacity: faker.number.int({ min: 5, max: 20 }),
    temperature: faker.number.float({ min: 2, max: 6, fractionDigits: 1 }),
    lastChecked: faker.date.recent(),
    notes: faker.helpers.arrayElement([
      'Regular maintenance required',
      'Temperature stable',
      'Recent maintenance completed',
      'Monitor humidity levels'
    ]),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent()
  }
}

export function generateFridge() {
  return {
    fridgeId: faker.string.alphanumeric(10).toUpperCase(),
    name: faker.company.name(),
    capacity: faker.number.int({ min: 100, max: 1000 }),
    currentTemperature: faker.number.float({ min: 2, max: 8, fractionDigits: 1 }),
    targetTemperature: faker.number.float({ min: 2, max: 8, fractionDigits: 1 }),
    status: faker.helpers.arrayElement(['ACTIVE', 'INACTIVE']),
    lastMaintenance: faker.date.recent(),
    location: faker.location.city(),
    notes: faker.lorem.paragraph()
  }
}

export function generateSecuritySettings() {
  return {
    passwordPolicy: faker.helpers.arrayElement(['basic', 'strong', 'complex']),
    sessionTimeout: faker.number.int({ min: 15, max: 120 }),
    twoFactorEnabled: faker.datatype.boolean(),
    officerId: faker.string.uuid(),
    accessLevel: faker.helpers.arrayElement(['l1', 'l2', 'l3']),
    biometricRequired: faker.datatype.boolean(),
    scannerDeviceId: faker.string.alphanumeric(10).toUpperCase(),
    calibrationSchedule: faker.date.future().toISOString(),
    entryScanning: faker.datatype.boolean(),
    exitScanning: faker.datatype.boolean(),
    auditRetention: faker.number.int({ min: 30, max: 365 }),
    alertsEnabled: faker.datatype.boolean(),
    recordFailedAttempts: faker.datatype.boolean(),
    passwordMinLength: faker.number.int({ min: 8, max: 16 }),
    passwordExpiry: faker.number.int({ min: 30, max: 90 }),
    loginAttempts: faker.number.int({ min: 3, max: 10 }),
    lastUpdated: new Date()
  }
}

export function generateStorageSettings() {
  return {
    facilityCapacity: faker.number.int({ min: 100, max: 1000 }),
    fridgeCount: faker.number.int({ min: 5, max: 50 }),
    temperatureRange: {
      min: faker.number.float({ min: 2, max: 4, fractionDigits: 1 }),
      max: faker.number.float({ min: 6, max: 8, fractionDigits: 1 })
    },
    alertThreshold: faker.number.int({ min: 5, max: 15 }),
    scanInterval: faker.number.int({ min: 10, max: 60 }),
    lastUpdated: new Date()
  }
}

export function generateIncidentReport() {
  return {
    type: faker.helpers.arrayElement(['security', 'equipment', 'procedural']),
    description: faker.lorem.paragraph(),
    reportedBy: faker.string.uuid(),
    reportedAt: faker.date.recent(),
    status: faker.helpers.arrayElement(['open', 'investigating', 'resolved']),
    resolution: faker.datatype.boolean() ? faker.lorem.paragraph() : null,
    severity: faker.helpers.arrayElement(['low', 'medium', 'high']),
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

export function generateAuditLog() {
  return {
    action: faker.helpers.arrayElement(['create', 'update', 'delete', 'view']),
    performedBy: faker.string.uuid(),
    timestamp: faker.date.recent(),
    details: {
      description: faker.lorem.sentence(),
      changes: faker.lorem.lines(2)
    },
    metadata: {
      browser: faker.internet.userAgent(),
      platform: faker.helpers.arrayElement(['Windows', 'MacOS', 'Linux'])
    },
    userId: faker.string.uuid(),
    entityType: faker.helpers.arrayElement(['body', 'user', 'report']),
    entityId: faker.string.uuid(),
    severity: faker.helpers.arrayElement(['info', 'warning', 'error']),
    ipAddress: faker.internet.ip(),
    userAgent: faker.internet.userAgent(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
}