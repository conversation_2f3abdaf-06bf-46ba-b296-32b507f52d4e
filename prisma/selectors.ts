import { PrismaClient, UserRole, UserStatus } from '@prisma/client'

export const prisma = new PrismaClient({
  log: ['error'],
})

export const selectRandomUser = async (role) => {
  const users = await prisma.user.findMany({
    where: {
      role: role,
      status: UserStatus.ACTIVE
    },
    include: {
      teamMemberships: true,
      supervisedUsers: true
    }
  })
  
  if (users.length === 0) {
    return null
  }

  const randomIndex = Math.floor(Math.random() * users.length)
  return users[randomIndex]
}

export const selectRandom = (items) => {
  if (items.length === 0) return null
  const randomIndex = Math.floor(Math.random() * items.length)
  return items[randomIndex]
}

export const selectUsersByRole = async (role) => {
  return await prisma.user.findMany({
    where: {
      role: role,
      status: UserStatus.ACTIVE
    },
    include: {
      teamMemberships: {
        include: {
          team: true
        }
      },
      supervisedUsers: true,
      schedules: true,
      shifts: true
    }
  })
}

export const selectPathologist<PERSON>or<PERSON><PERSON> = async (bodyId) => {
  return await prisma.user.findMany({
    where: {
      role: UserRole.PATHOLOGIST,
      status: UserStatus.ACTIVE,
      assignedBodies: {
        some: {
          id: bodyId
        }
      }
    },
    include: {
      procedures: true,
      labTests: true,
      histologyReports: true,
      pathologistReports: {
        where: {
          bodyId: bodyId
        }
      },
      schedules: {
        where: {
          status: 'SCHEDULED'
        }
      }
    }
  })
}

export const selectBodiesForPathologist = async (pathologistId) => {
  return await prisma.body.findMany({
    where: {
      assignedPathologists: {
        some: {
          id: pathologistId
        }
      }
    },
    include: {
      assignedPathologists: true,
      labTests: true,
      histologyReports: true,
      pathologyReports: true
    }
  })
}

export const selectUsersForNotification = async (bodyId) => {
  return await prisma.user.findMany({
    where: {
      status: UserStatus.ACTIVE
    }
  })
}

export const selectUsersForChainOfCustody = async (bodyId) => {
  return await prisma.user.findMany({
    where: {
      OR: [
        {
          chainOfCustody: {
            some: {
              bodyId: bodyId
            }
          }
        },
        {
          role: {
            in: ['FIELD_EMPLOYEE', 'MORGUE_STAFF', 'SECURITY_STAFF', 'ADMIN', 'PATHOLOGIST']
          },
          status: 'ACTIVE'
        }
      ]
    },
    include: {
      chainOfCustody: {
        where: {
          bodyId: bodyId
        }
      }
    }
  })
}

export const selectors = {
  selectRandomUser,
  selectRandom,
  selectUsersByRole,
  selectPathologistForBody,
  selectBodiesForPathologist,
  selectUsersForChainOfCustody,
  selectUsersForNotification
}