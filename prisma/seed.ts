import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker/locale/zu_ZA';
import {
  UserRole, UserStatus, BodyStatus, CollectionType,
  ReferralType, ReferralStatus, AdmissionType,
  ReleaseStatus, FridgeStatus, TagStatus, FacilityType,
  FacilityStatus, ShiftType, DocumentType, EquipmentType,
  ReferralTimerBehavior
} from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data
  try {
    // First, delete user invitations
    await prisma.userInvitation.deleteMany();

    // Delete activity logs
    await prisma.activityLog.deleteMany();

    // Delete notifications
    await prisma.notification.deleteMany();

    // Delete role permissions
    await prisma.rolePermission.deleteMany();

    // Delete sessions and accounts
    await prisma.session.deleteMany();
    await prisma.account.deleteMany();

    // Delete body-related records in order
    await prisma.bodyReferral.deleteMany();
    await prisma.bodyRelease.deleteMany();
    await prisma.bodyAdmission.deleteMany();
    await prisma.bodyCollection.deleteMany();
    await prisma.body.deleteMany();
    await prisma.bodyTag.deleteMany();

    // Delete facility-related records
    await prisma.equipment.deleteMany();
    await prisma.document.deleteMany();
    await prisma.shift.deleteMany();
    await prisma.department.deleteMany();
    await prisma.facilityMaintenance.deleteMany();
    await prisma.adminAudit.deleteMany();
    await prisma.fridge.deleteMany();

    // Delete users
    await prisma.user.deleteMany();

    // Delete roles and permissions
    await prisma.role.deleteMany();
    await prisma.permission.deleteMany();

    // Delete facilities
    await prisma.facility.deleteMany();

    // Data cleared successfully
  } catch (error) {
    console.error('Error clearing data:', error);
    throw error;
  }

  // Create Facilities
  const facilities = await Promise.all([
    // Main Forensic Pathology Facility
    prisma.facility.create({
      data: {
        name: "Pretoria Forensic Pathology Services",
        code: "PTA-FPS",
        type: FacilityType.MORGUE,
        status: FacilityStatus.ACTIVE,
        address: "123 Steve Biko Road",
        city: "Pretoria",
        province: "Gauteng",
        postalCode: "0001",
        phone: "+27 12 123 4567",
        email: "<EMAIL>",
        totalCapacity: 150,
        currentOccupancy: 45,
        operatingHours: {
          open: "00:00",
          close: "23:59"
        },
        emergencyContact: "+27 12 321 7654",
        licenseExpiry: new Date("2025-12-31").toISOString(),
        referralTimerBehavior: ReferralTimerBehavior.CONTINUE,
        metadata: {
          accreditation: "ISO 17025:2017"
        }
      }
    }),
    // Johannesburg Facility
    prisma.facility.create({
      data: {
        name: "Johannesburg Forensic Pathology Center",
        code: "JHB-FPC",
        type: FacilityType.MORGUE,
        status: FacilityStatus.ACTIVE,
        address: "456 Helen Joseph Street",
        city: "Johannesburg",
        province: "Gauteng",
        postalCode: "2000",
        phone: "+27 11 234 5678",
        email: "<EMAIL>",
        totalCapacity: 200,
        currentOccupancy: 75,
        operatingHours: {
          open: "00:00",
          close: "23:59"
        },
        emergencyContact: "+27 11 876 5432",
        licenseExpiry: new Date("2025-12-31").toISOString(),
        referralTimerBehavior: ReferralTimerBehavior.PAUSE,
        metadata: {
          accreditation: "ISO 17025:2017"
        }
      }
    }),
    // Cape Town Facility
    prisma.facility.create({
      data: {
        name: "Cape Town Forensic Laboratory",
        code: "CPT-FPL",
        type: FacilityType.PROCESSING,
        status: FacilityStatus.ACTIVE,
        address: "789 Main Road, Observatory",
        city: "Cape Town",
        province: "Western Cape",
        postalCode: "7925",
        phone: "+27 21 345 6789",
        email: "<EMAIL>",
        totalCapacity: 100,
        currentOccupancy: 30,
        operatingHours: {
          open: "07:00",
          close: "19:00"
        },
        emergencyContact: "+27 21 987 6543",
        licenseExpiry: new Date("2025-12-31").toISOString(),
        referralTimerBehavior: ReferralTimerBehavior.CONTINUE,
        metadata: {
          accreditation: "ISO 17025:2017"
        }
      }
    }),
    // Durban Storage Facility
    prisma.facility.create({
      data: {
        name: "Durban Forensic Storage Center",
        code: "DBN-FSC",
        type: FacilityType.STORAGE,
        status: FacilityStatus.ACTIVE,
        address: "321 Pixley ka Seme Street",
        city: "Durban",
        province: "KwaZulu-Natal",
        postalCode: "4001",
        phone: "+27 31 456 7890",
        email: "<EMAIL>",
        totalCapacity: 80,
        currentOccupancy: 20,
        operatingHours: {
          open: "06:00",
          close: "22:00"
        },
        emergencyContact: "+27 31 234 5678",
        licenseExpiry: new Date("2025-12-31").toISOString(),
        referralTimerBehavior: ReferralTimerBehavior.PAUSE,
        metadata: {
          accreditation: "ISO 17025:2017"
        }
      }
    }),
    // Temporary Facility
    prisma.facility.create({
      data: {
        name: "Bloemfontein Temporary Facility",
        code: "BFN-TMP",
        type: FacilityType.TEMPORARY,
        status: FacilityStatus.ACTIVE,
        address: "101 Nelson Mandela Drive",
        city: "Bloemfontein",
        province: "Free State",
        postalCode: "9301",
        phone: "+27 51 567 8901",
        email: "<EMAIL>",
        totalCapacity: 30,
        currentOccupancy: 5,
        operatingHours: {
          open: "08:00",
          close: "17:00"
        },
        emergencyContact: "+27 51 123 4567",
        licenseExpiry: new Date("2024-12-31").toISOString(),
        referralTimerBehavior: ReferralTimerBehavior.CONTINUE,
        metadata: {
          accreditation: "ISO 17025:2017"
        }
      }
    })
  ]);

  // Create Permissions
  const permissionData = [
    // User Management
    {
      name: 'users.view',
      description: 'View user profiles and details',
      category: 'User Management'
    },
    {
      name: 'users.create',
      description: 'Create new user accounts',
      category: 'User Management'
    },
    {
      name: 'users.edit',
      description: 'Modify existing user accounts',
      category: 'User Management'
    },
    {
      name: 'users.delete',
      description: 'Delete users',
      category: 'User Management'
    },
    // Role Management
    {
      name: 'roles.view',
      description: 'View roles and their permissions',
      category: 'Role Management'
    },
    {
      name: 'roles.create',
      description: 'Create new roles',
      category: 'Role Management'
    },
    {
      name: 'roles.edit',
      description: 'Modify existing roles',
      category: 'Role Management'
    },
    {
      name: 'roles.delete',
      description: 'Delete roles',
      category: 'Role Management'
    },
    // Body Management
    {
      name: 'bodies.view',
      description: 'View body details',
      category: 'Body Management'
    },
    {
      name: 'bodies.create',
      description: 'Create new body records',
      category: 'Body Management'
    },
    {
      name: 'bodies.edit',
      description: 'Edit body details',
      category: 'Body Management'
    },
    {
      name: 'bodies.delete',
      description: 'Delete body records',
      category: 'Body Management'
    },
    // Collection Management
    {
      name: 'collections.view',
      description: 'View collection details',
      category: 'Collection Management'
    },
    {
      name: 'collections.create',
      description: 'Create new collections',
      category: 'Collection Management'
    },
    {
      name: 'collections.edit',
      description: 'Edit collection details',
      category: 'Collection Management'
    },
    {
      name: 'collections.delete',
      description: 'Delete collections',
      category: 'Collection Management'
    },
    // Release Management
    {
      name: 'releases.view',
      description: 'View release details',
      category: 'Release Management'
    },
    {
      name: 'releases.create',
      description: 'Create new releases',
      category: 'Release Management'
    },
    {
      name: 'releases.edit',
      description: 'Edit release details',
      category: 'Release Management'
    },
    {
      name: 'releases.delete',
      description: 'Delete releases',
      category: 'Release Management'
    },
    // System Settings
    {
      name: 'settings.view',
      description: 'View system settings',
      category: 'System Settings'
    },
    {
      name: 'settings.edit',
      description: 'Edit system settings',
      category: 'System Settings'
    }
  ];

  const createdPermissions = await Promise.all(
    permissionData.map(permission =>
      prisma.permission.upsert({
        where: { name: permission.name },
        update: permission,
        create: permission
      })
    )
  );

  // Create Roles
  const superAdminPermissions = createdPermissions.map(p => p.id);

  const adminPermissions = createdPermissions
    .filter(p =>
      p.name.startsWith('users.view') ||
      p.name.startsWith('users.create') ||
      p.name.startsWith('users.edit') ||
      p.name.startsWith('roles.view')
    )
    .map(p => p.id);

  const pathologistPermissions = createdPermissions
    .filter(p =>
      p.name.startsWith('bodies.view') ||
      p.name.startsWith('bodies.edit') ||
      p.name.startsWith('collections.view') ||
      p.name.startsWith('releases.view')
    )
    .map(p => p.id);

  const morgueStaffPermissions = createdPermissions
    .filter(p =>
      p.name.startsWith('bodies.view') ||
      p.name.startsWith('bodies.edit') ||
      p.name.startsWith('collections.view') ||
      p.name.startsWith('collections.edit') ||
      p.name.startsWith('releases.view') ||
      p.name.startsWith('releases.edit')
    )
    .map(p => p.id);

  const fieldEmployeePermissions = createdPermissions
    .filter(p =>
      p.name.startsWith('collections.view') ||
      p.name.startsWith('collections.create') ||
      p.name.startsWith('collections.edit') ||
      p.name.startsWith('bodies.view')
    )
    .map(p => p.id);

  // Create roles with permissions
  const superAdminRole = await prisma.role.create({
    data: {
      name: 'Super Admin',
      description: 'Full system access',
      permissions: {
        create: superAdminPermissions.map(permissionId => ({
          permissionId
        }))
      }
    }
  });

  const adminRole = await prisma.role.create({
    data: {
      name: 'Admin',
      description: 'System administration with limited permissions',
      permissions: {
        create: adminPermissions.map(permissionId => ({
          permissionId
        }))
      }
    }
  });

  const pathologistRole = await prisma.role.create({
    data: {
      name: 'Pathologist',
      description: 'Access to pathology-related features',
      permissions: {
        create: pathologistPermissions.map(permissionId => ({
          permissionId
        }))
      }
    }
  });

  const morgueStaffRole = await prisma.role.create({
    data: {
      name: 'Morgue Staff',
      description: 'Access to morgue operations',
      permissions: {
        create: morgueStaffPermissions.map(permissionId => ({
          permissionId
        }))
      }
    }
  });

  const fieldEmployeeRole = await prisma.role.create({
    data: {
      name: 'Field Employee',
      description: 'Access to field operations',
      permissions: {
        create: fieldEmployeePermissions.map(permissionId => ({
          permissionId
        }))
      }
    }
  });

  // Create predefined test users with hashed passwords
  const hashedAdminPassword = await hash("Password@123!", 12);
  const hashedFieldPassword = await hash("Field@123", 12);
  const hashedMorguePassword = await hash("Morgue@123", 12);
  const hashedSecurityPassword = await hash("Security@123", 12);
  const hashedPathologistPassword = await hash("Path@123", 12);

  const predefinedUsers = await Promise.all([
    // Admin users
    // Main admin
    prisma.user.create({
      data: {
        name: "System Administrator",
        email: "<EMAIL>",
        password: hashedAdminPassword,
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
        department: "Administration",
        facilityId: facilities[0].id,
        persalNumber: "ADM12345",
        phoneNumber: "+27 12 345 6789",
        address: "123 Admin Street, Pretoria",
        emergencyContact: "+27 12 987 6543",
        position: "System Administrator",
        roles: {
          connect: { id: superAdminRole.id }
        },
        facilities: {
          connect: [{ id: facilities[0].id }]
        }
      }
    }),
    // Secondary admin
    prisma.user.create({
      data: {
        name: "Shawn Danisa",
        email: "<EMAIL>",
        password: hashedAdminPassword,
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
        department: "Administration",
        facilityId: facilities[1].id,
        persalNumber: "ADM54321",
        phoneNumber: "+27 11 123 4567",
        address: "456 Admin Avenue, Johannesburg",
        emergencyContact: "+27 11 765 4321",
        position: "System Administrator",
        roles: {
          connect: { id: adminRole.id }
        },
        facilities: {
          connect: [{ id: facilities[1].id }]
        }
      }
      }),

    // Client Admin
    prisma.user.create({
      data: {
        name: "Elizka Ivey",
        email: "<EMAIL>",
        password: hashedAdminPassword,
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
        department: "Administration",
        facilityId: facilities[1].id,
        persalNumber: "ADM54321",
        phoneNumber: "+27 11 123 4567",
        address: "456 Admin Avenue, Johannesburg",
        emergencyContact: "+27 11 765 4321",
        position: "System Administrator",
        roles: {
          connect: { id: adminRole.id }
        },
        facilities: {
          connect: [{ id: facilities[1].id }]
        }
      }
      }),

    // Field Employee
    prisma.user.create({
      data: {
        name: "Field Worker",
        email: "<EMAIL>",
        password: hashedFieldPassword,
        role: UserRole.FIELD_EMPLOYEE,
        status: UserStatus.ACTIVE,
        department: "Field Operations",
        facilityId: facilities[0].id,
        persalNumber: "FLD12345",
        phoneNumber: "+27 12 345 6790",
        address: "456 Field Street, Pretoria",
        emergencyContact: "+27 12 987 6544",
        position: "Field Collection Officer",
        roles: {
          connect: { id: fieldEmployeeRole.id }
        },
        facilities: {
          connect: [{ id: facilities[0].id }, { id: facilities[1].id }]
        }
      }
    }),
    // Morgue Staff
    prisma.user.create({
      data: {
        name: "Morgue Staff",
        email: "<EMAIL>",
        password: hashedMorguePassword,
        role: UserRole.MORGUE_STAFF,
        status: UserStatus.ACTIVE,
        department: "Morgue Operations",
        facilityId: facilities[0].id,
        persalNumber: "MRG12345",
        phoneNumber: "+27 12 345 6791",
        address: "789 Morgue Street, Pretoria",
        emergencyContact: "+27 12 987 6545",
        position: "Morgue Technician",
        roles: {
          connect: { id: morgueStaffRole.id }
        },
        facilities: {
          connect: [{ id: facilities[0].id }]
        }
      }
    }),
    // Security Staff
    prisma.user.create({
      data: {
        name: "Security Officer",
        email: "<EMAIL>",
        password: hashedSecurityPassword,
        role: UserRole.SECURITY_STAFF,
        status: UserStatus.ACTIVE,
        department: "Security",
        facilityId: facilities[0].id,
        persalNumber: "SEC12345",
        phoneNumber: "+27 12 345 6792",
        address: "101 Security Street, Pretoria",
        emergencyContact: "+27 12 987 6546",
        position: "Security Officer",
        roles: {
          connect: { id: adminRole.id }
        },
        facilities: {
          connect: [{ id: facilities[0].id }, { id: facilities[2].id }]
        }
      }
    }),
    // Pathologist
    prisma.user.create({
      data: {
        name: "Dr. Pathologist",
        email: "<EMAIL>",
        password: hashedPathologistPassword,
        role: UserRole.PATHOLOGIST,
        status: UserStatus.ACTIVE,
        department: "Pathology",
        facilityId: facilities[0].id,
        persalNumber: "PTH12345",
        phoneNumber: "+27 12 345 6793",
        address: "202 Pathology Street, Pretoria",
        emergencyContact: "+27 12 987 6547",
        position: "Senior Pathologist",
        roles: {
          connect: { id: pathologistRole.id }
        },
        facilities: {
          connect: [{ id: facilities[0].id }, { id: facilities[1].id }, { id: facilities[2].id }]
        }
      }
    })
  ]);

  // Create random Users
  const randomUsers = await Promise.all(
    Array(10).fill(null).map(async () => {
      const randomPassword = faker.internet.password();
      const hashedPassword = await hash(randomPassword, 12);
      const randomRole = faker.helpers.arrayElement([
        superAdminRole, adminRole, pathologistRole, morgueStaffRole, fieldEmployeeRole
      ]);

      // Assign 1-3 random facilities to each user
      const assignedFacilities = [];
      const numFacilities = faker.number.int({ min: 1, max: 3 });
      for (let i = 0; i < numFacilities; i++) {
        const facility = faker.helpers.arrayElement(facilities);
        if (!assignedFacilities.some(f => f.id === facility.id)) {
          assignedFacilities.push(facility);
        }
      }

      const primaryFacility = faker.helpers.arrayElement(assignedFacilities);

      return prisma.user.create({
        data: {
          name: faker.person.fullName(),
          email: faker.internet.email(),
          password: hashedPassword,
          role: faker.helpers.arrayElement(Object.values(UserRole)),
          status: faker.helpers.arrayElement(Object.values(UserStatus)),
          department: faker.commerce.department(),
          facilityId: primaryFacility.id,
          persalNumber: faker.string.alphanumeric(8).toUpperCase(),
          phoneNumber: faker.phone.number(),
          address: faker.location.streetAddress(),
          emergencyContact: faker.phone.number(),
          position: faker.person.jobTitle(),
          roles: {
            connect: { id: randomRole.id }
          },
          facilities: {
            connect: assignedFacilities.map(f => ({ id: f.id }))
          }
        }
      });
    })
  );

  // Combine predefined and random users
  const users = [...predefinedUsers, ...randomUsers];

  // Create Fridges
  const fridges = await Promise.all([
    // Pretoria Facility Fridges (10 units)
    ...Array(10).fill(null).map((_, index) =>
      prisma.fridge.create({
        data: {
          fridgeNumber: `PTA-F${String(index + 1).padStart(3, '0')}`,
          barcode: `PTA${String(index + 1).padStart(5, '0')}`,
          temperature: -2.5,
          status: FridgeStatus.AVAILABLE,
          capacity: 4,
          currentOccupancy: 0,
          facilityId: facilities[0].id // Pretoria facility
        }
      })
    ),
    // Johannesburg Facility Fridges (15 units)
    ...Array(15).fill(null).map((_, index) =>
      prisma.fridge.create({
        data: {
          fridgeNumber: `JHB-F${String(index + 1).padStart(3, '0')}`,
          barcode: `JHB${String(index + 1).padStart(5, '0')}`,
          temperature: -2.0,
          status: index < 10 ? FridgeStatus.AVAILABLE : FridgeStatus.MAINTENANCE,
          capacity: 4,
          currentOccupancy: index < 10 ? faker.number.int({ min: 0, max: 3 }) : 0,
          facilityId: facilities[1].id // Johannesburg facility
        }
      })
    ),
    // Cape Town Facility Fridges (8 units)
    ...Array(8).fill(null).map((_, index) =>
      prisma.fridge.create({
        data: {
          fridgeNumber: `CPT-F${String(index + 1).padStart(3, '0')}`,
          barcode: `CPT${String(index + 1).padStart(5, '0')}`,
          temperature: -3.0,
          status: FridgeStatus.AVAILABLE,
          capacity: 3,
          currentOccupancy: faker.number.int({ min: 0, max: 2 }),
          facilityId: facilities[2].id // Cape Town facility
        }
      })
    ),
    // Durban Facility Fridges (6 units)
    ...Array(6).fill(null).map((_, index) =>
      prisma.fridge.create({
        data: {
          fridgeNumber: `DBN-F${String(index + 1).padStart(3, '0')}`,
          barcode: `DBN${String(index + 1).padStart(5, '0')}`,
          temperature: -2.8,
          status: index < 5 ? FridgeStatus.AVAILABLE : FridgeStatus.OFFLINE,
          capacity: 3,
          currentOccupancy: index < 5 ? faker.number.int({ min: 0, max: 2 }) : 0,
          facilityId: facilities[3].id // Durban facility
        }
      })
    ),
    // Bloemfontein Facility Fridges (4 units)
    ...Array(4).fill(null).map((_, index) =>
      prisma.fridge.create({
        data: {
          fridgeNumber: `BFN-F${String(index + 1).padStart(3, '0')}`,
          barcode: `BFN${String(index + 1).padStart(5, '0')}`,
          temperature: -2.5,
          status: FridgeStatus.AVAILABLE,
          capacity: 2,
          currentOccupancy: faker.number.int({ min: 0, max: 1 }),
          facilityId: facilities[4].id // Bloemfontein facility
        }
      })
    )
  ]);

  // Create Bodies and related records - reduced quantity with proper relationships
  await Promise.all(
    Array(15).fill(null).map(async (_, index) => {
      // Create BodyTag first with consistent status
      const tagStatus = index < 10 ? TagStatus.COLLECTED : TagStatus.GENERATED
      ;
      const facilityForTag = faker.helpers.arrayElement(facilities);
      const generatedByUser = faker.helpers.arrayElement(users);
      const lastScannedByUser = faker.helpers.arrayElement(users);

      const bodyTag = await prisma.bodyTag.create({
        data: {
          tagNumber: `TAG-${faker.string.alphanumeric(8).toUpperCase()}`,
          generatedBy: generatedByUser.id,
          status: tagStatus,
          lastScannedAt: faker.date.recent(),
          lastScannedBy: lastScannedByUser.id,
          notes: faker.lorem.sentence(),
          qrCodeValue: faker.string.uuid(),
          qrCodeFormat: 'QR_CODE',
          qrCodeMetadata: {
            generatedAt: new Date().toISOString(),
            facility: facilityForTag.name
          },
          facilityId: facilityForTag.id
        }
      });

      // Determine body status based on index for a good distribution
      let bodyStatus: BodyStatus;
      if (index < 5) {
        bodyStatus = BodyStatus.ADMITTED;
      } else if (index < 8) {
        bodyStatus = BodyStatus.IN_STORAGE;
      } else if (index < 10) {
        bodyStatus = BodyStatus.PENDING_RELEASE;
      } else if (index < 12) {
        bodyStatus = BodyStatus.REFERRED;
      } else if (index < 14) {
        bodyStatus = BodyStatus.SECURITY_VERIFIED;
      } else {
        bodyStatus = BodyStatus.RELEASED;
      }

      // Create Body with enhanced fields
      const gender = faker.helpers.arrayElement(['Male', 'Female']);
      const approximateAge = faker.number.int({ min: 1, max: 95 });
      const height = faker.number.float({ min: 1.4, max: 2.1, fractionDigits: 2 });
      const weight = faker.number.float({ min: 40, max: 120, fractionDigits: 1 });
      const dateOfDeath = faker.date.recent({ days: 30 });

      const body = await prisma.body.create({
        data: {
          trackingNumber: `TRK-${faker.string.alphanumeric(10).toUpperCase()}`,
          bodyTagId: bodyTag.id,
          status: bodyStatus,
          deathRegistration: `DR-${faker.string.alphanumeric(12).toUpperCase()}`,
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          gender: gender,
          approximateAge: approximateAge,
          height: height,
          weight: weight,
          distinguishingFeatures: faker.helpers.arrayElement([
            'Tattoo on right arm',
            'Scar on left cheek',
            'Missing finger on right hand',
            'Birthmark on neck',
            'Surgical scar on abdomen',
            'None visible',
            'Burn marks on left leg',
            'Pierced ears',
            'Dental work',
            'Amputated left foot'
          ]),
          causeOfDeath: faker.helpers.arrayElement([
            'Natural causes',
            'Cardiac arrest',
            'Trauma',
            'Unknown - pending investigation',
            'Respiratory failure',
            'Drowning',
            'Gunshot wound',
            'Blunt force trauma',
            'Stabbing',
            'Motor vehicle accident'
          ]),
          placeOfDeath: faker.helpers.arrayElement([
            'Hospital',
            'Residence',
            'Public place',
            'Workplace',
            'Road accident',
            'Unknown'
          ]),
          dateOfDeath: dateOfDeath,
          photos: Array(faker.number.int({ min: 1, max: 3 })).fill(null).map(() => faker.image.url()),
          documents: Array(faker.number.int({ min: 0, max: 2 })).fill(null).map(() => faker.image.url()),
          metadata: {
            identificationMethod: faker.helpers.arrayElement(['Visual', 'Fingerprints', 'Dental records', 'DNA', 'Personal effects']),
            identifiedBy: faker.person.fullName(),
            identificationDate: faker.date.recent().toISOString(),
            additionalNotes: faker.lorem.paragraph()
          }
        }
      });

      // Create BodyCollection
      const collectionScenarios = [
        // Gauteng Hospitals
        {
          name: "Chris Hani Baragwanath Hospital",
          coords: { latitude: -26.2607, longitude: 27.9426 },
          city: "Johannesburg",
          type: CollectionType.HOSPITAL
        },
        {
          name: "Steve Biko Academic Hospital",
          coords: { latitude: -25.7313, longitude: 28.2088 },
          city: "Pretoria",
          type: CollectionType.HOSPITAL
        },
        // Western Cape Hospitals
        {
          name: "Groote Schuur Hospital",
          coords: { latitude: -33.9423, longitude: 18.4657 },
          city: "Cape Town",
          type: CollectionType.HOSPITAL
        },
        // Crime Scenes
        {
          name: "Residential Area - Brooklyn",
          coords: { latitude: -25.7544, longitude: 28.2376 },
          city: "Pretoria",
          type: CollectionType.CRIME_SCENE
        },
        // Other Locations
        {
          name: "Retirement Home - Rose Park",
          coords: { latitude: -26.1089, longitude: 28.0567 },
          city: "Johannesburg",
          type: CollectionType.OTHER
        }
      ];

      const scenario = faker.helpers.arrayElement(collectionScenarios);
      const collectionUser = faker.helpers.arrayElement(users.filter(u =>
        (u as any).role === UserRole.FIELD_EMPLOYEE || (u as any).role === UserRole.MORGUE_STAFF
      ));

      await prisma.bodyCollection.create({
        data: {
          bodyId: body.id,
          userId: collectionUser.id,
          name: body.firstName + " " + body.lastName,
          institution: scenario.name,
          vehicleReg: `FPS ${faker.number.int({ min: 100, max: 999 })} GP`,
          arrivalTime: faker.date.recent({ days: 5 }),
          gpsCoords: scenario.coords,
          collectionType: scenario.type,
          bodyDescription: `${gender} body, estimated age ${approximateAge} years, ${faker.helpers.arrayElement(['natural causes', 'suspected unnatural causes', 'pending investigation'])}`,
          sceneDescription: faker.lorem.paragraph(1),
          weatherConditions: faker.helpers.arrayElement(['Sunny', 'Cloudy', 'Rainy', 'Windy', 'Hot', 'Cold']),
          temperature: faker.number.float({ min: 15, max: 35, fractionDigits: 1 }),
          collectionNotes: faker.lorem.sentences(2),
          photos: Array(2).fill(null).map(() => faker.image.url()),
          status: BodyStatus.COLLECTED,
          barcodeValue: `BC-${faker.string.alphanumeric(10).toUpperCase()}`
        }
      });

      // Create BodyAdmission
      const facility = faker.helpers.arrayElement(facilities);
      const assignedTo = faker.helpers.arrayElement(users.filter(u =>
        (u as any).role === UserRole.MORGUE_STAFF || (u as any).role === UserRole.PATHOLOGIST
      ));
      const createdBy = faker.helpers.arrayElement(users.filter(u =>
        (u as any).role === UserRole.ADMIN || (u as any).role === UserRole.MORGUE_STAFF
      ));

      const bodyAdmission = await prisma.bodyAdmission.create({
        data: {
          facilityId: facility.id,
          bodyId: body.id,
          bodyTagId: bodyTag.id,
          admissionType: faker.helpers.arrayElement(Object.values(AdmissionType)),
          admissionDate: faker.date.recent({ days: 10 }),
          status: "ACTIVE",
          deathRegisterNumber: `DRN-${faker.string.alphanumeric(8).toUpperCase()}`,
          bodyCondition: faker.lorem.sentence(),
          notes: faker.lorem.paragraph(1),
          photo: faker.image.url(),
          assignedFridge: faker.helpers.arrayElement(fridges).fridgeNumber,
          temperature: faker.number.float({ min: -5, max: 5, fractionDigits: 1 }),
          barcodeValue: `BA-${faker.string.alphanumeric(10).toUpperCase()}`,
          assignedToId: assignedTo.id,
          createdById: createdBy.id
        }
      });

      // Create activity log for the admission
      await prisma.activityLog.create({
        data: {
          userId: createdBy.id,
          activityType: "BODY_ADMITTED",
          description: `Body ${body.trackingNumber} admitted to ${facility.name}`,
          bodyAdmissionId: bodyAdmission.id
        }
      });

      // Create notification for the admission
      await prisma.notification.create({
        data: {
          userId: assignedTo.id,
          title: "New Body Admission",
          message: `New body admission ${body.trackingNumber} has been assigned to you.`,
          type: "ADMISSION_ASSIGNMENT",
          isRead: false
        }
      });

      // Create BodyReferral for bodies with REFERRED status
      if (body.status === BodyStatus.REFERRED) {
        const referralTypes = [
          {
            type: ReferralType.LODOX,
            specimenKitNumber: `LODOX-${faker.string.alphanumeric(8).toUpperCase()}`,
            evidenceBagSerial: `EB-${faker.string.alphanumeric(10).toUpperCase()}`
          },
          {
            type: ReferralType.XRAY,
            specimenKitNumber: `XRAY-${faker.string.alphanumeric(8).toUpperCase()}`,
            evidenceBagSerial: `EB-${faker.string.alphanumeric(10).toUpperCase()}`
          },
          {
            type: ReferralType.SPECIMEN,
            specimenKitNumber: `SPEC-${faker.string.alphanumeric(8).toUpperCase()}`,
            evidenceBagSerial: `EB-${faker.string.alphanumeric(10).toUpperCase()}`
          }
        ];

        const referral = faker.helpers.arrayElement(referralTypes);
        const referredByUser = faker.helpers.arrayElement(users.filter(u =>
          (u as any).role === UserRole.PATHOLOGIST ||
          (u as any).role === UserRole.MORGUE_STAFF
        ));
        const assignedToUser = faker.helpers.arrayElement(users.filter(u =>
          (u as any).role === UserRole.PATHOLOGIST
        ));

        await prisma.bodyReferral.create({
          data: {
            referralType: referral.type,
            status: ReferralStatus.IN_PROGRESS,
            referralDate: faker.date.recent({ days: 5 }).toISOString(),
            returnDate: faker.date.recent({ days: 10 }),
            employeePersal: referredByUser.persalNumber || faker.string.alphanumeric(8).toUpperCase(),
            employeeName: referredByUser.name || faker.person.fullName(),
            institutionName: faker.company.name(),
            vehicleReg: `${faker.string.alpha(3).toUpperCase()} ${faker.number.int({ min: 100, max: 999 })} GP`,
            specimenKitNumber: referral.specimenKitNumber,
            evidenceBagSerial: referral.evidenceBagSerial,
            bodyTagPhoto: faker.image.url(),
            vehiclePhoto: faker.image.url(),
            gpsLatitude: faker.location.latitude() as number,
            gpsLongitude: faker.location.longitude() as number,
            body: {
              connect: {
                id: body.id
              }
            },
            referredBy: {
              connect: {
                id: referredByUser.id
              }
            },
            assignedTo: {
              connect: {
                id: assignedToUser.id
              }
            }
          }
        });
      }

      // Create BodyRelease for bodies with PENDING_RELEASE, SECURITY_VERIFIED or RELEASED status
      if (body.status === BodyStatus.PENDING_RELEASE ||
          body.status === BodyStatus.SECURITY_VERIFIED ||
          body.status === BodyStatus.RELEASED) {
        const relationshipTypes = [
          "Spouse",
          "Child",
          "Parent",
          "Sibling",
          "Legal Representative",
          "Funeral Home Director"
        ];

        const identificationTypes = [
          "National ID",
          "Passport",
          "Driver's License",
          "Military ID",
          "Professional License"
        ];

        // Select appropriate status based on body status
        let releaseStatus: ReleaseStatus;
        if (body.status === BodyStatus.PENDING_RELEASE) {
          releaseStatus = ReleaseStatus.PENDING;
        } else if (body.status === BodyStatus.SECURITY_VERIFIED) {
          releaseStatus = ReleaseStatus.VERIFIED;
        } else {
          releaseStatus = ReleaseStatus.COMPLETED;
        }

        const releasedByUser = faker.helpers.arrayElement(users.filter(u =>
          (u as any).role === UserRole.MORGUE_STAFF ||
          (u as any).role === UserRole.SECURITY_STAFF
        ));

        const verifiedByUser = body.status === BodyStatus.SECURITY_VERIFIED || body.status === BodyStatus.RELEASED
          ? faker.helpers.arrayElement(users.filter(u => (u as any).role === UserRole.SECURITY_STAFF))
          : null;

        await prisma.bodyRelease.create({
          data: {
            bodyId: body.id,
            facilityId: facility.id,
            releaseDate: faker.date.recent({ days: 3 }).toISOString(),
            releasedTo: faker.person.fullName(),
            relationship: faker.helpers.arrayElement(relationshipTypes),
            identificationDocument: `${faker.helpers.arrayElement(identificationTypes)} - ${faker.string.alphanumeric(10).toUpperCase()}`,
            contactDetails: faker.phone.number(),
            notes: faker.helpers.arrayElement([
              "All documentation verified and complete",
              "Next of kin properly identified",
              "Release authorized by pathologist",
              "Funeral home transfer arranged",
              "Special handling instructions provided"
            ]),
            status: releaseStatus,
            releasedById: releasedByUser.id,
            verifiedById: verifiedByUser?.id || null
          }
        });
      }

      return body;
    })
  );

  // Create additional records
  await Promise.all([
    // Create Departments
    ...[
      // Core Departments
      {
        name: "Forensic Pathology Services",
        code: "FPS",
        description: "Main department responsible for conducting autopsies and forensic investigations",
        headUserId: predefinedUsers.find(u => u.role === UserRole.PATHOLOGIST)?.id
      },
      {
        name: "Body Collection & Transport",
        code: "BCT",
        description: "Manages the collection and transportation of deceased bodies",
        headUserId: predefinedUsers.find(u => u.role === UserRole.FIELD_EMPLOYEE)?.id
      },
      {
        name: "Morgue Operations",
        code: "MOP",
        description: "Oversees daily morgue operations and body storage",
        headUserId: predefinedUsers.find(u => u.role === UserRole.MORGUE_STAFF)?.id
      },
      {
        name: "Security & Access Control",
        code: "SEC",
        description: "Manages facility security and access control",
        headUserId: predefinedUsers.find(u => u.role === UserRole.SECURITY_STAFF)?.id
      },
      // Support Departments
      {
        name: "Quality Assurance",
        code: "QA",
        description: "Ensures compliance with quality standards and procedures",
        headUserId: predefinedUsers.find(u => u.role === UserRole.ADMIN)?.id
      },
      {
        name: "Equipment & Maintenance",
        code: "EM",
        description: "Manages facility equipment and maintenance schedules",
        headUserId: randomUsers[0].id
      },
      {
        name: "Documentation & Records",
        code: "DR",
        description: "Handles documentation and record keeping",
        headUserId: randomUsers[1].id
      },
      {
        name: "Training & Development",
        code: "TD",
        description: "Manages staff training and professional development",
        headUserId: randomUsers[2].id
      },
      {
        name: "Health & Safety",
        code: "HS",
        description: "Oversees occupational health and safety compliance",
        headUserId: randomUsers[3].id
      },
      {
        name: "IT & Systems",
        code: "IT",
        description: "Manages information systems and technical infrastructure",
        headUserId: randomUsers[4].id
      }
    ].map(dept =>
      prisma.department.create({
        data: {
          ...dept,
          facilityId: facilities[0].id
        }
      })
    ),

    // Create Shifts
    ...Array(7).fill(null).flatMap((_, dayIndex) => {
      const shiftDate = new Date();
      shiftDate.setDate(shiftDate.getDate() + dayIndex);

      return [
        // Morning Shift (07:00 - 15:00)
        {
          type: ShiftType.MORNING,
          startTime: new Date(shiftDate.setHours(7, 0, 0, 0)).toISOString(),
          endTime: new Date(shiftDate.setHours(15, 0, 0, 0)).toISOString(),
          notes: "Regular morning shift",
          isComplete: dayIndex === 0
        },
        // Afternoon Shift (15:00 - 23:00)
        {
          type: ShiftType.AFTERNOON,
          startTime: new Date(shiftDate.setHours(15, 0, 0, 0)).toISOString(),
          endTime: new Date(shiftDate.setHours(23, 0, 0, 0)).toISOString(),
          notes: "Regular afternoon shift",
          isComplete: dayIndex === 0
        },
        // Night Shift (23:00 - 07:00)
        {
          type: ShiftType.NIGHT,
          startTime: new Date(shiftDate.setHours(23, 0, 0, 0)).toISOString(),
          endTime: new Date(new Date(shiftDate).setHours(31, 0, 0, 0)).toISOString(),
          notes: "Regular night shift",
          isComplete: dayIndex === 0
        },
        // On-Call Shift (24 hours)
        {
          type: ShiftType.ON_CALL,
          startTime: new Date(shiftDate.setHours(0, 0, 0, 0)).toISOString(),
          endTime: new Date(shiftDate.setHours(24, 0, 0, 0)).toISOString(),
          notes: "24-hour on-call coverage",
          isComplete: dayIndex === 0
        }
      ];
    }).map(shift => {
      let eligibleStaff = users;
      switch (shift.type) {
        case ShiftType.MORNING:
        case ShiftType.AFTERNOON:
          eligibleStaff = users.filter(u => (u as any).role !== UserRole.ADMIN);
          break;
        case ShiftType.NIGHT:
          eligibleStaff = users.filter(u =>
            (u as any).role === UserRole.MORGUE_STAFF ||
            (u as any).role === UserRole.SECURITY_STAFF
          );
          break;
        case ShiftType.ON_CALL:
          eligibleStaff = users.filter(u =>
            (u as any).role === UserRole.PATHOLOGIST ||
            (u as any).role === UserRole.FIELD_EMPLOYEE
          );
          break;
      }

      return prisma.shift.create({
        data: {
          ...shift,
          userId: faker.helpers.arrayElement(eligibleStaff).id,
          facilityId: faker.helpers.arrayElement(facilities).id
        }
      });
    }),

    // Create Documents
    ...Array(20).fill(null).map(() => {
      const documentTypes = [
        {
          title: "Standard Operating Procedure - Body Collection",
          type: DocumentType.PROCEDURE,
          description: "Detailed procedures for collecting and transporting deceased bodies",
          version: "2.1"
        },
        {
          title: "Chain of Custody Form",
          type: DocumentType.FORM,
          description: "Official form for tracking body custody transfers",
          version: "1.3"
        },
        {
          title: "Facility Safety Protocol",
          type: DocumentType.POLICY,
          description: "Safety guidelines and protocols for facility operations",
          version: "3.0"
        },
        {
          title: "Monthly Quality Assurance Report",
          type: DocumentType.REPORT,
          description: "Quality management system compliance report",
          version: "1.0"
        },
        {
          title: "Death Certificate Template",
          type: DocumentType.CERTIFICATE,
          description: "Official template for death certificate issuance",
          version: "2.0"
        },
        {
          title: "Morgue Equipment Maintenance Guide",
          type: DocumentType.PROCEDURE,
          description: "Procedures for maintaining morgue equipment",
          version: "1.5"
        },
        {
          title: "Incident Report Form",
          type: DocumentType.FORM,
          description: "Form for reporting workplace incidents and accidents",
          version: "2.2"
        },
        {
          title: "Biohazard Handling Policy",
          type: DocumentType.POLICY,
          description: "Guidelines for handling biohazardous materials",
          version: "2.4"
        },
        {
          title: "Annual Facility Audit Template",
          type: DocumentType.FORM,
          description: "Template for conducting annual facility audits",
          version: "1.1"
        },
        {
          title: "Emergency Response Protocol",
          type: DocumentType.PROCEDURE,
          description: "Procedures for handling emergency situations",
          version: "3.1"
        }
      ];

      const doc = faker.helpers.arrayElement(documentTypes);
      return prisma.document.create({
        data: {
          title: doc.title,
          type: doc.type,
          fileUrl: `https://pathology.gov.za/docs/${doc.title.toLowerCase().replace(/ /g, '-')}-v${doc.version}.pdf`,
          version: doc.version,
          facilityId: faker.helpers.arrayElement(facilities).id,
          uploadedBy: faker.helpers.arrayElement(users).id,
          description: doc.description,
          expiryDate: faker.date.future().toISOString()
        }
      });
    }),

    // Create Equipment
    ...Array(25).fill(null).map(() => {
      const equipmentTypes = [
        {
          name: "Lodox Statscan Critical Imaging System",
          type: EquipmentType.SCANNER,
          manufacturer: "Lodox Systems",
          model: "Statscan 8.1"
        },
        {
          name: "Digital Autopsy Workstation",
          type: EquipmentType.COMPUTER,
          manufacturer: "Dell",
          model: "Precision 7920"
        },
        {
          name: "Body Identification Scanner",
          type: EquipmentType.SCANNER,
          manufacturer: "Leica",
          model: "BIS-2000"
        },
        {
          name: "Evidence Photography System",
          type: EquipmentType.CAMERA,
          manufacturer: "Nikon",
          model: "D850 Forensic Kit"
        },
        {
          name: "Mortuary Refrigeration Unit",
          type: EquipmentType.MEDICAL_DEVICE,
          manufacturer: "Mopec",
          model: "MB600"
        },
        {
          name: "Digital X-Ray System",
          type: EquipmentType.MEDICAL_DEVICE,
          manufacturer: "Siemens",
          model: "Multix Impact"
        },
        {
          name: "Evidence Label Printer",
          type: EquipmentType.PRINTER,
          manufacturer: "Zebra",
          model: "ZT411"
        },
        {
          name: "Autopsy Table with Dissection Board",
          type: EquipmentType.MEDICAL_DEVICE,
          manufacturer: "Mopec",
          model: "CD100"
        }
      ];

      const equipment = faker.helpers.arrayElement(equipmentTypes);
      return prisma.equipment.create({
        data: {
          name: equipment.name,
          type: equipment.type,
          serialNumber: `${equipment.manufacturer.substring(0,3).toUpperCase()}${faker.string.alphanumeric(12).toUpperCase()}`,
          facilityId: faker.helpers.arrayElement(facilities).id,
          location: "Autopsy Suite",
          status: faker.helpers.arrayElement(['ACTIVE', 'MAINTENANCE', 'RETIRED']),
          purchaseDate: faker.date.past({ years: 3 }).toISOString(),
          warrantyEnd: faker.date.future({ years: 2 }).toISOString(),
          lastMaintenance: faker.date.recent({ days: 30 }).toISOString(),
          nextMaintenance: faker.date.soon({ days: 30 }).toISOString(),
          notes: `${equipment.manufacturer} ${equipment.model} - Regular maintenance required every 3 months`
        }
      });
    }),

    // Create Notifications
    ...Array(40).fill(null).map(() => {
      const notificationTypes = [
        {
          title: "New Body Collection Assignment",
          message: "You have been assigned to collect a body from Chris Hani Baragwanath Hospital. Please check the collection details.",
          type: "COLLECTION_ASSIGNMENT"
        },
        {
          title: "Equipment Maintenance Due",
          message: "The Lodox Statscan system is due for routine maintenance next week. Please schedule with technical team.",
          type: "MAINTENANCE_ALERT"
        },
        {
          title: "Temperature Alert",
          message: "Fridge PTA-F001 temperature has fluctuated above normal range. Please check immediately.",
          type: "TEMPERATURE_ALERT"
        },
        {
          title: "Document Update",
          message: "The Standard Operating Procedure for Body Collection has been updated to version 2.2. Please review the changes.",
          type: "DOCUMENT_UPDATE"
        },
        {
          title: "Shift Change Reminder",
          message: "Your next shift starts tomorrow at 07:00. Please arrive 15 minutes early for handover.",
          type: "SHIFT_REMINDER"
        }
      ];

      const notificationType = faker.helpers.arrayElement(notificationTypes);
      return prisma.notification.create({
        data: {
          userId: faker.helpers.arrayElement(users).id,
          title: notificationType.title,
          message: notificationType.message,
          type: notificationType.type,
          isRead: faker.datatype.boolean()
        }
      });
    }),

    // Additional system configurations and audit logs can be added here

    // Create default admin user
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        name: 'SoImagine',
        password: await hash('admin123', 12),
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
        department: "Administration",
        facilityId: facilities[0].id,
        persalNumber: "ADMIN001",
        phoneNumber: "+27 12 345 6789",
        address: "123 Admin Street, Pretoria",
        emergencyContact: "+27 12 987 6543",
        position: "System Administrator",
        roles: {
          connect: { id: superAdminRole.id }
        },
        facilities: {
          connect: [{ id: facilities[0].id }]
        }
      },
      create: {
        name: 'SoImagine',
        email: '<EMAIL>',
        password: await hash('admin123', 12),
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
        department: "Administration",
        facilityId: facilities[0].id,
        persalNumber: "ADMIN001",
        phoneNumber: "+27 12 345 6789",
        address: "123 Admin Street, Pretoria",
        emergencyContact: "+27 12 987 6543",
        position: "System Administrator",
        roles: {
          connect: { id: superAdminRole.id }
        },
        facilities: {
          connect: [{ id: facilities[0].id }]
        }
      }
    })
  ]);

  // Seed completed successfully
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
