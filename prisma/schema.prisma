generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL_PROD")
}

model User {
  id                 String           @id @default(cuid())
  name               String?
  email              String           @unique
  password           String
  role               UserRole         @default(MORGUE_STAFF)
  department         String?
  facilityId         String?
  status             UserStatus       @default(ACTIVE)
  emailVerified      DateTime?
  image              String?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  persalNumber       String?
  phoneNumber        String?
  address            String?
  emergencyContact   String?
  dateOfBirth        DateTime?
  hireDate           DateTime?
  position           String?
  mfaEnabled         Boolean          @default(false)
  mfaSecret          String?
  mfaVerified        Boolean          @default(false)
  mfaBackupCodes     String[]         @default([])
  accounts           Account[]
  auditLogs          AuditLog[]
  assignedAdmissions BodyAdmission[]  @relation("AssignedAdmissions")
  createdAdmissions  BodyAdmission[]  @relation("CreatedAdmissions")
  collections        BodyCollection[]
  assignedReferrals  BodyReferral[]   @relation("AssignedReferrals")
  referrals          BodyReferral[]   @relation("ReferredReferrals")
  createdReleases    BodyRelease[]    @relation("CreatedReleases")
  verifiedReleases   BodyRelease[]    @relation("VerifiedReleases")
  departments        Department[]     @relation("DepartmentHead")
  notifications      Notification[]
  sessions           Session[]
  shifts             Shift[]
  facility           Facility?        @relation(fields: [facilityId], references: [id])
  sentInvitations    UserInvitation[] @relation("SentInvitations")
  invitation         UserInvitation?  @relation("UserInvitation")
  activities         ActivityLog[]
  facilities         Facility[]       @relation("FacilityStaff")
  roles              Role[]           @relation("RoleToUser")
  devices            UserDevice[]

  // Status transition relations
  statusTransitionsPerformed StatusTransitionAudit[]
  referralInReceiving        ReferralIn[]            @relation("ReferralInReceiving")
  referralInProcessed        ReferralIn[]            @relation("ReferralInProcessed")
  referralOutInitiated       ReferralOut[]           @relation("ReferralOutInitiated")
  pendingReleaseAuthorized   PendingRelease[]        @relation("PendingReleaseAuthorized")
  overdueDetected            OverdueRecord[]         @relation("OverdueDetected")
  overdueResolved            OverdueRecord[]         @relation("OverdueResolved")

  @@index([facilityId])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Facility {
  id                    String                @id @default(cuid())
  name                  String
  code                  String                @unique
  type                  FacilityType
  status                FacilityStatus        @default(ACTIVE)
  address               String?
  city                  String?
  province              String?
  postalCode            String?
  phone                 String?
  email                 String?
  totalCapacity         Int
  currentOccupancy      Int                   @default(0)
  operatingHours        Json?
  emergencyContact      String?
  licenseExpiry         DateTime?
  metadata              Json?
  referralTimerBehavior ReferralTimerBehavior @default(CONTINUE)
  createdAt             DateTime              @default(now())
  updatedAt             DateTime              @updatedAt
  admissions            BodyAdmission[]
  releases              BodyRelease[]
  bodyTags              BodyTag[]
  departments           Department[]
  documents             Document[]            @relation("FacilityDocuments")
  equipment             Equipment[]           @relation("FacilityEquipment")
  maintenance           FacilityMaintenance[]
  fridges               Fridge[]              @relation("FacilityFridges")
  shifts                Shift[]               @relation("FacilityShifts")
  users                 User[]
  staff                 User[]                @relation("FacilityStaff")
  bodyNumberSequences   BodyNumberSequence[]  @relation("FacilityBodySequences")

  // Status transition relations
  referralInSource      ReferralIn[]          @relation("ReferralInSource")
  referralOutDestination ReferralOut[]        @relation("ReferralOutDestination")

  @@index([type])
  @@index([status])
}

model SystemConfig {
  id                       String   @id @default("1")
  emailNotifications       Boolean  @default(true)
  temperatureAlerts        Boolean  @default(true)
  capacityWarningThreshold Int      @default(80)
  maintenanceSchedule      String   @default("WEEKLY")
  auditLogRetention        Int      @default(90)
  defaultLanguage          String   @default("en")
  timeZone                 String   @default("UTC")
  temperatureUnit          String   @default("C")
  autoLogout               Int      @default(30)
  backupSchedule           String   @default("DAILY")
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt
}

model AuditLog {
  id           String   @id @default(cuid())
  action       String
  details      String
  user         String
  userId       String?
  performedBy  String
  ipAddress    String?
  userAgent    String?
  timestamp    DateTime @default(now())
  severity     String   @default("info")
  resourceType String?
  resourceId   String?
  createdAt    DateTime @default(now())
  userRelation User     @relation(fields: [performedBy], references: [id])

  @@index([performedBy])
  @@index([timestamp])
  @@index([severity])
  @@index([action])
  @@index([resourceType])
}

model ActivityLog {
  id              String         @id @default(cuid())
  timestamp       DateTime       @default(now())
  activityType    String
  description     String
  userId          String
  bodyAdmissionId String?
  bodyReferralId  String?
  bodyAdmission   BodyAdmission? @relation("BodyAdmissionLogs", fields: [bodyAdmissionId], references: [id])
  bodyReferral    BodyReferral?  @relation("BodyReferralActivityLogs", fields: [bodyReferralId], references: [id])
  user            User           @relation(fields: [userId], references: [id])

  @@map("activity_logs")
}

model BodyAdmission {
  id                  String        @id @default(cuid())
  facilityId          String
  bodyId              String
  bodyTagId           String?
  admissionType       AdmissionType @default(INITIAL)
  admissionDate       DateTime      @default(now())
  status              String        @default("ACTIVE")
  deathRegisterNumber String?
  bodyCondition       String?
  notes               String?
  photo               String?
  assignedFridge      String?
  temperature         Float?
  barcodeValue        String?       @unique
  assignedToId        String?
  createdById         String?
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt
  assignedTo          User?         @relation("AssignedAdmissions", fields: [assignedToId], references: [id])
  body                Body          @relation(fields: [bodyId], references: [id])
  bodyTag             BodyTag?      @relation("BodyTagAdmissions", fields: [bodyTagId], references: [id])
  createdBy           User?         @relation("CreatedAdmissions", fields: [createdById], references: [id])
  facility            Facility      @relation(fields: [facilityId], references: [id])
  activityLogs        ActivityLog[] @relation("BodyAdmissionLogs")

  @@index([facilityId])
  @@index([bodyId])
  @@index([bodyTagId])
  @@index([assignedToId])
  @@index([createdById])
}

model Notification {
  id        String   @id @default(cuid())
  title     String
  message   String
  type      String
  isRead    Boolean  @default(false)
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([isRead])
}

model Shift {
  id         String    @id @default(cuid())
  startTime  DateTime
  endTime    DateTime
  userId     String
  facilityId String
  type       ShiftType @default(MORNING)
  notes      String?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  isComplete Boolean   @default(false)
  facility   Facility  @relation("FacilityShifts", fields: [facilityId], references: [id])
  user       User      @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([facilityId])
}

model BodyRelease {
  id                     String        @id @default(cuid())
  facilityId             String
  bodyId                 String
  releaseDate            DateTime      @default(now())
  status                 ReleaseStatus @default(PENDING)
  releasedTo             String
  relationship           String
  identificationDocument String
  contactDetails         String?
  notes                  String?
  verifiedById           String?
  releasedById           String?
  verificationDate       DateTime?
  photoEvidence          String?
  signatureImage         String?
  createdAt              DateTime      @default(now())
  updatedAt              DateTime      @updatedAt
  body                   Body          @relation("BodyReleases", fields: [bodyId], references: [id])
  facility               Facility      @relation(fields: [facilityId], references: [id])
  releasedBy             User?         @relation("CreatedReleases", fields: [releasedById], references: [id])
  verifiedBy             User?         @relation("VerifiedReleases", fields: [verifiedById], references: [id])

  @@index([facilityId])
  @@index([bodyId])
  @@index([verifiedById])
  @@index([releasedById])
}

model Body {
  id                     String          @id @default(cuid())
  trackingNumber         String          @unique
  bodyTagId              String          @unique
  status                 BodyStatus
  firstName              String?
  lastName               String?
  gender                 String?
  approximateAge         Int?
  height                 Float?
  weight                 Float?
  distinguishingFeatures String?
  deathRegistration      String?         @default("")
  causeOfDeath           String?
  placeOfDeath           String?
  dateOfDeath            DateTime?
  photos                 String[]        @default([])
  documents              String[]        @default([])
  metadata               Json?
  createdAt              DateTime        @default(now())
  updatedAt              DateTime        @updatedAt
  bodyTag                BodyTag         @relation("BodyTags", fields: [bodyTagId], references: [id])
  admissions             BodyAdmission[]
  collection             BodyCollection?
  referrals              BodyReferral[]  @relation("BodyReferrals")
  releases               BodyRelease[]   @relation("BodyReleases")

  @@index([status])
  @@index([lastName])
  @@index([dateOfDeath])
}

model BodyTag {
  id             String          @id @default(cuid())
  tagNumber      String          @unique
  generatedBy    String
  generatedAt    DateTime        @default(now())
  status         TagStatus       @default(GENERATED)
  bodyId         String?
  collectionId   String?
  admissionId    String?
  lastScannedAt  DateTime?
  lastScannedBy  String?
  notes          String?
  qrCodeValue    String?         @unique
  qrCodeFormat   String?
  qrCodeMetadata Json?
  isActive       Boolean         @default(true)
  facilityId     String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  body           Body?           @relation("BodyTags")
  admissions     BodyAdmission[] @relation("BodyTagAdmissions")
  facility       Facility?       @relation(fields: [facilityId], references: [id])

  // Status transition relations
  statusTransitions StatusTransitionAudit[]
  referralIn        ReferralIn?
  referralOut       ReferralOut?
  pendingRelease    PendingRelease?
  overdueRecord     OverdueRecord?

  @@index([facilityId])
  @@index([status])
  @@index([bodyId])
}

model BodyCollection {
  id                String          @id @default(cuid())
  bodyId            String          @unique
  userId            String?
  name              String?
  institution       String?
  vehicleReg        String?
  arrivalTime       DateTime?
  gpsCoords         Json?
  collectionType    CollectionType?
  bodyDescription   String?
  sceneDescription  String?
  weatherConditions String?
  temperature       Float?
  collectionNotes   String?
  photos            String[]        @default([])
  documents         String[]        @default([])
  handedOverBy      String?
  handedOverRole    String?
  handedOverContact String?
  handoverNotes     String?
  status            BodyStatus      @default(COLLECTED)
  barcodeValue      String          @unique
  metadata          Json?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  body              Body            @relation(fields: [bodyId], references: [id])
  user              User?           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([collectionType])
  @@index([status])
  @@index([userId])
}

model BodyReferral {
  id                String         @id @default(cuid())
  bodyId            String
  referralType      ReferralType
  status            ReferralStatus @default(PENDING)
  referralDate      DateTime       @default(now())
  returnDate        DateTime?
  employeePersal    String
  employeeName      String
  institutionName   String
  vehicleReg        String
  specimenKitNumber String?
  evidenceBagSerial String?
  bodyTagPhoto      String?
  vehiclePhoto      String?
  gpsLatitude       Float?
  gpsLongitude      Float?
  referredById      String
  assignedToId      String
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  notes             String?
  assignedTo        User           @relation("AssignedReferrals", fields: [assignedToId], references: [id])
  body              Body           @relation("BodyReferrals", fields: [bodyId], references: [id])
  referredBy        User           @relation("ReferredReferrals", fields: [referredById], references: [id])
  activityLogs      ActivityLog[]  @relation("BodyReferralActivityLogs")

  @@index([bodyId])
  @@index([referredById])
  @@index([assignedToId])
}

model Fridge {
  id               String       @id @default(cuid())
  fridgeNumber     String       @unique
  barcode          String       @unique
  temperature      Float
  status           FridgeStatus @default(AVAILABLE)
  capacity         Int
  currentOccupancy Int          @default(0)
  facilityId       String
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  facility         Facility     @relation("FacilityFridges", fields: [facilityId], references: [id])

  @@index([facilityId])
}

model AdminAudit {
  id          String   @id @default(cuid())
  action      String
  entityType  String
  entityId    String
  changes     Json?
  performedBy String
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  @@index([action])
  @@index([entityType])
  @@index([performedBy])
  @@index([createdAt])
}

model FacilityMaintenance {
  id            String    @id @default(cuid())
  facilityId    String
  type          String
  description   String
  scheduledDate DateTime?
  completedDate DateTime?
  performedBy   String?
  notes         String?
  status        String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  facility      Facility  @relation(fields: [facilityId], references: [id])

  @@index([facilityId])
  @@index([status])
}

model Department {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  description String?
  headUserId  String?
  facilityId  String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  facility    Facility @relation(fields: [facilityId], references: [id])
  head        User?    @relation("DepartmentHead", fields: [headUserId], references: [id])

  @@index([facilityId])
  @@index([headUserId])
}

model PrismaQueryLog {
  id        String   @id @default(cuid())
  modelName String
  operation String
  query     Json?
  result    Json?
  duration  Float
  error     String?
  metadata  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([modelName])
  @@index([operation])
  @@index([createdAt])
}

model Document {
  id          String       @id @default(cuid())
  title       String
  type        DocumentType
  fileUrl     String
  version     String
  facilityId  String
  uploadedBy  String
  description String?
  isActive    Boolean      @default(true)
  expiryDate  DateTime?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  facility    Facility     @relation("FacilityDocuments", fields: [facilityId], references: [id])

  @@index([facilityId])
  @@index([type])
}

model BodyNumberSequence {
  id         String   @id @default(cuid())
  facilityId String
  year       Int
  lastNumber Int      @default(0)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  facility   Facility @relation("FacilityBodySequences", fields: [facilityId], references: [id])

  @@unique([facilityId, year])
  @@index([facilityId])
  @@index([year])
}

model Equipment {
  id              String        @id @default(cuid())
  name            String
  type            EquipmentType
  serialNumber    String?       @unique
  facilityId      String
  location        String?
  status          String
  purchaseDate    DateTime?
  warrantyEnd     DateTime?
  lastMaintenance DateTime?
  nextMaintenance DateTime?
  notes           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  facility        Facility      @relation("FacilityEquipment", fields: [facilityId], references: [id])

  @@index([facilityId])
  @@index([type])
}

model Role {
  id          String           @id @default(cuid())
  name        String           @unique
  description String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  permissions RolePermission[]
  users       User[]           @relation("RoleToUser")
}

model Permission {
  id          String           @id @default(cuid())
  name        String           @unique
  description String?
  category    String
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  roles       RolePermission[]
}

model RolePermission {
  id           String     @id @default(cuid())
  roleId       String
  permissionId String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
}

model InvitationLog {
  id     String   @id @default(cuid())
  email  String
  status String   @default("sent")
  sentAt DateTime @default(now())

  @@index([email])
  @@index([status])
  @@index([sentAt])
}

model UserInvitation {
  id           String    @id @default(cuid())
  email        String
  name         String
  role         String
  token        String    @unique
  status       String    @default("PENDING")
  expiresAt    DateTime
  createdAt    DateTime  @default(now())
  acceptedAt   DateTime?
  canceledAt   DateTime?
  resendCount  Int       @default(0)
  lastResendAt DateTime?
  facilityId   String?
  metadata     Json?
  sentById     String
  userId       String?   @unique
  sentBy       User      @relation("SentInvitations", fields: [sentById], references: [id])
  user         User?     @relation("UserInvitation", fields: [userId], references: [id])

  @@index([email])
  @@index([status])
  @@index([token])
  @@index([createdAt])
}

model UserDevice {
  id             String   @id @default(cuid())
  userId         String
  deviceId       String
  deviceType     String
  browser        String
  browserVersion String
  os             String
  osVersion      String
  deviceModel    String?
  deviceVendor   String?
  userAgent      String
  lastIp         String?
  firstSeen      DateTime @default(now())
  lastActive     DateTime
  isTrusted      Boolean  @default(false)
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, deviceId])
  @@index([userId])
  @@index([deviceId])
  @@index([lastActive])
}

enum UserRole {
  ADMIN
  PATHOLOGIST
  MORGUE_STAFF
  SECURITY_STAFF
  FIELD_EMPLOYEE
  FACILITY_MANAGER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  INVITED
}

enum BodyStatus {
  COLLECTED
  ADMITTED
  IN_STORAGE
  PENDING_RELEASE
  SECURITY_VERIFIED
  RELEASED
  REFERRED
}

enum CollectionType {
  CRIME_SCENE
  HOSPITAL
  OTHER
}

enum ReferralType {
  LODOX
  XRAY
  SPECIMEN
  OTHER
}

enum ReferralStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  RETURNED
  CANCELLED
}

enum AdmissionType {
  INITIAL
  POST_REFERRAL
  TRANSFER
}

enum ReleaseStatus {
  PENDING
  VERIFIED
  COMPLETED
  CANCELLED
}

enum FridgeStatus {
  AVAILABLE
  OCCUPIED
  MAINTENANCE
  OFFLINE
}

enum TagStatus {
  GENERATED
  COLLECTED
  ADMITTED
  IN_TRANSIT
  RELEASED
  REFERRAL_OUT
  REFERRAL_IN
  PENDING_RELEASE
  OVERDUE
}

enum FacilityType {
  MORGUE
  STORAGE
  PROCESSING
  TEMPORARY
}

enum FacilityStatus {
  ACTIVE
  INACTIVE
  UNDER_MAINTENANCE
  DECOMMISSIONED
}

enum ShiftType {
  MORNING
  AFTERNOON
  NIGHT
  ON_CALL
}

enum DocumentType {
  POLICY
  PROCEDURE
  FORM
  REPORT
  CERTIFICATE
}

enum EquipmentType {
  SCANNER
  COMPUTER
  PRINTER
  CAMERA
  MEDICAL_DEVICE
  OTHER
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ReferralTimerBehavior {
  CONTINUE
  PAUSE
  RESET
}

enum TransitionType {
  ADMISSION_TO_REFERRAL_IN
  REFERRAL_IN_TO_REFERRAL_OUT
  REFERRAL_OUT_TO_PENDING_RELEASE
  AUTO_OVERDUE_DETECTION
}

enum EscalationLevel {
  SUPERVISOR
  MANAGER
  DIRECTOR
  EXTERNAL
}

enum PriorityLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum UrgencyLevel {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TransportMethod {
  AMBULANCE
  HEARSE
  PRIVATE_VEHICLE
  OTHER
}

enum TransferReason {
  SPECIALIST_CONSULTATION
  AUTOPSY_REQUIRED
  CAPACITY_CONSTRAINTS
  FAMILY_REQUEST
  LEGAL_REQUIREMENT
  OTHER
}

enum ReleaseRelation {
  FAMILY_MEMBER
  LEGAL_GUARDIAN
  FUNERAL_DIRECTOR
  LEGAL_REPRESENTATIVE
  OTHER
}

enum OverdueReason {
  DOCUMENTATION_INCOMPLETE
  FAMILY_UNAVAILABLE
  LEGAL_PROCEEDINGS
  INVESTIGATION_ONGOING
  FACILITY_CAPACITY
  TRANSPORT_DELAYS
  OTHER
}

// Status Transition Audit Log
model StatusTransitionAudit {
  id            String         @id @default(cuid())
  bodyTagId     String
  fromStatus    String
  toStatus      String
  transitionType TransitionType
  performedBy   String
  timestamp     DateTime       @default(now())
  metadata      Json?
  notes         String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  // Relations
  bodyTag       BodyTag        @relation(fields: [bodyTagId], references: [id], onDelete: Cascade)
  performedByUser User         @relation(fields: [performedBy], references: [id])

  @@map("status_transition_audit")
}

// Referral In Records
model ReferralIn {
  id                String          @id @default(cuid())
  bodyTagId         String          @unique
  sourceFacilityId  String
  referralNumber    String
  receivingStaffId  String
  transportMethod   TransportMethod?
  transportNotes    String?
  processedAt       DateTime        @default(now())
  processedBy       String
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  bodyTag           BodyTag         @relation(fields: [bodyTagId], references: [id], onDelete: Cascade)
  sourceFacility    Facility        @relation("ReferralInSource", fields: [sourceFacilityId], references: [id])
  receivingStaff    User            @relation("ReferralInReceiving", fields: [receivingStaffId], references: [id])
  processedByUser   User            @relation("ReferralInProcessed", fields: [processedBy], references: [id])

  @@map("referral_in")
}

// Referral Out Records
model ReferralOut {
  id                    String         @id @default(cuid())
  bodyTagId             String         @unique
  destinationFacilityId String
  transferReason        TransferReason
  transferReasonOther   String?
  expectedArrivalDate   DateTime?
  transportArrangements String?
  urgencyLevel          UrgencyLevel   @default(MEDIUM)
  initiatedAt           DateTime       @default(now())
  initiatedBy           String
  createdAt             DateTime       @default(now())
  updatedAt             DateTime       @updatedAt

  // Relations
  bodyTag               BodyTag        @relation(fields: [bodyTagId], references: [id], onDelete: Cascade)
  destinationFacility   Facility       @relation("ReferralOutDestination", fields: [destinationFacilityId], references: [id])
  initiatedByUser       User           @relation("ReferralOutInitiated", fields: [initiatedBy], references: [id])

  @@map("referral_out")
}

// Pending Release Records
model PendingRelease {
  id                        String          @id @default(cuid())
  bodyTagId                 String          @unique
  releaseAuthorizationNumber String
  releaseDate               DateTime
  releaseToPersonName       String
  releaseToPersonRelation   ReleaseRelation
  releaseToPersonContact    String
  releaseConditions         String?
  authorizedAt              DateTime        @default(now())
  authorizedBy              String
  createdAt                 DateTime        @default(now())
  updatedAt                 DateTime        @updatedAt

  // Relations
  bodyTag                   BodyTag         @relation(fields: [bodyTagId], references: [id], onDelete: Cascade)
  authorizedByUser          User            @relation("PendingReleaseAuthorized", fields: [authorizedBy], references: [id])

  @@map("pending_release")
}

// Overdue Records
model OverdueRecord {
  id                     String          @id @default(cuid())
  bodyTagId              String          @unique
  overdueReason          OverdueReason
  overdueReasonOther     String?
  escalationLevel        EscalationLevel @default(SUPERVISOR)
  escalationNotes        String
  priorityLevel          PriorityLevel   @default(HIGH)
  expectedResolutionDate DateTime?
  actionRequired         String
  detectedAt             DateTime        @default(now())
  detectedBy             String
  resolvedAt             DateTime?
  resolvedBy             String?
  createdAt              DateTime        @default(now())
  updatedAt              DateTime        @updatedAt

  // Relations
  bodyTag                BodyTag         @relation(fields: [bodyTagId], references: [id], onDelete: Cascade)
  detectedByUser         User            @relation("OverdueDetected", fields: [detectedBy], references: [id])
  resolvedByUser         User?           @relation("OverdueResolved", fields: [resolvedBy], references: [id])

  @@map("overdue_record")
}
