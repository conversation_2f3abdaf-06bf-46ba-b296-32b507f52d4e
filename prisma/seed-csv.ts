import { PrismaClient } from '@prisma/client';
import bcryptjs from 'bcryptjs';
const { hash } = bcryptjs;
import * as fs from 'fs';
import * as path from 'path';
import { parse } from 'csv-parse/sync';
import {
  UserRole, UserStatus, FacilityType, FacilityStatus, ReferralTimerBehavior
} from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data
  try {
    // First, delete audit logs that reference users
    await prisma.auditLog.deleteMany();

    // Delete user invitations
    await prisma.userInvitation.deleteMany();

    // Delete activity logs
    await prisma.activityLog.deleteMany();

    // Delete notifications
    await prisma.notification.deleteMany();

    // Delete role permissions
    await prisma.rolePermission.deleteMany();

    // Delete sessions and accounts
    await prisma.session.deleteMany();
    await prisma.account.deleteMany();

    // Delete body-related records in order
    await prisma.bodyReferral.deleteMany();
    await prisma.bodyRelease.deleteMany();
    await prisma.bodyAdmission.deleteMany();
    await prisma.bodyCollection.deleteMany();
    await prisma.body.deleteMany();
    await prisma.bodyTag.deleteMany();

    // Delete facility-related records
    await prisma.equipment.deleteMany();
    await prisma.document.deleteMany();
    await prisma.shift.deleteMany();
    await prisma.department.deleteMany();
    await prisma.facilityMaintenance.deleteMany();
    await prisma.adminAudit.deleteMany();
    await prisma.fridge.deleteMany();

    // Delete user devices
    await prisma.userDevice.deleteMany();

    // Delete users
    await prisma.user.deleteMany();

    // Delete roles and permissions
    await prisma.role.deleteMany();
    await prisma.permission.deleteMany();

    // Delete facilities
    await prisma.facility.deleteMany();

    // Data cleared successfully
  } catch (error) {
    console.error('Error clearing data:', error);
    throw error;
  }

  // Read and parse CSV data
  const csvFilePath = path.join(process.cwd(), 'public/assets/FPS_INSTITUTIONS.csv');
  const csvData = fs.readFileSync(csvFilePath, 'utf8');
  const records = parse(csvData, {
    columns: true,
    skip_empty_lines: true
  });

  // Create permissions
  const permissionData = [
    {
      name: 'users.view',
      description: 'View users',
      category: 'User Management'
    },
    {
      name: 'users.create',
      description: 'Create users',
      category: 'User Management'
    },
    {
      name: 'users.edit',
      description: 'Edit users',
      category: 'User Management'
    },
    {
      name: 'users.delete',
      description: 'Delete users',
      category: 'User Management'
    },
    {
      name: 'roles.view',
      description: 'View roles',
      category: 'Role Management'
    },
    {
      name: 'roles.create',
      description: 'Create roles',
      category: 'Role Management'
    },
    {
      name: 'roles.edit',
      description: 'Edit roles',
      category: 'Role Management'
    },
    {
      name: 'roles.delete',
      description: 'Delete roles',
      category: 'Role Management'
    },
    {
      name: 'facilities.view',
      description: 'View facilities',
      category: 'Facility Management'
    },
    {
      name: 'facilities.create',
      description: 'Create facilities',
      category: 'Facility Management'
    },
    {
      name: 'facilities.edit',
      description: 'Edit facilities',
      category: 'Facility Management'
    },
    {
      name: 'facilities.delete',
      description: 'Delete facilities',
      category: 'Facility Management'
    },
    {
      name: 'settings.view',
      description: 'View system settings',
      category: 'System Settings'
    },
    {
      name: 'settings.edit',
      description: 'Edit system settings',
      category: 'System Settings'
    }
  ];

  const createdPermissions = await Promise.all(
    permissionData.map(permission =>
      prisma.permission.upsert({
        where: { name: permission.name },
        update: permission,
        create: permission
      })
    )
  );

  // Create Roles
  const superAdminPermissions = createdPermissions.map(p => p.id);

  const adminPermissions = createdPermissions
    .filter(p =>
      p.name.startsWith('users.view') ||
      p.name.startsWith('users.create') ||
      p.name.startsWith('users.edit') ||
      p.name.startsWith('roles.view')
    )
    .map(p => p.id);

  // Create roles with permissions
  const superAdminRole = await prisma.role.create({
    data: {
      name: 'Super Admin',
      description: 'Full system access',
      permissions: {
        create: superAdminPermissions.map(permissionId => ({
          permissionId
        }))
      }
    }
  });

  const adminRole = await prisma.role.create({
    data: {
      name: 'Admin',
      description: 'System administration with limited permissions',
      permissions: {
        create: adminPermissions.map(permissionId => ({
          permissionId
        }))
      }
    }
  });

  // Create facilities from CSV data
  const facilities = await Promise.all(
    records.map(async (record: any) => {
      const facilityName = record.INSTITUTIONS;
      const contactEmail = record.CONTACTS;

      // Skip "Cluster Manager" entries as they're not facilities
      if (facilityName === "Cluster Manager") {
        return null;
      }

      return prisma.facility.create({
        data: {
          name: `${facilityName} Forensic Pathology Services`,
          code: `${facilityName.substring(0, 3).toUpperCase()}-FPS`,
          type: FacilityType.MORGUE,
          status: FacilityStatus.ACTIVE,
          address: `123 Main Street`,
          city: facilityName,
          province: "Gauteng",
          postalCode: "0001",
          phone: "+27 12 123 4567",
          email: contactEmail,
          totalCapacity: 100,
          currentOccupancy: 0,
          operatingHours: {
            open: "00:00",
            close: "23:59"
          },
          emergencyContact: "+27 12 321 7654",
          licenseExpiry: new Date("2025-12-31").toISOString(),
          referralTimerBehavior: ReferralTimerBehavior.CONTINUE,
          metadata: {
            accreditation: "ISO 17025:2017"
          }
        }
      });
    })
  );

  // Filter out null facilities (Cluster Manager entries)
  const validFacilities = facilities.filter(facility => facility !== null);

  // Create admin users (keeping from original script)
  const hashedAdminPassword = await hash("Password@123!", 12);

  const adminUsers = await Promise.all([
    // Main admin
    prisma.user.create({
      data: {
        name: "System Administrator",
        email: "<EMAIL>",
        password: hashedAdminPassword,
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
        department: "Administration",
        facilityId: validFacilities[0].id,
        persalNumber: "ADM12345",
        phoneNumber: "+27 12 345 6789",
        address: "123 Admin Street, Pretoria",
        emergencyContact: "+27 12 987 6543",
        position: "System Administrator",
        roles: {
          connect: { id: superAdminRole.id }
        },
        facilities: {
          connect: validFacilities.map(f => ({ id: f.id }))
        }
      }
    }),
    // Default admin user
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        name: 'SoImagine',
        password: await hash('admin123', 12),
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
        department: "Administration",
        facilityId: validFacilities[0].id,
        persalNumber: "ADMIN001",
        phoneNumber: "+27 12 345 6789",
        address: "123 Admin Street, Pretoria",
        emergencyContact: "+27 12 987 6543",
        position: "System Administrator",
        roles: {
          connect: { id: superAdminRole.id }
        },
        facilities: {
          connect: validFacilities.map(f => ({ id: f.id }))
        }
      },
      create: {
        name: 'SoImagine',
        email: '<EMAIL>',
        password: await hash('admin123', 12),
        role: UserRole.ADMIN,
        status: UserStatus.ACTIVE,
        department: "Administration",
        facilityId: validFacilities[0].id,
        persalNumber: "ADMIN001",
        phoneNumber: "+27 12 345 6789",
        address: "123 Admin Street, Pretoria",
        emergencyContact: "+27 12 987 6543",
        position: "System Administrator",
        roles: {
          connect: { id: superAdminRole.id }
        },
        facilities: {
          connect: validFacilities.map(f => ({ id: f.id }))
        }
      }
    })
  ]);

  // Create facility users from CSV data
  await Promise.all(
    records.map(async (record: any) => {
      const facilityName = record.INSTITUTIONS;
      const contactEmail = record.CONTACTS;

      // Skip "Cluster Manager" entries as they're handled separately
      if (facilityName === "Cluster Manager") {
        // Create cluster manager user
        return prisma.user.create({
          data: {
            name: "Cluster Manager",
            email: contactEmail,
            password: hashedAdminPassword,
            role: UserRole.ADMIN,
            status: UserStatus.ACTIVE,
            department: "Administration",
            facilityId: validFacilities[0].id,
            persalNumber: `CM${Math.floor(Math.random() * 10000).toString().padStart(5, '0')}`,
            phoneNumber: "+27 12 345 6789",
            address: "123 Admin Street, Pretoria",
            emergencyContact: "+27 12 987 6543",
            position: "Cluster Manager",
            roles: {
              connect: { id: adminRole.id }
            },
            facilities: {
              connect: validFacilities.map(f => ({ id: f.id }))
            }
          }
        });
      }

      // Find the facility for this user
      const facility = validFacilities.find(f => f.name.startsWith(facilityName));

      if (!facility) return null;

      // Create facility manager user
      return prisma.user.create({
        data: {
          name: `${facilityName} Manager`,
          email: contactEmail,
          password: hashedAdminPassword,
          role: UserRole.ADMIN,
          status: UserStatus.ACTIVE,
          department: "Administration",
          facilityId: facility.id,
          persalNumber: `FM${Math.floor(Math.random() * 10000).toString().padStart(5, '0')}`,
          phoneNumber: "+27 12 345 6789",
          address: `123 Main Street, ${facilityName}`,
          emergencyContact: "+27 12 987 6543",
          position: "Facility Manager",
          roles: {
            connect: { id: adminRole.id }
          },
          facilities: {
            connect: [{ id: facility.id }]
          }
        }
      });
    })
  );

  console.log('Seed completed successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
