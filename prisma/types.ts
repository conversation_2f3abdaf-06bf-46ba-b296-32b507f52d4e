import { Prisma } from '@prisma/client'

export enum ActionType {
  SCAN_BARCODE = 'SCAN_BARCODE',
  CAPTURE_IMAGE = 'CAPTURE_IMAGE',
  RECORD_LOCATION = 'RECORD_LOCATION',
  ASSIGN_STORAGE = 'ASSIGN_STORAGE',
  DOCUMENT_DETAILS = 'DOCUMENT_DETAILS',
  VERIFY_IDENTITY = 'VERIFY_IDENTITY',
  COLLECT_BODY = 'COLLECT_BODY',
  ADMIT_BODY = 'ADMIT_BODY',
  RELEASE_BODY = 'RELEASE_BODY',
  UPDATE_STATUS = 'UPDATE_STATUS'
}

export enum UserRole {
  FIELD_EMPLOYEE = 'FIELD_EMPLOYEE',
  MORGUE_STAFF = 'MORGUE_STAFF',
  SECURITY_STAFF = 'SECURITY_STAFF',
  ADMIN = 'ADMIN',
  PATHOLOGIST = 'PATHOLOGIST',
  FACILITY_MANAGER = 'FACILITY_MANAGER'
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}

export enum BodyStatus {
  COLLECTED = 'COLLECTED',
  ADMITTED = 'ADMITTED',
  IN_STORAGE = 'IN_STORAGE',
  PENDING_RELEASE = 'PENDING_RELEASE',
  SECURITY_VERIFIED = 'SECURITY_VERIFIED',
  RELEASED = 'RELEASED',
  REFERRED = 'REFERRED'
}

export enum CollectionType {
  CRIME_SCENE = 'CRIME_SCENE',
  HOSPITAL = 'HOSPITAL',
  OTHER = 'OTHER'
}

export enum FacilityType {
  PRIMARY = 'PRIMARY',
  SECONDARY = 'SECONDARY',
  TERTIARY = 'TERTIARY',
  HOSPITAL = 'HOSPITAL',
  CLINIC = 'CLINIC',
  OTHER = 'OTHER'
}

export enum ReferralType {
  LODOX = 'LODOX',
  XRAY = 'XRAY',
  SPECIMEN = 'SPECIMEN',
  OTHER = 'OTHER'
}

export enum ReferralStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  RETURNED = 'RETURNED',
  CANCELLED = 'CANCELLED'
}

export enum ReleaseStatus {
  PENDING = 'PENDING',
  VERIFIED = 'VERIFIED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum CustodyRecordType {
  INITIAL_COLLECTION = 'INITIAL_COLLECTION',
  SCENE_COLLECTION = 'SCENE_COLLECTION',
  HOSPITAL_TRANSFER = 'HOSPITAL_TRANSFER',
  MEDICAL_EXAMINER_TRANSFER = 'MEDICAL_EXAMINER_TRANSFER',

  // Morgue and Storage Movements
  MORGUE_INTAKE = 'MORGUE_INTAKE',
  MORGUE_TRANSFER = 'MORGUE_TRANSFER',
  MORGUE_RETURN = 'MORGUE_RETURN',
  MORGUE_INTERNAL_MOVE = 'MORGUE_INTERNAL_MOVE',
  COLD_STORAGE_TRANSFER = 'COLD_STORAGE_TRANSFER',

  // Pathology and Laboratory Movements
  PATHOLOGY_TRANSFER = 'PATHOLOGY_TRANSFER',
  LAB_TRANSFER = 'LAB_TRANSFER',
  SPECIMEN_COLLECTION = 'SPECIMEN_COLLECTION',
  TISSUE_SAMPLE_TRANSFER = 'TISSUE_SAMPLE_TRANSFER',

  // Referral and External Movements
  REFERRAL_OUT = 'REFERRAL_OUT',
  REFERRAL_TRANSIT = 'REFERRAL_TRANSIT',
  REFERRAL_RETURN = 'REFERRAL_RETURN',
  EXTERNAL_FACILITY_TRANSFER = 'EXTERNAL_FACILITY_TRANSFER',

  // Release and Final Disposition
  RELEASE_TO_FAMILY = 'RELEASE_TO_FAMILY',
  RELEASE_TO_AUTHORITIES = 'RELEASE_TO_AUTHORITIES',
  BURIAL_TRANSFER = 'BURIAL_TRANSFER',
  CREMATION_TRANSFER = 'CREMATION_TRANSFER',

  // Security and Forensic Movements
  SECURITY_SCREENING = 'SECURITY_SCREENING',
  FORENSIC_INVESTIGATION_TRANSFER = 'FORENSIC_INVESTIGATION_TRANSFER',
  EVIDENCE_CHAIN_TRANSFER = 'EVIDENCE_CHAIN_TRANSFER'
}

export enum CustodyStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION'
}

export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE'
}

export enum DecompositionStage {
  EARLY = 'EARLY',
  MODERATE = 'MODERATE',
  ADVANCED = 'ADVANCED'
}

export enum PathologyReportStatus {
  DRAFT = 'DRAFT',
  PENDING_REVIEW = 'PENDING_REVIEW',
  REVIEWED = 'REVIEWED',
  FINALIZED = 'FINALIZED',
  AMENDED = 'AMENDED'
}

export enum PathologyReportType {
  INITIAL = 'INITIAL',
  SUPPLEMENTARY = 'SUPPLEMENTARY',
  FINAL = 'FINAL',
  AMENDMENT = 'AMENDMENT'
}

export enum ImageStudyType {
  X_RAY = 'X_RAY',
  CT_SCAN = 'CT_SCAN',
  MRI = 'MRI',
  ULTRASOUND = 'ULTRASOUND',
  PET_SCAN = 'PET_SCAN'
}

export enum ImageStudyStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum QualityControlStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum ChainOfCustodyStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum GeneticTestType {
  BLOOD_ANALYSIS = 'BLOOD_ANALYSIS',
  TISSUE_ANALYSIS = 'TISSUE_ANALYSIS',
  DNA_PROFILING = 'DNA_PROFILING',
  GENETIC_MARKERS = 'GENETIC_MARKERS',
  FAMILIAL_DNA = 'FAMILIAL_DNA',
  ANCESTRY_ANALYSIS = 'ANCESTRY_ANALYSIS',
  DNA_ANALYSIS = 'DNA_ANALYSIS',
  TOXICOLOGY_SCREEN = 'TOXICOLOGY_SCREEN',
  OTHER = 'OTHER'
}

export enum GeneticTestStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum LabTestPriority {
  ROUTINE = 'ROUTINE',
  URGENT = 'URGENT',
  STAT = 'STAT'
}

export enum LabTestStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum HistologyReportStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum ToxicologyReportStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum TissueSpecimenStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum ChainOfCustodyType {
  PATHOLOGY = 'PATHOLOGY',
  LABORATORY = 'LABORATORY',
  EXTERNAL_FACILITY = 'EXTERNAL_FACILITY'
}

export enum NotificationType {
  SYSTEM = 'SYSTEM',
  COLLECTION = 'COLLECTION',
  ASSIGNMENT = 'ASSIGNMENT',
  DOCUMENT = 'DOCUMENT',
  STATUS_UPDATE = 'STATUS_UPDATE'
}

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum NotificationStatus {
  PENDING = 'PENDING',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
  FAILED = 'FAILED'
}

export enum TeamMemberRole {
  MEMBER = 'MEMBER',
  DEPUTY = 'DEPUTY',
  SPECIALIST = 'SPECIALIST',
  LEAD = 'LEAD'
}

export enum ScheduleType {
  REGULAR = 'REGULAR',
  ON_CALL = 'ON_CALL',
  EMERGENCY = 'EMERGENCY'
}

export enum ScheduleStatus {
  SCHEDULED = 'SCHEDULED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum ShiftType {
  DAY = 'DAY',
  NIGHT = 'NIGHT',
  ON_CALL = 'ON_CALL'
}

export enum ShiftStatus {
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED'
}

export enum AdmissionType {
  INITIAL = 'INITIAL',
  POST_REFERRAL = 'POST_REFERRAL',
  TRANSFER = 'TRANSFER'
}

export enum FridgeStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  MAINTENANCE = 'MAINTENANCE'
}


export interface User {
  id: string
  email: string
  name: string | null
  role: UserRole
  persalNumber: string | null
  employeeId: string | null
  institution: string | null
  department: string | null
  phoneNumber: string | null
  status: string | null
  permissions: string[]
  password: string
  emailVerified: Date | null
  image: string | null
  resetToken: string | null
  resetTokenExpiry: Date | null
  supervisor: string | null
  vehicleReg: string | null
  lastLogin: Date | null
  createdAt: Date
  updatedAt: Date
}

export interface Body {
  id: string
  trackingNumber: string
  bodyTag: string
  bodyTagContent: string
  status: BodyStatus
  deathRegistration: string
}

export interface BodyMeasurements {
  height: number
  weight: number
  bmi: number
}

export interface SecurityScanResults {
  scanDate: Date
  operator: string
  findings: string
  cleared: boolean
}

export interface ReferralDocument {
  type: string
  content: string
  verified: boolean
}

export interface ToxicologyFinding {
  substance: string
  level: number
  interpretation: string
}

export interface GeneticMarker {
  name: string
  value: string
  significance: string
}

export interface AuditMetadata {
  location: string
  device: string
  browser: string
  [key: string]: string | number | boolean | null
}

export type JsonValue = Prisma.JsonValue
export type Decimal = Prisma.Decimal