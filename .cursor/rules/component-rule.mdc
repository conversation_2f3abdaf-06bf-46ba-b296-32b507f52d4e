---
description: Guidelines on creating components
globs: 
---
### 1. Component Categorization
```text
- Shared Components: Reusable UI elements (buttons, inputs)
- Layout Components: Page structure (headers, footers, grids)
- Specific Components: Feature/page-specific logic
- Route Components: Page-level components in app/router
```

### 2. Server vs Client Components
```typescript:components/Shared/Button.client.tsx
// Client Components (interactivity)
'use client';
export default function InteractiveButton() {
  // ... client-side logic
}

// Server Components (default)
export default function StaticCard({ data }) { 
  // ... server-rendered content
}
```
*Best Practice:*
- Use Server Components by default for better performance ([Next.js Docs](mdc:https:/nextjs.org/docs))
- Only add 'use client' when needing React state/effects

### 3. Type Safety
```typescript:components/Profile/types.ts
export interface ProfileProps {
  userId: string;
  showActions?: boolean;
  // ... other typed props
}
```
*Production Considerations:*
- Create `.types.ts` files per component category
- Use TypeScript generics for reusable components

### 4. File Organization
```text
/src
  /components
    /Shared      # Global reusable
    /Layout      # Structural
    /FeatureName # Feature-specific
    /ui          # Headless UI primitives
```

### 5. Data Handling
```typescript:components/Product/ProductCard.tsx
async function ProductCard({ sku }) {
  const data = await fetchProduct(sku); // Server-side fetch
  return <div>{data.name}</div>;
}
```
*Best Practices:*
- Fetch data directly in Server Components
- Use `loading.js` for suspense boundaries
- Implement ISR for dynamic content ([Next.js ISR Docs](mdc:https:/nextjs.org/docs))

### 6. Performance Patterns
```typescript:components/Layout/DynamicHeader.tsx
import dynamic from 'next/dynamic';

const HeavyHeader = dynamic(
  () => import('./HeavyHeader'),
  { loading: () => <SkeletonHeader /> }
);
```
*Production Must:*
- Lazy load non-critical components
- Use `next/image` for optimized assets
- Implement memoization for frequent re-renders

### 7. Testing Strategy
```text
/components
  /Shared
    /Button
      Button.tsx
      Button.test.tsx  # Unit tests
      Button.stories.tsx # Storybook
      Button.cy.tsx     # E2E tests
```

### 8. Documentation Standards
```typescript:components/Layout/MainGrid.tsx
/**
 * Responsive grid layout wrapper
 * @param {ReactNode} children - Grid items
 * @param {number} [gap=4] - Gap between items
 * @example <MainGrid gap={8}>{items}</MainGrid>
 */
export const MainGrid = ({ children, gap = 4 }) => (...)
```

### 9. Security Practices
```typescript:components/User/ProfileForm.tsx
'use client';
function ProfileForm() {
  const handleSubmit = (e) => {
    e.preventDefault();
    // Always validate on server AND client
  }
}
```

*Implementation Tips:*
1. Create wrapper components for complex Joy components
2. Use CSS variables for theme customization
3. Combine with CSS Modules for custom styling

```typescript:components/Shared/JoyButton.tsx
'use client';
import Button from '@mui/joy/Button';

export default function JoyButton({ children, variant }) {
  return (
    <Button
      variant={variant}
      sx={{
        borderRadius: 'md',
        transition: 'all 0.3s ease',
      }}
    >
      {children}
    </Button>
  );
}
```

### 10. Animation (Framer Motion)
```typescript:components/Shared/AnimatedCard.tsx
'use client';
import { motion } from 'framer-motion';

export default function AnimatedCard({ children }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {children}
    </motion.div>
  );
}
```

*Best Practices:*
- Wrap motion components in custom abstractions
- Use `AnimatePresence` for mount/unmount transitions
- Lazy load heavy animations with `dynamic()`

### 11. API Route Handlers + SWR
```typescript:app/api/users/route.ts
import { NextResponse } from 'next/server';

export async function GET() {
  const res = await fetch('https://api.example.com/users', {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  if (!res.ok) throw new Error('Failed to fetch');
  const data = await res.json();

  return NextResponse.json({ data });
}
```

*Data Fetching Flow:*
1. Create API routes for internal endpoints
2. Use SWR for client-side data fetching
3. Implement revalidation strategies

```typescript:hooks/useUsers.ts
import useSWR from 'swr';

const fetcher = (...args) => fetch(...args).then(res => res.json());

export function useUsers() {
  const { data, error, isLoading } = useSWR(
    '/api/users',
    fetcher,
    {
      revalidateOnFocus: false,
      refreshInterval: 10000,
    }
  );

  return {
    users: data,
    isLoading,
    isError: error,
  };
}
```

*Enhanced Data Handling:*
```typescript:components/Users/<USER>
'use client';
import { useUsers } from '@/hooks/useUsers';
import { Skeleton } from '@mui/joy';

export default function UserList() {
  const { users, isLoading, isError } = useUsers();

  if (isError) return <div>Error loading users</div>;
  
  return (
    <div>
      {isLoading ? (
        <Skeleton variant="rectangular" height={50} />
      ) : (
        users?.map(user => (
          <div key={user.id}>{user.name}</div>
        ))
      )}
    </div>
  );
}
```

**Key Integration Points:**
1. API Route Best Practices:
   - Add validation with Zod
   - Implement rate limiting
   - Use NextResponse helpers
   - Set security headers

```typescript:app/api/posts/route.ts
import { NextResponse } from 'next/server';
import { z } from 'zod';

const schema = z.object({
  title: z.string().min(3),
  content: z.string().max(500),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const validated = schema.parse(body);
    
    // Process data...
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid input' },
      { status: 400 }
    );
  }
}
```

2. Motion + Joy Synergy:
```typescript:components/Shared/AnimatedJoyButton.tsx
'use client';
import { motion } from 'framer-motion';
import Button from '@mui/joy/Button';

export default function AnimatedJoyButton({ children }) {
  return (
    <motion.div whileHover={{ scale: 1.05 }}>
      <Button
        variant="solid"
        sx={{
          transition: 'transform 0.2s ease',
          '&:active': { transform: 'scale(0.95)' },
        }}
      >
        {children}
      </Button>
    </motion.div>
  );
}
```

**Production Considerations:**
- Use SWR's `mutation` for optimistic UI updates
- Implement API route caching headers
- Combine server actions with client-side mutations
- Use MUI Joy's `styled` function for animated components
- Prefer CSS transforms over layout properties for animations
- Implement error boundaries for API data fetching
- Use MUI Joy's loading states with SWR's isLoading
