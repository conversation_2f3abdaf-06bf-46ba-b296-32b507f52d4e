---
description:
globs:
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to

# AI Development Rules

## Project Setup Rules
1. **Environment Configuration**
   - Always create a `.env.local` file for API keys
   - Never expose API keys in the codebase
   - Use environment variables for all sensitive configurations
   - Required env variables:
     ```bash
     OPENAI_API_KEY=your_key_here
     AI_MODEL_PROVIDER=openai # or anthropic, cohere, etc.
     AI_MODEL_NAME=gpt-4-turbo # or specific model name
     MAX_TOKENS=2000 # adjust based on needs
     TEMPERATURE=0.7 # default creativity setting
     ```

2. **Dependencies**
   - Required core packages:
     ```json
     {
       "dependencies": {
         "ai": "^4.1.0",
         "@ai-sdk/openai": "^1.0.0",
         "zod": "^3.0.0",
         "@vercel/ai": "^1.0.0"
       }
     }
     ```
   - Install command: `pnpm add ai @ai-sdk/openai zod @vercel/ai`
   - Optional packages for enhanced functionality:
     ```bash
     pnpm add @ai-sdk/anthropic # for Claude models
     pnpm add @ai-sdk/cohere # for Cohere models
     ```

## API Route Handler Rules
1. **Route Structure**
   ```typescript
   // app/api/chat/route.ts
   import { openai } from '@ai-sdk/openai';
   import { streamText } from 'ai';
   import { NextResponse } from 'next/server';

   export const runtime = 'edge'; // Optional: better streaming performance
   export const maxDuration = 30; // 30 second timeout

   export async function POST(req: Request) {
     try {
       const { messages, options } = await req.json();
       const result = streamText({
         model: openai(process.env.AI_MODEL_NAME || 'gpt-4-turbo'),
         messages,
         temperature: parseFloat(process.env.TEMPERATURE || '0.7'),
         maxTokens: parseInt(process.env.MAX_TOKENS || '2000'),
         ...options
       });
       return result.toDataStreamResponse();
     } catch (error) {
       return NextResponse.json(
         { error: 'Internal Server Error' },
         { status: 500 }
       );
     }
   }
   ```

2. **Response Handling**
   - Implement proper error types:
     ```typescript
     type AIError = {
       code: string;
       message: string;
       details?: any;
     };

     const handleAIError = (error: unknown): AIError => {
       if (error instanceof OpenAIError) {
         return {
           code: 'OPENAI_ERROR',
           message: error.message,
           details: error.details
         };
       }
       // Add other error types...
       return {
         code: 'UNKNOWN_ERROR',
         message: 'An unexpected error occurred'
       };
     };
     ```

## Model Configuration Rules
1. **Provider Setup**
   - Import providers from appropriate SDK packages
   - Configure model settings based on use case
   - Example: `openai('gpt-4o')` for OpenAI's GPT-4

2. **Stream Configuration**
   ```typescript
   streamText({
     model: provider('model-name'),
     messages,
     // Additional configurations
   })
   ```

## Tool Implementation Rules
1. **Tool Structure**
   - Define clear tool descriptions
   - Use Zod for parameter validation
   - Implement async execute functions
   - Return structured data

2. **Tool Definition Template**
   ```typescript
   toolName: tool({
     description: 'Clear description of tool purpose',
     parameters: z.object({
       param: z.string().describe('Parameter description'),
     }),
     execute: async ({ param }) => {
       // Implementation
       return { result };
     },
   })
   ```

## UI Component Rules
1. **Chat Interface**
   - Use 'use client' directive for client components
   - Implement useChat hook for chat functionality
   - Handle messages, input, and submissions properly
   - Display both AI responses and tool results

2. **State Management**
   - Use appropriate maxSteps for multi-step interactions
   - Handle loading states
   - Manage error states
   - Preserve chat history when needed

## Best Practices
1. **Performance**
   - Enable streaming for real-time responses
   - Implement proper error boundaries
   - Use appropriate caching strategies
   - Monitor response times and optimize as needed

2. **Security**
   - Validate all user inputs
   - Sanitize outputs before display
   - Implement rate limiting
   - Use appropriate CORS policies

3. **Error Handling**
   - Implement proper error boundaries
   - Handle network failures gracefully
   - Provide user-friendly error messages
   - Log errors appropriately

## Multi-Step Tool Usage
1. **Configuration**
   - Set appropriate maxSteps in useChat
   - Chain tool calls logically
   - Handle tool results appropriately
   - Provide clear context for each step

2. **Implementation Example**
   ```typescript
   const { messages, input, handleInputChange, handleSubmit } = useChat({
     maxSteps: 5,
     // Additional configurations
   });
   ```

## Testing Guidelines
1. **Unit Testing Implementation**
   ```typescript
   // __tests__/tools/weather.test.ts
   import { weatherTool } from '../tools/weather';
   import { WeatherAPI } from '../tools/weather/api';
   
   jest.mock('../tools/weather/api');
   
   describe('Weather Tool', () => {
     beforeEach(() => {
       jest.clearAllMocks();
     });
   
     it('should return weather data for valid location', async () => {
       const mockWeather = {
         current: { temp: 20, humidity: 65 },
         location: 'London',
         timestamp: '2024-01-01T12:00:00Z'
       };
   
       (WeatherAPI.prototype.getWeather as jest.Mock).mockResolvedValue(mockWeather);
   
       const result = await weatherTool.execute({
         location: 'London',
         units: 'celsius',
         include_forecast: false
       });
   
       expect(result).toMatchObject({
         current: mockWeather.current,
         location: mockWeather.location
       });
     });
   
     it('should handle API errors gracefully', async () => {
       (WeatherAPI.prototype.getWeather as jest.Mock).mockRejectedValue(
         new Error('API Error')
       );
   
       await expect(
         weatherTool.execute({
           location: 'Invalid',
           units: 'celsius',
           include_forecast: false
         })
       ).rejects.toThrow('Weather tool error: API Error');
     });
   });
   ```

2. **Integration Testing Setup**
   ```typescript
   // __tests__/integration/chat.test.ts
   import { createMocks } from 'node-mocks-http';
   import { POST } from '@/app/api/chat/route';
   
   describe('Chat API Integration', () => {
     it('should handle chat requests with tools', async () => {
       const { req, res } = createMocks({
         method: 'POST',
         body: {
           messages: [
             { role: 'user', content: 'What\'s the weather in London?' }
           ],
           options: {
             temperature: 0.7,
             maxTokens: 1000
           }
         }
       });
   
       const response = await POST(req);
       expect(response.status).toBe(200);
       
       const data = await response.json();
       expect(data).toHaveProperty('content');
     });
   
     it('should handle errors gracefully', async () => {
       const { req, res } = createMocks({
         method: 'POST',
         body: { invalid: 'data' }
       });
   
       const response = await POST(req);
       expect(response.status).toBe(500);
     });
   });
   ```

## Error Handling Implementation
1. **Comprehensive Error Types**
   ```typescript
   // types/errors.ts
   export class AIBaseError extends Error {
     constructor(
       message: string,
       public code: string,
       public status: number,
       public details?: any
     ) {
       super(message);
       this.name = 'AIBaseError';
     }
   }
   
   export class ModelError extends AIBaseError {
     constructor(message: string, details?: any) {
       super(message, 'MODEL_ERROR', 500, details);
       this.name = 'ModelError';
     }
   }
   
   export class ToolError extends AIBaseError {
     constructor(
       message: string,
       public toolName: string,
       details?: any
     ) {
       super(message, 'TOOL_ERROR', 500, { toolName, ...details });
       this.name = 'ToolError';
     }
   }
   ```

2. **Error Handling Middleware**
   ```typescript
   // middleware/errorHandler.ts
   import { NextResponse } from 'next/server';
   import { AIBaseError } from '../types/errors';
   
   export async function errorHandler(
     error: unknown
   ): Promise<NextResponse> {
     if (error instanceof AIBaseError) {
       return NextResponse.json(
         {
           error: {
             code: error.code,
             message: error.message,
             details: error.details
           }
         },
         { status: error.status }
       );
     }
   
     // Handle unexpected errors
     console.error('Unexpected error:', error);
     return NextResponse.json(
       {
         error: {
           code: 'INTERNAL_ERROR',
           message: 'An unexpected error occurred'
         }
       },
       { status: 500 }
     );
   }
   ```

## Performance Optimization
1. **Caching Implementation**
   ```typescript
   // utils/cache.ts
   import { Redis } from 'ioredis';
   
   export class AICache {
     private redis: Redis;
     private prefix: string;
   
     constructor() {
       this.redis = new Redis(process.env.REDIS_URL);
       this.prefix = 'ai:';
     }
   
     async get<T>(key: string): Promise<T | null> {
       const data = await this.redis.get(this.prefix + key);
       return data ? JSON.parse(data) : null;
     }
   
     async set<T>(
       key: string,
       value: T,
       ttlSeconds: number = 3600
     ): Promise<void> {
       await this.redis.setex(
         this.prefix + key,
         ttlSeconds,
         JSON.stringify(value)
       );
     }
   
     createKey(parts: string[]): string {
       return parts.join(':');
     }
   }
   ```

2. **Rate Limiting**
   ```typescript
   // middleware/rateLimiter.ts
   import { rateLimit } from 'express-rate-limit';
   import { Redis } from 'ioredis';
   
   export const createRateLimiter = (
     windowMs: number = 60000,
     max: number = 10
   ) => {
     const redis = new Redis(process.env.REDIS_URL);
   
     return rateLimit({
       windowMs,
       max,
       standardHeaders: true,
       legacyHeaders: false,
       store: {
         async increment(key) {
           const count = await redis.incr(key);
           await redis.expire(key, windowMs / 1000);
           return count;
         },
         async decrement(key) {
           await redis.decr(key);
         },
         async resetKey(key) {
           await redis.del(key);
         }
       }
     });
   };
   ```

## Security Implementation
1. **Input Validation**
   ```typescript
   // utils/validation.ts
   import { z } from 'zod';
   
   export const messageSchema = z.object({
     role: z.enum(['user', 'assistant', 'system']),
     content: z.string().min(1).max(4000),
     name: z.string().optional(),
     toolCalls: z.array(z.any()).optional()
   });
   
   export const chatRequestSchema = z.object({
     messages: z.array(messageSchema),
     options: z.object({
       temperature: z.number().min(0).max(1).optional(),
       maxTokens: z.number().positive().optional(),
       tools: z.array(z.any()).optional()
     }).optional()
   });
   
   export const validateChatRequest = (data: unknown) => {
     return chatRequestSchema.parse(data);
   };
   ```

2. **Sanitization**
   ```typescript
   // utils/sanitization.ts
   import DOMPurify from 'dompurify';
   import { JSDOM } from 'jsdom';
   
   const window = new JSDOM('').window;
   const purify = DOMPurify(window);
   
   export const sanitizeOutput = (content: string): string => {
     return purify.sanitize(content, {
       ALLOWED_TAGS: ['p', 'b', 'i', 'em', 'strong', 'code'],
       ALLOWED_ATTR: []
     });
   };
   
   export const sanitizeToolResult = <T>(result: T): T => {
     if (typeof result === 'string') {
       return sanitizeOutput(result) as unknown as T;
     }
     if (typeof result === 'object' && result !== null) {
       return Object.entries(result).reduce((acc, [key, value]) => ({
         ...acc,
         [key]: typeof value === 'string' ? sanitizeOutput(value) : value
       }), {}) as T;
     }
     return result;
   };
   ```

## Documentation Requirements
1. **Code Documentation Structure**
   ```typescript
   /**
    * @module AI Tools
    * @description Collection of AI tools for various functionalities
    */
   
   /**
    * Weather tool for fetching current weather and forecasts
    * @class WeatherTool
    * @implements {AITool}
    * 
    * @example
    * ```typescript
    * const weather = await weatherTool.execute({
    *   location: 'London',
    *   units: 'celsius'
    * });
    * ```
    */
   export class WeatherTool implements AITool {
     /**
      * Fetches weather data for a specific location
      * @param {WeatherParams} params - Weather request parameters
      * @returns {Promise<WeatherResult>} Weather data
      * @throws {ToolError} When API request fails
      */
     async execute(params: WeatherParams): Promise<WeatherResult> {
       // Implementation
     }
   }
   ```

2. **API Documentation Template**
   ```markdown
   # AI API Documentation

   ## Overview
   This document describes the AI API endpoints and their usage.

   ## Authentication
   All endpoints require an API key passed in the Authorization header:
   ```bash
   Authorization: Bearer YOUR_API_KEY
   ```

   ## Endpoints

   ### POST /api/chat
   Creates a new chat completion.

   #### Request Body
   ```typescript
   {
     messages: Array<{
       role: 'user' | 'assistant' | 'system';
       content: string;
     }>;
     options?: {
       temperature?: number;
       maxTokens?: number;
       tools?: Array<Tool>;
     };
   }
   ```

   #### Response
   ```typescript
   {
     content: string;
     toolCalls?: Array<ToolCall>;
     usage?: {
       promptTokens: number;
       completionTokens: number;
       totalTokens: number;
     };
   }
   ```

   #### Error Responses
   - 400: Invalid request body
   - 401: Unauthorized
   - 429: Rate limit exceeded
   - 500: Internal server error
   ```

3. **Setup Documentation**
   ```markdown
   # AI Development Setup

   ## Prerequisites
   - Node.js 18+
   - pnpm
   - Redis (for caching)
   - OpenAI API key

   ## Installation
   1. Clone the repository
   2. Install dependencies:
      ```bash
      pnpm install
      ```
   3. Create `.env.local`:
      ```bash
      OPENAI_API_KEY=your_key_here
      REDIS_URL=redis://localhost:6379
      ```
   4. Start development server:
      ```bash
      pnpm dev
      ```

   ## Configuration
   ### Environment Variables
   | Variable | Description | Required | Default |
   |----------|-------------|----------|---------|
   | OPENAI_API_KEY | OpenAI API key | Yes | - |
   | AI_MODEL_NAME | Model to use | No | gpt-4-turbo |
   | MAX_TOKENS | Max tokens per request | No | 2000 |
   | TEMPERATURE | Model temperature | No | 0.7 |

   ### Rate Limiting
   Default limits:
   - 10 requests per minute per IP
   - 100 requests per hour per API key
   ```

4. **Troubleshooting Guide**
   ```markdown
   # AI Development Troubleshooting

   ## Common Issues

   ### 1. Rate Limiting Errors
   **Symptom**: 429 Too Many Requests
   **Solution**:
   - Implement exponential backoff
   - Increase rate limits if needed
   - Cache frequent requests

   ### 2. Model Timeout
   **Symptom**: Request timeout after 30s
   **Solution**:
   - Reduce prompt complexity
   - Use streaming response
   - Implement request chunking

   ### 3. High Token Usage
   **Symptom**: Excessive API costs
   **Solution**:
   - Implement token counting
   - Use context windowing
   - Cache common responses

   ## Monitoring

   ### Logging
   ```typescript
   const logger = {
     error: (err: Error, context?: any) => {
       console.error({
         timestamp: new Date().toISOString(),
         error: err.message,
         stack: err.stack,
         ...context
       });
     }
   };
   ```

   ### Metrics
   - Request latency
   - Token usage
   - Error rates
   - Cache hit rates
   ```

5. **Changelog Format**
   ```markdown
   # Changelog

   ## [1.0.0] - 2024-01-20
   ### Added
   - Initial release
   - Basic chat functionality
   - Weather tool integration

   ### Changed
   - Updated streaming implementation
   - Improved error handling

   ### Fixed
   - Rate limiting issues
   - Token counting accuracy

   ## [0.9.0] - 2024-01-10
   ### Added
   - Beta release
   - Core functionality
   ```

## Prompt Engineering Rules
1. **Advanced Prompt Structure**
   ```typescript
   const createSystemPrompt = (context: PromptContext) => `
     Role: ${context.role || 'AI Assistant'}
     
     Background:
     ${context.background}
     
     Constraints:
     ${context.constraints.join('\n')}
     
     Goals:
     ${context.goals.join('\n')}
     
     Output Format:
     ${context.outputFormat}
     
     Additional Instructions:
     ${context.additionalInstructions}
   `;

   type PromptContext = {
     role?: string;
     background: string;
     constraints: string[];
     goals: string[];
     outputFormat: string;
     additionalInstructions?: string;
   };
   ```

2. **Temperature Strategy**
   ```typescript
   const getTemperature = (task: AITask): number => {
     const temperatureMap = {
       creative: 0.8,
       balanced: 0.5,
       precise: 0.2,
       factual: 0
     };
     return temperatureMap[task] || 0.7;
   };

   type AITask = 'creative' | 'balanced' | 'precise' | 'factual';
   ```

## Streaming Implementation Rules
1. **Advanced Stream Handling**
   ```typescript
   // components/AIStream.tsx
   'use client';

   import { useEffect, useState } from 'react';
   import { useChat } from 'ai/react';

   export function AIStream() {
     const [streamParts, setStreamParts] = useState<string[]>([]);
     const [isComplete, setIsComplete] = useState(false);

     const {
       messages,
       input,
       handleInputChange,
       handleSubmit,
       isLoading,
       error
     } = useChat({
       api: '/api/chat',
       onResponse: (response) => {
         // Handle stream start
         setIsComplete(false);
       },
       onFinish: () => {
         setIsComplete(true);
       },
       onError: (error) => {
         console.error('Stream error:', error);
       }
     });

     return (
       <div className="ai-stream-container">
         {/* Render UI components */}
       </div>
     );
   }
   ```

2. **Stream Error Handling**
   ```typescript
   const handleStreamError = async (error: Error) => {
     if (error.name === 'AbortError') {
       // Handle user cancellation
       return { type: 'abort', message: 'Stream cancelled by user' };
     }
     
     if (error instanceof NetworkError) {
       // Attempt to reconnect
       return await attemptReconnection();
     }
     
     // Log other errors
     console.error('Stream error:', error);
     return { type: 'error', message: error.message };
   };
   ```

## Enhanced Tool Usage Rules
1. **Advanced Tool Definition**
   ```typescript
   // tools/weather/index.ts
   import { z } from 'zod';
   import { tool } from 'ai';
   import { WeatherAPI } from './api';
   
   export const weatherTool = tool({
     name: 'weather',
     description: 'Get detailed weather information for a location',
     version: '1.0.0',
     parameters: z.object({
       location: z.string().describe('The location to get weather for'),
       units: z.enum(['celsius', 'fahrenheit']).default('celsius'),
       include_forecast: z.boolean().default(false)
     }),
     execute: async ({ location, units, include_forecast }) => {
       try {
         const api = new WeatherAPI(process.env.WEATHER_API_KEY);
         const weather = await api.getWeather(location, {
           units,
           includeForecast: include_forecast
         });
         return {
           current: weather.current,
           forecast: include_forecast ? weather.forecast : undefined,
           location: weather.location,
           timestamp: new Date().toISOString()
         };
       } catch (error) {
         throw new Error(`Weather tool error: ${error.message}`);
       }
     }
   });
   ```

2. **Tool Result Processing**
   ```typescript
   // tools/processing.ts
   export const processToolResult = <T>(
     result: T,
     options: ProcessingOptions
   ): ProcessedResult<T> => {
     return {
       data: result,
       metadata: {
         processedAt: new Date().toISOString(),
         success: true,
         ...options.metadata
       },
       formatting: options.formatting || 'json'
     };
   };

   interface ProcessingOptions {
     metadata?: Record<string, any>;
     formatting?: 'json' | 'text' | 'markdown';
   }

   interface ProcessedResult<T> {
     data: T;
     metadata: {
       processedAt: string;
       success: boolean;
       [key: string]: any;
     };
     formatting: string;
   }
   ```
