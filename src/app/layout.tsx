import * as React from "react";
import "./output.css";
import { primary, secondary } from "@/components/fonts";
import NextTopLoader from "nextjs-toploader";
import { Providers } from "@/components/Providers";
import { ErrorBoundary } from 'react-error-boundary'
import dynamicImport from 'next/dynamic';

// Force dynamic rendering for the entire application
export const dynamic = 'force-dynamic';

const ErrorFallback = dynamicImport(() => import('@/components/ErrorFallback'), { ssr: false });

export const metadata = {
  title: "GP Pathology",
  description: "Forensics and Pathology Management System",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${primary.variable} ${secondary.variable}`}
      >
        <div id="portal" />
        <NextTopLoader />
        <ErrorBoundary fallback={<ErrorFallback />}>
          <Providers>
            {children}
          </Providers>
        </ErrorBoundary>
      </body>
    </html>
  );
}
