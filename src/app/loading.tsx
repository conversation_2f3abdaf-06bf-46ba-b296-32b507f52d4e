'use client'

import React from 'react';

const Loading: React.FC = () => {
  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
      <div style={{ textAlign: 'center' }}>
        <div className="spinner" style={{ marginBottom: '20px' }}>
          <style jsx>{`
            .spinner {
              border: 16px solid var(--joy-palette-neutral-200);
              border-top: 16px solid var(--joy-palette-primary-500);
              border-radius: 50%;
              width: 120px;
              height: 120px;
              animation: spin 2s linear infinite;
            }

            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}</style>
        </div>
        <h1>Loading...</h1>
      </div>
    </div>
  );
};

export default Loading;
