:root {
  /*************
  DEFAULTS
  *************/
  /*Colors*/
  --primary-100: var(--joy-palette-primary-100);
  --primary-200: var(--joy-palette-primary-200);
  --primary-300: var(--joy-palette-primary-300);
  --primary-400: var(--joy-palette-primary-400);
  --primary-500: var(--joy-palette-primary-500);
  --primary-600: var(--joy-palette-primary-600);
  --primary-700: var(--joy-palette-primary-700);
  --primary-800: var(--joy-palette-primary-800);
  --primary-900: var(--joy-palette-primary-900);

  --success-100: var(--joy-palette-success-100);
  --success-200: var(--joy-palette-success-200);
  --success-300: var(--joy-palette-success-300);
  --success-400: var(--joy-palette-success-400);
  --success-500: var(--joy-palette-success-500);
  --success-600: var(--joy-palette-success-600);
  --success-700: var(--joy-palette-success-700);
  --success-800: var(--joy-palette-success-800);
  --success-900: var(--joy-palette-success-900);

  --danger-100: var(--joy-palette-danger-100);
  --danger-200: var(--joy-palette-danger-200);
  --danger-300: var(--joy-palette-danger-300);
  --danger-400: var(--joy-palette-danger-400);
  --danger-500: var(--joy-palette-danger-500);
  --danger-600: var(--joy-palette-danger-600);
  --danger-700: var(--joy-palette-danger-700);
  --danger-800: var(--joy-palette-danger-800);
  --danger-900: var(--joy-palette-danger-900);

  --neutral-100: var(--joy-palette-neutral-100);
  --neutral-200: var(--joy-palette-neutral-200);
  --neutral-300: var(--joy-palette-neutral-300);
  --neutral-400: var(--joy-palette-neutral-400);
  --neutral-500: var(--joy-palette-neutral-500);
  --neutral-600: var(--joy-palette-neutral-600);
  --neutral-700: var(--joy-palette-neutral-700);
  --neutral-800: var(--joy-palette-neutral-800);
  --neutral-900: var(--joy-palette-neutral-900);

  /*************
  Global Settings
  **************/
  --global-primary-color: var(--joy-palette-primary-500);
  --global-contrast-color: var(--joy-palette-neutral-900);
  --global-background-color: var(--joy-palette-neutral-100);
  --global-secondary-color: var(--joy-palette-primary-200);
  --global-muted-color: var(--joy-palette-neutral-500);
  --global-separator-color: var(--joy-palette-primary-300);
  --global-scrollbar-color: var(--joy-palette-primary-400);

  /*************
  Copilot Kit
  **************/
  --copilot-kit-primary-color: var(--global-primary-color);
  --copilot-kit-contrast-color: var(--global-contrast-color);
  --copilot-kit-secondary-color: var(--global-secondary-color);
  --copilot-kit-secondary-contrast-color: var(--global-contrast-color);
  --copilot-kit-background-color: var(--global-background-color);
  --copilot-kit-muted-color: var(--global-muted-color);
  --copilot-kit-separator-color: var(--global-separator-color);
  --copilot-kit-scrollbar-color: var(--global-scrollbar-color);
  --copilot-kit-response-button-color: var(--global-contrast-color);
  --copilot-kit-response-button-background-color: var(--global-background-color);

  /*************
  React-Var-UI
  *************/
  --react-var-ui-foreground-color: var(--global-contrast-color);
  --react-var-ui-background-color: var(--global-secondary-color);
  --react-var-ui-accent-color: var(--global-primary-color);
  --react-var-ui-input-background-color: var(--global-background-color);
  --react-var-ui-input-background-hover-color: var(--global-secondary-color);
  --react-var-ui-input-background-pressed-color: var(--global-primary-color);
  --react-var-ui-label-background-normal-color: var(--global-secondary-color);
  --react-var-ui-label-background-hover-color: var(--global-primary-color);
  --react-var-ui-label-border-color: var(--global-primary-color);
}

.copilotKitChat {
  height: 100%;
}
