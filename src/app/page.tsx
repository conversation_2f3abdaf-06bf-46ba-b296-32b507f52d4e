'use client';

import { Box, Container, Typography, <PERSON>rid, Card, But<PERSON>, Stack, List, ListItem, ListItemDecorator } from '@mui/joy';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { DashboardContent } from '@/components/dashboard/DashboardContent';

interface Feature {
  icon: string;
  title: string;
  description: string;
}

interface Benefit {
  icon: string;
  text: string;
}

const features: Feature[] = [
  {
    icon: 'healthicons:pathology',
    title: 'Centralized Pathology Management',
    description: 'Streamlined management of pathology services across all Gauteng healthcare facilities.',
  },
  {
    icon: 'healthicons:medical-records',
    title: 'Digital Records',
    description: 'Secure digital storage and access to pathology records and test results.',
  },
  {
    icon: 'healthicons:health',
    title: 'Healthcare Integration',
    description: 'Seamless integration with provincial healthcare systems and facilities.',
  },
  {
    icon: 'healthicons:research',
    title: 'Research & Analytics',
    description: 'Advanced analytics for healthcare research and policy development.',
  },
];

const benefits: Benefit[] = [
  {
    icon: 'solar:clock-circle-bold',
    text: 'Faster turnaround times for test results',
  },
  {
    icon: 'solar:chart-2-bold',
    text: 'Improved accuracy in diagnosis and reporting',
  },
  {
    icon: 'solar:shield-check-bold',
    text: 'Enhanced patient data security and privacy',
  },
  {
    icon: 'solar:graph-new-bold',
    text: 'Better resource allocation and planning',
  },
  {
    icon: 'solar:users-group-rounded-bold',
    text: 'Improved collaboration between healthcare providers',
  },
  {
    icon: 'solar:monitor-smartphone-bold',
    text: 'Accessible from any authorized facility',
  },
];

export default function HomePage() {
  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Hero Section */}
      <Box
        sx={{
          bgcolor: 'primary.softBg',
          py: { xs: 8, md: 12 },
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Typography
                  level="h1"
                  sx={{
                    mb: 2,
                    fontSize: { xs: '2rem', md: '3rem' },
                    fontWeight: 'bold',
                  }}
                >
                  Gauteng Province Pathology Management System
                </Typography>
                <Typography level="body-lg" sx={{ mb: 4, color: 'text.secondary' }}>
                  Empowering healthcare through digital transformation. A comprehensive
                  solution for managing pathology services across the Gauteng province.
                </Typography>
                <Stack direction="row" spacing={2}>
                  <Button
                    component={Link}
                    href="/auth/login"
                    size="lg"
                    startDecorator={<Icon icon="solar:login-2-bold" />}
                  >
                    Access System
                  </Button>
                  <Button
                    component={Link}
                    href="#learn-more"
                    variant="outlined"
                    size="lg"
                    startDecorator={<Icon icon="solar:info-circle-bold" />}
                  >
                    Learn More
                  </Button>
                </Stack>
              </motion.div>
            </Grid>
            <Grid xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Box
                  sx={{
                    position: 'relative',
                    transform: 'scale(0.7)',
                    transformOrigin: 'top center',
                    boxShadow: 'lg',
                    borderRadius: 'xl',
                    overflow: 'hidden',
                    bgcolor: 'background.surface',
                    pointerEvents: 'none',
                    userSelect: 'none',
                  }}
                >
                  <Box
                    sx={{
                      height: '40px',
                      bgcolor: 'background.level1',
                      display: 'flex',
                      alignItems: 'center',
                      px: 2,
                      gap: 1,
                    }}
                  >
                    <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: 'danger.500' }} />
                    <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: 'warning.500' }} />
                    <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: 'success.500' }} />
                  </Box>
                  <Box sx={{ p: 2 }}>
                    <DashboardContent previewMode={true} />
                  </Box>
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 8, md: 12 } }}>
        <Typography
          level="h2"
          sx={{ textAlign: 'center', mb: 6 }}
          id="learn-more"
        >
          Key Features
        </Typography>
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid xs={12} sm={6} md={3} key={index}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card variant="soft" sx={{ height: '100%' }}>
                  <Icon
                    icon={feature.icon}
                    style={{ fontSize: '2.5rem', color: 'var(--joy-palette-primary-500)' }}
                  />
                  <Typography level="h4" sx={{ mt: 2, mb: 1 }}>
                    {feature.title}
                  </Typography>
                  <Typography level="body-sm" sx={{ color: 'text.secondary' }}>
                    {feature.description}
                  </Typography>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Benefits Section */}
      <Box sx={{ bgcolor: 'background.level1', py: { xs: 8, md: 12 } }}>
        <Container maxWidth="lg">
          <Typography level="h2" sx={{ textAlign: 'center', mb: 6 }}>
            Benefits to Healthcare
          </Typography>
          <Grid container spacing={4}>
            <Grid xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <List
                  size="lg"
                  sx={{
                    gap: 2,
                    '& > li': {
                      bgcolor: 'background.surface',
                      p: 2,
                      borderRadius: 'sm',
                    },
                  }}
                >
                  {benefits.map((benefit, index) => (
                    <ListItem key={index}>
                      <ListItemDecorator>
                        <Icon icon={benefit.icon} width={24} height={24} />
                      </ListItemDecorator>
                      {benefit.text}
                    </ListItem>
                  ))}
                </List>
              </motion.div>
            </Grid>
            <Grid xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Typography level="h3" sx={{ mb: 3 }}>
                  Serving the Gauteng Community
                </Typography>
                <Typography level="body-lg" sx={{ mb: 2, color: 'text.secondary' }}>
                  The GP Pathology Management System is a cornerstone of healthcare
                  delivery in Gauteng Province, supporting:
                </Typography>
                <List>
                  <ListItem>
                    <ListItemDecorator>
                      <Icon icon="solar:hospital-bold" />
                    </ListItemDecorator>
                    Public Hospitals and Clinics
                  </ListItem>
                  <ListItem>
                    <ListItemDecorator>
                      <Icon icon="solar:users-group-rounded-bold" />
                    </ListItemDecorator>
                    Healthcare Professionals
                  </ListItem>
                  <ListItem>
                    <ListItemDecorator>
                      <Icon icon="solar:heart-bold" />
                    </ListItemDecorator>
                    Patient Care Services
                  </ListItem>
                  <ListItem>
                    <ListItemDecorator>
                      <Icon icon="solar:chart-2-bold" />
                    </ListItemDecorator>
                    Health Research Initiatives
                  </ListItem>
                </List>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Call to Action */}
      <Container maxWidth="lg" sx={{ py: { xs: 8, md: 12 } }}>
        <Card
          variant="soft"
          sx={{
            textAlign: 'center',
            p: { xs: 4, md: 6 },
            bgcolor: 'primary.softBg',
          }}
        >
          <Typography level="h3" sx={{ mb: 2 }}>
            Ready to Get Started?
          </Typography>
          <Typography sx={{ mb: 4, maxWidth: '600px', mx: 'auto' }}>
            Join the digital transformation of healthcare in Gauteng Province.
            Access the system to start managing pathology services more efficiently.
          </Typography>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            justifyContent="center"
          >
            <Button
              component={Link}
              href="/auth/login"
              size="lg"
              startDecorator={<Icon icon="solar:login-2-bold" />}
            >
              Sign In
            </Button>
            <Button
              component={Link}
              href="/contact"
              variant="outlined"
              size="lg"
              startDecorator={<Icon icon="solar:phone-bold" />}
            >
              Contact Support
            </Button>
          </Stack>
        </Card>
      </Container>
    </Box>
  );
}
