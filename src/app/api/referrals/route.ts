// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { z } from "zod"

// Schema for body referral validation based on Prisma schema
const bodyReferralSchema = z.object({
  bodyId: z.string().min(1, { message: "Body ID is required" }),
  referralType: z.enum(["LODOX", "XRAY", "SPECIMEN", "OTHER"], {
    required_error: "Referral type is required",
  }),
  status: z.enum(["PENDING", "IN_PROGRESS", "COMPLETED", "RETURNED", "CANCELLED"]).default("PENDING"),

  // Employee Details
  employeePersal: z.string().min(1, { message: "Employee PERSAL number is required" }),
  employeeName: z.string().min(1, { message: "Employee name is required" }),
  institutionName: z.string().min(1, { message: "Institution name is required" }),
  vehicleReg: z.string().min(1, { message: "Vehicle registration is required" }),

  // Specimen Details
  specimenKitNumber: z.string().optional(),
  evidenceBagSerial: z.string().optional(),

  // Photos
  bodyTagPhoto: z.string().optional(),
  vehiclePhoto: z.string().optional(),

  // GPS Location
  gpsLatitude: z.number().optional(),
  gpsLongitude: z.number().optional(),

  // References
  referredById: z.string().min(1, { message: "Referring staff is required" }),
  assignedToId: z.string().min(1, { message: "Assigned staff is required" }),

  // Notes
  notes: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request body
    const validatedData = bodyReferralSchema.parse(body)

    // Create new body referral
    const bodyReferral = await prisma.bodyReferral.create({
      data: {
        ...validatedData,
        referralDate: new Date(),
      },
      include: {
        body: true,
        referredBy: true,
        assignedTo: true,
      },
    })

   /*  // Create activity log
    await prisma.activityLog.create({
      data: {
        activityType: "CREATE_BODY_REFERRAL",
        description: "Body referral created",
        details: JSON.stringify(bodyReferral),
        userId: validatedData.referredById,
        bodyReferralId: bodyReferral.id,
      },
    }) */

    // Update body status to REFERRED
    await prisma.body.update({
      where: { id: validatedData.bodyId },
      data: { status: "REFERRED" },
    })

    return NextResponse.json(bodyReferral, { status: 201 })
  } catch (error) {
    console.error("Error creating body referral:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to create body referral" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const bodyId = searchParams.get("bodyId")
    const status = searchParams.get("status")
    const referralType = searchParams.get("referralType")
    const referredById = searchParams.get("referredById")
    const assignedToId = searchParams.get("assignedToId")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Build filter object
    const where: any = {}
    if (bodyId) where.bodyId = bodyId
    if (status) where.status = status
    if (referralType) where.referralType = referralType
    if (referredById) where.referredById = referredById
    if (assignedToId) where.assignedToId = assignedToId

    // Get total count for pagination
    const total = await prisma.bodyReferral.count({ where })

    // Get body referrals with pagination
    const bodyReferrals = await prisma.bodyReferral.findMany({
      where,
      include: {
        body: true,
        referredBy: true,
        assignedTo: true,
        activityLogs: {
          take: 5,
          orderBy: {
            timestamp: "desc",
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        referralDate: "desc",
      },
    })

    return NextResponse.json({
      data: bodyReferrals,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching body referrals:", error)
    return NextResponse.json({ error: "Failed to fetch body referrals" }, { status: 500 })
  }
}

