// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { ReferralStatus, ReferralType } from '@prisma/client';

interface UpdateReferralData {
  status?: ReferralStatus;
  referralType?: ReferralType;
  employeePersal?: string;
  employeeName?: string;
  institutionName?: string;
  vehicleReg?: string;
  specimenKitNumber?: string;
  evidenceBagSerial?: string;
  bodyTagPhoto?: string;
  vehiclePhoto?: string;
  gpsLatitude?: number;
  gpsLongitude?: number;
  notes?: string;
  assignedToId?: string;
}

function validateReferralData(data: UpdateReferralData) {
  const errors: string[] = [];

  if (data.employeePersal && data.employeePersal.length < 3) {
    errors.push('Employee Persal must be at least 3 characters long');
  }

  if (data.employeeName && data.employeeName.length < 2) {
    errors.push('Employee Name must be at least 2 characters long');
  }

  if (data.institutionName && data.institutionName.length < 2) {
    errors.push('Institution Name must be at least 2 characters long');
  }

  if (data.vehicleReg && data.vehicleReg.length < 2) {
    errors.push('Vehicle Registration must be at least 2 characters long');
  }

  if (data.specimenKitNumber && data.specimenKitNumber.length < 2) {
    errors.push('Specimen Kit Number must be at least 2 characters long');
  }

  if (data.evidenceBagSerial && data.evidenceBagSerial.length < 2) {
    errors.push('Evidence Bag Serial must be at least 2 characters long');
  }

  if (data.notes && data.notes.length > 1000) {
    errors.push('Notes must not exceed 1000 characters');
  }

  return errors;
}

// GET /api/referrals/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const referral = await prisma.bodyReferral.findUnique({
      where: { id: params.id },
      include: {
        body: {
          include: {
            bodyTag: true,
          },
        },
        referredBy: {
          select: {
            id: true,
            name: true,
            role: true,
            department: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            role: true,
            department: true,
          },
        },
      },
    });

    if (!referral) {
      return new NextResponse('Referral not found', { status: 404 });
    }

    return NextResponse.json(referral);
  } catch (error) {
    console.error('Error fetching referral:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

// PUT /api/referrals/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const data: UpdateReferralData = await request.json();
    const errors = validateReferralData(data);

    if (errors.length > 0) {
      return NextResponse.json({ errors }, { status: 400 });
    }

    const existingReferral = await prisma.bodyReferral.findUnique({
      where: { id: params.id },
    });

    if (!existingReferral) {
      return new NextResponse('Referral not found', { status: 404 });
    }

    const updatedReferral = await prisma.bodyReferral.update({
      where: { id: params.id },
      data: {
        ...data,
      },
      include: {
        body: {
          include: {
            bodyTag: true,
          },
        },
        referredBy: {
          select: {
            id: true,
            name: true,
            role: true,
            department: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            role: true,
            department: true,
          },
        },
      },
    });

    return NextResponse.json(updatedReferral);
  } catch (error) {
    console.error('Error updating referral:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

// DELETE /api/referrals/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const existingReferral = await prisma.bodyReferral.findUnique({
      where: { id: params.id },
    });

    if (!existingReferral) {
      return new NextResponse('Referral not found', { status: 404 });
    }

    await prisma.bodyReferral.delete({
      where: { id: params.id },
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting referral:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}