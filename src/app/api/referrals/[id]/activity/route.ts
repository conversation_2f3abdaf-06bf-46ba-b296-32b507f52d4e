// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';

// GET /api/referrals/[id]/activity
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const activities = await prisma.activityLog.findMany({
      where: {
        bodyReferralId: params.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true,
            department: true,
          },
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
    });

    return NextResponse.json(activities);
  } catch (error) {
    console.error('Error fetching referral activities:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

// POST /api/referrals/[id]/activity
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const data = await request.json();
    const { activityType, description } = data;

    if (!activityType || !description) {
      return new NextResponse('Missing required fields', { status: 400 });
    }

    // Verify referral exists
    const referral = await prisma.bodyReferral.findUnique({
      where: { id: params.id },
    });

    if (!referral) {
      return new NextResponse('Referral not found', { status: 404 });
    }

    // Create activity log
    const activity = await prisma.activityLog.create({
      data: {
        activityType,
        description,
        userId: session.user.id,
        bodyReferralId: params.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true,
            department: true,
          },
        },
      },
    });

    return NextResponse.json(activity);
  } catch (error) {
    console.error('Error creating referral activity:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
