// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'

// GET /api/shifts - Get all shifts
export async function GET(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const facilityId = searchParams.get('facilityId')
    const userId = searchParams.get('userId')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Build where clause based on filters
    const where: any = {}
    if (facilityId) where.facilityId = facilityId
    if (userId) where.userId = userId
    if (startDate) where.startTime = { gte: new Date(startDate) }
    if (endDate) where.endTime = { lte: new Date(endDate) }

    // If not admin, only show shifts from user's facility
    if (session.user.role !== 'ADMIN') {
      where.facilityId = session.user.facilityId
    }

    const shifts = await prisma.shift.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        facility: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        startTime: 'asc',
      },
    })

    return NextResponse.json(shifts)
  } catch (error) {
    console.error('Error fetching shifts:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST /api/shifts - Create a new shift
export async function POST(request: Request) {
  try {
    const session = await auth()
    if (!session || !['ADMIN', 'MORGUE_STAFF'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { userId, facilityId, type, startTime, endTime, notes } = body

    // Basic validation
    if (!userId || !facilityId || !type || !startTime || !endTime) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate dates
    const start = new Date(startTime)
    const end = new Date(endTime)
    if (end <= start) {
      return NextResponse.json(
        { error: 'End time must be after start time' },
        { status: 400 }
      )
    }

    // Check for overlapping shifts
    const overlapping = await prisma.shift.findFirst({
      where: {
        userId,
        OR: [
          {
            AND: [
              { startTime: { lte: start } },
              { endTime: { gt: start } },
            ],
          },
          {
            AND: [
              { startTime: { lt: end } },
              { endTime: { gte: end } },
            ],
          },
        ],
      },
    })

    if (overlapping) {
      return NextResponse.json(
        { error: 'Shift overlaps with existing shift' },
        { status: 400 }
      )
    }

    const shift = await prisma.shift.create({
      data: {
        userId,
        facilityId,
        type,
        startTime: start,
        endTime: end,
        notes,
      },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'CREATE_SHIFT',
        entityType: 'SHIFT',
        entityId: shift.id,
        performedBy: session.user.id,
        changes: body,
      },
    })

    return NextResponse.json(shift)
  } catch (error) {
    console.error('Error creating shift:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT /api/shifts - Update a shift
export async function PUT(request: Request) {
  try {
    const session = await auth()
    if (!session || !['ADMIN', 'MORGUE_STAFF'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = body

    // If updating times, validate them
    if (updateData.startTime && updateData.endTime) {
      const start = new Date(updateData.startTime)
      const end = new Date(updateData.endTime)
      if (end <= start) {
        return NextResponse.json(
          { error: 'End time must be after start time' },
          { status: 400 }
        )
      }

      // Check for overlapping shifts
      const overlapping = await prisma.shift.findFirst({
        where: {
          id: { not: id },
          userId: updateData.userId || undefined,
          OR: [
            {
              AND: [
                { startTime: { lte: start } },
                { endTime: { gt: start } },
              ],
            },
            {
              AND: [
                { startTime: { lt: end } },
                { endTime: { gte: end } },
              ],
            },
          ],
        },
      })

      if (overlapping) {
        return NextResponse.json(
          { error: 'Shift overlaps with existing shift' },
          { status: 400 }
        )
      }
    }

    const shift = await prisma.shift.update({
      where: { id },
      data: updateData,
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_SHIFT',
        entityType: 'SHIFT',
        entityId: id,
        performedBy: session.user.id,
        changes: updateData,
      },
    })

    return NextResponse.json(shift)
  } catch (error) {
    console.error('Error updating shift:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 