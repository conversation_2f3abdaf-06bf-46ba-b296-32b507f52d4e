import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { 
  updateOverdueBodyTags, 
  getOverdueBodyTags, 
  getUpcomingOverdueBodyTags,
  markBodyTagAsOverdue,
  resolveOverdueBodyTag
} from '@/lib/utils/bodyTagStatusUpdater';

/**
 * GET /api/body-tags/overdue
 * Get overdue body tags or upcoming overdue tags
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'current'; // 'current' | 'upcoming'

    let result;
    if (type === 'upcoming') {
      result = await getUpcomingOverdueBodyTags();
    } else {
      result = await getOverdueBodyTags();
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching overdue body tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch overdue body tags' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/body-tags/overdue
 * Update overdue status for eligible body tags or manually mark a tag as overdue
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin permissions
    if (session.user.role !== 'ADMIN' && session.user.role !== 'FACILITY_MANAGER') {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { action, bodyTagId, newStatus } = body;

    if (action === 'update-all') {
      // Update all eligible tags to overdue
      const result = await updateOverdueBodyTags();
      return NextResponse.json({
        message: `Updated ${result.updated} body tags to OVERDUE status`,
        updated: result.updated,
        overdueTagIds: result.overdueTagIds
      });
    } else if (action === 'mark-overdue' && bodyTagId) {
      // Manually mark a specific tag as overdue
      const result = await markBodyTagAsOverdue(bodyTagId);
      return NextResponse.json({
        message: `Marked body tag ${result.tagNumber} as OVERDUE`,
        bodyTag: result
      });
    } else if (action === 'resolve-overdue' && bodyTagId && newStatus) {
      // Resolve an overdue tag
      const result = await resolveOverdueBodyTag(bodyTagId, newStatus);
      return NextResponse.json({
        message: `Resolved overdue body tag ${result.tagNumber} to ${newStatus}`,
        bodyTag: result
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid action or missing parameters' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error processing overdue body tags:', error);
    return NextResponse.json(
      { error: 'Failed to process overdue body tags' },
      { status: 500 }
    );
  }
}
