// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { auth } from '@/auth';

const prisma = new PrismaClient();

export async function GET(req: NextRequest) {
  try {
    // Authenticate request
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extract tag number from query parameters
    const tagNumber = req.nextUrl.searchParams.get('tagNumber');
    
    if (!tagNumber) {
      return NextResponse.json({ error: 'Tag number is required' }, { status: 400 });
    }

    // Validate body tag
    const bodyTag = await prisma.bodyTag.findUnique({
      where: { tagNumber },
      include: {
        body: true,
        facility: true,
      },
    });

    if (!bodyTag) {
      return NextResponse.json({ error: 'Body tag not found' }, { status: 404 });
    }

    // Additional validation checks
    if (!bodyTag.isActive) {
      return NextResponse.json({ error: 'Body tag is no longer active' }, { status: 400 });
    }

    // Return validated body tag details
    return NextResponse.json({
      id: bodyTag.id,
      tagNumber: bodyTag.tagNumber,
      status: bodyTag.status,
      generatedAt: bodyTag.generatedAt,
      generatedBy: bodyTag.generatedBy,
      lastScannedAt: bodyTag.lastScannedAt,
      lastScannedBy: bodyTag.lastScannedBy,
      notes: bodyTag.notes,
      facility: bodyTag.facility ? {
        id: bodyTag.facility.id,
        name: bodyTag.facility.name,
      } : null,
    });
  } catch (error) {
    console.error('Body tag validation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
