// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { TagStatus } from '@prisma/client';
import { auth } from '@/auth';

export async function DELETE(request: Request) {
  try {
    // Check authentication and authorization
    const session = await auth();
    if (!session || !['ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Delete all body tags with status GENERATED
    const result = await prisma.bodyTag.deleteMany({
      where: {
        status: TagStatus.GENERATED,
        bodyId: null,
        collectionId: null,
        admissionId: null
      }
    });

    return NextResponse.json({
      success: true,
      message: `Successfully removed ${result.count} generated body tags`,
      count: result.count
    });
  } catch (error) {
    console.error('Error removing generated body tags:', error);
    return NextResponse.json(
      { error: 'Failed to remove generated body tags', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
