// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { BodyTagScanType } from '@/types';
import { BodyTagValidator } from '@/lib/body-tag-validator';

// Validation schema for scan request
const scanRequestSchema = z.object({
  tagNumber: z.string().min(1, "Tag number is required"),
  qrCodeValue: z.string().min(1, "QR code value is required"),
  scanType: z.nativeEnum(BodyTagScanType).default(BodyTagScanType.NONE),
});


export async function POST(request: Request) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = scanRequestSchema.parse(body);

    // Create validator instance
    const validator = new BodyTagValidator(
      validatedData.tagNumber,
      validatedData.qrCodeValue,
      validatedData.scanType
    );

    // Validate the scan
    const validationResult = await validator.validate();

    // If validation fails, return the error details
    if (!validationResult.isValid) {
      return NextResponse.json({
        success: false,
        error: validationResult.message,
        requirements: validationResult.requirements,
        errorDetails: {
          code: 'VALIDATION_FAILED',
          description: validationResult.message
        }
      }, { status: 400 });
    }

    // Update last scanned info
    const updatedTag = await prisma.bodyTag.update({
      where: { 
        tagNumber: validatedData.tagNumber,
        qrCodeValue: validatedData.qrCodeValue,
        isActive: true
      },
      data: {
        lastScannedAt: new Date(),
      },
      include: {
        facility: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Return success response with validation requirements
    return NextResponse.json({
      success: true,
      message: validationResult.message,
      requirements: validationResult.requirements,
      bodyTag: {
        ...updatedTag,
        facilityName: updatedTag.facility?.name || null
      }
    });

  } catch (error) {
    console.error('Error processing body tag scan:', error);
    
    // Handle validation errors specifically
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        errorDetails: {
          code: 'INVALID_REQUEST',
          description: error.errors.map(e => e.message).join(', ')
        }
      }, { status: 400 });
    }

    // Handle Prisma errors
    if (error instanceof Error && error.name === 'PrismaClientKnownRequestError') {
      return NextResponse.json({
        success: false,
        error: 'Database operation failed',
        errorDetails: {
          code: 'DATABASE_ERROR',
          description: 'Failed to update body tag information'
        }
      }, { status: 500 });
    }

    // Handle other errors
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      errorDetails: {
        code: 'SERVER_ERROR',
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      }
    }, { status: 500 });
  }
}