import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { getFacilityCodeForBodyTag } from '@/utils/facilityCodeUtils';

/**
 * GET /api/body-tags/next-number
 * 
 * Returns the next sequential body tag number for a facility without incrementing the counter.
 * This is used for preview purposes in the UI.
 * 
 * Query Parameters:
 * - facilityId: string (required) - The facility ID to get the next number for
 * 
 * Returns:
 * - nextTagNumber: string - The next body tag number in format GP/facility/number/year
 * - currentSequence: number - The current sequence number
 * - year: number - The current year
 * - facilityCode: string - The facility short code
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication using NextAuth v5
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get facility ID from query parameters
    const { searchParams } = new URL(request.url);
    const facilityId = searchParams.get('facilityId');

    if (!facilityId) {
      return NextResponse.json(
        { error: 'facilityId parameter is required' },
        { status: 400 }
      );
    }

    const user = session.user;

    // Validate facility exists and user has access
    const facility = await prisma.facility.findUnique({
      where: { id: facilityId },
      select: {
        id: true,
        name: true,
        code: true,
      },
    });

    if (!facility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Get facility code and current year
    const { shortCode } = await getFacilityCodeForBodyTag(facilityId);
    const currentYear = new Date().getFullYear();

    // Get current sequence for this facility and year
    const sequence = await prisma.bodyNumberSequence.findUnique({
      where: {
        facilityId_year: {
          facilityId,
          year: currentYear,
        },
      },
    });

    // Calculate next number (current + 1, or 1 if no sequence exists)
    const nextNumber = sequence ? sequence.lastNumber + 1 : 1;

    // Format the next body tag number with 4-digit padding
    const paddedNumber = nextNumber.toString().padStart(4, '0');
    const nextTagNumber = `GP/${shortCode}/${paddedNumber}/${currentYear}`;

    return NextResponse.json({
      success: true,
      nextTagNumber,
      currentSequence: sequence?.lastNumber || 0,
      nextSequence: nextNumber,
      year: currentYear,
      facilityCode: shortCode,
      facilityName: facility.name,
    });

  } catch (error) {
    console.error('Error getting next body tag number:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/body-tags/next-number/reserve
 * 
 * Reserves the next sequential number by incrementing the counter.
 * This should be called when actually generating a body tag.
 * 
 * Body:
 * - facilityId: string (required) - The facility ID to reserve the next number for
 * 
 * Returns:
 * - reservedTagNumber: string - The reserved body tag number
 * - sequenceNumber: number - The reserved sequence number
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication using NextAuth v5
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = session.user;

    // Parse request body
    const body = await request.json();
    const { facilityId } = body;

    if (!facilityId) {
      return NextResponse.json(
        { error: 'facilityId is required' },
        { status: 400 }
      );
    }

    // Validate facility exists
    const facility = await prisma.facility.findUnique({
      where: { id: facilityId },
      select: {
        id: true,
        name: true,
        code: true,
      },
    });

    if (!facility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Get facility code and current year
    const { shortCode } = await getFacilityCodeForBodyTag(facilityId);
    const currentYear = new Date().getFullYear();

    // Atomically increment the sequence number
    const sequence = await prisma.bodyNumberSequence.upsert({
      where: {
        facilityId_year: {
          facilityId,
          year: currentYear,
        },
      },
      update: {
        lastNumber: {
          increment: 1,
        },
      },
      create: {
        facilityId,
        year: currentYear,
        lastNumber: 1,
      },
    });

    // Format the reserved body tag number with 4-digit padding
    const paddedNumber = sequence.lastNumber.toString().padStart(4, '0');
    const reservedTagNumber = `GP/${shortCode}/${paddedNumber}/${currentYear}`;

    return NextResponse.json({
      success: true,
      reservedTagNumber,
      sequenceNumber: sequence.lastNumber,
      year: currentYear,
      facilityCode: shortCode,
      facilityName: facility.name,
    });

  } catch (error) {
    console.error('Error reserving body tag number:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
