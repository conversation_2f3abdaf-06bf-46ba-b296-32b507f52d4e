// Force dynamic rendering
export const dynamic = "force-dynamic";

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { TagStatus } from '@prisma/client';
import { generateBodyTag } from '@/utils/generateBodyTag';
import { auth } from '@/auth';

// Validation schema for body tag generation
const generateBodyTagSchema = z.object({
  facilityId: z.string().min(1, "Facility ID is required"),
  count: z.number().int().min(1).max(100).default(1), // Allow generating multiple tags at once
  qrCodeFormat: z.string().default('QR_CODE'),
  metadata: z.record(z.any()).optional(),
});

export async function POST(request: Request) {
  try {
    // Check authentication using NextAuth v5
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = session.user;

    const body = await request.json();

    // Validate request body
    const validationResult = generateBodyTagSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Check if facility exists
    const facility = await prisma.facility.findUnique({
      where: { id: data.facilityId },
      select: { 
        id: true,
        name: true, 
        code: true,
      }
    });

    if (!facility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Generate the requested number of body tags
    const generatedTags = [];

    for (let i = 0; i < data.count; i++) {
      try {
        // Generate the body tag number using the new format
        const tagNumber = await generateBodyTag(data.facilityId);

        // Create QR code value (same as tag number for now)
        const qrCodeValue = tagNumber;

        // Check for duplicate tag number (should be very rare with sequential numbering)
        const existingTag = await prisma.bodyTag.findUnique({
          where: { tagNumber },
        });

        if (existingTag) {
          console.warn(`Duplicate tag number generated: ${tagNumber}, skipping...`);
          continue;
        }

        // Create body tag
        const bodyTag = await prisma.bodyTag.create({
          data: {
            tagNumber,
            generatedBy: user.id!,
            status: TagStatus.GENERATED,
            qrCodeValue,
            qrCodeFormat: data.qrCodeFormat,
            qrCodeMetadata: {
              ...data.metadata,
              generatedAt: new Date().toISOString(),
              generatedBy: user.id!,
              generatedByEmail: user.email!,
            },
            facilityId: data.facilityId,
            isActive: true,
            notes: null,
            bodyId: null,
            collectionId: null,
            admissionId: null,
            lastScannedAt: null,
            lastScannedBy: null,
          }
        });

        generatedTags.push({
          ...bodyTag,
          facility: {
            id: data.facilityId,
            name: facility.name,
            code: facility.code,
          }
        });

      } catch (tagError) {
        console.error(`Error generating tag ${i + 1}:`, tagError);
        // Continue with next tag generation
      }
    }

    if (generatedTags.length === 0) {
      return NextResponse.json(
        { error: 'Failed to generate any body tags' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Successfully generated ${generatedTags.length} body tag(s)`,
      tags: generatedTags,
      count: generatedTags.length
    }, { status: 201 });

  } catch (error) {
    console.error('Error generating body tags:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
