// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { TagStatus } from '@prisma/client';

// Validation schema for body tag creation
const createBodyTagSchema = z.object({
  tagNumber: z.string().min(1, "Tag number is required"),
  facilityId: z.string().min(1, "Facility ID is required"),
  qrCodeValue: z.string().min(1, "QR code value is required"),
  qrCodeFormat: z.string().default('QR_CODE'),
  status: z.nativeEnum(TagStatus).default('GENERATED'),
  metadata: z.record(z.any()).optional(),
});

// Validation schema for updating tag status
const updateTagSchema = z.object({
  isActive: z.boolean(),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Validate request body
    const validationResult = createBodyTagSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Check if facility exists
    const facility = await prisma.facility.findUnique({
      where: { id: data.facilityId },
      select: { name: true }
    });

    if (!facility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Check for duplicate tag number
    const existingTag = await prisma.bodyTag.findUnique({
      where: { tagNumber: data.tagNumber },
    });

    if (existingTag) {
      return NextResponse.json(
        { error: 'Tag number already exists' },
        { status: 409 }
      );
    }

    // Create body tag with exact schema fields
    const bodyTag = await prisma.bodyTag.create({
      data: {
        tagNumber: data.tagNumber,
        generatedBy: 'system', // TODO: Replace with actual user ID from session
        status: data.status,
        qrCodeValue: data.qrCodeValue,
        qrCodeFormat: data.qrCodeFormat,
        qrCodeMetadata: data.metadata,
        facilityId: data.facilityId,
        isActive: true,
        notes: null,
        bodyId: null,
        collectionId: null,
        admissionId: null,
        lastScannedAt: null,
        lastScannedBy: null,
      }
    });

    // Return the tag with facility info
    return NextResponse.json({
      ...bodyTag,
      facility: {
        id: data.facilityId,
        name: facility.name
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating body tag:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    console.log('API Request URL:', request.url);
    console.log('Search Params:', Object.fromEntries(searchParams.entries()));

    //Status is an array of strings
    const status = searchParams.get('status');
    const statusArray = status ? status.split(',') : [];
    //Facility ID is a string
    const facilityId = searchParams.get('facilityId');
    // Check if we should filter out tags already associated with a body
    const scanType = searchParams.get('scanType');
    console.log('Scan Type:', scanType);
    // Handle both lowercase and uppercase scan types for robustness
    const isCollection = scanType?.toLowerCase() === 'collection';
    console.log('Is Collection:', isCollection);

    // Get a list of body tag IDs that are already associated with bodies
    let usedBodyTagIds: string[] = [];
    if (isCollection) {
      console.log('Filtering for collection scan type, excluding used body tags');
      try {
        const bodies = await prisma.body.findMany({
          select: { bodyTagId: true, id: true },
          where: { bodyTagId: { not: null } }
        });
        usedBodyTagIds = bodies.map(body => body.bodyTagId as string).filter(Boolean);
        console.log(`Found ${usedBodyTagIds.length} body tags already in use:`, usedBodyTagIds);
      } catch (bodyQueryError) {
        console.error('Error fetching used body tags:', bodyQueryError);
        // Continue with empty usedBodyTagIds array
      }
    }

    //to filter we can use the fields "facilityId" and "status"
    // Build the where clause step by step to avoid issues
    const whereClause: any = {
      isActive: true
    };

    // Add status filter if provided
    if (statusArray.length > 0) {
      whereClause.status = { in: statusArray as TagStatus[] };
    }

    // Add facility filter if provided
    if (facilityId) {
      whereClause.facilityId = facilityId;
    }

    // For collection, only show tags that are not already associated with a body
    if (isCollection) {
      whereClause.bodyId = null;

      // Only add notIn filter if there are actually used body tags
      if (usedBodyTagIds.length > 0) {
        whereClause.id = { notIn: usedBodyTagIds };
      }
    }

    console.log('Final where clause:', JSON.stringify(whereClause, null, 2));

    let bodyTags = [];
    try {
      bodyTags = await prisma.bodyTag.findMany({
        where: whereClause,
        include: {
          facility: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      console.log(`Found ${bodyTags.length} body tags matching criteria`);
    } catch (queryError) {
      console.error('Error in prisma.bodyTag.findMany:', queryError);
      if (queryError instanceof Error) {
        console.error('Query error message:', queryError.message);
        console.error('Query error stack:', queryError.stack);
      }
      throw queryError; // Re-throw to be caught by the outer catch block
    }

    // Transform the data to include facility name
    const transformedTags = bodyTags.map(tag => ({
      id: tag.id,
      tagNumber: tag.tagNumber,
      status: tag.status,
      generatedAt: tag.generatedAt,
      facilityName: tag.facility?.name,
    }));

    return NextResponse.json(transformedTags);
  } catch (error) {
    console.error('Error fetching body tags:', error);
    // Log more detailed error information
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    return NextResponse.json(
      {
        error: 'Failed to fetch body tags',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { id } = body;

    // Validate request body
    const validatedData = updateTagSchema.parse(body);

    // Update the tag
    const updatedTag = await prisma.bodyTag.update({
      where: { id },
      data: {
        isActive: validatedData.isActive,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(updatedTag);
  } catch (error) {
    console.error('Error updating body tag:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update body tag' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

    // Delete the tag
    await prisma.bodyTag.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting body tag:', error);
    return NextResponse.json(
      { error: 'Failed to delete body tag' },
      { status: 500 }
    );
  }
}