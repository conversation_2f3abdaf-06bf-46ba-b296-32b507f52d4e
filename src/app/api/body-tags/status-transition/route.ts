import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { 
  validateStatusTransition, 
  getTransitionType,
  type StatusTransitionAuditData 
} from '@/lib/schema/statusTransitionSchemas';

const prisma = new PrismaClient();

/**
 * POST /api/body-tags/status-transition
 * Handle body tag status transitions with audit logging
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      bodyTagId, 
      transitionType, 
      fromStatus, 
      toStatus, 
      responsibleStaffId,
      ...transitionData 
    } = body;

    // Validate required fields
    if (!bodyTagId || !transitionType || !fromStatus || !toStatus) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate the status transition is allowed
    if (!validateStatusTransition(fromStatus, toStatus)) {
      return NextResponse.json(
        { error: `Invalid status transition: ${fromStatus} → ${toStatus}` },
        { status: 400 }
      );
    }

    // Start a database transaction
    const result = await prisma.$transaction(async (tx) => {
      // 1. Verify the body tag exists and has the correct current status
      const bodyTag = await tx.bodyTag.findUnique({
        where: { id: bodyTagId },
        include: {
          body: true,
          facility: true
        }
      });

      if (!bodyTag) {
        throw new Error('Body tag not found');
      }

      if (bodyTag.status !== fromStatus) {
        throw new Error(`Body tag status is ${bodyTag.status}, expected ${fromStatus}`);
      }

      // 2. Update the body tag status
      const updatedBodyTag = await tx.bodyTag.update({
        where: { id: bodyTagId },
        data: {
          status: toStatus,
          updatedAt: new Date(),
        },
        include: {
          body: true,
          facility: true
        }
      });

      // 3. Create audit log entry
      const auditData: StatusTransitionAuditData = {
        bodyTagId,
        fromStatus,
        toStatus,
        transitionType: transitionType as any,
        performedBy: session.user.id,
        timestamp: new Date(),
        metadata: transitionData,
        notes: transitionData.notes || null
      };

      await tx.statusTransitionAudit.create({
        data: auditData
      });

      // 4. Handle specific transition logic
      switch (transitionType) {
        case 'ADMISSION_TO_REFERRAL_IN':
          // Create referral in record
          await tx.referralIn.create({
            data: {
              bodyTagId,
              sourceFacilityId: transitionData.sourceFacilityId,
              referralNumber: transitionData.referralNumber,
              receivingStaffId: transitionData.receivingStaffId || responsibleStaffId,
              transportMethod: transitionData.transportMethod,
              transportNotes: transitionData.transportNotes,
              processedAt: new Date(),
              processedBy: responsibleStaffId
            }
          });
          break;

        case 'REFERRAL_IN_TO_REFERRAL_OUT':
          // Create referral out record
          await tx.referralOut.create({
            data: {
              bodyTagId,
              destinationFacilityId: transitionData.destinationFacilityId,
              transferReason: transitionData.transferReason,
              transferReasonOther: transitionData.transferReasonOther,
              expectedArrivalDate: transitionData.expectedArrivalDate,
              transportArrangements: transitionData.transportArrangements,
              urgencyLevel: transitionData.urgencyLevel,
              initiatedAt: new Date(),
              initiatedBy: responsibleStaffId
            }
          });
          break;

        case 'REFERRAL_OUT_TO_PENDING_RELEASE':
          // Create pending release record
          await tx.pendingRelease.create({
            data: {
              bodyTagId,
              releaseAuthorizationNumber: transitionData.releaseAuthorizationNumber,
              releaseDate: transitionData.releaseDate,
              releaseToPersonName: transitionData.releaseToPersonName,
              releaseToPersonRelation: transitionData.releaseToPersonRelation,
              releaseToPersonContact: transitionData.releaseToPersonContact,
              releaseConditions: transitionData.releaseConditions,
              authorizedAt: new Date(),
              authorizedBy: responsibleStaffId
            }
          });
          break;

        case 'AUTO_OVERDUE_DETECTION':
          // Create overdue record
          await tx.overdueRecord.create({
            data: {
              bodyTagId,
              overdueReason: transitionData.overdueReason,
              overdueReasonOther: transitionData.overdueReasonOther,
              escalationLevel: transitionData.escalationLevel,
              escalationNotes: transitionData.escalationNotes,
              priorityLevel: transitionData.priorityLevel,
              expectedResolutionDate: transitionData.expectedResolutionDate,
              actionRequired: transitionData.actionRequired,
              detectedAt: new Date(),
              detectedBy: responsibleStaffId
            }
          });
          break;
      }

      return updatedBodyTag;
    });

    return NextResponse.json({
      success: true,
      message: `Successfully transitioned from ${fromStatus} to ${toStatus}`,
      bodyTag: result,
      transitionType
    });

  } catch (error) {
    console.error('Error processing status transition:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to process status transition' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/body-tags/status-transition
 * Get status transition history for a body tag
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const bodyTagId = searchParams.get('bodyTagId');

    if (!bodyTagId) {
      return NextResponse.json(
        { error: 'Body tag ID is required' },
        { status: 400 }
      );
    }

    const transitions = await prisma.statusTransitionAudit.findMany({
      where: { bodyTagId },
      orderBy: { timestamp: 'desc' },
      include: {
        performedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true
          }
        }
      }
    });

    return NextResponse.json(transitions);

  } catch (error) {
    console.error('Error fetching status transition history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch status transition history' },
      { status: 500 }
    );
  }
}
