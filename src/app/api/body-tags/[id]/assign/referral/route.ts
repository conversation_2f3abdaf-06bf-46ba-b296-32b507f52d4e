// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { BodyStatus, TagStatus, ReferralType, ReferralStatus } from '@prisma/client';

const assignReferralSchema = z.object({
  // User and Assignment Details
  userId: z.string().min(1, "User ID is required"),
  assignedToId: z.string().min(1, "Assigned user ID is required"),
  
  // Employee Details
  employeePersal: z.string().min(1, "Employee PERSAL number is required"),
  employeeName: z.string().min(1, "Employee name is required"),
  institutionName: z.string().min(1, "Institution name is required"),
  vehicleReg: z.string().min(1, "Vehicle registration is required"),
  
  // Referral Details
  referralType: z.nativeEnum(ReferralType),
  returnDate: z.string().optional(),
  
  // Specimen Details (optional)
  specimenKitNumber: z.string().optional(),
  evidenceBagSerial: z.string().optional(),
  
  // Photos (optional)
  bodyTagPhoto: z.string().url("Body tag photo must be a valid URL").optional(),
  vehiclePhoto: z.string().url("Vehicle photo must be a valid URL").optional(),
  
  // GPS Location (optional)
  gpsLatitude: z.number().optional(),
  gpsLongitude: z.number().optional(),
  
  // Additional Details
  notes: z.string().optional(),
});

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = assignReferralSchema.parse(body);

    // Start a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Check if tag exists and has a body
      const tag = await tx.bodyTag.findUnique({
        where: { 
          id: params.id,
          isActive: true,
          bodyId: { not: null }
        },
        include: {
          body: true
        }
      });

      if (!tag || !tag.body) {
        throw new Error('Tag not found or not eligible for referral');
      }

      // Create the referral record
      const referral = await tx.bodyReferral.create({
        data: {
          bodyId: tag.body.id,
          referralType: validatedData.referralType,
          status: ReferralStatus.PENDING,
          referralDate: new Date(),
          returnDate: validatedData.returnDate ? new Date(validatedData.returnDate) : null,
          
          // Employee Details
          employeePersal: validatedData.employeePersal,
          employeeName: validatedData.employeeName,
          institutionName: validatedData.institutionName,
          vehicleReg: validatedData.vehicleReg,
          
          // Specimen Details
          specimenKitNumber: validatedData.specimenKitNumber,
          evidenceBagSerial: validatedData.evidenceBagSerial,
          
          // Photos
          bodyTagPhoto: validatedData.bodyTagPhoto,
          vehiclePhoto: validatedData.vehiclePhoto,
          
          // GPS Location
          gpsLatitude: validatedData.gpsLatitude,
          gpsLongitude: validatedData.gpsLongitude,
          
          // References
          referredById: validatedData.userId,
          assignedToId: validatedData.assignedToId,
          
          // Additional Details
          notes: validatedData.notes,
        },
      });

      // Update the body status
      await tx.body.update({
        where: { id: tag.body.id },
        data: {
          status: BodyStatus.REFERRED,
        },
      });

      // Update the tag status
      const updatedTag = await tx.bodyTag.update({
        where: { id: tag.id },
        data: {
          status: TagStatus.IN_TRANSIT,
          lastScannedAt: new Date(),
          lastScannedBy: validatedData.userId,
        },
        include: {
          body: {
            include: {
              referrals: true
            }
          }
        },
      });

      return { 
        tag: updatedTag, 
        body: updatedTag.body,
        referral: updatedTag.body.referrals[0] 
      };
    });

    return NextResponse.json({
      success: true,
      message: 'Referral assigned successfully',
      data: result,
    });

  } catch (error) {
    console.error('Error assigning referral:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to assign referral',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 