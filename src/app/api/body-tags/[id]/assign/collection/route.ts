// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { CollectionType, BodyStatus, TagStatus } from '@prisma/client';

const assignCollectionSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  name: z.string().optional(),
  institution: z.string().min(1, "Institution is required"),
  vehicleReg: z.string().min(1, "Vehicle registration is required"),
  arrivalTime: z.string().min(1, "Arrival time is required"),
  gpsCoords: z.string().regex(/^-?\d+(\.\d+)?,\s*-?\d+(\.\d+)?$/, 'Invalid GPS coordinates'),
  collectionType: z.nativeEnum(CollectionType),
  collectionPoint: z.string().min(1, "Collection point is required"),
  bodyDescription: z.string().min(1, "Body description is required"),
  weatherConditions: z.string().optional(),
  temperature: z.number().optional(),
  identificationMarks: z.string().optional(),
  personalEffects: z.string().optional(),
  witnessStatements: z.string().optional(),
  collectionNotes: z.string().optional(),
  photos: z.array(z.string()).default([]),
});

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = assignCollectionSchema.parse(body);

    // Start a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Check if tag exists and is available
      const tag = await tx.bodyTag.findUnique({
        where: { 
          id: params.id,
          isActive: true,
          bodyId: null,
          collectionId: null
        },
      });

      if (!tag) {
        throw new Error('Tag not found or already assigned');
      }

      // Create a new body record
      const newBody = await tx.body.create({
        data: {
          trackingNumber: `B${Date.now()}`, // Generate a unique tracking number
          bodyTagId: tag.id,
          status: BodyStatus.COLLECTED,
          deathRegistration: "",
        },
      });

      // Create the collection record
      const collection = await tx.bodyCollection.create({
        data: {
          bodyId: newBody.id,
          userId: validatedData.userId,
          name: validatedData.name,
          institution: validatedData.institution,
          vehicleReg: validatedData.vehicleReg,
          arrivalTime: new Date(validatedData.arrivalTime),
          gpsCoords: validatedData.gpsCoords,
          collectionType: validatedData.collectionType,
          bodyDescription: validatedData.bodyDescription,
          photos: validatedData.photos,
          status: BodyStatus.COLLECTED,
          barcodeValue: tag.qrCodeValue || tag.tagNumber,
        },
      });

      // Update the tag with the new assignments
      const updatedTag = await tx.bodyTag.update({
        where: { id: tag.id },
        data: {
          bodyId: newBody.id,
          collectionId: collection.id,
          status: TagStatus.COLLECTED,
          lastScannedAt: new Date(),
          lastScannedBy: validatedData.userId,
        },
        include: {
          body: {
            include: {
              collection: true
            }
          }
        },
      });

      return { 
        tag: updatedTag, 
        body: updatedTag.body,
        collection: updatedTag.body.collection 
      };
    });

    return NextResponse.json({
      success: true,
      message: 'Collection assigned successfully',
      data: result,
    });

  } catch (error) {
    console.error('Error assigning collection:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to assign collection',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 