// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { BodyStatus, TagStatus, AdmissionType } from '@prisma/client';

const assignAdmissionSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  facilityId: z.string().min(1, "Facility ID is required"),
  admissionType: z.nativeEnum(AdmissionType),
  admittedBy: z.string().min(1, "Admitting officer is required"),
  storageLocation: z.string().min(1, "Storage location is required"),
  storageTemp: z.number().optional(),
  photos: z.array(z.string()).default([]),
  personalEffects: z.string().optional(),
  identificationMarks: z.string().optional(),
  notes: z.string().optional(),
});

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = assignAdmissionSchema.parse(body);

    // Start a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Check if tag exists and has a body but no admission
      const tag = await tx.bodyTag.findUnique({
        where: { 
          id: params.id,
          isActive: true,
          bodyId: { not: null },
          admissionId: null
        },
        include: {
          body: true
        }
      });

      if (!tag || !tag.body) {
        throw new Error('Tag not found or not eligible for admission');
      }

      // Create the admission record
      const admission = await tx.bodyAdmission.create({
        data: {
          bodyId: tag.body.id,
          bodyTagId: tag.id,
          facilityId: validatedData.facilityId,
          admissionType: validatedData.admissionType,
          admissionDate: new Date(),
          status: "ACTIVE",
          notes: validatedData.notes,
          photo: validatedData.photos[0],
          assignedFridge: validatedData.storageLocation,
          temperature: validatedData.storageTemp,
          barcodeValue: tag.qrCodeValue || tag.tagNumber,
          createdById: validatedData.userId,
          assignedToId: validatedData.userId,
        },
      });

      // Update the body status
      await tx.body.update({
        where: { id: tag.body.id },
        data: {
          status: BodyStatus.ADMITTED,
        },
      });

      // Update the tag with the admission assignment
      const updatedTag = await tx.bodyTag.update({
        where: { id: tag.id },
        data: {
          admissionId: admission.id,
          status: TagStatus.ADMITTED,
          lastScannedAt: new Date(),
          lastScannedBy: validatedData.userId,
        },
        include: {
          body: true,
          admissions: true,
        },
      });

      return { 
        tag: updatedTag, 
        body: updatedTag.body,
        admission: updatedTag.admissions[0] 
      };
    });

    return NextResponse.json({
      success: true,
      message: 'Admission assigned successfully',
      data: result,
    });

  } catch (error) {
    console.error('Error assigning admission:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to assign admission',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 