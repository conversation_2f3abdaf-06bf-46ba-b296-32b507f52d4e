// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { BodyStatus, TagStatus, ReleaseStatus } from '@prisma/client';

const assignReleaseSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  facilityId: z.string().min(1, "Facility ID is required"),
  releaseDate: z.string().min(1, "Release date is required"),
  releasedTo: z.string().min(1, "Released to person/institution is required"),
  relationship: z.string().min(1, "Relationship to deceased is required"),
  identificationDocument: z.string().min(1, "Identification document is required"),
  contactDetails: z.string().min(1, "Contact details are required"),
  notes: z.string().optional(),
  attachments: z.array(z.string()).default([]),
});

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = assignReleaseSchema.parse(body);

    // Start a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Check if tag exists and has a body
      const tag = await tx.bodyTag.findUnique({
        where: { 
          id: params.id,
          isActive: true,
          bodyId: { not: null }
        },
        include: {
          body: true
        }
      });

      if (!tag || !tag.body) {
        throw new Error('Tag not found or not eligible for release');
      }

      // Create the release record
      const release = await tx.bodyRelease.create({
        data: {
          bodyId: tag.body.id,
          facilityId: validatedData.facilityId,
          releaseDate: new Date(validatedData.releaseDate),
          releasedTo: validatedData.releasedTo,
          relationship: validatedData.relationship,
          identificationDocument: validatedData.identificationDocument,
          contactDetails: validatedData.contactDetails,
          notes: validatedData.notes,
          status: ReleaseStatus.PENDING,
          releasedBy: {
            connect: { id: validatedData.userId }
          }
        },
      });

      // Update the body status
      await tx.body.update({
        where: { id: tag.body.id },
        data: {
          status: BodyStatus.RELEASED,
        },
      });

      // Update the tag status and deactivate it
      const updatedTag = await tx.bodyTag.update({
        where: { id: tag.id },
        data: {
          status: TagStatus.RELEASED,
          isActive: false,
          lastScannedAt: new Date(),
          lastScannedBy: validatedData.userId,
        },
        include: {
          body: {
            include: {
              releases: true
            }
          }
        },
      });

      return { tag: updatedTag, release };
    });

    return NextResponse.json({
      success: true,
      message: 'Release assigned successfully',
      data: result,
    });

  } catch (error) {
    console.error('Error assigning release:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to assign release',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 