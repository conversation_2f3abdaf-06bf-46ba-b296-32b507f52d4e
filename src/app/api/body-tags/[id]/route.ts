// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check if body tag exists
    const bodyTag = await prisma.bodyTag.findUnique({
      where: { id: params.id },
    });

    if (!bodyTag) {
      return NextResponse.json(
        { error: 'Body tag not found' },
        { status: 404 }
      );
    }

    // Check if body tag can be deleted (e.g., not in use)
    if (bodyTag.bodyId) {
      return NextResponse.json(
        { error: 'Cannot delete body tag that is associated with a body' },
        { status: 400 }
      );
    }

    // Delete the body tag
    await prisma.bodyTag.delete({
      where: { id: params.id },
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting body tag:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 

//When Updating a record with a BodyTag field 
//We Should update its Status As well To Avoid Re-use
//as Well As Associating it with new details. This Will be 
//based on the operation_type field found in the request body
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const body = await request.json();
    const { status, bodyId, lastScannedBy, notes, operation_type } = body;

    // Check if body tag exists
    const bodyTag = await prisma.bodyTag.findUnique({
      where: { id: params.id },
    });

    if (!bodyTag) {
      return NextResponse.json(
        { error: 'Body tag not found' },
        { status: 404 }
      );
    }

    // Update the body tag based on operation type
    const updateData: any = {
      lastScannedAt: new Date(),
    };

    // Add conditional fields
    if (status) updateData.status = status;
    if (bodyId) updateData.bodyId = bodyId;
    if (lastScannedBy) updateData.lastScannedBy = lastScannedBy;
    if (notes) updateData.notes = notes;

    // Handle specific operation types
    if (operation_type === 'COLLECT') {
      updateData.status = 'COLLECTED';
    } else if (operation_type === 'ADMIT') {
      updateData.status = 'ADMITTED';
    } else if (operation_type === 'RELEASE') {
      updateData.status = 'RELEASED';
      // Clear body association when released
      updateData.bodyId = null;
    } else if (operation_type === 'TRANSIT') {
      updateData.status = 'IN_TRANSIT';
    }

    // Update the body tag
    const updatedBodyTag = await prisma.bodyTag.update({
      where: { id: params.id },
      data: updateData,
    });

    return NextResponse.json(updatedBodyTag);
  } catch (error) {
    console.error('Error updating body tag:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const bodyTag = await prisma.bodyTag.findUnique({
      where: { id: params.id },
      include: {
        body: true,
        facility: true,
        admissions: true
      }
    });

    if (!bodyTag) {
      return NextResponse.json(
        { error: 'Body tag not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(bodyTag);
  } catch (error) {
    console.error('Error fetching body tag:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
