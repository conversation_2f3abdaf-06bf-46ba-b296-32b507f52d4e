// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for active status update
const activeStatusSchema = z.object({
  isActive: z.boolean(),
});

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = activeStatusSchema.parse(body);

    // Update the tag's active status
    const updatedTag = await prisma.bodyTag.update({
      where: { id: params.id },
      data: {
        isActive: validatedData.isActive,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: `Body tag ${validatedData.isActive ? 'activated' : 'deactivated'} successfully`,
      bodyTag: updatedTag,
    });

  } catch (error) {
    console.error('Error updating body tag active status:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update body tag status' },
      { status: 500 }
    );
  }
} 