// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { Facility, Prisma } from "@prisma/client";

// Custom error types
class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}



// Error logging function
const logError = (error: unknown, context: Record<string, any> = {}) => {
  const errorObject = {
    timestamp: new Date().toISOString(),
    name: error instanceof Error ? error.name : 'UnknownError',
    message: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    context,
  };
  
  // Log to console in development, in production you might want to use a logging service
  console.error('API Error:', JSON.stringify(errorObject, null, 2));
  return errorObject;
};

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Validate input parameters
    if (!params.id) {
      throw new ApiError('Body tag ID is required', 400, 'MISSING_ID');
    }

    // Log request
    console.log(`Fetching body tag details for ID: ${params.id}`);

    const bodyTag = await prisma.bodyTag.findUnique({
      where: {
        id: params.id,
      },
      include: {
        // Include related data
        facility: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            status: true,
            address: true,
            city: true,
            province: true,
            phone: true,
            email: true,
          },
        },
        body: {
          include: {
            collection: true,
            admissions: {
              include: {
                facility: true,
                assignedTo: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true,
                    persalNumber: true,
                  },
                },
              },
            },
            releases: {
              include: {
                facility: true,
                releasedBy: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true,
                    persalNumber: true,
                  },
                },
              },
            },
            referrals: {
              include: {
                referredBy: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true,
                    persalNumber: true,
                  },
                },
                assignedTo: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true,
                    persalNumber: true,
                  },
                },
              },
            },
          },
        },
      },
    }).catch((error) => {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // Handle known Prisma errors
        switch (error.code) {
          case 'P2002':
            throw new ApiError('Duplicate entry found', 409, 'DUPLICATE_ENTRY');
          case 'P2025':
            throw new ApiError('Record not found', 404, 'NOT_FOUND');
          default:
            throw new ApiError('Database error', 500, `PRISMA_${error.code}`);
        }
      }
      throw error;
    });

    if (!bodyTag) {
      const error = new ApiError('Body tag not found', 404, 'TAG_NOT_FOUND');
      logError(error, { id: params.id });
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code,
        },
        { status: error.statusCode }
      );
    }

    // Log successful retrieval
    console.log(`Successfully retrieved body tag details for ID: ${params.id}`);

    // Structure the response data
    const response: BodyTagDetails = {
      tagDetails: {
        id: bodyTag.id,
        tagNumber: bodyTag.tagNumber,
        status: bodyTag.status,
        generatedAt: bodyTag.generatedAt,
        lastScannedAt: bodyTag.lastScannedAt,
        qrCodeValue: bodyTag.qrCodeValue,
        qrCodeFormat: bodyTag.qrCodeFormat,
        qrCodeMetadata: bodyTag.qrCodeMetadata,
        isActive: bodyTag.isActive,
        notes: bodyTag.notes,
      },
      facility: bodyTag.facility,
      bodyDetails: bodyTag.body ? {
        id: bodyTag.body.id,
        trackingNumber: bodyTag.body.trackingNumber,
        status: bodyTag.body.status,
        deathRegistration: bodyTag.body.deathRegistration,
        collection: bodyTag.body.collection,
        admissions: bodyTag.body.admissions,
        releases: bodyTag.body.releases,
        referrals: bodyTag.body.referrals,
      } : null,
    };

    return NextResponse.json(response);
  } catch (error) {
    // Log the error with context
    const errorLog = logError(error, {
      id: params.id,
      endpoint: 'GET /api/body-tags/[id]/details',
    });

    // Handle known API errors
    if (error instanceof ApiError) {
      return NextResponse.json(
        {
          error: error.message,
          code: error.code,
        },
        { status: error.statusCode }
      );
    }

    // Handle unexpected errors
    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        code: 'INTERNAL_SERVER_ERROR',
        requestId: errorLog.timestamp, // Can be used for error tracking
      },
      { status: 500 }
    );
  }
}

export interface BodyTagDetails {
  tagDetails: {
    id: string;
    tagNumber: string;
    status: string;
    generatedAt: Date;
    lastScannedAt: Date | null;
    qrCodeValue?: string;
    qrCodeFormat?: string;
    qrCodeMetadata?: any;
    isActive: boolean;
    notes?: string;
  }
  facility: Partial<Facility>;
  bodyDetails: {
    id: string;
    trackingNumber: string;
    status: string;
    deathRegistration?: string;
    collection?: any;
    admissions: any[];
    releases: any[];
    referrals: any[];
  } | null;
}