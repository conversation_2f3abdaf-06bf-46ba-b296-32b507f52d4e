// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { BodyStatus, TagStatus } from '@prisma/client'
import type { Prisma } from '@prisma/client'

// GET /api/bodies - Get all bodies
export async function GET(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const searchTerm = searchParams.get('search')
    const facilityId = searchParams.get('facilityId')

    // Build where clause based on filters
    const where: Prisma.BodyWhereInput = {
      AND: [
        // Status filter
        status ? { status: status as BodyStatus } : {},
        
        // Date range filter
        dateFrom || dateTo ? {
          createdAt: {
            ...(dateFrom && { gte: new Date(dateFrom) }),
            ...(dateTo && { lte: new Date(dateTo) })
          }
        } : {},

        // Search term filter
        searchTerm ? {
          OR: [
            { bodyTag: { tagNumber: { contains: searchTerm, mode: 'insensitive' } } },
            { trackingNumber: { contains: searchTerm, mode: 'insensitive' } },
            { collection: { 
              OR: [
                { name: { contains: searchTerm, mode: 'insensitive' } },
                { institution: { contains: searchTerm, mode: 'insensitive' } },
                { barcodeValue: { contains: searchTerm, mode: 'insensitive' } }
              ]
            }}
          ]
        } : {},

        // Facility filter
        facilityId ? {
          OR: [
            { admissions: { some: { facilityId } } },
            { collection: { user: { facilityId } } }
          ]
        } : {}
      ]
    };

    const bodies = await prisma.body.findMany({
      where,
      include: {
        bodyTag: {
          select: {
            tagNumber: true,
            status: true,
            lastScannedAt: true,
            lastScannedBy: true,
            facility: {
              select: {
                name: true,
                code: true
              }
            }
          }
        },
        collection: {
          select: {
            id: true,
            name: true,
            institution: true,
            vehicleReg: true,
            arrivalTime: true,
            collectionType: true,
            barcodeValue: true,
            user: {
              select: {
                id: true,
                name: true,
                role: true,
                facility: {
                  select: {
                    name: true,
                    code: true
                  }
                }
              }
            }
          }
        },
        admissions: {
          orderBy: {
            createdAt: 'desc'
          },
          select: {
            id: true,
            createdAt: true,
            facilityId: true,
            facility: {
              select: {
                name: true,
                code: true
              }
            },
          }
        },
        referrals: {
          orderBy: {
            createdAt: 'desc'
          },
          select: {
            id: true,
            status: true,
            referralType: true,
            createdAt: true,
            referredBy: {
              select: {
                id: true,
                name: true,
                role: true
              }
            },
            assignedTo: {
              select: {
                id: true,
                name: true,
                role: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
    return NextResponse.json(bodies)
  } catch (error) {
    console.error('Error fetching bodies:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST /api/bodies - Register a new body
export async function POST(request: Request) {
  try {
    const session = await auth()
    if (!session || !['FIELD_EMPLOYEE', 'MORGUE_STAFF', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      trackingNumber,
      tagNumber,
      deathRegistration,
    } = body

    // Basic validation
    if (!trackingNumber || !tagNumber) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if tracking number or tag number already exists
    const existingBody = await prisma.body.findFirst({
      where: {
        OR: [
          { trackingNumber },
          {
            bodyTag: {
              tagNumber,
            },
          },
        ],
      },
    })

    if (existingBody) {
      return NextResponse.json(
        { error: 'Tracking number or tag number already exists' },
        { status: 400 }
      )
    }

    // Create body tag first
    const bodyTag = await prisma.bodyTag.create({
      data: {
        tagNumber,
        status: TagStatus.GENERATED,
        generatedBy: session.user.id,
      },
    })

    // Create body
    const newBody = await prisma.body.create({
      data: {
        trackingNumber,
        bodyTagId: bodyTag.id,
        deathRegistration,
        status: BodyStatus.COLLECTED,
      },
      include: {
        bodyTag: true,
      },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'CREATE_BODY',
        entityType: 'BODY',
        entityId: newBody.id,
        performedBy: session.user.id,
        changes: body,
      },
    })

    return NextResponse.json(newBody)
  } catch (error) {
    console.error('Error creating body:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT /api/bodies - Update body details
export async function PUT(request: Request) {
  try {
    const session = await auth()
    if (!session || !['MORGUE_STAFF', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, tagNumber, status, ...updateData } = body

    const existingBody = await prisma.body.findUnique({
      where: { id },
      include: {
        bodyTag: true,
      },
    })

    if (!existingBody) {
      return NextResponse.json({ error: 'Body not found' }, { status: 404 })
    }

    // If updating tag number
    if (tagNumber && tagNumber !== existingBody.bodyTag.tagNumber) {
      // Check if tag number already exists
      const existingTag = await prisma.bodyTag.findUnique({
        where: { tagNumber },
      })

      if (existingTag) {
        return NextResponse.json(
          { error: 'Tag number already exists' },
          { status: 400 }
        )
      }

      // Update tag number
      await prisma.bodyTag.update({
        where: { id: existingBody.bodyTag.id },
        data: { 
          tagNumber,
          status: status ? TagStatus[status as keyof typeof TagStatus] : undefined,
        },
      })
    }

    // Update body
    const updatedBody = await prisma.body.update({
      where: { id },
      data: {
        ...updateData,
        status: status ? BodyStatus[status as keyof typeof BodyStatus] : undefined,
      },
      include: {
        bodyTag: true,
      },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_BODY',
        entityType: 'BODY',
        entityId: id,
        performedBy: session.user.id,
        changes: body,
      },
    })

    return NextResponse.json(updatedBody)
  } catch (error) {
    console.error('Error updating body:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 