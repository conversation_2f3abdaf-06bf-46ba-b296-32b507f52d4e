// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { z } from 'zod';
import { BodyStatus, UserRole } from '@prisma/client';

// Schema for body status update
const bodyStatusUpdateSchema = z.object({
  status: z.enum([
    'COLLECTED',
    'ADMITTED',
    'IN_STORAGE',
    'PENDING_RELEASE',
    'SECURITY_VERIFIED',
    'RELEASED',
    'REFERRED'
  ]),
  notes: z.string().optional(),
  reason: z.string().optional(),
});

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const validatedData = bodyStatusUpdateSchema.parse(body);

    // Check if body exists
    const existingBody = await prisma.body.findUnique({
      where: { id: params.id },
      include: {
        bodyTag: true,
      },
    });

    if (!existingBody) {
      return NextResponse.json({ error: 'Body not found' }, { status: 404 });
    }

    // Check authorization for specific status changes
    if (validatedData.status === 'PENDING_RELEASE') {
      // Only pathologists can set a body to PENDING_RELEASE
      if (session.user.role !== UserRole.PATHOLOGIST) {
        return NextResponse.json(
          { error: 'Only pathologists can authorize a body for release' },
          { status: 403 }
        );
      }
    }

    // Update body status
    const updatedBody = await prisma.body.update({
      where: { id: params.id },
      data: {
        status: validatedData.status as BodyStatus,
      },
      include: {
        bodyTag: true,
      },
    });

    // Create an activity log for the status change
    await prisma.activityLog.create({
      data: {
        activityType: 'UPDATE',
        entity: 'BODY',
        entityId: params.id,
        details: JSON.stringify({
          previousStatus: existingBody.status,
          newStatus: validatedData.status,
          notes: validatedData.notes,
          reason: validatedData.reason,
        }),
        userId: session.user.id,
      },
    });

    return NextResponse.json({
      success: true,
      message: `Body status updated to ${validatedData.status}`,
      body: updatedBody,
    });
  } catch (error) {
    console.error('Error updating body status:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
