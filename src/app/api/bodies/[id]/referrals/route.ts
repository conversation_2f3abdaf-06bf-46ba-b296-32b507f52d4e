// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { ReferralType, ReferralStatus } from '@prisma/client';

// GET /api/bodies/[id]/referrals - Get all referrals for a body
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const bodyId = params.id;
    const referrals = await prisma.bodyReferral.findMany({
      where: { bodyId },
      include: {
        referredBy: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        facility: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        documents: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json(referrals);
  } catch (error) {
    console.error('Error fetching referrals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch referrals' },
      { status: 500 }
    );
  }
}

// POST /api/bodies/[id]/referrals - Create a new referral
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await prisma.body.findUnique({
      where: { id: params.id },
      include: { bodyTag: true },
    });

    if (!body) {
      return NextResponse.json({ error: 'Body not found' }, { status: 404 });
    }

    const data = await request.json();

    // Validate required fields
    if (!data.referralType || !Object.values(ReferralType).includes(data.referralType)) {
      return NextResponse.json(
        { error: 'Invalid referral type' },
        { status: 400 }
      );
    }

    // Create referral
    const referral = await prisma.bodyReferral.create({
      data: {
        bodyId: body.id,
        referralType: data.referralType,
        status: ReferralStatus.PENDING,
        priority: data.priority,
        description: data.description,
        referralDate: new Date(),
        expectedReturnDate: data.expectedReturnDate ? new Date(data.expectedReturnDate) : null,
        referredById: session.user.id,
        assignedToId: data.assignedToId,
        facilityId: data.facilityId,
        metadata: data.metadata || {},
      },
    });

    // Update body status
    await prisma.body.update({
      where: { id: body.id },
      data: { status: 'REFERRED' },
    });

    return NextResponse.json(referral);
  } catch (error) {
    console.error('Error creating referral:', error);
    return NextResponse.json(
      { error: 'Failed to create referral' },
      { status: 500 }
    );
  }
}

// PUT /api/bodies/[id]/referrals/[referralId] - Update a referral
export async function PUT(
  request: Request,
  { params }: { params: { id: string; referralId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { id: bodyId, referralId } = params;

    const referral = await prisma.bodyReferral.findUnique({
      where: { id: referralId, bodyId },
    });

    if (!referral) {
      return NextResponse.json({ error: 'Referral not found' }, { status: 404 });
    }

    // Update referral
    const updatedReferral = await prisma.bodyReferral.update({
      where: { id: referralId },
      data: {
        status: data.status,
        priority: data.priority,
        description: data.description,
        expectedReturnDate: data.expectedReturnDate ? new Date(data.expectedReturnDate) : undefined,
        assignedToId: data.assignedToId,
        facilityId: data.facilityId,
        completionNotes: data.completionNotes,
        metadata: data.metadata,
      },
    });

    // If referral is completed, update body status
    if (data.status === ReferralStatus.COMPLETED) {
      await prisma.body.update({
        where: { id: bodyId },
        data: { status: 'ADMITTED' },
      });
    }

    return NextResponse.json(updatedReferral);
  } catch (error) {
    console.error('Error updating referral:', error);
    return NextResponse.json(
      { error: 'Failed to update referral' },
      { status: 500 }
    );
  }
}
