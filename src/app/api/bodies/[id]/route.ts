// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await prisma.body.findUnique({
      where: { id: params.id },
      include: {
        bodyTag: {
          include: {
            facility: {
              select: {
                name: true,
                code: true,
                type: true
              }
            }
          }
        },
        collection: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                role: true,
                department: true
              }
            }
          }
        },
        admissions: {
          orderBy: { createdAt: 'desc' },
          include: {
            facility: {
              select: {
                id: true,
                name: true,
                code: true,
                type: true
              }
            },
            assignedTo: {
              select: {
                id: true,
                name: true,
                role: true,
                department: true
              }
            },
            createdBy: {
              select: {
                id: true,
                name: true,
                role: true
              }
            }
          }
        },
        releases: {
          orderBy: { createdAt: 'desc' },
          include: {
            facility: {
              select: {
                name: true,
                code: true
              }
            },
            releasedBy: {
              select: {
                id: true,
                name: true,
                role: true
              }
            }
          }
        }
      }
    });

    if (!body) {
      return NextResponse.json(
        { error: 'Body record not found' },
        { status: 404 }
      );
    }

    // Get current admission
    const currentAdmission = body.admissions[0];

    // Transform the response
    const response = {
      ...body,
      currentAdmission,
      _metadata: {
        hasActiveReferrals: false, // Since referrals are not in schema
        totalAdmissions: body.admissions.length,
        lastUpdated: new Date().toISOString(),
      },
      activeReferrals: [] // Since referrals are not in schema
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching body details:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session || !['MORGUE_STAFF', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { status, ...updateData } = body;

    const updatedBody = await prisma.body.update({
      where: { id: params.id },
      data: {
        ...updateData,
        status: status || undefined,
      },
      include: {
        bodyTag: {
          include: {
            facility: true
          }
        },
        collection: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                role: true,
                department: true
              }
            }
          }
        }
      }
    });

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_BODY',
        entityType: 'BODY',
        entityId: params.id,
        performedBy: session.user.id,
        changes: body,
      },
    });

    return NextResponse.json(updatedBody);
  } catch (error) {
    console.error('Error updating body:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
} 