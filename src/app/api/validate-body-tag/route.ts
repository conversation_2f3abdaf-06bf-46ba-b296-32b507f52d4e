// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { BodyTagValidator } from '@/lib/body-tag-validator';
import { BodyTagScanType } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tagId, scanType } = body;

    // Validate required fields
    if (!tagId || !scanType) {
      return NextResponse.json(
        { error: 'Missing required fields: tagId and scanType are required' },
        { status: 400 }
      );
    }

    // Validate scan type
    if (!Object.values(BodyTagScanType).includes(scanType)) {
      return NextResponse.json(
        { error: 'Invalid scan type' },
        { status: 400 }
      );
    }

    // Create validator instance and validate
    const validator = new BodyTagValidator(tagId, scanType);
    const result = await validator.validate();

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error validating body tag:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
