// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get("id")
    const name = searchParams.get("name")
    const code = searchParams.get("code")
    const type = searchParams.get("type")
    const status = searchParams.get("status")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Build filter object
    const where: any = {}
    if (id) where.id = id
    if (name) where.name = { contains: name, mode: "insensitive" }
    if (code) where.code = code
    if (type) where.type = type
    if (status) where.status = status

    // Get total count for pagination
    const total = await prisma.facility.count({ where })

    // Get facilities with pagination
    const facilities = await prisma.facility.findMany({
      where,
      include: {
        fridges: true,
        departments: true,
        _count: {
          select: {
            users: true,
            admissions: true,
            releases: true,
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        name: "asc",
      },
    })

    return NextResponse.json({
      data: facilities,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching facilities:", error)
    return NextResponse.json({ error: "Failed to fetch facilities" }, { status: 500 })
  }
}

