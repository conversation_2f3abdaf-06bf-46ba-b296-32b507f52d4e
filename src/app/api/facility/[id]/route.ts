// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id

    const facility = await prisma.facility.findUnique({
      where: { id },
      include: {
        fridges: true,
        departments: true,
        users: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            persalNumber: true,
            position: true,
          },
        },
        _count: {
          select: {
            admissions: true,
            releases: true,
            bodyTags: true,
          },
        },
      },
    })

    if (!facility) {
      return NextResponse.json({ error: "Facility not found" }, { status: 404 })
    }

    return NextResponse.json(facility)
  } catch (error) {
    console.error("Error fetching facility:", error)
    return NextResponse.json({ error: "Failed to fetch facility" }, { status: 500 })
  }
}

