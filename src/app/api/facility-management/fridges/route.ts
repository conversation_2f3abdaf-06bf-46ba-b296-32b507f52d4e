// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { getUserFacilityId } from '@/lib/auth';

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const facilityId = searchParams.get('facilityId');

    // If facilityId provided, use that, otherwise get user's facility
    let targetFacilityId = facilityId;
    if (!targetFacilityId) {
      targetFacilityId = await getUserFacilityId(session.user.id);
      if (!targetFacilityId) {
        return NextResponse.json(
          { error: 'User not associated with a facility' },
          { status: 400 }
        );
      }
    }

    const fridges = await prisma.fridge.findMany({
      where: {
        facilityId: targetFacilityId,
      },
      orderBy: {
        fridgeNumber: 'asc',
      },
      select: {
        id: true,
        fridgeNumber: true,
        barcode: true,
        temperature: true,
        status: true,
        capacity: true,
        currentOccupancy: true,
      },
    });

    return NextResponse.json(fridges);
  } catch (error) {
    console.error('Error fetching fridges:', error);
    return NextResponse.json(
      { error: 'Failed to fetch fridges' },
      { status: 500 }
    );
  }
}