// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { ReferralType } from '@prisma/client';

// Mock facility ID for test environments
const MOCK_FACILITY_ID = "mock-facility-123";

// GET /api/dashboard/referral-distribution
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's ID from the session
    const userId = session.user.id;
    if (!userId) {
      return NextResponse.json({ error: 'User ID not found in session' }, { status: 400 });
    }

    // Get the user's current facility from the database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { facilityId: true }
    });

    if (!user || !user.facilityId) {
      return NextResponse.json({ error: 'User has no assigned facility' }, { status: 400 });
    }

    const facilityId = user.facilityId;

    // Get bodies in this facility
    const bodiesInFacility = await prisma.bodyAdmission.findMany({
      where: {
        facilityId: facilityId,
        status: 'ACTIVE'
      },
      select: {
        bodyId: true
      }
    });

    const bodyIds = bodiesInFacility.map(b => b.bodyId);

    // Get referrals for these bodies, including the referring user
    const referrals = await prisma.bodyReferral.findMany({
      where: {
        bodyId: {
          in: bodyIds
        }
      },
      include: {
        referredBy: {
          include: {
            facility: true
          }
        }
      }
    });

    const totalReferrals = referrals.length;

    // If no referrals, return empty data
    if (totalReferrals === 0) {
      return NextResponse.json({
        data: {
          sources: [],
          counts: [],
          colors: []
        },
        totalReferrals: 0
      });
    }

    // Group referrals by facility
    const referralsByFacility: Record<string, number> = {};
    
    // Get referring facility names from the referredBy user's facility
    for (const referral of referrals) {
      const facilityName = referral.referredBy?.facility?.name || 'Unknown Facility';
      referralsByFacility[facilityName] = (referralsByFacility[facilityName] || 0) + 1;
    }

    // Sort facilities by count in descending order and take top 5
    const sortedFacilities = Object.entries(referralsByFacility)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);

    // Generate colors for each facility
    const facilityColors = [
      '#3498db', // Blue
      '#9b59b6', // Purple
      '#2ecc71', // Green
      '#e74c3c', // Red
      '#f39c12'  // Orange
    ];

    // Format the response
    const sources = sortedFacilities.map(([name]) => name);
    const counts = sortedFacilities.map(([_, count]) => count);
    const colors = facilityColors.slice(0, sources.length);

    return NextResponse.json({
      data: {
        sources,
        counts,
        colors
      },
      totalReferrals
    });
  } catch (error) {
    console.error('Error fetching referral distribution:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

function getReferralTypeColor(type: ReferralType): string {
  switch (type) {
    case 'LODOX':
      return 'var(--joy-palette-primary-500)';
    case 'XRAY':
      return 'var(--joy-palette-success-500)';
    case 'SPECIMEN':
      return 'var(--joy-palette-warning-500)';
    case 'OTHER':
      return 'var(--joy-palette-neutral-500)';
    default:
      return 'var(--joy-palette-neutral-300)';
  }
} 