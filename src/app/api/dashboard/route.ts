// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { auth } from '@/auth'

// Mock facility ID for test environments
const MOCK_FACILITY_ID = "mock-facility-123"

// GET /api/dashboard/overview
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user's current assigned facility from the session or use mock ID
    const user = session.user
    
    // Return session info for debugging
    return NextResponse.json({
      message: 'API is functioning correctly',
      session: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          image: user.image,
          role: user.role,
          facilityId: user.facilityId || MOCK_FACILITY_ID,
        }
      },
      hasRealFacilityId: !!user.facilityId,
      mockFacilityId: MOCK_FACILITY_ID
    })
  } catch (error) {
    console.error('Error in dashboard API:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 