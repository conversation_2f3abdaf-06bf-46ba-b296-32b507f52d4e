// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { BodyStatus } from '@prisma/client';

export async function GET() {
  try {
    // Get total bodies
    const totalBodies = await prisma.body.count();

    // Get today's collections
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayCollections = await prisma.bodyCollection.count({
      where: {
        createdAt: {
          gte: today
        }
      }
    });

    // Get pending releases
    const pendingReleases = await prisma.bodyRelease.count({
      where: {
        status: 'PENDING'
      }
    });

    // Get active referrals
    const activeReferrals = await prisma.bodyReferral.count({
      where: {
        status: {
          in: ['PENDING', 'IN_PROGRESS']
        }
      }
    });

    // Calculate bodies percentage change (comparing to last month)
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    const lastMonthBodies = await prisma.body.count({
      where: {
        createdAt: {
          lt: today,
          gte: lastMonth
        }
      }
    });

    const thisMonth = await prisma.body.count({
      where: {
        createdAt: {
          gte: today
        }
      }
    });

    const bodiesPercentageChange = lastMonthBodies === 0 
      ? 100 
      : Math.round(((thisMonth - lastMonthBodies) / lastMonthBodies) * 100);

    // Get total counts by status
    const totalCollected = await prisma.body.count({
      where: { status: BodyStatus.COLLECTED }
    });

    const totalInStorage = await prisma.body.count({
      where: { status: BodyStatus.IN_STORAGE }
    });

    const totalReferred = await prisma.body.count({
      where: { status: BodyStatus.REFERRED }
    });

    const totalReleased = await prisma.body.count({
      where: { status: BodyStatus.RELEASED }
    });

    // Get monthly admissions for the past 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);
    
    const monthlyAdmissions = await Promise.all(
      Array.from({ length: 6 }).map(async (_, index) => {
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - (5 - index));
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + 1);

        return prisma.bodyAdmission.count({
          where: {
            createdAt: {
              gte: startDate,
              lt: endDate
            }
          }
        });
      })
    );

    return NextResponse.json({
      totalBodies,
      todayCollections,
      pendingReleases,
      activeReferrals,
      bodiesPercentageChange,
      totalCollected,
      totalInStorage,
      totalReferred,
      totalReleased,
      monthlyAdmissions
    });
  } catch (error) {
    console.error('Error fetching pathology metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pathology metrics' },
      { status: 500 }
    );
  }
} 