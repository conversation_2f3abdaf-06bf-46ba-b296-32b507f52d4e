// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { AdmissionType, ReferralType } from '@prisma/client'
import { subDays } from 'date-fns'

// Mock facility ID for test environments
const MOCK_FACILITY_ID = "mock-facility-123"

// GET /api/dashboard/time-distribution
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user's ID from the session
    const userId = session.user.id
    if (!userId) {
      return NextResponse.json({ error: 'User ID not found in session' }, { status: 400 })
    }

    // Get the user's current facility from the database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { facilityId: true }
    })

    if (!user || !user.facilityId) {
      return NextResponse.json({ error: 'User has no assigned facility' }, { status: 400 })
    }

    const facilityId = user.facilityId

    // Set time range - last 30 days
    const thirtyDaysAgo = subDays(new Date(), 30)

    // Get bodies in this facility
    const bodiesInFacility = await prisma.bodyAdmission.findMany({
      where: {
        facilityId: facilityId,
        status: 'ACTIVE'
      },
      select: {
        bodyId: true
      }
    })

    const bodyIds = bodiesInFacility.map(b => b.bodyId)

    // Get activities related to these bodies
    const activityCounts = {
      'Specimen Collection': await prisma.bodyCollection.count({
        where: {
          bodyId: {
            in: bodyIds
          },
          createdAt: {
            gte: thirtyDaysAgo
          }
        }
      }),
      'Initial Examination': await prisma.bodyAdmission.count({
        where: {
          facilityId: facilityId,
          admissionType: AdmissionType.INITIAL,
          createdAt: {
            gte: thirtyDaysAgo
          }
        }
      }),
      'Lab Analysis': await prisma.bodyReferral.count({
        where: {
          bodyId: {
            in: bodyIds
          },
          referralType: {
            in: [ReferralType.SPECIMEN, ReferralType.LODOX, ReferralType.XRAY]
          },
          createdAt: {
            gte: thirtyDaysAgo
          }
        }
      }),
      'Report Preparation': await prisma.activityLog.count({
        where: {
          bodyAdmissionId: {
            in: await prisma.bodyAdmission.findMany({
              where: {
                facilityId: facilityId
              },
              select: {
                id: true
              }
            }).then(admissions => admissions.map(a => a.id))
          },
          activityType: {
            contains: 'REPORT'
          },
          timestamp: {
            gte: thirtyDaysAgo
          }
        }
      }),
      'Verification & Review': await prisma.activityLog.count({
        where: {
          bodyAdmissionId: {
            in: await prisma.bodyAdmission.findMany({
              where: {
                facilityId: facilityId
              },
              select: {
                id: true
              }
            }).then(admissions => admissions.map(a => a.id))
          },
          activityType: {
            contains: 'REVIEW'
          },
          timestamp: {
            gte: thirtyDaysAgo
          }
        }
      })
    }

    // Convert to hours spent (approximate)
    const timeMultipliers = {
      'Specimen Collection': 2, // 2 hours per collection
      'Initial Examination': 3, // 3 hours per examination
      'Lab Analysis': 4, // 4 hours per lab analysis
      'Report Preparation': 1.5, // 1.5 hours per report
      'Verification & Review': 1 // 1 hour per review
    }

    const timeDistribution = Object.entries(activityCounts).map(([activity, count]) => ({
      activity,
      hours: count * (timeMultipliers as any)[activity]
    }))

    // Calculate total time
    const totalTime = timeDistribution.reduce((sum, item) => sum + item.hours, 0)

    // Format data for the component
    const labels = timeDistribution.map(item => item.activity)
    const values = timeDistribution.map(item => item.hours)
    const colors = [
      'var(--joy-palette-primary-400)',
      'var(--joy-palette-success-400)',
      'var(--joy-palette-warning-400)',
      'var(--joy-palette-info-400)',
      'var(--joy-palette-neutral-400)'
    ]

    return NextResponse.json({
      data: {
        labels,
        values,
        colors
      },
      totalTime: Math.round(totalTime)
    })
  } catch (error) {
    console.error('Error fetching time distribution:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 