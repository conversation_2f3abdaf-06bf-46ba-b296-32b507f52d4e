// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { FacilityStatus } from '@prisma/client';

export async function GET() {
  try {
    // Get all active facilities with their occupancy data
    const facilities = await prisma.facility.findMany({
      where: {
        status: FacilityStatus.ACTIVE
      },
      select: {
        id: true,
        name: true,
        currentOccupancy: true,
        totalCapacity: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json({
      facilities
    });
  } catch (error) {
    console.error('Error fetching location metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch location metrics' },
      { status: 500 }
    );
  }
} 