// Force dynamic rendering
export const dynamic = "force-dynamic";

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { format, subDays, subWeeks, subMonths } from 'date-fns';

// GET /api/dashboard/trends?period=daily|weekly|monthly|yearly
export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || 'weekly';

    // Generate mock data based on period
    const { labels, currentPeriodData, previousPeriodData } = getMockDataForPeriod(period);

    // Generate full response
    return NextResponse.json({
      labels,
      datasets: [
        {
          label: 'Current Period',
          data: currentPeriodData,
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Previous Period',
          data: previousPeriodData,
          borderColor: 'rgba(75, 192, 192, 0.3)',
          backgroundColor: 'transparent',
          tension: 0.4,
          borderDash: [5, 5]
        }
      ]
    });
  } catch (error) {
    console.error('Error fetching admission trends:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

function getMockDataForPeriod(period: string) {
  const now = new Date();
  const labels: string[] = [];
  let currentPeriodData: number[] = [];
  let previousPeriodData: number[] = [];

  switch (period) {
    case 'daily':
      // Last 7 days
      for (let i = 6; i >= 0; i--) {
        const date = subDays(now, i);
        labels.push(format(date, 'EEE'));
      }
      currentPeriodData = [5, 7, 10, 12, 9, 11, 14];
      previousPeriodData = [4, 6, 8, 10, 8, 9, 11];
      break;

    case 'weekly':
      // Last 12 weeks
      for (let i = 11; i >= 0; i--) {
        const date = subWeeks(now, i);
        labels.push(`Week ${format(date, 'w')}`);
      }
      currentPeriodData = [45, 52, 49, 60, 55, 58, 56, 62, 65, 59, 70, 78];
      previousPeriodData = [40, 45, 42, 50, 48, 52, 48, 55, 58, 52, 60, 65];
      break;

    case 'monthly':
      // Last 12 months
      for (let i = 11; i >= 0; i--) {
        const date = subMonths(now, i);
        labels.push(format(date, 'MMM'));
      }
      currentPeriodData = [180, 200, 210, 190, 220, 250, 240, 260, 280, 270, 290, 300];
      previousPeriodData = [160, 170, 180, 165, 185, 205, 200, 220, 245, 235, 250, 260];
      break;

    default:
      // Default to weekly
      for (let i = 11; i >= 0; i--) {
        const date = subWeeks(now, i);
        labels.push(`Week ${format(date, 'w')}`);
      }
      currentPeriodData = [45, 52, 49, 60, 55, 58, 56, 62, 65, 59, 70, 78];
      previousPeriodData = [40, 45, 42, 50, 48, 52, 48, 55, 58, 52, 60, 65];
      break;
  }

  return { labels, currentPeriodData, previousPeriodData };
}