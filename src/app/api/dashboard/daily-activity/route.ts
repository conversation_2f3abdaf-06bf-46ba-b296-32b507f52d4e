// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { format, subDays, startOfDay, endOfDay } from 'date-fns'

// Mock facility ID for test environments
const MOCK_FACILITY_ID = "mock-facility-123"

// GET /api/dashboard/daily-activity
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user's ID from the session
    const userId = session.user.id
    if (!userId) {
      return NextResponse.json({ error: 'User ID not found in session' }, { status: 400 })
    }

    // Get the user's current facility from the database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { facilityId: true }
    })

    if (!user || !user.facilityId) {
      return NextResponse.json({ error: 'User has no assigned facility' }, { status: 400 })
    }

    const facilityId = user.facilityId

    const now = new Date()
    const labels: string[] = []
    const current: number[] = []
    const previous: number[] = []

    // Get data for the last 7 days
    for (let i = 6; i >= 0; i--) {
      const date = subDays(now, i)
      const dayStart = startOfDay(date)
      const dayEnd = endOfDay(date)
      
      labels.push(format(date, 'EEE')) // Day name (Mon, Tue, etc.)
      
      // Get current period data (last 7 days)
      const currentDayActivities = await prisma.activityLog.count({
        where: {
          userId: {
            in: await getUsersInFacility(facilityId)
          },
          timestamp: {
            gte: dayStart,
            lte: dayEnd
          }
        }
      })
      current.push(currentDayActivities)
      
      // Get previous period data (7 days before the current period)
      const previousDayStart = startOfDay(subDays(date, 7))
      const previousDayEnd = endOfDay(subDays(date, 7))
      
      const previousDayActivities = await prisma.activityLog.count({
        where: {
          userId: {
            in: await getUsersInFacility(facilityId)
          },
          timestamp: {
            gte: previousDayStart,
            lte: previousDayEnd
          }
        }
      })
      previous.push(previousDayActivities)
    }
    
    const startDate = subDays(now, 6)
    const endDate = now
    const dateRange = `${format(startDate, 'MMM d')} - ${format(endDate, 'MMM d, yyyy')}`

    return NextResponse.json({
      labels,
      current,
      previous,
      dateRange
    })
  } catch (error) {
    console.error('Error fetching daily activity:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// Helper function to get all user IDs in a facility
async function getUsersInFacility(facilityId: string): Promise<string[]> {
  const users = await prisma.user.findMany({
    where: {
      facilityId: facilityId
    },
    select: {
      id: true
    }
  })
  
  return users.map(user => user.id)
} 