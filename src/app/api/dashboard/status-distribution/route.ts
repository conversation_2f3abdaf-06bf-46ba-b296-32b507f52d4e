// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { BodyStatus } from '@prisma/client';

// Mock facility ID for test environments
const MOCK_FACILITY_ID = "mock-facility-123";

// GET /api/dashboard/status-distribution
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's current assigned facility from the session or use mock ID
    const { user } = session;
    const facilityId = user.facilityId || MOCK_FACILITY_ID;
    
    // Mock data
    const mockStatuses = [
      {
        status: 'ADMITTED',
        count: 24,
        percentage: 48,
        color: 'var(--joy-palette-primary-500)'
      },
      {
        status: 'IN_STORAGE',
        count: 12,
        percentage: 24,
        color: 'var(--joy-palette-success-500)'
      },
      {
        status: 'REFERRED',
        count: 8,
        percentage: 16,
        color: 'var(--joy-palette-warning-500)'
      },
      {
        status: 'RELEASED',
        count: 6,
        percentage: 12,
        color: 'var(--joy-palette-danger-500)'
      }
    ];

    const totalBodies = mockStatuses.reduce((sum, status) => sum + status.count, 0);

    return NextResponse.json({
      statuses: mockStatuses,
      totalBodies
    });
  } catch (error) {
    console.error('Error fetching status distribution:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

function getStatusColor(status: BodyStatus): string {
  switch (status) {
    case 'COLLECTED':
      return 'var(--joy-palette-primary-500)';
    case 'ADMITTED':
      return 'var(--joy-palette-success-500)';
    case 'IN_STORAGE':
      return 'var(--joy-palette-info-500)';
    case 'PENDING_RELEASE':
      return 'var(--joy-palette-warning-500)';
    case 'SECURITY_VERIFIED':
      return 'var(--joy-palette-success-700)';
    case 'RELEASED':
      return 'var(--joy-palette-neutral-500)';
    case 'REFERRED':
      return 'var(--joy-palette-danger-500)';
    default:
      return 'var(--joy-palette-neutral-300)';
  }
} 