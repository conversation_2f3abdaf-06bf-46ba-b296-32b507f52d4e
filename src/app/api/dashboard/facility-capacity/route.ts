// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { FridgeStatus } from '@prisma/client'

// Mock facility ID for test environments
const MOCK_FACILITY_ID = "mock-facility-123"

// GET /api/dashboard/facility-capacity
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user's ID from the session
    const userId = session.user.id
    if (!userId) {
      return NextResponse.json({ error: 'User ID not found in session' }, { status: 400 })
    }

    // Get the user's current facility from the database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { facilityId: true }
    })

    if (!user || !user.facilityId) {
      return NextResponse.json({ error: 'User has no assigned facility' }, { status: 400 })
    }

    const facilityId = user.facilityId

    // Get facility details
    const facility = await prisma.facility.findUnique({
      where: {
        id: facilityId
      },
      select: {
        currentOccupancy: true,
        totalCapacity: true
      }
    })

    if (!facility) {
      return NextResponse.json({ error: 'Facility not found' }, { status: 404 })
    }

    // Get fridges for this facility
    const fridges = await prisma.fridge.findMany({
      where: {
        facilityId: facilityId
      },
      select: {
        status: true,
        capacity: true,
        currentOccupancy: true
      }
    })

    // Calculate occupancy by type
    const occupancyByType = [
      {
        type: 'Standard Storage',
        count: fridges
          .filter(f => f.status === FridgeStatus.AVAILABLE || f.status === FridgeStatus.OCCUPIED)
          .reduce((sum, f) => sum + f.currentOccupancy, 0),
        color: 'var(--joy-palette-primary-500)'
      },
      {
        type: 'Long-term Storage',
        count: Math.round(facility.currentOccupancy * 0.25), // Estimate long-term as 25% of total
        color: 'var(--joy-palette-success-500)'
      },
      {
        type: 'Special Cases',
        count: fridges
          .filter(f => f.status !== FridgeStatus.MAINTENANCE && f.status !== FridgeStatus.OFFLINE)
          .reduce((sum, f) => Math.round(sum + (f.currentOccupancy * 0.15)), 0), // Estimate special as 15% of occupied
        color: 'var(--joy-palette-warning-500)'
      }
    ]

    return NextResponse.json({
      currentOccupancy: facility.currentOccupancy,
      totalCapacity: facility.totalCapacity,
      occupancyByType
    })
  } catch (error) {
    console.error('Error fetching facility capacity:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 