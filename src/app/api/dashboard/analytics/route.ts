// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { BodyStatus, CollectionType } from '@prisma/client'

// GET /api/dashboard/analytics
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get The Users Current Assigned Facility from the session
    const { user } = session
    const currentFacility = await prisma.facility.findUnique({
      where: {
        id: user.facilityId
      }
    })

    if (!currentFacility) {
      return NextResponse.json({ error: 'Facility not found' }, { status: 404 })
    }
    
    // Get occupancy trends
    const occupancyTrends = await getOccupancyTrends(currentFacility.id)

    // Get processing time trends
    const processingTimeTrends = await getProcessingTimeTrends(currentFacility.id)

    // Get case distribution
    const caseDistribution = await getCaseDistribution(currentFacility.id)

    // Get performance metrics
    const performance = await getPerformanceMetrics(currentFacility.id)

    // Get forecasts
    const forecasts = await generateForecasts(currentFacility.id)

    return NextResponse.json({
      trends: {
        occupancy: occupancyTrends,
        processingTime: processingTimeTrends,
        caseDistribution
      },
      performance,
      forecasts
    })
  } catch (error) {
    console.error('Error fetching analytics dashboard:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

async function getOccupancyTrends(facilityId: string) {
  // Filter by specific facility ID instead of all morgue facilities
  const facility = await prisma.facility.findUnique({
    where: {
      id: facilityId
    },
    select: {
      currentOccupancy: true,
      totalCapacity: true
    }
  })

  if (!facility) {
    return [{
      date: new Date().toISOString().split('T')[0],
      value: 0,
      benchmark: 70
    }]
  }

  const occupancyRate = Math.round((facility.currentOccupancy / facility.totalCapacity) * 100)

  return [{
    date: new Date().toISOString().split('T')[0],
    value: occupancyRate,
    benchmark: 70
  }]
}

async function getProcessingTimeTrends(facilityId: string) {
  const admissions = await prisma.bodyAdmission.findMany({
    where: {
      facilityId: facilityId,
      status: 'ACTIVE'
    },
    select: {
      createdAt: true,
      admissionDate: true
    }
  })

  const averageTime = admissions.reduce((sum, admission) => {
    const processingTime = new Date(admission.admissionDate).getTime() - new Date(admission.createdAt).getTime()
    return sum + processingTime
  }, 0) / (admissions.length || 1)

  return [{
    type: 'ADMISSION',
    averageTime: `${Math.round(averageTime / (1000 * 60 * 60))} hours`,
    trend: '+5%'
  }]
}

async function getCaseDistribution(facilityId: string) {
  // We need to join with body admissions to filter by facility ID
  // since the collection itself doesn't have a facilityId
  const bodiesInFacility = await prisma.bodyAdmission.findMany({
    where: {
      facilityId: facilityId
    },
    select: {
      bodyId: true
    }
  })

  const bodyIds = bodiesInFacility.map(b => b.bodyId)

  const collections = await prisma.bodyCollection.findMany({
    where: {
      bodyId: {
        in: bodyIds
      }
    },
    select: {
      collectionType: true
    }
  })

  const total = collections.length
  const crimeScene = collections.filter(c => c.collectionType === CollectionType.CRIME_SCENE).length
  const hospital = collections.filter(c => c.collectionType === CollectionType.HOSPITAL).length
  const other = collections.filter(c => c.collectionType === CollectionType.OTHER).length

  return {
    crimeScene: Math.round((crimeScene / total) * 100) || 0,
    hospital: Math.round((hospital / total) * 100) || 0,
    other: Math.round((other / total) * 100) || 0
  }
}

async function getPerformanceMetrics(facilityId: string) {
  // Get all bodies admitted to this facility
  const bodiesInFacility = await prisma.bodyAdmission.findMany({
    where: {
      facilityId: facilityId
    },
    select: {
      bodyId: true
    }
  })

  const bodyIds = bodiesInFacility.map(b => b.bodyId)

  const totalCases = bodyIds.length

  const completedCases = await prisma.body.count({
    where: {
      id: {
        in: bodyIds
      },
      status: {
        in: [BodyStatus.RELEASED, BodyStatus.REFERRED]
      }
    }
  })

  return {
    slaCompliance: Math.round((completedCases / totalCases) * 100) || 95,
    averageResponseTime: '30 minutes',
    qualityMetrics: {
      documentationAccuracy: 98,
      procedureCompliance: 97
    }
  }
}

async function generateForecasts(facilityId: string) {
  // Here we would use the facilityId to generate facility-specific forecasts
  // For now, this is a placeholder
  const facility = await prisma.facility.findUnique({
    where: {
      id: facilityId
    },
    select: {
      totalCapacity: true,
      currentOccupancy: true
    }
  })

  const currentOccupancyRate = facility ? 
    Math.round((facility.currentOccupancy / facility.totalCapacity) * 100) : 70

  // Add 10% for forecast (simplified approach)
  const forecastRate = Math.min(100, currentOccupancyRate + 10)

  return {
    expectedOccupancy: [{
      date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      value: forecastRate,
      confidence: 0.85
    }],
    resourceRequirements: {
      staff: Math.ceil(forecastRate / 4), // Simplified staffing calculation
      fridges: Math.ceil(forecastRate * 2) // Simplified fridge calculation
    }
  }
} 