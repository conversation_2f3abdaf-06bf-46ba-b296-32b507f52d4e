// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { ReferralStatus, ReleaseStatus } from '@prisma/client';

// GET /api/dashboard/metrics
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user's current assigned facility from the session
    const { user } = session;
    const facilityId = user.facilityId;

    if (!facilityId) {
      return NextResponse.json({ error: 'User has no assigned facility' }, { status: 400 });
    }

    // Get bodies in this facility
    const bodiesInFacility = await prisma.bodyAdmission.findMany({
      where: {
        facilityId: facilityId,
        status: 'ACTIVE'
      },
      select: {
        bodyId: true
      }
    });

    const bodyIds = bodiesInFacility.map(b => b.bodyId);

    // Get total bodies count
    const totalBodies = bodyIds.length;

    // Get previous period for trend calculation
    const previousPeriodBodies = await prisma.bodyAdmission.count({
      where: {
        facilityId: facilityId,
        createdAt: {
          lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
        },
        status: 'ACTIVE'
      }
    });

    // Calculate trend (percentage change)
    const totalBodiesTrend = previousPeriodBodies > 0
      ? Math.round((totalBodies - previousPeriodBodies) / previousPeriodBodies * 100)
      : 0;

    // Get today's collections
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayCollections = await prisma.bodyCollection.count({
      where: {
        bodyId: {
          in: bodyIds
        },
        createdAt: {
          gte: today
        }
      }
    });

    // Get pending releases
    const pendingReleases = await prisma.bodyRelease.count({
      where: {
        facilityId: facilityId,
        status: ReleaseStatus.PENDING
      }
    });

    const previousPendingReleases = await prisma.bodyRelease.count({
      where: {
        facilityId: facilityId,
        createdAt: {
          lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
        },
        status: ReleaseStatus.PENDING
      }
    });

    const pendingReleasesTrend = previousPendingReleases > 0
      ? Math.round((pendingReleases - previousPendingReleases) / previousPendingReleases * 100)
      : 0;

    // Get active referrals
    const activeReferrals = await prisma.bodyReferral.count({
      where: {
        bodyId: {
          in: bodyIds
        },
        status: {
          in: [ReferralStatus.PENDING, ReferralStatus.IN_PROGRESS]
        }
      }
    });

    const previousActiveReferrals = await prisma.bodyReferral.count({
      where: {
        bodyId: {
          in: bodyIds
        },
        createdAt: {
          lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
        },
        status: {
          in: [ReferralStatus.PENDING, ReferralStatus.IN_PROGRESS]
        }
      }
    });

    const activeReferralsTrend = previousActiveReferrals > 0
      ? Math.round((activeReferrals - previousActiveReferrals) / previousActiveReferrals * 100)
      : 0;

    // Get pending admissions
    const pendingAdmissions = await prisma.bodyAdmission.count({
      where: {
        facilityId: facilityId,
        createdAt: {
          gte: today
        }
      }
    });

    const previousPendingAdmissions = await prisma.bodyAdmission.count({
      where: {
        facilityId: facilityId,
        createdAt: {
          lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
          gte: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000) // 14 days ago
        }
      }
    });

    const pendingAdmissionsTrend = previousPendingAdmissions > 0
      ? Math.round((pendingAdmissions - previousPendingAdmissions) / previousPendingAdmissions * 100)
      : 0;

    return NextResponse.json({
      totalBodies,
      totalBodiesTrend,
      pendingAdmissions,
      pendingAdmissionsTrend,
      activeReferrals,
      activeReferralsTrend,
      pendingReleases,
      pendingReleasesTrend,
      todayCollections
    });
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 