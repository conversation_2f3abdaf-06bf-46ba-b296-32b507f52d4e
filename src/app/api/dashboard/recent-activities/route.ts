// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

// Mock facility ID for test environments
const MOCK_FACILITY_ID = "mock-facility-123"

// GET /api/dashboard/recent-activities
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user's ID from the session
    const userId = session.user.id
    if (!userId) {
      return NextResponse.json({ error: 'User ID not found in session' }, { status: 400 })
    }

    // Get the user's current facility from the database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { facilityId: true }
    })

    if (!user || !user.facilityId) {
      return NextResponse.json({ error: 'User has no assigned facility' }, { status: 400 })
    }

    const facilityId = user.facilityId

    // Get facility users for filtering
    const facilityUsers = await prisma.user.findMany({
      where: {
        facilityId: facilityId
      },
      select: {
        id: true
      }
    })

    const userIds = facilityUsers.map(user => user.id)

    // Get the 10 most recent activity logs for this facility
    const activityLogs = await prisma.activityLog.findMany({
      where: {
        userId: {
          in: userIds
        }
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: 10,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true,
            image: true
          }
        }
      }
    })

    // Format activities for the frontend
    const activities = activityLogs.map(log => {
      // Determine activity type based on the activityType or related records
      let type = 'ADMISSION'
      if (log.activityType.includes('COLLECTION')) type = 'COLLECTION'
      if (log.activityType.includes('REFERRAL')) type = 'REFERRAL'
      if (log.activityType.includes('RELEASE')) type = 'RELEASE'

      // Build the response object
      return {
        id: log.id,
        type,
        description: log.description,
        date: log.timestamp,
        user: {
          name: log.user.name || 'Unknown User',
          avatar: log.user.image,
          role: getRoleName(log.user.role)
        },
        status: log.activityType.includes('COMPLETED') ? 'COMPLETED' : 'IN_PROGRESS'
      }
    })

    // Determine date range (today, this week, this month)
    const now = new Date()
    const oldestActivity = activities.length > 0 
      ? activities[activities.length - 1].date 
      : now
      
    const daysAgo = Math.floor((now.getTime() - new Date(oldestActivity).getTime()) / (1000 * 60 * 60 * 24))
    
    let dateRange = 'Today'
    if (daysAgo > 30) {
      dateRange = 'Past Month'
    } else if (daysAgo > 7) {
      dateRange = 'Past Week'
    } else if (daysAgo > 1) {
      dateRange = `Past ${daysAgo} Days`
    }

    return NextResponse.json({
      activities,
      dateRange
    })
  } catch (error) {
    console.error('Error fetching recent activities:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// Helper function to format role names
function getRoleName(role: string): string {
  const roleMap: Record<string, string> = {
    'ADMIN': 'Administrator',
    'PATHOLOGIST': 'Pathologist',
    'MORGUE_STAFF': 'Morgue Staff',
    'FIELD_EMPLOYEE': 'Field Employee',
    'SECURITY_STAFF': 'Security Staff'
  }
  
  return roleMap[role] || 'Staff Member'
} 