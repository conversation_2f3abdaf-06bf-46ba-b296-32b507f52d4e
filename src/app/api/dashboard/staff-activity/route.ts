// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { UserRole } from '@prisma/client'

// Mock facility ID for test environments
const MOCK_FACILITY_ID = "mock-facility-123"

// GET /api/dashboard/staff-activity
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user's ID from the session
    const userId = session.user.id
    if (!userId) {
      return NextResponse.json({ error: 'User ID not found in session' }, { status: 400 })
    }

    // Get the user's current facility from the database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { facilityId: true }
    })

    if (!user || !user.facilityId) {
      return NextResponse.json({ error: 'User has no assigned facility' }, { status: 400 })
    }

    const facilityId = user.facilityId

    // Get staff members at this facility
    const staffMembers = await prisma.user.findMany({
      where: {
        facilityId: facilityId,
        // Only include active users who are not admins
        status: 'ACTIVE',
        role: {
          not: 'ADMIN'
        }
      },
      select: {
        id: true,
        name: true,
        role: true,
      },
      take: 5 // Limit to 5 staff members
    })

    // For each staff member, get their active cases and completed cases today
    const staffWithActivity = await Promise.all(
      staffMembers.map(async (staff) => {
        // Get current active cases assigned to this staff member
        const currentCases = await prisma.bodyAdmission.count({
          where: {
            facilityId: facilityId,
            status: 'ACTIVE',
            assignedToId: staff.id
          }
        })

        // Get cases completed today by this staff member
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        
        const completedToday = await prisma.activityLog.count({
          where: {
            userId: staff.id,
            activityType: 'CASE_COMPLETED',
            timestamp: {
              gte: today
            }
          }
        })

        return {
          name: staff.name || 'Staff Member',
          role: formatRole(staff.role),
          currentCases,
          completedToday
        }
      })
    )

    return NextResponse.json({
      staff: staffWithActivity
    })
  } catch (error) {
    console.error('Error fetching staff activity:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// Helper function to format user roles
function formatRole(role: UserRole): string {
  switch (role) {
    case UserRole.PATHOLOGIST:
      return 'Pathologist'
    case UserRole.MORGUE_STAFF:
      return 'Morgue Staff'
    case UserRole.SECURITY_STAFF:
      return 'Security'
    case UserRole.FIELD_EMPLOYEE:
      return 'Field Employee'
    case UserRole.ADMIN:
      return 'Administrator'
    default:
      return 'Staff Member' // Fallback to generic role name
  }
} 