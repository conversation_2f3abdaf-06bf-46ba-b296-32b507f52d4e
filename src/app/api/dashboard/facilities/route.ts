// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { FacilityType, FridgeStatus, UserStatus } from '@prisma/client'

// GET /api/dashboard/facilities
export async function GET(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const facilityId = searchParams.get('facilityId')
    const type = searchParams.get('type') as FacilityType | null

    // Build where clause
    const where: any = {}
    if (facilityId) where.id = facilityId
    if (type) where.type = type

    // Get facilities with metrics
    const facilities = await prisma.facility.findMany({
      where,
      include: {
        fridges: true,
        admissions: {
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)) // Today
            }
          }
        },
        releases: {
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)) // Today
            }
          }
        },
        staff: true
      }
    })

    // Process facility data
    const facilitiesData = facilities.map(facility => {
      const availableFridges = facility.fridges.filter(f => f.status === FridgeStatus.AVAILABLE).length
      const maintenanceFridges = facility.fridges.filter(f => f.status === FridgeStatus.MAINTENANCE).length
      const activeStaff = facility.staff.filter(s => s.status === UserStatus.ACTIVE).length

      const alerts = []
      
      // Check capacity
      const occupancyRate = (facility.currentOccupancy / facility.totalCapacity) * 100
      if (occupancyRate >= 90) {
        alerts.push({
          type: 'CAPACITY_WARNING',
          message: 'Facility nearing maximum capacity',
          severity: 'WARNING'
        })
      }

      // Check fridges
      if (maintenanceFridges > 0) {
        alerts.push({
          type: 'MAINTENANCE_REQUIRED',
          message: `${maintenanceFridges} fridge(s) require maintenance`,
          severity: 'INFO'
        })
      }

      return {
        id: facility.id,
        name: facility.name,
        type: facility.type,
        metrics: {
          totalCapacity: facility.totalCapacity,
          currentOccupancy: facility.currentOccupancy,
          occupancyRate: Math.round(occupancyRate),
          availableFridges,
          maintenanceFridges
        },
        activity: {
          admissionsToday: facility.admissions.length,
          releasesToday: facility.releases.length,
          pendingReferrals: 0 // Calculate if needed
        },
        staff: {
          onDuty: activeStaff,
          total: facility.staff.length
        },
        alerts
      }
    })

    // Calculate summary
    const summary = {
      totalFacilities: facilitiesData.length,
      totalCapacity: facilitiesData.reduce((sum, f) => sum + f.metrics.totalCapacity, 0),
      currentOccupancy: facilitiesData.reduce((sum, f) => sum + f.metrics.currentOccupancy, 0),
      averageOccupancyRate: Math.round(
        facilitiesData.reduce((sum, f) => sum + f.metrics.occupancyRate, 0) / facilitiesData.length
      )
    }

    return NextResponse.json({
      facilities: facilitiesData,
      summary
    })
  } catch (error) {
    console.error('Error fetching facilities dashboard:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 