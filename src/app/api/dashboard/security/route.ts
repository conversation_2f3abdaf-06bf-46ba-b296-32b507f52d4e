// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { UserRole, ReleaseStatus, UserStatus } from '@prisma/client';

export async function GET() {
  try {
    // Get active security staff (those who have shifts today)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const activeSecurityStaff = await prisma.user.count({
      where: {
        role: UserRole.SECURITY_STAFF,
        status: UserStatus.ACTIVE,
        shifts: {
          some: {
            startTime: {
              gte: today,
              lt: tomorrow
            }
          }
        }
      }
    });

    // Get verified releases for today
    const verifiedReleases = await prisma.bodyRelease.count({
      where: {
        status: ReleaseStatus.VERIFIED,
        updatedAt: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    // Get pending verifications
    const pendingVerifications = await prisma.bodyRelease.count({
      where: {
        status: ReleaseStatus.PENDING
      }
    });

    return NextResponse.json({
      activeSecurityStaff,
      verifiedReleases,
      pendingVerifications
    });
  } catch (error) {
    console.error('Error fetching security metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch security metrics' },
      { status: 500 }
    );
  }
} 