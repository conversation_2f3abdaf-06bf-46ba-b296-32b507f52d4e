// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { subDays } from 'date-fns';

// GET /api/admin/dashboard/facility-metrics
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get all facilities
    const facilities = await prisma.facility.findMany({
      select: {
        id: true,
        name: true,
        currentOccupancy: true,
        totalCapacity: true,
        address: true,
        city: true,
        province: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Time range - last 30 days
    const thirtyDaysAgo = subDays(new Date(), 30);

    // Get metrics for each facility
    const facilityMetrics = await Promise.all(facilities.map(async (facility) => {
      // Count case processing times for this facility
      const admissions = await prisma.bodyAdmission.findMany({
        where: {
          facilityId: facility.id,
          createdAt: {
            gte: thirtyDaysAgo
          }
        },
        include: {
          body: true
        }
      });

      // Calculate average processing time (in days)
      let totalProcessingDays = 0;
      let completedCases = 0;

      admissions.forEach(admission => {
        if (admission.body.status === 'RELEASED') {
          const admissionDate = new Date(admission.createdAt);
          const releaseDate = new Date(admission.updatedAt); // Assuming updatedAt reflects release time
          const processingDays = Math.ceil((releaseDate.getTime() - admissionDate.getTime()) / (1000 * 60 * 60 * 24));
          
          totalProcessingDays += processingDays;
          completedCases++;
        }
      });

      // Staff count at this facility
      const staffCount = await prisma.user.count({
        where: {
          facilityId: facility.id,
          status: 'ACTIVE'
        }
      });

      // Active cases at this facility
      const activeCases = await prisma.bodyAdmission.count({
        where: {
          facilityId: facility.id,
          status: 'ACTIVE'
        }
      });

      // Incoming cases (last 30 days)
      const incomingCases = await prisma.bodyAdmission.count({
        where: {
          facilityId: facility.id,
          createdAt: {
            gte: thirtyDaysAgo
          }
        }
      });

      // Outgoing/completed cases (last 30 days)
      const outgoingCases = await prisma.bodyRelease.count({
        where: {
          facilityId: facility.id,
          releaseDate: {
            gte: thirtyDaysAgo
          }
        }
      });

      // Format location from address components
      const location = `${facility.city || ''}, ${facility.province || ''}`.trim();
      const formattedLocation = location !== ',' ? location : 'Unknown location';

      return {
        id: facility.id,
        name: facility.name,
        location: formattedLocation,
        address: facility.address,
        capacityUtilization: {
          current: facility.currentOccupancy,
          total: facility.totalCapacity,
          percentage: facility.totalCapacity > 0 
            ? Math.round((facility.currentOccupancy / facility.totalCapacity) * 100)
            : 0
        },
        staffCount,
        avgProcessingTime: completedCases > 0 
          ? Math.round(totalProcessingDays / completedCases * 10) / 10 // Round to 1 decimal
          : 0,
        caseload: {
          active: activeCases,
          incoming: incomingCases,
          outgoing: outgoingCases,
          caseChangeRate: incomingCases - outgoingCases
        }
      };
    }));

    return NextResponse.json({
      facilities: facilityMetrics
    });
  } catch (error) {
    console.error('Error fetching facility metrics:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 