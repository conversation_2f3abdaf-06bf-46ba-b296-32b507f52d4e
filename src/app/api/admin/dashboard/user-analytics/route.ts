// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { subDays, startOfDay, endOfDay, format } from 'date-fns';
import { UserRole } from '@prisma/client';

// GET /api/admin/dashboard/user-analytics
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Use default values - request parameters would be handled in a more complex implementation
    const timeRange = 30; // 30 days
    const limit = 10; // 10 users
    
    const startDate = subDays(new Date(), timeRange);

    // Get most active users
    const activeUsers = await prisma.activityLog.groupBy({
      by: ['userId'],
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      },
      take: limit,
      where: {
        timestamp: {
          gte: startDate
        }
      }
    });

    // Get user details for the active users
    const userIds = activeUsers.map(u => u.userId);
    const users = await prisma.user.findMany({
      where: {
        id: {
          in: userIds
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        image: true,
        facilityId: true,
        status: true,
        facility: {
          select: {
            name: true
          }
        }
      }
    });

    // Daily activity trends (last 7 days)
    const activityTrends = [];
    const now = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = subDays(now, i);
      const dayStart = startOfDay(date);
      const dayEnd = endOfDay(date);
      
      // Count activities by role
      const adminCount = await getActivityCountByRole(dayStart, dayEnd, UserRole.ADMIN);
      const pathologistCount = await getActivityCountByRole(dayStart, dayEnd, UserRole.PATHOLOGIST);
      const morgueStaffCount = await getActivityCountByRole(dayStart, dayEnd, UserRole.MORGUE_STAFF);
      const fieldEmployeeCount = await getActivityCountByRole(dayStart, dayEnd, UserRole.FIELD_EMPLOYEE);
      const securityStaffCount = await getActivityCountByRole(dayStart, dayEnd, UserRole.SECURITY_STAFF);
      
      activityTrends.push({
        date: format(date, 'yyyy-MM-dd'),
        dayName: format(date, 'EEE'),
        admin: adminCount,
        pathologist: pathologistCount,
        morgueStaff: morgueStaffCount,
        fieldEmployee: fieldEmployeeCount,
        securityStaff: securityStaffCount,
        total: adminCount + pathologistCount + morgueStaffCount + fieldEmployeeCount + securityStaffCount
      });
    }

    // Format user activity data
    const userActivity = users.map(user => {
      const activities = activeUsers.find(a => a.userId === user.id)?._count.id || 0;
      
      return {
        id: user.id,
        name: user.name || 'Unknown User',
        email: user.email || 'No email',
        role: formatRole(user.role),
        avatar: user.image,
        facility: user.facility?.name || 'No facility',
        status: user.status,
        activityCount: activities,
        // Calculate an activity score (arbitrary metric for visualization)
        activityScore: Math.min(100, Math.round((activities / (timeRange * 5)) * 100))
      };
    }).sort((a, b) => b.activityCount - a.activityCount);

    return NextResponse.json({
      topUsers: userActivity,
      activityTrends,
      timeRange
    });
  } catch (error) {
    console.error('Error fetching user analytics:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// Helper function to get activity count by role
async function getActivityCountByRole(startDate: Date, endDate: Date, role: UserRole): Promise<number> {
  // Find users with the specified role
  const users = await prisma.user.findMany({
    where: {
      role: role
    },
    select: {
      id: true
    }
  });
  
  const userIds = users.map(u => u.id);
  
  // Count activities for users with this role
  if (userIds.length === 0) return 0;
  
  return prisma.activityLog.count({
    where: {
      userId: {
        in: userIds
      },
      timestamp: {
        gte: startDate,
        lte: endDate
      }
    }
  });
}

// Helper function to format role names
function formatRole(role: UserRole | undefined): string {
  if (!role) return 'Unknown Role';
  
  switch (role) {
    case UserRole.ADMIN:
      return 'Administrator';
    case UserRole.PATHOLOGIST:
      return 'Pathologist';
    case UserRole.MORGUE_STAFF:
      return 'Morgue Staff';
    case UserRole.FIELD_EMPLOYEE:
      return 'Field Employee';
    case UserRole.SECURITY_STAFF:
      return 'Security Staff';
    default:
      return 'Staff Member';
  }
} 