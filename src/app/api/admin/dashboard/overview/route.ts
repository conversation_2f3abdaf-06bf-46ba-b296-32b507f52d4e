// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { UserRole } from '@prisma/client';

// GET /api/admin/dashboard/overview
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // System-wide metrics
    const totalFacilities = await prisma.facility.count();
    const totalUsers = await prisma.user.count();
    const activeBodies = await prisma.body.count({
      where: {
        status: {
          not: 'RELEASED'
        }
      }
    });
    const totalCases = await prisma.body.count();

    // Count by user role
    const usersByRole = await Promise.all([
      prisma.user.count({ where: { role: UserRole.ADMIN } }),
      prisma.user.count({ where: { role: UserRole.PATHOLOGIST } }),
      prisma.user.count({ where: { role: UserRole.MORGUE_STAFF } }),
      prisma.user.count({ where: { role: UserRole.FIELD_EMPLOYEE } }),
      prisma.user.count({ where: { role: UserRole.SECURITY_STAFF } })
    ]);

    // Facility capacity utilization
    const facilities = await prisma.facility.findMany({
      select: {
        id: true,
        name: true,
        currentOccupancy: true,
        totalCapacity: true,
      }
    });

    // Calculate total system capacity and utilization
    const totalSystemCapacity = facilities.reduce((sum, facility) => sum + facility.totalCapacity, 0);
    const totalSystemOccupancy = facilities.reduce((sum, facility) => sum + facility.currentOccupancy, 0);
    const systemUtilizationPercentage = totalSystemCapacity > 0 
      ? Math.round((totalSystemOccupancy / totalSystemCapacity) * 100) 
      : 0;

    // Get recent activity
    const recentActivity = await prisma.activityLog.findMany({
      take: 5,
      orderBy: {
        timestamp: 'desc'
      },
      include: {
        user: {
          select: {
            name: true,
            role: true
          }
        }
      }
    });

    return NextResponse.json({
      statistics: {
        totalFacilities,
        totalUsers,
        activeBodies,
        totalCases,
        systemUtilization: systemUtilizationPercentage
      },
      userDistribution: {
        labels: ['Administrators', 'Pathologists', 'Morgue Staff', 'Field Employees', 'Security Staff'],
        data: usersByRole,
        colors: [
          'var(--joy-palette-primary-500)',
          'var(--joy-palette-success-500)',
          'var(--joy-palette-info-500)',
          'var(--joy-palette-warning-500)',
          'var(--joy-palette-danger-500)'
        ]
      },
      facilityUtilization: facilities.map(facility => ({
        name: facility.name,
        capacity: facility.totalCapacity,
        occupancy: facility.currentOccupancy,
        utilizationPercentage: facility.totalCapacity > 0 
          ? Math.round((facility.currentOccupancy / facility.totalCapacity) * 100) 
          : 0
      })),
      recentActivity: recentActivity.map(activity => ({
        id: activity.id,
        description: activity.description,
        user: activity.user?.name || 'Unknown User',
        role: formatRole(activity.user?.role),
        timestamp: activity.timestamp
      }))
    });
  } catch (error) {
    console.error('Error fetching admin dashboard overview:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// Helper function to format role names
function formatRole(role: UserRole | undefined): string {
  if (!role) return 'Unknown Role';
  
  switch (role) {
    case UserRole.ADMIN:
      return 'Administrator';
    case UserRole.PATHOLOGIST:
      return 'Pathologist';
    case UserRole.MORGUE_STAFF:
      return 'Morgue Staff';
    case UserRole.FIELD_EMPLOYEE:
      return 'Field Employee';
    case UserRole.SECURITY_STAFF:
      return 'Security Staff';
    default:
      return 'Staff Member';
  }
} 