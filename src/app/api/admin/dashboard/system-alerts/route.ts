// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { subDays, formatDistance } from 'date-fns';

// Priority levels for alerts
type AlertPriority = 'critical' | 'high' | 'medium' | 'low';

// Alert type definition
interface SystemAlert {
  id: string;
  type: string;
  title: string;
  message: string;
  priority: AlertPriority;
  timestamp: Date;
  source: string;
  facilityId?: string;
  facilityName?: string;
  isRead: boolean;
}

// GET /api/admin/dashboard/system-alerts
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const alerts: SystemAlert[] = [];
    
    // Generate alerts based on system data
    // This is where you'd implement actual alert logic based on your system requirements
    
    // 1. Check for facilities nearing capacity
    const allFacilities = await prisma.facility.findMany({
      select: {
        id: true,
        name: true,
        currentOccupancy: true,
        totalCapacity: true
      }
    });
    
    // Filter facilities that are at 90% capacity or more
    const facilitiesNearCapacity = allFacilities.filter(
      facility => facility.totalCapacity > 0 && 
                 (facility.currentOccupancy / facility.totalCapacity) >= 0.9
    );
    
    // Add capacity alerts
    facilitiesNearCapacity.forEach((facility, index) => {
      const capacityPercentage = Math.round((facility.currentOccupancy / facility.totalCapacity) * 100);
      const priority: AlertPriority = capacityPercentage >= 95 ? 'critical' : 'high';
      
      alerts.push({
        id: `capacity-${facility.id}`,
        type: 'capacity',
        title: `Facility Near Capacity`,
        message: `${facility.name} is at ${capacityPercentage}% capacity (${facility.currentOccupancy}/${facility.totalCapacity}).`,
        priority,
        timestamp: new Date(Date.now() - (index * 1000 * 60 * 60)), // Stagger timestamps for display
        source: 'System Monitor',
        facilityId: facility.id,
        facilityName: facility.name,
        isRead: false
      });
    });
    
    // 2. Check for inactive users (active accounts with no recent activity)
    const thirtyDaysAgo = subDays(new Date(), 30);
    
    // Get active users
    const activeUsers = await prisma.user.findMany({
      where: {
        status: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
        email: true,
        facilityId: true
      }
    });
    
    // Get users with recent activity
    const usersWithActivity = await prisma.activityLog.findMany({
      where: {
        timestamp: {
          gte: thirtyDaysAgo
        }
      },
      distinct: ['userId'],
      select: {
        userId: true
      }
    });
    
    const activeUserIds = new Set(usersWithActivity.map(a => a.userId));
    const inactiveUsers = activeUsers.filter(user => !activeUserIds.has(user.id));
    
    // Get facility details for inactive users
    const facilityIds = [...new Set(inactiveUsers.map(u => u.facilityId).filter(Boolean))];
    const facilities = await prisma.facility.findMany({
      where: {
        id: {
          in: facilityIds as string[]
        }
      },
      select: {
        id: true,
        name: true
      }
    });
    
    const facilityMap = Object.fromEntries(
      facilities.map(f => [f.id, f.name])
    );
    
    // Add inactive user alerts
    inactiveUsers.forEach((user, index) => {
      alerts.push({
        id: `inactive-${user.id}`,
        type: 'user_inactive',
        title: 'Inactive User Account',
        message: `${user.name || user.email} has not been active in over 30 days.`,
        priority: 'medium',
        timestamp: new Date(Date.now() - (index * 1000 * 60 * 3)), // Stagger timestamps
        source: 'User Management',
        facilityId: user.facilityId as string | undefined,
        facilityName: user.facilityId ? facilityMap[user.facilityId] : undefined,
        isRead: false
      });
    });
    
    // 3. Check for long-term storage cases
    const longTermCases = await prisma.bodyAdmission.count({
      where: {
        status: 'ACTIVE',
        createdAt: {
          lte: subDays(new Date(), 60) // Bodies in facilities for more than 60 days
        }
      }
    });
    
    if (longTermCases > 0) {
      alerts.push({
        id: 'long-term-cases',
        type: 'case_management',
        title: 'Long-term Storage Cases',
        message: `There are ${longTermCases} cases in storage for more than 60 days.`,
        priority: 'medium',
        timestamp: new Date(),
        source: 'Case Management',
        isRead: false
      });
    }
    
    // Sort alerts by priority and timestamp
    const sortedAlerts = alerts.sort((a, b) => {
      const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      
      if (priorityDiff !== 0) return priorityDiff;
      return b.timestamp.getTime() - a.timestamp.getTime(); // Newest first within priority
    });
    
    // Format timestamps for display
    const formattedAlerts = sortedAlerts.map(alert => ({
      ...alert,
      timeAgo: formatDistance(alert.timestamp, new Date(), { addSuffix: true })
    }));
    
    // Group alerts by priority
    const criticalAlerts = formattedAlerts.filter(a => a.priority === 'critical');
    const highAlerts = formattedAlerts.filter(a => a.priority === 'high');
    const mediumAlerts = formattedAlerts.filter(a => a.priority === 'medium');
    const lowAlerts = formattedAlerts.filter(a => a.priority === 'low');
    
    return NextResponse.json({
      alerts: {
        critical: criticalAlerts,
        high: highAlerts,
        medium: mediumAlerts,
        low: lowAlerts
      },
      totalAlerts: formattedAlerts.length,
      criticalCount: criticalAlerts.length,
      highCount: highAlerts.length
    });
  } catch (error) {
    console.error('Error retrieving system alerts:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 