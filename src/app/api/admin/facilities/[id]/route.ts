// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/prisma';
import { updateFacilitySchema } from '@/lib/schema/facilitySchema';

// GET /api/admin/facilities/[id]
// Get a specific facility by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }

    // Validate that the ID exists
    if (!params.id) {
      return NextResponse.json(
        { error: 'Facility ID is required' },
        { status: 400 }
      );
    }

    // Fetch the facility with associated data
    const facility = await prisma.facility.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            staff: true,
            fridges: true,
            departments: true,
            equipment: true,
          },
        },
      },
    });

    // Check if facility exists
    if (!facility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Return the facility data
    return NextResponse.json(facility);
  } catch (error: any) {
    console.error('Error fetching facility:', error);
    return NextResponse.json(
      { error: 'Failed to fetch facility', details: error.message },
      { status: 500 }
    );
  }
}

// PUT /api/admin/facilities/[id]
// Update a specific facility by ID
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }

    // Validate that the ID exists
    if (!params.id) {
      return NextResponse.json(
        { error: 'Facility ID is required' },
        { status: 400 }
      );
    }

    // Check if the facility exists
    const existingFacility = await prisma.facility.findUnique({
      where: { id: params.id },
    });

    if (!existingFacility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validatedData = updateFacilitySchema.safeParse(body);

    if (!validatedData.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validatedData.error.format() },
        { status: 400 }
      );
    }

    // If updating the code, check if the new code is already in use by another facility
    if (validatedData.data.code && validatedData.data.code !== existingFacility.code) {
      const facilityWithSameCode = await prisma.facility.findUnique({
        where: { code: validatedData.data.code },
      });

      if (facilityWithSameCode && facilityWithSameCode.id !== params.id) {
        return NextResponse.json(
          { error: 'A facility with this code already exists.' },
          { status: 409 }
        );
      }
    }

    // Prepare the update data
    const updateData: any = { ...validatedData.data };

    // Handle operatingHours as a JSON field
    if (updateData.operatingHours) {
      updateData.operatingHours = JSON.parse(JSON.stringify(updateData.operatingHours));
    }

    // Update the facility
    const updatedFacility = await prisma.facility.update({
      where: { id: params.id },
      data: updateData,
    });

    // Create an audit log for the update
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_FACILITY',
        entityType: 'FACILITY',
        entityId: params.id,
        performedBy: session.user.id,
        changes: {
          before: existingFacility,
          after: updatedFacility,
        },
      },
    });

    return NextResponse.json(updatedFacility);
  } catch (error: any) {
    console.error('Error updating facility:', error);
    return NextResponse.json(
      { error: 'Failed to update facility', details: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/facilities/[id]
// Delete a specific facility by ID
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }

    // Validate that the ID exists
    if (!params.id) {
      return NextResponse.json(
        { error: 'Facility ID is required' },
        { status: 400 }
      );
    }

    // Check if the facility exists
    const existingFacility = await prisma.facility.findUnique({
      where: { id: params.id },
    });

    if (!existingFacility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Check if facility has associated bodies, equipment, etc.
    const occupancy = await prisma.facility.findUnique({
      where: { id: params.id },
      select: { currentOccupancy: true },
    });

    if (occupancy && occupancy.currentOccupancy > 0) {
      return NextResponse.json(
        { error: 'Cannot delete facility with active cases. Transfer or resolve all cases first.' },
        { status: 400 }
      );
    }

    // Delete the facility
    await prisma.facility.delete({
      where: { id: params.id },
    });

    // Create an audit log for the deletion
    await prisma.adminAudit.create({
      data: {
        action: 'DELETE_FACILITY',
        entityType: 'FACILITY',
        entityId: params.id,
        performedBy: session.user.id,
        changes: {
          before: existingFacility,
          after: null,
        },
      },
    });

    return NextResponse.json({ message: 'Facility deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting facility:', error);

    // Handle constraint violations (e.g., if the facility has associated records)
    if (error.code === 'P2003') {
      return NextResponse.json(
        { error: 'Cannot delete facility because it has associated records. Remove all associated records first or archive the facility instead.' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete facility', details: error.message },
      { status: 500 }
    );
  }
}