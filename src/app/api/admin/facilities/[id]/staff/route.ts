// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/prisma';
import { facilityStaffAssignmentSchema, queryFacilityStaffSchema } from '@/lib/schema/facilityStaffSchema';

// GET /api/admin/facilities/[id]/staff
// Get all staff members assigned to a specific facility
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }

    // Validate facility ID
    if (!params.id) {
      return NextResponse.json(
        { error: 'Facility ID is required' },
        { status: 400 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const role = url.searchParams.get('role');
    const department = url.searchParams.get('department');
    const isActive = url.searchParams.get('isActive') === 'true' ? true :
                    url.searchParams.get('isActive') === 'false' ? false : undefined;
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const sortBy = url.searchParams.get('sortBy') || 'name';
    const sortOrder = url.searchParams.get('sortOrder') || 'asc';

    // Validate the query parameters
    const validatedQuery = queryFacilityStaffSchema.safeParse({
      facilityId: params.id,
      role,
      department,
      isActive,
      page,
      limit,
      sortBy,
      sortOrder,
    });

    if (!validatedQuery.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: validatedQuery.error.format() },
        { status: 400 }
      );
    }

    // Build the where conditions
    const where: any = {
      facilityId: params.id,
    };

    if (role) {
      where.role = role;
    }

    if (department) {
      where.department = department;
    }

    if (isActive !== undefined) {
      where.status = isActive ? 'ACTIVE' : 'INACTIVE';
    }

    // Count total staff matching the filters
    const totalCount = await prisma.user.count({ where });

    // Fetch staff members with pagination
    const staff = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        department: true,
        position: true,
        image: true,
        status: true,
        createdAt: true,
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
    });

    // Return staff members with pagination metadata
    return NextResponse.json({
      staff,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error: any) {
    console.error('Error fetching facility staff:', error);
    return NextResponse.json(
      { error: 'Failed to fetch facility staff', details: error.message },
      { status: 500 }
    );
  }
}

// POST /api/admin/facilities/[id]/staff
// Assign a user to a facility
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }



    // Validate facility ID
    if (!params.id) {
      return NextResponse.json(
        { error: 'Facility ID is required' },
        { status: 400 }
      );
    }

    // Check if the facility exists
    const facility = await prisma.facility.findUnique({
      where: { id: params.id },
    });

    if (!facility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validatedData = facilityStaffAssignmentSchema.safeParse({
      ...body,
      facilityId: params.id,
      assignedById: session.user.id,
      assignedAt: new Date(),
    });

    if (!validatedData.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validatedData.error.format() },
        { status: 400 }
      );
    }

    // Check if the user exists
    const userToAssign = await prisma.user.findUnique({
      where: { id: validatedData.data.userId },
    });

    if (!userToAssign) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if the user is already assigned to another facility
    if (userToAssign.facilityId && userToAssign.facilityId !== params.id) {
      return NextResponse.json(
        {
          error: 'User is already assigned to another facility',
          currentFacilityId: userToAssign.facilityId
        },
        { status: 409 }
      );
    }

    // Assign the user to the facility
    const updatedUser = await prisma.user.update({
      where: { id: validatedData.data.userId },
      data: {
        facilityId: params.id,
        role: validatedData.data.role || userToAssign.role,
        department: validatedData.data.department || userToAssign.department,
        position: validatedData.data.position || userToAssign.position,
        status: validatedData.data.isActive ? 'ACTIVE' : 'INACTIVE',
      },
    });

    // Create an audit log for the staff assignment
    await prisma.adminAudit.create({
      data: {
        action: 'ASSIGN_FACILITY_STAFF',
        entityType: 'USER',
        entityId: validatedData.data.userId,
        performedBy: session.user.id,
        changes: {
          before: { facilityId: userToAssign.facilityId },
          after: { facilityId: params.id },
        },
      },
    });

    return NextResponse.json({
      message: 'User assigned to facility successfully',
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        department: updatedUser.department,
        position: updatedUser.position,
        facilityId: updatedUser.facilityId,
        status: updatedUser.status,
        assignedAt: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    console.error('Error assigning user to facility:', error);
    return NextResponse.json(
      { error: 'Failed to assign user to facility', details: error.message },
      { status: 500 }
    );
  }
}