// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/prisma';
import { removeFacilityStaffSchema } from '@/lib/schema/facilityStaffSchema';

// DELETE /api/admin/facilities/[id]/staff/[userId]
// Remove a staff member from a facility
export async function DELETE(
  request: Request,
  { params }: { params: { id: string; userId: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }

    // Check permission (admin or facility manager)
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { roles: true },
    });



    // Validate parameters
    if (!params.id || !params.userId) {
      return NextResponse.json(
        { error: 'Facility ID and User ID are required' },
        { status: 400 }
      );
    }

    // Parse and validate the request body
    const validatedData = removeFacilityStaffSchema.safeParse({
      facilityId: params.id,
      userId: params.userId,
      removedById: session.user.id,
      reason: request.headers.get('X-Removal-Reason') || 'Administrative action',
    });

    if (!validatedData.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validatedData.error.format() },
        { status: 400 }
      );
    }

    // Check if the facility exists
    const facility = await prisma.facility.findUnique({
      where: { id: params.id },
    });

    if (!facility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Check if the user exists and is assigned to this facility
    const userToRemove = await prisma.user.findUnique({
      where: { id: params.userId },
    });

    if (!userToRemove) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (userToRemove.facilityId !== params.id) {
      return NextResponse.json(
        { error: 'User is not assigned to this facility' },
        { status: 400 }
      );
    }

    // Remove the user from the facility
    const updatedUser = await prisma.user.update({
      where: { id: params.userId },
      data: {
        facilityId: null, // Remove the facility assignment
      },
    });

    // Remove the user from the facility staff relationship
    await prisma.facility.update({
      where: { id: params.id },
      data: {
        staff: {
          disconnect: { id: params.userId },
        },
      },
    });

    // Create an audit log for the staff removal
    await prisma.adminAudit.create({
      data: {
        action: 'REMOVE_FACILITY_STAFF',
        entityType: 'USER',
        entityId: params.userId,
        performedBy: session.user.id,
        changes: {
          before: { facilityId: params.id },
          after: { facilityId: null },
        },
      },
    });

    return NextResponse.json({
      message: 'User removed from facility successfully',
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        facilityId: updatedUser.facilityId,
      },
    });
  } catch (error: any) {
    console.error('Error removing user from facility:', error);
    return NextResponse.json(
      { error: 'Failed to remove user from facility', details: error.message },
      { status: 500 }
    );
  }
}