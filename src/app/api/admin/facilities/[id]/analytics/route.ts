// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/prisma';
import { subDays, format } from 'date-fns';

// GET /api/admin/facilities/[id]/analytics
// Get analytics data for a specific facility
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }

    // Validate that the facility ID exists
    if (!params.id) {
      return NextResponse.json(
        { error: 'Facility ID is required' },
        { status: 400 }
      );
    }

    // Check if the facility exists
    const facility = await prisma.facility.findUnique({
      where: { id: params.id },
    });

    if (!facility) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    // Get query parameters for time range
    const url = new URL(request.url);
    const timeRange = url.searchParams.get('timeRange') || '30d'; // Default to 30 days

    // Determine the start date based on the time range
    let startDate = new Date();
    switch (timeRange) {
      case '7d':
        startDate = subDays(new Date(), 7);
        break;
      case '30d':
        startDate = subDays(new Date(), 30);
        break;
      case '90d':
        startDate = subDays(new Date(), 90);
        break;
      case '1y':
        startDate = subDays(new Date(), 365);
        break;
      default:
        startDate = subDays(new Date(), 30);
    }

    // Fetch capacity data and history
    const capacityData = await getCapacityData(params.id, startDate);
    
    // Fetch cases data and history
    const casesData = await getCasesData(params.id, startDate);
    
    // Fetch activity data
    const activityData = await getActivityData(params.id, startDate);
    
    // Fetch equipment data
    const equipmentData = await getEquipmentData(params.id);

    // Return the analytics data
    return NextResponse.json({
      name: facility.name,
      type: facility.type,
      capacityData,
      casesData,
      activityData,
      equipmentData,
    });
  } catch (error: any) {
    console.error('Error fetching facility analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch facility analytics', details: error.message },
      { status: 500 }
    );
  }
}

// Helper function to get capacity data and history
async function getCapacityData(facilityId: string, startDate: Date) {
  // Get current capacity data
  const facility = await prisma.facility.findUnique({
    where: { id: facilityId },
    select: {
      totalCapacity: true,
      currentOccupancy: true,
    },
  });

  if (!facility) {
    throw new Error('Facility not found');
  }

  const percentage = Math.round((facility.currentOccupancy / facility.totalCapacity) * 100);

  // Get historical capacity data
  // Note: This is a simplified example, in a real application you might have
  // a table that logs capacity changes or calculate them from admission/release records
  
  // Generate mock data for capacity history (this would be replaced with real data)
  const numDays = Math.ceil((new Date().getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const history = Array.from({ length: numDays }, (_, i) => {
    const date = subDays(new Date(), numDays - i - 1);
    // Generate a somewhat realistic variation in occupancy percentage
    const variance = Math.floor(Math.random() * 10) - 5; // -5 to +4
    let value = percentage + variance;
    value = Math.min(100, Math.max(0, value)); // Ensure value is between 0 and 100
    
    return {
      date: format(date, 'yyyy-MM-dd'),
      value,
    };
  });

  return {
    current: facility.currentOccupancy,
    total: facility.totalCapacity,
    percentage,
    history,
  };
}

// Helper function to get cases data and history
async function getCasesData(facilityId: string, startDate: Date) {
  // Get the total number of active bodies (cases) for this facility
  const activeCount = await prisma.bodyAdmission.count({
    where: {
      facilityId,
      status: 'ACTIVE',
    },
  });

  // Get the total number of completed cases (released bodies) for this facility
  const completedCount = await prisma.bodyRelease.count({
    where: {
      facilityId,
      status: 'COMPLETED',
    },
  });

  // Get department distribution (use admission data or other relevant metrics)
  // This is a simplified example, adjust based on your actual schema
  const departmentCounts = await prisma.$queryRaw`
    SELECT u.department as label, COUNT(*) as value
    FROM "BodyAdmission" ba
    JOIN "User" u ON ba.assignedToId = u.id
    WHERE ba.facilityId = ${facilityId}
    AND ba.status = 'ACTIVE'
    AND u.department IS NOT NULL
    GROUP BY u.department
  `;

  // Format department data for the pie chart
  const colorScheme = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b'];
  const byDepartment = Array.isArray(departmentCounts) 
    ? departmentCounts.map((item: any, index: number) => ({
        id: item.label,
        label: item.label,
        value: Number(item.value),
        color: colorScheme[index % colorScheme.length],
      }))
    : [];

  // Generate case history (simplification, should be replaced with real data)
  const numDays = Math.ceil((new Date().getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const history = Array.from({ length: numDays }, (_, i) => {
    const date = subDays(new Date(), numDays - i - 1);
    const dateStr = format(date, 'yyyy-MM-dd');
    
    // Generate somewhat realistic variations in active and completed cases
    const active = Math.max(0, activeCount - Math.floor(Math.random() * (activeCount * 0.2)));
    const completed = Math.max(0, completedCount - Math.floor(Math.random() * (completedCount * 0.3)));
    
    return {
      date: dateStr,
      active,
      completed,
    };
  });

  return {
    total: activeCount + completedCount,
    active: activeCount,
    completed: completedCount,
    byDepartment: byDepartment.length > 0 ? byDepartment : [
      { id: 'No Data', label: 'No Department Data', value: 1, color: '#cccccc' }
    ],
    history,
  };
}

// Helper function to get activity data
async function getActivityData(facilityId: string, startDate: Date) {
  // Get recent activity data
  const activityLogs = await prisma.activityLog.count({
    where: {
      user: {
        facilityId,
      },
      timestamp: {
        gte: startDate,
      },
    },
  });

  // Calculate activity by day
  const numDays = Math.ceil((new Date().getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // This would ideally be a database query aggregating actual activity by day
  // For simplicity, we're generating mock data based on a reasonable distribution
  const recentDays = Array.from({ length: numDays }, (_, i) => {
    const date = subDays(new Date(), numDays - i - 1);
    // Generate a somewhat realistic activity count for each day
    const baseValue = Math.floor(activityLogs / numDays);
    const dayVariance = Math.floor(Math.random() * (baseValue * 0.5)) - (baseValue * 0.25);
    const value = Math.max(0, baseValue + dayVariance);
    
    return {
      date: format(date, 'yyyy-MM-dd'),
      value,
    };
  });

  // Generate hourly activity distribution (mock data)
  const workHours = [9, 10, 11, 12, 13, 14, 15, 16, 17];
  const byHour = workHours.map(hour => {
    // Create a bell curve distribution peaking at midday
    const distanceFromNoon = Math.abs(hour - 13);
    const multiplier = 1 - (distanceFromNoon / 10);
    const value = Math.floor((activityLogs / 30) * multiplier * (0.7 + Math.random() * 0.6));
    
    return {
      hour: `${hour}:00`,
      value,
    };
  });

  // Get staff activity data (top staff members)
  // This would ideally be a database query
  // For simplicity, we're generating mock data
  const staffActivity = await prisma.user.findMany({
    where: {
      facilityId,
      status: 'ACTIVE',
    },
    select: {
      id: true,
      name: true,
      role: true,
      _count: {
        select: {
          activities: {
            where: {
              timestamp: {
                gte: startDate,
              },
            },
          },
        },
      },
    },
    take: 5,
    orderBy: {
      activities: {
        _count: 'desc',
      },
    },
  });

  const byStaff = staffActivity.map(staff => ({
    staff: staff.name || 'Unknown Staff',
    role: staff.role,
    value: staff._count.activities,
  }));

  return {
    recentDays,
    byHour,
    byStaff,
  };
}

// Helper function to get equipment data
async function getEquipmentData(facilityId: string) {
  // Get equipment stats
  const equipmentStats = await prisma.equipment.groupBy({
    by: ['status'],
    where: {
      facilityId,
    },
    _count: {
      id: true,
    },
  });

  // Calculate totals
  const total = equipmentStats.reduce((sum, stat) => sum + stat._count.id, 0);
  const functional = equipmentStats.find(stat => stat.status === 'ACTIVE')?._count.id || 0;
  const maintenance = equipmentStats.find(stat => stat.status === 'MAINTENANCE')?._count.id || 0;

  // Get equipment usage data
  const equipment = await prisma.equipment.findMany({
    where: {
      facilityId,
      status: 'ACTIVE',
    },
    select: {
      name: true,
    },
    take: 5,
  });

  // Generate mock usage hours for equipment
  // In a real application, this would come from usage logs or similar
  const usage = equipment.map(item => ({
    equipment: item.name,
    usageHours: Math.floor(Math.random() * 150) + 20,
  }));

  return {
    total,
    functional,
    maintenance,
    usage,
  };
} 