// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/prisma';
import { createFacilitySchema } from '@/lib/schema/facilitySchema';

// GET /api/admin/facilities
// Get all facilities with pagination, filtering, and sorting
export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }



    // Get query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const status = url.searchParams.get('status');
    const type = url.searchParams.get('type');
    const search = url.searchParams.get('search');
    const sortBy = url.searchParams.get('sortBy') || 'name';
    const sortOrder = url.searchParams.get('sortOrder') || 'asc';

    // Build query filters
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (type) {
      where.type = type;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
        { city: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Count total facilities matching the filter
    const totalCount = await prisma.facility.count({ where });

    // Fetch facilities with pagination, filtering, and sorting
    const facilities = await prisma.facility.findMany({
      where,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
      include: {
        // Include related data as needed
        _count: {
          select: {
            staff: true,
            fridges: true,
          },
        },
      },
    });

    // Return response with pagination metadata
    return NextResponse.json({
      facilities,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error: any) {
    console.error('Error fetching facilities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch facilities', details: error.message },
      { status: 500 }
    );
  }
}

// POST /api/admin/facilities
// Create a new facility
export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized. You must be logged in to access this resource.' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate the request body
    const validatedData = createFacilitySchema.safeParse(body);
    if (!validatedData.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validatedData.error.format() },
        { status: 400 }
      );
    }

    // Check if a facility with the same code already exists
    const existingFacility = await prisma.facility.findUnique({
      where: { code: validatedData.data.code },
    });

    if (existingFacility) {
      return NextResponse.json(
        { error: 'A facility with this code already exists.' },
        { status: 409 }
      );
    }

    // Create the facility with type assertion to satisfy TypeScript
    const facility = await prisma.facility.create({
      data: validatedData.data as any, // Safe because we validated with Zod
    });

    // Create an audit log for the creation
    await prisma.adminAudit.create({
      data: {
        action: 'CREATE_FACILITY',
        entityType: 'FACILITY',
        entityId: facility.id,
        performedBy: session.user.id,
        changes: { before: null, after: facility },
      },
    });

    return NextResponse.json(facility, { status: 201 });
  } catch (error: any) {
    console.error('Error creating facility:', error);
    return NextResponse.json(
      { error: 'Failed to create facility', details: error.message },
      { status: 500 }
    );
  }
}