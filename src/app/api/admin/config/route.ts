export const dynamic = 'force-dynamic'

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import type { SystemConfig } from '@/types';

// Default configuration
const defaultConfig: SystemConfig = {
  emailNotifications: true,
  temperatureAlerts: true,
  capacityWarningThreshold: 80,
  maintenanceSchedule: 'WEEKLY',
  auditLogRetention: 90,
  defaultLanguage: 'en',
  timeZone: 'UTC',
  temperatureUnit: 'C',
  autoLogout: 30,
  backupSchedule: 'DAILY',
};

export async function GET() {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const config = await prisma.systemConfig.findFirst();
    return NextResponse.json(config || defaultConfig);
  } catch (error) {
    console.error('Error fetching system config:', error);
    return NextResponse.json(
      { error: 'Failed to fetch system configuration' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();

    // Validate configuration data
    const config = await prisma.systemConfig.upsert({
      where: {
        id: '1', // Single configuration record
      },
      update: {
        emailNotifications: data.emailNotifications,
        temperatureAlerts: data.temperatureAlerts,
        capacityWarningThreshold: data.capacityWarningThreshold,
        maintenanceSchedule: data.maintenanceSchedule,
        auditLogRetention: data.auditLogRetention,
        defaultLanguage: data.defaultLanguage,
        timeZone: data.timeZone,
        temperatureUnit: data.temperatureUnit,
        autoLogout: data.autoLogout,
        backupSchedule: data.backupSchedule,
      },
      create: {
        id: '1',
        ...data,
      },
    });

    return NextResponse.json(config);
  } catch (error) {
    console.error('Error updating system config:', error);
    return NextResponse.json(
      { error: 'Failed to update system configuration' },
      { status: 500 }
    );
  }
}