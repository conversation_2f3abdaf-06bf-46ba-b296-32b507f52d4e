// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { auth } from '@/auth';
import { z } from 'zod';

const prisma = new PrismaClient();

// Schema for query parameters
const querySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  severity: z.string().optional(),
  action: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['timestamp', 'user', 'action', 'severity']).default('timestamp'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const parsed = querySchema.safeParse(Object.fromEntries(url.searchParams.entries()));
    
    if (!parsed.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: parsed.error.format() },
        { status: 400 }
      );
    }

    const { 
      page, 
      limit, 
      severity, 
      action, 
      search, 
      sortBy, 
      sortOrder,
      startDate,
      endDate
    } = parsed.data;

    // Build filter conditions
    const where: any = {};
    
    if (severity) {
      where.severity = severity;
    }
    
    if (action) {
      where.action = action;
    }
    
    if (search) {
      where.OR = [
        { user: { contains: search, mode: 'insensitive' } },
        { details: { contains: search, mode: 'insensitive' } },
        { ipAddress: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (startDate && endDate) {
      where.timestamp = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      where.timestamp = {
        gte: new Date(startDate),
      };
    } else if (endDate) {
      where.timestamp = {
        lte: new Date(endDate),
      };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Get total count for pagination
    const totalCount = await prisma.auditLog.count({ where });
    
    // Fetch audit logs
    const auditLogs = await prisma.auditLog.findMany({
      where,
      take: limit,
      skip,
      orderBy: {
        [sortBy]: sortOrder,
      },
      select: {
        id: true,
        user: true,
        action: true,
        details: true,
        ipAddress: true,
        userAgent: true,
        timestamp: true,
        severity: true,
        resourceType: true,
        resourceId: true,
        performedBy: true,
        createdAt: true,
      },
    });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      auditLogs,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page,
        limit,
      },
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch audit logs' },
      { status: 500 }
    );
  }
}

// Create a new audit log entry
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Validate input
    const body = await req.json();
    
    const schema = z.object({
      user: z.string(),
      performedBy: z.string(),
      action: z.string(),
      details: z.string(),
      ipAddress: z.string().optional(),
      userAgent: z.string().optional(),
      severity: z.string(),
      resourceType: z.string().optional(),
      resourceId: z.string().optional(),
    });
    
    const validated = schema.safeParse(body);
    
    if (!validated.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validated.error.format() },
        { status: 400 }
      );
    }
    
    // Create the audit log with user relation
    const auditLog = await prisma.auditLog.create({
      data: {
        user: validated.data.user,
        userRelation: {
          connect: { id: validated.data.performedBy }
        },
        performedBy: validated.data.performedBy as never,
        action: validated.data.action,
        details: validated.data.details,
        ipAddress: validated.data.ipAddress || req.headers.get('x-forwarded-for') || 'unknown',
        userAgent: validated.data.userAgent || req.headers.get('user-agent') || 'unknown',
        severity: validated.data.severity,
        resourceType: validated.data.resourceType,
        resourceId: validated.data.resourceId,
        timestamp: new Date(),
      },
    });
    
    return NextResponse.json({ success: true, auditLog }, { status: 201 });
  } catch (error) {
    console.error('Error creating audit log:', error);
    return NextResponse.json(
      { error: 'Failed to create audit log' },
      { status: 500 }
    );
  }
} 