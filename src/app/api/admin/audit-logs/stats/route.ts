// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, Prisma } from '@prisma/client';
import { auth } from '@/auth';
import { z } from 'zod';
import { format, subDays } from 'date-fns';

const prisma = new PrismaClient();

// Schema for query parameters
const querySchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  limit: z.coerce.number().int().min(1).max(100).default(10),
});

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const parsed = querySchema.safeParse(Object.fromEntries(url.searchParams.entries()));
    
    if (!parsed.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: parsed.error.format() },
        { status: 400 }
      );
    }

    const { startDate, endDate, limit } = parsed.data;

    // Default to last 30 days if no date range is provided
    const effectiveStartDate = startDate 
      ? new Date(startDate)
      : subDays(new Date(), 30);
    
    const effectiveEndDate = endDate
      ? new Date(endDate)
      : new Date();

    // Build date range filter as Prisma SQL fragment
    const dateRangeFilter = Prisma.sql`
      WHERE "timestamp" >= ${effectiveStartDate.toISOString()} 
      AND "timestamp" <= ${effectiveEndDate.toISOString()}
    `;

    // Get total count using raw query instead of count
    const totalCountResult = await prisma.$queryRaw`
      SELECT COUNT(*) as "count" 
      FROM "AuditLog" 
      ${dateRangeFilter}
    `;
    const totalCount = Number((totalCountResult as any[])[0].count || 0);

    // Get action breakdown
    const actionBreakdown = await prisma.$queryRaw`
      SELECT "action", COUNT(*) as "count"
      FROM "AuditLog"
      ${dateRangeFilter}
      GROUP BY "action"
      ORDER BY "count" DESC
      LIMIT ${limit}
    `;

    // Get severity breakdown
    const severityBreakdown = await prisma.$queryRaw`
      SELECT "severity", COUNT(*) as "count"
      FROM "AuditLog"
      ${dateRangeFilter}
      GROUP BY "severity"
      ORDER BY "count" DESC
    `;

    // Get user breakdown
    const userBreakdown = await prisma.$queryRaw`
      SELECT "user", COUNT(*) as "count"
      FROM "AuditLog"
      ${dateRangeFilter}
      GROUP BY "user"
      ORDER BY "count" DESC
      LIMIT ${limit}
    `;

    // Get resource type breakdown
    const resourceTypeBreakdown = await prisma.$queryRaw`
      SELECT "resourceType", COUNT(*) as "count"
      FROM "AuditLog"
      ${dateRangeFilter}
      AND "resourceType" IS NOT NULL
      GROUP BY "resourceType"
      ORDER BY "count" DESC
      LIMIT ${limit}
    `;

    // Get time distribution (daily)
    const timeDistribution = await prisma.$queryRaw`
      SELECT 
        to_char("timestamp"::date, 'YYYY-MM-DD') as "date",
        COUNT(*) as "count"
      FROM "AuditLog"
      ${dateRangeFilter}
      GROUP BY "date"
      ORDER BY "date"
    `;

    // Process and format the results
    const formattedActionBreakdown = {};
    const formattedSeverityBreakdown = {
      info: 0,
      warning: 0,
      error: 0,
    };
    const formattedUserBreakdown = {};
    const formattedResourceTypeBreakdown = {};
    const formattedTimeDistribution = {};

    // Format action breakdown
    (actionBreakdown as any[]).forEach(item => {
      formattedActionBreakdown[item.action] = Number(item.count);
    });

    // Format severity breakdown
    (severityBreakdown as any[]).forEach(item => {
      formattedSeverityBreakdown[item.severity] = Number(item.count);
    });

    // Format user breakdown
    (userBreakdown as any[]).forEach(item => {
      formattedUserBreakdown[item.user] = Number(item.count);
    });

    // Format resource type breakdown
    (resourceTypeBreakdown as any[]).forEach(item => {
      formattedResourceTypeBreakdown[item.resourceType || 'UNKNOWN'] = Number(item.count);
    });

    // Format time distribution
    (timeDistribution as any[]).forEach(item => {
      formattedTimeDistribution[item.date] = Number(item.count);
    });

    // Return the statistics
    return NextResponse.json({
      totalCount,
      actionBreakdown: formattedActionBreakdown,
      severityBreakdown: formattedSeverityBreakdown,
      userBreakdown: formattedUserBreakdown,
      resourceTypeBreakdown: formattedResourceTypeBreakdown,
      timeDistribution: formattedTimeDistribution,
      dateRange: {
        startDate: format(effectiveStartDate, 'yyyy-MM-dd'),
        endDate: format(effectiveEndDate, 'yyyy-MM-dd'),
      },
    });
  } catch (error) {
    console.error('Error fetching audit log statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch audit log statistics' },
      { status: 500 }
    );
  }
} 