// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from "next/server";
import { auth } from "@/auth";
import * as z from "zod";
import fs from "fs";
import path from "path";
import matter from "gray-matter";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Schema for article parameters
const articleParamsSchema = z.object({
  slug: z.string().min(1),
});

// Function to read markdown file
const getMarkdownContent = (slug: string) => {
  const docsDirectory = path.join(process.cwd(), 'docs', 'knowledgebase');
  const fullPath = path.join(docsDirectory, `${slug}.md`);
  
  try {
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);
    return {
      metadata: data,
      content,
      slug,
    };
  } catch (error) {
    return null;
  }
};

export async function GET(
  req: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const session = await auth();
    
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const validatedParams = articleParamsSchema.parse(params);
    const article = getMarkdownContent(validatedParams.slug);

    if (!article) {
      return new NextResponse("Article not found", { status: 404 });
    }

    // Log access to the article
    await prisma.activityLog.create({
      data: {
        userId: session.user.id as never,
        activityType: "VIEW",
        description: `Viewed article: ${validatedParams.slug}`,
        user: session.user as never,
      },
    });

    return NextResponse.json(article);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return new NextResponse("Invalid request data", { status: 422 });
    }

    return new NextResponse("Internal error", { status: 500 });
  }
}
