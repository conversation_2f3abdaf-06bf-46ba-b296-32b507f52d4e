// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from "next/server";
import { auth } from "@/auth"
import * as z from "zod";
import fs from "fs";
import path from "path";
import matter from "gray-matter";

// Schema for knowledge base search
const searchSchema = z.object({
  query: z.string().min(1).max(100),
  category: z.string().optional(),
});

// Schema for knowledge base article
const articleSchema = z.object({
  slug: z.string().min(1),
});

// Function to read markdown files
const getMarkdownContent = (slug: string) => {
  const docsDirectory = path.join(process.cwd(), 'docs', 'knowledgebase');
  const fullPath = path.join(docsDirectory, `${slug}.md`);
  
  try {
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);
    return {
      metadata: data,
      content,
    };
  } catch (error) {
    return null;
  }
};

// Function to get all markdown files
const getAllMarkdownFiles = () => {
  const docsDirectory = path.join(process.cwd(), 'docs', 'knowledgebase');
  const files = fs.readdirSync(docsDirectory);
  
  return files
    .filter(file => file.endsWith('.md'))
    .map(file => {
      const fullPath = path.join(docsDirectory, file);
      const fileContents = fs.readFileSync(fullPath, 'utf8');
      const { data, content } = matter(fileContents);
      const slug = file.replace(/\.md$/, '');
      
      return {
        slug,
        metadata: data,
        preview: content.substring(0, 200) + '...',
      };
    });
};

// GET endpoint for searching knowledge base
export async function GET(req: Request) {
  try {
    
    const session = await auth();

    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const query = searchParams.get("query");
    const category = searchParams.get("category");

    const validatedParams = searchSchema.parse({
      query,
      category,
    });

    const articles = getAllMarkdownFiles();
    
    let filteredArticles = articles;

    // Filter by search query if provided
    if (validatedParams.query) {
      const searchQuery = validatedParams.query.toLowerCase();
      filteredArticles = articles.filter(article => 
        article.preview.toLowerCase().includes(searchQuery) ||
        article.slug.toLowerCase().includes(searchQuery)
      );
    }

    // Filter by category if provided
    if (validatedParams.category) {
      filteredArticles = filteredArticles.filter(article =>
        article.metadata.category === validatedParams.category
      );
    }

    return NextResponse.json(filteredArticles);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return new NextResponse("Invalid request data", { status: 422 });
    }

    return new NextResponse("Internal error", { status: 500 });
  }
}
