// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { auth } from '@/auth';
import { Parser } from 'json2csv';

const prisma = new PrismaClient();

export async function POST(req: NextRequest) {
  try {
    // Authenticate and check admin role
    const session = await auth();
    if (!session || session.user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Parse request body
    const { admissionIds } = await req.json();
    if (!admissionIds || !Array.isArray(admissionIds) || admissionIds.length === 0) {
      return NextResponse.json({ error: 'Invalid admission IDs' }, { status: 400 });
    }

    // Fetch admission details
    const admissions = await prisma.bodyAdmission.findMany({
      where: { id: { in: admissionIds } },
      include: {
        body: true,
        bodyTag: true,
        facility: true,
      },
    });

    // Transform admissions to CSV-friendly format
    const transformedAdmissions = admissions.map(admission => ({
      'Admission ID': admission.id,
      'Body Name': admission.body?.firstName || 'N/A',
      'Body Tag': admission.bodyTag?.tagNumber || 'N/A',
      'Facility': admission.facility?.name || 'N/A',
      'Status': admission.status,
      'Admission Date': admission.admissionDate?.toISOString() || 'N/A',
      'Tracking Number': admission.body?.trackingNumber || 'N/A',
    }));

    // Convert to CSV
    const json2csvParser = new Parser();
    const csv = json2csvParser.parse(transformedAdmissions);

    // Return CSV as response
    return new NextResponse(csv, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=admissions_export.csv',
      },
    });
  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json({ error: 'Failed to export admissions' }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
