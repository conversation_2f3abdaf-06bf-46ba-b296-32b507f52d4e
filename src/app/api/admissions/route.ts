// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { z } from "zod"

// Schema for body admission validation based on Prisma schema
const bodyAdmissionSchema = z.object({
  facilityId: z.string().min(1, { message: "Facility is required" }),
  bodyId: z.string().min(1, { message: "Body ID is required" }),
  bodyTagId: z.string().optional(),
  admissionType: z.enum(["INITIAL", "POST_REFERRAL", "TRANSFER"], {
    required_error: "Admission type is required",
  }),
  status: z.string().default("ACTIVE"),
  deathRegisterNumber: z.string().optional(),
  bodyCondition: z.string().optional(),
  notes: z.string().optional(),
  photo: z.string().optional(),
  assignedFridge: z.string().optional(),
  temperature: z.number().optional(),
  barcodeValue: z.string().optional(),
  assignedToId: z.string().optional(),
  createdById: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request body
    const validatedData = bodyAdmissionSchema.parse(body)

    // Create new body admission
    const bodyAdmission = await prisma.bodyAdmission.create({
      data: {
        ...validatedData,
        admissionDate: new Date(),
      },
      include: {
        facility: true,
        body: true,
        bodyTag: true,
        assignedTo: true,
        createdBy: true,
      },
    })

    // Update body status to IN_STORAGE
    await prisma.body.update({
      where: { id: validatedData.bodyId },
      data: { status: "IN_STORAGE" },
    })

    // Create activity log
    await prisma.activityLog.create({
      data: {
        activityType: "CREATE",
        description: "Body admission created",
        userId: validatedData.createdById || "system",
        bodyAdmissionId: bodyAdmission.id,
      },
    })

    return NextResponse.json(bodyAdmission, { status: 201 })
  } catch (error) {
    console.error("Error creating body admission:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to create body admission" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const facilityId = searchParams.get("facilityId")
    const bodyId = searchParams.get("bodyId")
    const status = searchParams.get("status")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Build filter object
    const where: any = {}
    if (facilityId) where.facilityId = facilityId
    if (bodyId) where.bodyId = bodyId
    if (status) where.status = status

    // Get total count for pagination
    const total = await prisma.bodyAdmission.count({ where })

    // Get body admissions with pagination
    const bodyAdmissions = await prisma.bodyAdmission.findMany({
      where,
      include: {
        facility: true,
        body: true,
        bodyTag: true,
        assignedTo: true,
        createdBy: true,
        activityLogs: {
          take: 5,
          orderBy: {
            timestamp: "desc",
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        admissionDate: "desc",
      },
    })

    return NextResponse.json({
      data: bodyAdmissions,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching body admissions:", error)
    return NextResponse.json({ error: "Failed to fetch body admissions" }, { status: 500 })
  }
}

