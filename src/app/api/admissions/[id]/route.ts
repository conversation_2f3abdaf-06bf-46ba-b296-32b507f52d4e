// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prismaClient } from '@/lib/prismaClient';
import { ComprehensiveAdmission } from '@/types/admission';
import { auth } from '@/auth';
import { AdmissionType, BodyStatus, Prisma } from '@prisma/client';
import { debugPrismaQuery } from '@/utils/prismaDebugger';


export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const data = await request.json();
    console.log('Update admission request body:', data);

    // Validate required fields
    if (!data.facilityId) {
      return NextResponse.json(
        { error: 'Facility ID is required' },
        { status: 400 }
      );
    }

    if (!data.admissionDate) {
      return NextResponse.json(
        { error: 'Admission date is required' },
        { status: 400 }
      );
    }

    // Validate admission type
    if (data.admissionType && !Object.values(AdmissionType).includes(data.admissionType)) {
      return NextResponse.json(
        { error: 'Invalid admission type' },
        { status: 400 }
      );
    }

    // Validate body status if provided
    if (data.body?.status && !Object.values(BodyStatus).includes(data.body.status as BodyStatus)) {
      return NextResponse.json(
        {
          error: 'Invalid body status',
          message: `Body status must be one of: ${Object.values(BodyStatus).join(', ')}`,
          received: data.body.status
        },
        { status: 400 }
      );
    }

    // Validate temperature if provided
    if (data.temperature !== null && data.temperature !== undefined) {
      const temp = parseFloat(data.temperature.toString());
      if (isNaN(temp) || temp < -50 || temp > 50) {
        return NextResponse.json(
          { error: 'Temperature must be a valid number between -50°C and 50°C' },
          { status: 400 }
        );
      }
    }

    // Validate approximate age if provided
    if (data.body?.approximateAge !== null && data.body?.approximateAge !== undefined) {
      const age = parseInt(data.body.approximateAge.toString());
      if (isNaN(age) || age < 0 || age > 150) {
        return NextResponse.json(
          { error: 'Approximate age must be a valid number between 0 and 150' },
          { status: 400 }
        );
      }
    }

    // Validate height if provided
    if (data.body?.height !== null && data.body?.height !== undefined) {
      const height = parseFloat(data.body.height.toString());
      if (isNaN(height) || height < 0 || height > 300) {
        return NextResponse.json(
          { error: 'Height must be a valid number between 0 and 300 cm' },
          { status: 400 }
        );
      }
    }

    // Validate weight if provided
    if (data.body?.weight !== null && data.body?.weight !== undefined) {
      const weight = parseFloat(data.body.weight.toString());
      if (isNaN(weight) || weight < 0 || weight > 500) {
        return NextResponse.json(
          { error: 'Weight must be a valid number between 0 and 500 kg' },
          { status: 400 }
        );
      }
    }

    // Validate date of death if provided
    if (data.body?.dateOfDeath) {
      const deathDate = new Date(data.body.dateOfDeath);
      const now = new Date();
      if (isNaN(deathDate.getTime()) || deathDate > now) {
        return NextResponse.json(
          { error: 'Date of death must be a valid date not in the future' },
          { status: 400 }
        );
      }
    }

    // Prepare the update data according to Prisma schema
    const updateData: Prisma.BodyAdmissionUpdateInput = {
      admissionType: data.admissionType as AdmissionType,
      admissionDate: new Date(data.admissionDate),
      status: data.status || undefined,
      temperature: data.temperature !== null && data.temperature !== undefined ?
        parseFloat(data.temperature.toString()) : null,
      bodyCondition: data.bodyCondition || null,
      deathRegisterNumber: data.deathRegisterNumber || null,
      barcodeValue: data.barcodeValue || null,
      assignedFridge: data.assignedFridge || null,
      assignedTo: data.assignedToId ? { connect: { id: data.assignedToId } } : undefined,
      facility: { connect: { id: data.facilityId } },
      notes: data.notes || null,
      photo: data.photo || null,
      // Update body information if provided
      body: data.body ? {
        update: {
          firstName: data.body.firstName || null,
          lastName: data.body.lastName || null,
          gender: data.body.gender || null,
          approximateAge: data.body.approximateAge !== null && data.body.approximateAge !== undefined ?
            parseInt(data.body.approximateAge.toString()) : null,
          height: data.body.height ? parseFloat(data.body.height.toString()) : null,
          weight: data.body.weight ? parseFloat(data.body.weight.toString()) : null,
          distinguishingFeatures: data.body.distinguishingFeatures || null,
          deathRegistration: data.body.deathRegistration || null,
          causeOfDeath: data.body.causeOfDeath || null,
          placeOfDeath: data.body.placeOfDeath || null,
          dateOfDeath: data.body.dateOfDeath ? new Date(data.body.dateOfDeath) : null,
          status: data.body.status as BodyStatus,
          photos: data.body.photos || undefined,
          documents: data.body.documents || undefined,
          metadata: data.body.metadata || null
        }
      } : undefined
    };

    // Log the data being sent to Prisma for debugging
    console.log('Updating admission with data:', updateData);

    // Update admission
    const updatedAdmission = await prismaClient.bodyAdmission.update({
      where: { id: params.id },
      data: updateData,
      include: {
        body: true,
        facility: true,
        bodyTag: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            department: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            department: true
          }
        }
      }
    });

    return NextResponse.json(updatedAdmission);
  } catch (error: any) {
    console.error('Error updating admission:', error);

    // Check if it's a Prisma validation error
    if (error.name === 'PrismaClientValidationError') {
      console.error('Prisma validation error:', error.message);
      return NextResponse.json(
        {
          error: 'Validation Error',
          message: 'Invalid data format. Please check field names match the database schema.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Check if it's a Prisma not found error
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Admission not found' },
        { status: 404 }
      );
    }

    // Check for foreign key constraint errors
    if (error.code === 'P2003') {
      return NextResponse.json(
        {
          error: 'Reference Error',
          message: 'Invalid reference to facility or assigned user',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Check for unique constraint errors
    if (error.code === 'P2002') {
      return NextResponse.json(
        {
          error: 'Duplicate Error',
          message: 'A record with this information already exists',
          details: error.message
        },
        { status: 409 }
      );
    }

    // Check for invalid date errors
    if (error.message && error.message.includes('Invalid date')) {
      return NextResponse.json(
        {
          error: 'Date Error',
          message: 'Invalid date format provided',
          details: error.message
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to update admission',
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
        details: error.stack || 'No additional details available'
      },
      { status: 500 }
    );
  }
}


export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get admission with all related data in a single query
    // First get the admission with body details
    const admissionData = await debugPrismaQuery(prismaClient, 'bodyAdmission', 'findUnique', () => prismaClient.bodyAdmission.findUnique({
      where: { id: params.id },
      include: {
        body: true,
        facility: true,
        bodyTag: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            department: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            department: true
          }
        }
      }
    }));

    if (!admissionData) {
      return NextResponse.json({ error: 'Admission not found' }, { status: 404 });
    }

    // Get collection, referrals, and releases using bodyId
    const [collection, referrals, releases] = await Promise.all([
      debugPrismaQuery(prismaClient, 'bodyCollection', 'findUnique', () =>
        prismaClient.bodyCollection.findUnique({
          where: { bodyId: admissionData.bodyId },
          select: {
            id: true,
            bodyId: true,
            userId: true,
            name: true,
            institution: true,
            vehicleReg: true,
            arrivalTime: true,
            gpsCoords: true,
            collectionType: true,
            bodyDescription: true,
            sceneDescription: true,
            weatherConditions: true,
            temperature: true,
            collectionNotes: true,
            photos: true,
            documents: true,
            handedOverBy: true,
            handedOverRole: true,
            handedOverContact: true,
            handoverNotes: true,
            status: true,
            barcodeValue: true,
            metadata: true,
            createdAt: true,
            updatedAt: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
                department: true
              }
            },
            body: {
              select: {
                id: true,
                trackingNumber: true,
                status: true,
                bodyTagId: true,
                firstName: true,
                lastName: true,
                gender: true,
                approximateAge: true,
                height: true,
                weight: true,
                distinguishingFeatures: true,
                deathRegistration: true,
                causeOfDeath: true,
                placeOfDeath: true,
                dateOfDeath: true,
                photos: true,
                documents: true,
                metadata: true,
                createdAt: true,
                updatedAt: true
              }
            }
          }
        })
      ),
      debugPrismaQuery(prismaClient, 'bodyReferral', 'findMany', () =>
        prismaClient.bodyReferral.findMany({
          where: { bodyId: admissionData.bodyId },
          select: {
            id: true,
            bodyId: true,
            referralType: true,
            status: true,
            referralDate: true,
            returnDate: true,
            employeePersal: true,
            employeeName: true,
            institutionName: true,
            vehicleReg: true,
            specimenKitNumber: true,
            evidenceBagSerial: true,
            bodyTagPhoto: true,
            vehiclePhoto: true,
            gpsLatitude: true,
            gpsLongitude: true,
            referredById: true,
            assignedToId: true,
            notes: true,
            createdAt: true,
            updatedAt: true,
            referredBy: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
                department: true
              }
            },
            assignedTo: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
                department: true
              }
            },
            body: {
              select: {
                id: true,
                trackingNumber: true,
                status: true,
                bodyTagId: true,
                firstName: true,
                lastName: true,
                gender: true,
                approximateAge: true,
                height: true,
                weight: true,
                distinguishingFeatures: true,
                deathRegistration: true,
                causeOfDeath: true,
                placeOfDeath: true,
                dateOfDeath: true,
                photos: true,
                documents: true,
                metadata: true,
                createdAt: true,
                updatedAt: true
              }
            }
          }
        })
      ),
      debugPrismaQuery(prismaClient, 'bodyRelease', 'findMany', () =>
        prismaClient.bodyRelease.findMany({
          where: { bodyId: admissionData.bodyId },
          include: {
            facility: true,
            body: true,
            verifiedBy: true,
            releasedBy: true
          }
        })
      )
    ]);

    // Get the body with all its relations
    const bodyWithRelations = await debugPrismaQuery(prismaClient, 'body', 'findUnique', () => prismaClient.body.findUnique({
      where: { id: admissionData.bodyId },
      include: {
        bodyTag: true
      }
    }));

    if (!admissionData) {
      return NextResponse.json({ error: 'Admission not found' }, { status: 404 });
    }

    if (!bodyWithRelations) {
      return NextResponse.json({ error: 'Body not found' }, { status: 404 });
    }

    // Calculate risk assessment
    const riskScore = calculateRiskScore(admissionData);
    const riskCategory = getRiskCategory(riskScore);
    const riskFactors = identifyRiskFactors(admissionData);

    // Get storage history
    const storageHistory = await fetchStorageHistory(admissionData.bodyId);

    // Construct the response object
    const comprehensiveAdmission: ComprehensiveAdmission = {
      ...admissionData,
      body: {
        ...bodyWithRelations,
        collection: collection ? {
          ...collection,
          gpsCoords: collection.gpsCoords as { lat: number; lng: number; } | null,
          user: collection.user,
          body: collection.body
        } : undefined,
        referrals: referrals.map(ref => ({
          ...ref,
          referredBy: ref.referredBy,
          assignedTo: ref.assignedTo,
          body: ref.body
        })) || [],
        releases: releases || [],
        photos: bodyWithRelations.photos || [],
        documents: bodyWithRelations.documents || []
      },
      facility: {
        ...admissionData.facility,
        operatingHours: admissionData.facility?.operatingHours
          ? typeof admissionData.facility.operatingHours === 'string'
            ? JSON.parse(admissionData.facility.operatingHours)
            : admissionData.facility.operatingHours
          : undefined
      },
      riskAssessment: {
        score: riskScore,
        category: riskCategory,
        factors: riskFactors
      },
      storageHistory: {
        previousLocations: storageHistory.previousFacilities || [],
        temperatureLog: storageHistory.temperatureLog.map(log => ({
          timestamp: log.timestamp,
          temperature: typeof log.temperature === 'number'
            ? log.temperature.toString()
            : log.temperature
        }))
      }
    };

    return NextResponse.json(comprehensiveAdmission);
  } catch (error) {
    console.error('Error fetching comprehensive admission:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/admissions/[id] - Delete an admission
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has permission to delete admissions (only ADMIN and FACILITY_MANAGER)
    if (!['ADMIN', 'FACILITY_MANAGER'].includes(session.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to delete admissions' },
        { status: 403 }
      );
    }

    // Check if admission exists
    const admission = await prismaClient.bodyAdmission.findUnique({
      where: { id: params.id },
      include: {
        body: true,
        facility: true
      }
    });

    if (!admission) {
      return NextResponse.json(
        { error: 'Admission not found' },
        { status: 404 }
      );
    }

    // Check if admission can be deleted (business rules)
    // For example, don't allow deletion if body has been released
    if (admission.body?.status === 'RELEASED') {
      return NextResponse.json(
        { error: 'Cannot delete admission for a released body' },
        { status: 400 }
      );
    }

    // Check if there are any referrals associated with this admission
    const referralCount = await prismaClient.bodyReferral.count({
      where: { bodyId: admission.bodyId }
    });

    if (referralCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete admission with existing referrals. Delete referrals first.' },
        { status: 400 }
      );
    }

    // Start transaction to delete admission and update body status
    await prismaClient.$transaction(async (tx) => {
      // Delete the admission
      await tx.bodyAdmission.delete({
        where: { id: params.id }
      });

      // Update body status back to COLLECTED if this was the only admission
      const remainingAdmissions = await tx.bodyAdmission.count({
        where: { bodyId: admission.bodyId }
      });

      if (remainingAdmissions === 0) {
        await tx.body.update({
          where: { id: admission.bodyId },
          data: { status: 'COLLECTED' }
        });
      }

      // Create audit log
      await tx.adminAudit.create({
        data: {
          action: 'DELETE_ADMISSION',
          entityType: 'ADMISSION',
          entityId: params.id,
          performedBy: session.user.id,
          changes: {
            deletedAdmission: {
              id: admission.id,
              bodyId: admission.bodyId,
              facilityId: admission.facilityId,
              admissionType: admission.admissionType
            }
          }
        }
      });
    });

    return NextResponse.json({
      message: 'Admission deleted successfully',
      success: true
    });

  } catch (error: any) {
    console.error('Error deleting admission:', error);

    // Check for foreign key constraint errors
    if (error.code === 'P2003') {
      return NextResponse.json(
        {
          error: 'Cannot delete admission due to existing references',
          message: 'This admission has associated records that must be removed first',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Check if it's a Prisma not found error
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Admission not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to delete admission',
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
        details: error.stack || 'No additional details available'
      },
      { status: 500 }
    );
  }
}

// Risk calculation functions
function calculateRiskScore(admission: any): number {
  let score = 0;

  // Body condition risk
  if (admission.bodyCondition === 'DECOMPOSED') score += 30;
  if (admission.bodyCondition === 'SEVERELY_DAMAGED') score += 50;

  // Temperature risk
  if (admission.temperature && admission.temperature > 25) score += 20;

  // Additional risk factors can be added here
  return score;
}

function getRiskCategory(score: number): 'Low' | 'Medium' | 'High' | 'Critical' {
  if (score < 20) return 'Low';
  if (score < 40) return 'Medium';
  if (score < 60) return 'High';
  return 'Critical';
}

function identifyRiskFactors(admission: any): string[] {
  const factors: string[] = [];

  // Example risk factors
  if (admission.bodyCondition === 'DECOMPOSED') {
    factors.push('Advanced Decomposition');
  }
  if (admission.temperature && admission.temperature > 25) {
    factors.push('High Temperature Risk');
  }

  return factors;
}

async function fetchStorageHistory(bodyId: string) {
  try {
    // Get previous facilities
    const previousAdmissions = await prismaClient.bodyAdmission.findMany({
      where: { bodyId },
      select: {
        facility: { select: { name: true } },
        temperature: true,
        admissionDate: true
      },
      orderBy: { admissionDate: 'desc' },
      take: 10
    });

    return {
      previousFacilities: previousAdmissions.map(a => a.facility?.name || 'Unknown'),
      temperatureLog: previousAdmissions.map(a => ({
        timestamp: a.admissionDate,
        temperature: a.temperature || 0
      }))
    };
  } catch (error) {
    console.error('Error fetching storage history:', error);
    return {
      previousFacilities: [],
      temperatureLog: []
    };
  }
}
