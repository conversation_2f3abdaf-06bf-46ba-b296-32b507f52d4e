// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { z } from 'zod';

// Validation schema for fridge assignment
const fridgeAssignmentSchema = z.object({
  fridgeBarcode: z.string().min(1, "Fridge barcode is required"),
  fridgeLocation: z.string().min(1, "Fridge location is required"),
  temperature: z.number().optional(),
  notes: z.string().optional(),
});

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();

    // Validate request data
    const validationResult = fridgeAssignmentSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Check if admission exists
    const admission = await prisma.bodyAdmission.findUnique({
      where: { id: params.id },
      include: {
        body: true,
      },
    });

    if (!admission) {
      return NextResponse.json(
        { error: 'Admission not found' },
        { status: 404 }
      );
    }

    // Update admission with new fridge assignment
    const updatedAdmission = await prisma.bodyAdmission.update({
      where: { id: params.id },
      data: {
        assignedFridge: validatedData.fridgeLocation,
        barcodeValue: validatedData.fridgeBarcode,
        temperature: validatedData.temperature?.toString(),
        notes: validatedData.notes 
          ? `${admission.notes || ''}\nFridge reassignment: ${validatedData.notes}`
          : admission.notes,
        updatedAt: new Date(),
      },
      include: {
        body: true,
        assignedTo: {
          select: {
            name: true,
            role: true,
          }
        },
      },
    });

    return NextResponse.json(updatedAdmission);
  } catch (error) {
    console.error('Error assigning fridge:', error);
    return NextResponse.json(
      { error: 'Failed to assign fridge' },
      { status: 500 }
    );
  }
} 