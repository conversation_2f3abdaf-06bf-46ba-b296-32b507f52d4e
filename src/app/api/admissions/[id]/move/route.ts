// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { getUserFacilityId } from '@/lib/auth';

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { fridgeId } = await request.json();
    if (!fridgeId) {
      return NextResponse.json(
        { error: 'Fridge ID is required' },
        { status: 400 }
      );
    }

    // Get user's facility ID
    const userFacilityId = await getUserFacilityId(session.user.id);
    if (!userFacilityId && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'User not associated with a facility' },
        { status: 400 }
      );
    }

    // Start a transaction to handle the move
    const result = await prisma.$transaction(async (tx) => {
      // Get the current admission and fridge details
      const [admission, fridge] = await Promise.all([
        tx.bodyAdmission.findUnique({
          where: { id: params.id },
          include: { 
            body: true,
            facility: true
          }
        }),
        tx.fridge.findUnique({
          where: { id: fridgeId },
          include: {
            facility: true
          }
        })
      ]);

      if (!admission) {
        throw new Error('Admission not found');
      }

      if (!fridge) {
        throw new Error('Target fridge not found');
      }

      // Check if user has permission to move this admission
      if (session.user.role !== 'ADMIN' && admission.facilityId !== userFacilityId) {
        throw new Error('Not authorized to move this admission');
      }

      // Check if target fridge is in user's facility
      if (session.user.role !== 'ADMIN' && fridge.facilityId !== userFacilityId) {
        throw new Error('Not authorized to use this storage unit');
      }

      if (fridge.currentOccupancy >= fridge.capacity) {
        throw new Error('Target fridge is at capacity');
      }

      // If there's a current fridge assignment, decrease its occupancy
      if (admission.assignedFridge) {
        await tx.fridge.update({
          where: { id: admission.assignedFridge },
          data: {
            currentOccupancy: {
              decrement: 1
            },
            status: 'AVAILABLE'
          }
        });
      }

      // Update the target fridge's occupancy
      const updatedFridge = await tx.fridge.update({
        where: { id: fridgeId },
        data: {
          currentOccupancy: {
            increment: 1
          },
          status: 'OCCUPIED'
        }
      });

      // Update the admission with the new fridge
      const updatedAdmission = await tx.bodyAdmission.update({
        where: { id: params.id },
        data: {
          assignedFridge: fridgeId,
          status: 'IN_STORAGE',
          facilityId: fridge.facilityId // Update facility if moving between facilities
        },
        include: {
          body: {
            select: {
              trackingNumber: true,
              bodyTag: {
                select: {
                  tagNumber: true
                }
              }
            }
          },
          facility: true
        }
      });

      return { admission: updatedAdmission, fridge: updatedFridge };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error moving admission:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to move admission' },
      { status: 500 }
    );
  }
} 