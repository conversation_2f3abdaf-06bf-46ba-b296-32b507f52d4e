// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { client } from "@/trigger";
import { headers } from "next/headers";

export async function POST(request: Request) {
  const headersList = headers();
  const signature = headersList.get("x-trigger-signature");

  if (!signature) {
    return new Response("No signature", { status: 401 });
  }

  try {
    const payload = await request.json();
    await client.verify({
      payload,
      signature,
    });

    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("Error verifying webhook:", error);
    return new Response("Invalid signature", { status: 401 });
  }
} 