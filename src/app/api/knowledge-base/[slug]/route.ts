// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import path from "path";
import fs from "fs/promises";
import matter from "gray-matter";

const DOCS_DIRECTORY = path.join(process.cwd(), "docs", "knowledgebase");

export async function GET(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { slug } = params;
    const filePath = path.join(DOCS_DIRECTORY, `${slug}.md`);

    try {
      const fileContent = await fs.readFile(filePath, "utf8");
      const { data: metadata, content } = matter(fileContent);

      return NextResponse.json({
        slug,
        metadata,
        content,
      });
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === "ENOENT") {
        return new NextResponse("Article not found", { status: 404 });
      }
      throw error;
    }
  } catch (error) {
    console.error("Error in knowledge-base article API:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
