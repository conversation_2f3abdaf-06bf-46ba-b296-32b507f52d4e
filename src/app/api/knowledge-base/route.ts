// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import path from "path";
import fs from "fs/promises";
import matter from "gray-matter";

const DOCS_DIRECTORY = path.join(process.cwd(), "docs", "knowledgebase");

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const query = searchParams.get("query")?.toLowerCase();
    const category = searchParams.get("category");

    // Read all markdown files from the docs directory
    const files = await fs.readdir(DOCS_DIRECTORY);
    const articles = await Promise.all(
      files
        .filter((file) => file.endsWith(".md"))
        .map(async (file) => {
          const filePath = path.join(DOCS_DIRECTORY, file);
          const fileContent = await fs.readFile(filePath, "utf8");
          const { data: metadata, content } = matter(fileContent);
          const slug = file.replace(/\.md$/, "");

          // Create a preview from the content (first 200 characters)
          const preview = content.slice(0, 200).trim() + "...";

          return {
            slug,
            metadata,
            preview,
          };
        })
    );

    // Filter articles based on search query and category
    const filteredArticles = articles.filter((article) => {
      const matchesQuery = !query
        ? true
        : article.metadata.title?.toLowerCase().includes(query) ||
          article.metadata.description?.toLowerCase().includes(query) ||
          article.preview.toLowerCase().includes(query);

      const matchesCategory = !category
        ? true
        : article.metadata.category === category;

      return matchesQuery && matchesCategory;
    });

    return NextResponse.json(filteredArticles);
  } catch (error) {
    console.error("Error in knowledge-base API:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
