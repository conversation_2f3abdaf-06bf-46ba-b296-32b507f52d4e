# Send Invitations API

## Overview
This API allows sending various types of invitations with customizable templates.

## Invitation Types
- `standard`: Default invitation to join the platform
- `mobile-app`: Invitation to install the mobile app

## Request Body Parameters

### Standard Fields
- `recipients`: Array of email addresses (1-10)
- `message`: Optional custom message (max 500 chars)
- `subject`: Optional email subject
- `customTemplate`: Optional custom template flag
- `templateData`: Optional custom template data

### Mobile App Specific Fields
- `type`: Must be `'mobile-app'`
- `invitedBy`: Name of person/org inviting (default: 'GP Pathology Admin')
- `installLink`: Optional custom install link
- `qrCodeUrl`: Optional QR code image URL

## Example Requests

### Standard Invitation
```typescript
fetch('/api/send-invitations', {
  method: 'POST',
  body: JSON.stringify({
    recipients: ['<EMAIL>'],
    message: 'Join our platform!',
    templateData: {
      name: '<PERSON>'
    }
  })
})
```

### Mobile App Invitation
```typescript
fetch('/api/send-invitations', {
  method: 'POST',
  body: JSON.stringify({
    type: 'mobile-app',
    recipients: ['<EMAIL>'],
    invitedBy: 'Department Admin',
    message: 'Download our new forensic pathology mobile app!',
    installLink: 'https://yourapp.com/mobile',
    qrCodeUrl: 'https://yourapp.com/qr-code.png',
    templateData: {
      name: 'Jane Smith',
      platformInstructions: {
        ios: 'Tap Share > Add to Home Screen',
        android: 'Tap Install in browser',
        desktop: 'Scan QR code with mobile device'
      }
    }
  })
})
```

## Response
- `200`: Invitations sent successfully
- `207`: Some emails failed (partial success)
- `400`: Invalid input
- `429`: Rate limit exceeded
- `500`: Server error

## Error Handling
Always check the `status` and `results` in the response to confirm delivery.
