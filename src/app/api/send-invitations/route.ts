// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { z } from 'zod';
import { rateLimit } from '@/lib/rate-limit';
import { logInvitation } from '@/lib/logging';
import { 
  createInvitationEmailTemplate, 
  createMobileAppInvitationTemplate 
} from '@/lib/email-templates';

// Input validation schema
const invitationSchema = z.object({
  recipients: z.array(z.string().email()).min(1).max(10),
  message: z.string().max(500).optional(),
  subject: z.string().optional().default('Invitation to Join'),
  type: z.enum(['standard', 'mobile-app']).optional().default('standard'),
  customTemplate: z.boolean().optional().default(false),
  templateData: z.object({
    name: z.string().optional(),
    platformInstructions: z.union([
      z.string(), 
      z.object({
        ios: z.string(),
        android: z.string(),
        desktop: z.string()
      })
    ]).optional()
  }).optional(),
  invitedBy: z.string().optional().default('GP Pathology Admin'),
  installLink: z.string().optional(),
  qrCodeUrl: z.string().optional(),
});

// Create a rate limiter to prevent email spam
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // Max 500 users per minute
});

// Create a transporter using SMTP
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: Number.parseInt(process.env.SMTP_PORT || "587"),
  secure: process.env.SMTP_SECURE === "true",
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    await limiter.check(5, "INVITATION_TOKEN", "CACHE_TOKEN");

    // Parse and validate request body
    const body = await request.json();
    const { 
      recipients, 
      message, 
      subject, 
      type,
      customTemplate, 
      templateData,
      invitedBy,
      installLink,
      qrCodeUrl
    } = invitationSchema.parse(body);

    // Prepare email tasks
    const emailTasks = recipients.map(async (email) => {
      try {
        // Determine HTML content based on invitation type
        const htmlContent = type === 'mobile-app' 
          ? createMobileAppInvitationTemplate({
              recipientName: templateData?.name || 'Valued Team Member',
              invitedBy,
              installLink: installLink || process.env.MOBILE_APP_INSTALL_URL,
              qrCodeUrl,
              message,
              platformSpecificInstructions: templateData?.platformInstructions || undefined
            })
          : createInvitationEmailTemplate({
              title: subject,
              message: message || "You have been invited to join our platform.",
              ctaLink: process.env.SIGNUP_URL,
              recipientName: templateData?.name || 'Valued Team Member',
              ...templateData
            });

        const mailOptions = {
          from: process.env.SMTP_FROM,
          to: email,
          subject: subject,
          html: htmlContent,
          text: message || "You have been invited to join our platform.",
        };

        // Send email
        const info = await transporter.sendMail(mailOptions);
        
        // Log successful invitation
        await logInvitation(email);

        return {
          email,
          status: 'success',
          messageId: info.messageId,
        };
      } catch (emailError) {
        console.error(`Failed to send email to ${email}:`, emailError);
        await logInvitation(email, 'failed');
        return {
          email,
          status: 'failed',
          error: emailError instanceof Error ? emailError.message : 'Unknown error',
        };
      }
    });

    // Wait for all email tasks to complete
    const results = await Promise.all(emailTasks);

    // Check if any emails failed
    const failedEmails = results.filter(result => result.status === 'failed');

    if (failedEmails.length > 0) {
      return NextResponse.json(
        { 
          message: 'Some emails failed to send', 
          results,
          failedEmails 
        }, 
        { status: 207 } // Multi-Status
      );
    }

    return NextResponse.json({ 
      message: 'Invitations sent successfully', 
      results 
    }, { status: 200 });

  } catch (error) {
    console.error('Invitation sending error:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid input', 
          details: error.errors 
        }, 
        { status: 400 }
      );
    }

    // Handle rate limiting errors
    if (error.message === 'Rate limit exceeded') {
      return NextResponse.json(
        { error: 'Too many invitation requests. Please try again later.' }, 
        { status: 429 }
      );
    }

    // Generic error handler
    return NextResponse.json(
      { error: 'Failed to send invitations' }, 
      { status: 500 }
    );
  }
}
