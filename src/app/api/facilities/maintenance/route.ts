// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';

// Validation schema for maintenance task creation
const MaintenanceTaskSchema = z.object({
  facilityId: z.string().min(1, "Facility ID is required"),
  type: z.enum(['ROUTINE', 'EMERGENCY', 'INSPECTION', 'REPAIR']),
  description: z.string().min(5, "Description must be at least 5 characters"),
  scheduledDate: z.string().datetime("Invalid date format"),
  notes: z.string().optional(),
  status: z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'OVERDUE'])
});

// GET all maintenance tasks
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const maintenanceTasks = await prisma.facilityMaintenance.findMany({
      include: {
        facility: {
          select: { name: true }
        }
      },
      orderBy: { scheduledDate: 'desc' }
    });

    // Transform tasks to include facility name
    const transformedTasks = maintenanceTasks.map(task => ({
      ...task,
      facilityName: task.facility.name
    }));

    return NextResponse.json(transformedTasks);
  } catch (error) {
    console.error('Maintenance tasks fetch error:', error);
    return NextResponse.json({
      error: 'Failed to fetch maintenance tasks',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST create a new maintenance task
export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();

    // Validate input
    const validatedData = MaintenanceTaskSchema.parse(data);

    // Check if facility exists
    const facility = await prisma.facility.findUnique({
      where: { id: validatedData.facilityId }
    });

    if (!facility) {
      return NextResponse.json({
        error: 'Facility not found'
      }, { status: 404 });
    }

    // Create maintenance task
    const newMaintenanceTask = await prisma.facilityMaintenance.create({
      data: {
        facilityId: validatedData.facilityId,
        type: validatedData.type,
        description: validatedData.description,
        scheduledDate: new Date(validatedData.scheduledDate),
        notes: validatedData.notes,
        status: validatedData.status
      }
    });

    return NextResponse.json(newMaintenanceTask, { status: 201 });
  } catch (error) {
    console.error('Maintenance task creation error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      error: 'Failed to create maintenance task',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}