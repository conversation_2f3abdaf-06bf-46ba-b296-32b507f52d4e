// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';

// Validation schema for maintenance task update
const MaintenanceTaskUpdateSchema = z.object({
  facilityId: z.string().min(1, "Facility ID is required").optional(),
  type: z.enum(['ROUTINE', 'EMERGENCY', 'INSPECTION', 'REPAIR']).optional(),
  description: z.string().min(5, "Description must be at least 5 characters").optional(),
  scheduledDate: z.string().datetime("Invalid date format").optional(),
  completedDate: z.string().datetime("Invalid date format").optional(),
  notes: z.string().optional(),
  status: z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'OVERDUE']).optional(),
  performedBy: z.string().optional()
});

// GET a specific maintenance task
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const maintenanceTask = await prisma.facilityMaintenance.findUnique({
      where: { id: params.id },
      include: {
        facility: {
          select: { name: true }
        }
      }
    });

    if (!maintenanceTask) {
      return NextResponse.json({ error: 'Maintenance task not found' }, { status: 404 });
    }

    return NextResponse.json({
      ...maintenanceTask,
      facilityName: maintenanceTask.facility.name
    });
  } catch (error) {
    console.error('Maintenance task fetch error:', error);
    return NextResponse.json({
      error: 'Failed to fetch maintenance task',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT update a maintenance task
export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();

    // Validate input
    const validatedData = MaintenanceTaskUpdateSchema.parse(data);

    // Check if maintenance task exists
    const existingTask = await prisma.facilityMaintenance.findUnique({
      where: { id: params.id }
    });

    if (!existingTask) {
      return NextResponse.json({
        error: 'Maintenance task not found'
      }, { status: 404 });
    }

    // Check if facility exists (if provided)
    if (validatedData.facilityId) {
      const facility = await prisma.facility.findUnique({
        where: { id: validatedData.facilityId }
      });

      if (!facility) {
        return NextResponse.json({
          error: 'Facility not found'
        }, { status: 404 });
      }
    }

    // Update maintenance task
    const updatedTask = await prisma.facilityMaintenance.update({
      where: { id: params.id },
      data: {
        ...(validatedData.facilityId && { facilityId: validatedData.facilityId }),
        ...(validatedData.type && { type: validatedData.type }),
        ...(validatedData.description && { description: validatedData.description }),
        ...(validatedData.scheduledDate && { scheduledDate: new Date(validatedData.scheduledDate) }),
        ...(validatedData.completedDate && { completedDate: new Date(validatedData.completedDate) }),
        ...(validatedData.notes !== undefined && { notes: validatedData.notes }),
        ...(validatedData.status && { status: validatedData.status }),
        ...(validatedData.performedBy && { performedBy: validatedData.performedBy })
      }
    });

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error('Maintenance task update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      error: 'Failed to update maintenance task',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE a maintenance task
export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if maintenance task exists
    const existingTask = await prisma.facilityMaintenance.findUnique({
      where: { id: params.id }
    });

    if (!existingTask) {
      return NextResponse.json({
        error: 'Maintenance task not found'
      }, { status: 404 });
    }

    // Prevent deletion of completed tasks
    if (existingTask.status === 'COMPLETED') {
      return NextResponse.json({
        error: 'Cannot delete a completed maintenance task'
      }, { status: 400 });
    }

    // Delete maintenance task
    await prisma.facilityMaintenance.delete({
      where: { id: params.id }
    });

    return NextResponse.json({
      message: 'Maintenance task deleted successfully'
    });
  } catch (error) {
    console.error('Maintenance task deletion error:', error);
    return NextResponse.json({
      error: 'Failed to delete maintenance task',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}