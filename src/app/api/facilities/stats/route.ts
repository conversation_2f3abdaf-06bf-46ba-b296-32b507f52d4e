// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';

export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Total facilities
    const totalFacilities = await prisma.facility.count();

    // Total capacity and current occupancy
    const capacityStats = await prisma.facility.aggregate({
      _sum: {
        totalCapacity: true,
        currentOccupancy: true
      }
    });

    // Facility types distribution
    const facilityTypes = await prisma.facility.groupBy({
      by: ['type'],
      _count: { type: true }
    });

    // Status distribution
    const statusDistribution = await prisma.facility.groupBy({
      by: ['status'],
      _count: { status: true }
    });

    // Equipment count
    const equipmentCount = await prisma.equipment.count();

    // Maintenance count
    const maintenanceCount = await prisma.facilityMaintenance.count({
      where: { 
        status: {
          not: 'COMPLETED'
        } 
      }
    });

    // Recent maintenance activities
    const recentMaintenance = await prisma.facilityMaintenance.findMany({
      take: 5,
      orderBy: { scheduledDate: 'desc' },
      include: {
        facility: {
          select: { name: true }
        }
      }
    });

    return NextResponse.json({
      totalFacilities,
      totalCapacity: capacityStats._sum.totalCapacity || 0,
      currentOccupancy: capacityStats._sum.currentOccupancy || 0,
      facilityTypes: facilityTypes.map(type => ({
        type: type.type,
        count: type._count.type
      })),
      statusDistribution: statusDistribution.map(status => ({
        status: status.status,
        count: status._count.status
      })),
      equipmentCount,
      maintenanceCount,
      recentMaintenance: recentMaintenance.map(maintenance => ({
        id: maintenance.id,
        facilityName: maintenance.facility.name,
        type: maintenance.type,
        date: maintenance.scheduledDate.toISOString(),
        status: maintenance.status
      }))
    });
  } catch (error) {
    console.error('Facility statistics error:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch facility statistics', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
} 