// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { FacilityType, FacilityStatus } from '@prisma/client';

// Define ReferralTimerBehavior enum until Prisma client is updated
enum ReferralTimerBehavior {
  CONTINUE = 'CONTINUE',
  PAUSE = 'PAUSE',
  RESET = 'RESET'
}

// Validation schema for facility update - matches Prisma schema
const FacilityUpdateSchema = z.object({
  name: z.string().min(1, "Facility name is required").optional(),
  code: z.string().min(1, "Facility code is required").optional(),
  type: z.enum(['MORGUE', 'STORAGE', 'PROCESSING', 'TEMPORARY']).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'UNDER_MAINTENANCE', 'DECOMMISSIONED']).optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  postalCode: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email("Invalid email address").optional(),
  totalCapacity: z.number().int().positive("Capacity must be a positive number").optional(),
  currentOccupancy: z.number().int().min(0, "Occupancy cannot be negative").optional(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format")
  }).optional(),
  emergencyContact: z.string().optional(),
  licenseExpiry: z.string().datetime("Invalid date format").optional(),
  referralTimerBehavior: z.enum([
    ReferralTimerBehavior.CONTINUE,
    ReferralTimerBehavior.PAUSE,
    ReferralTimerBehavior.RESET
  ]).optional()
});

// GET a specific facility
export async function GET(
  _req: Request, // Prefix with underscore to indicate it's intentionally unused
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const facility = await prisma.facility.findUnique({
      where: { id: params.id },
      include: {
        equipment: true,
        maintenance: true,
        staff: true
      }
    });

    if (!facility) {
      return NextResponse.json({ error: 'Facility not found' }, { status: 404 });
    }

    return NextResponse.json(facility);
  } catch (error) {
    console.error('Facility fetch error:', error);
    return NextResponse.json({
      error: 'Failed to fetch facility',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT update a facility
export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();

    // Validate input
    const validatedData = FacilityUpdateSchema.parse(data);

    // Check if facility exists
    const existingFacility = await prisma.facility.findUnique({
      where: { id: params.id }
    });

    if (!existingFacility) {
      return NextResponse.json({
        error: 'Facility not found'
      }, { status: 404 });
    }

    // Check if new code (if provided) is unique
    if (validatedData.code && validatedData.code !== existingFacility.code) {
      const codeExists = await prisma.facility.findUnique({
        where: { code: validatedData.code as string }
      });

      if (codeExists) {
        return NextResponse.json({
          error: 'Facility code already exists'
        }, { status: 400 });
      }
    }

    // Update facility
    const updatedFacility = await prisma.facility.update({
      where: { id: params.id },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.code && { code: validatedData.code }),
        ...(validatedData.type && { type: validatedData.type as FacilityType }),
        ...(validatedData.status && { status: validatedData.status as FacilityStatus }),
        ...(validatedData.address && { address: validatedData.address }),
        ...(validatedData.city && { city: validatedData.city }),
        ...(validatedData.province && { province: validatedData.province }),
        ...(validatedData.postalCode && { postalCode: validatedData.postalCode }),
        ...(validatedData.phone && { phone: validatedData.phone }),
        ...(validatedData.email && { email: validatedData.email }),
        ...(validatedData.totalCapacity !== undefined && { totalCapacity: validatedData.totalCapacity }),
        ...(validatedData.currentOccupancy !== undefined && { currentOccupancy: validatedData.currentOccupancy }),
        ...(validatedData.operatingHours && { operatingHours: validatedData.operatingHours }),
        ...(validatedData.emergencyContact && { emergencyContact: validatedData.emergencyContact as string }),
        ...(validatedData.licenseExpiry && { licenseExpiry: new Date(validatedData.licenseExpiry as string) }),
        ...(validatedData.referralTimerBehavior && { referralTimerBehavior: validatedData.referralTimerBehavior as ReferralTimerBehavior })
      }
    });

    return NextResponse.json(updatedFacility);
  } catch (error) {
    console.error('Facility update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      error: 'Failed to update facility',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE a facility
export async function DELETE(_req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if facility exists
    const existingFacility = await prisma.facility.findUnique({
      where: { id: params.id }
    });

    if (!existingFacility) {
      return NextResponse.json({
        error: 'Facility not found'
      }, { status: 404 });
    }

    // Check if facility has any active dependencies
    const equipmentCount = await prisma.equipment.count({
      where: { facilityId: params.id }
    });

    const staffCount = await prisma.user.count({
      where: { facilityId: params.id }
    });

    if (equipmentCount > 0 || staffCount > 0) {
      return NextResponse.json({
        error: 'Cannot delete facility with associated equipment or staff'
      }, { status: 400 });
    }

    // Delete facility
    await prisma.facility.delete({
      where: { id: params.id }
    });

    return NextResponse.json({
      message: 'Facility deleted successfully'
    });
  } catch (error) {
    console.error('Facility deletion error:', error);
    return NextResponse.json({
      error: 'Failed to delete facility',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}