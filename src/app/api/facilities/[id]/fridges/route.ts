// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { facilityId: string } }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    //first lets get the facility id from user session
    //Thats if we have a facility id in the request, otherwise we get it from the session
    const facilityId = params.facilityId || session.user.facilityId;

    if (!facilityId) {
      return NextResponse.json(
        { error: 'Facility not found' },
        { status: 404 }
      );
    }

    const fridges = await prisma.fridge.findMany({
      where: {
        facilityId: facilityId,
      },
      select: {
        id: true,
        fridgeNumber: true,
        barcode: true,
        temperature: true,
        status: true,
        capacity: true,
        currentOccupancy: true,
        facilityId: true,
      },
    });

    return NextResponse.json(fridges);
  } catch (error) {
    console.error('Error fetching facility fridges:', error);
    return NextResponse.json(
      { error: 'Failed to fetch facility fridges' },
      { status: 500 }
    );
  }
}
