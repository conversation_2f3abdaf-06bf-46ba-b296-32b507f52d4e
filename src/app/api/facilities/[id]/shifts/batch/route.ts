// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { sendEmail } from '@/lib/email';

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { shifts } = data;

    // Get facility and admin users
    const facility = await prisma.facility.findUnique({
      where: { id: params.id },
      include: {
        staff: {
          where: { role: 'ADMIN' },
          select: { email: true },
        },
      },
    });

    if (!facility) {
      return NextResponse.json({ error: 'Facility not found' }, { status: 404 });
    }

    // Create all shifts in a transaction
    const createdShifts = await prisma.$transaction(async (tx) => {
      const results = await Promise.all(
        shifts.map(async (shift: any) => {
          const createdShift = await tx.shift.create({
            data: {
              startTime: new Date(shift.startTime),
              endTime: new Date(shift.endTime),
              type: shift.type,
              notes: shift.notes,
              facilityId: params.id,
              userId: shift.userId,
            },
            include: {
              user: true,
            },
          });

          // Send notification email to assigned staff
          if (createdShift.user?.email) {
            await sendEmail({
              to: createdShift.user.email,
              subject: 'New Shift Assignment',
              text: `You have been assigned a new ${createdShift.type} shift at ${facility.name}
              \nDate: ${new Date(createdShift.startTime).toLocaleDateString()}
              \nTime: ${new Date(createdShift.startTime).toLocaleTimeString()} - ${new Date(createdShift.endTime).toLocaleTimeString()}
              \nNotes: ${createdShift.notes || 'No additional notes'}`,
            });
          }

          return createdShift;
        })
      );

      // Send notification to facility admins about new shifts
      const adminEmails = facility.staff
        .map(admin => admin.email)
        .filter(Boolean);

      if (adminEmails.length > 0) {
        await sendEmail({
          to: adminEmails.join(','),
          subject: `New Shifts Created - ${facility.name}`,
          text: `${shifts.length} new shifts have been created for ${facility.name}.
          \nPlease review the schedule in the facility management system.`,
        });
      }

      return results;
    });

    return NextResponse.json(createdShifts);
  } catch (error) {
    console.error('Error creating shifts:', error);
    return NextResponse.json(
      { error: 'Failed to create shifts' },
      { status: 500 }
    );
  }
} 