// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { Facility, FacilityType, FacilityStatus } from '@prisma/client'

// Validation schema for facility creation/update - matches Prisma schema
const FacilitySchema = z.object({
  name: z.string().min(1, "Facility name is required"),
  code: z.string().min(1, "Facility code is required"),
  type: z.enum(['MORGUE', 'STORAGE', 'PROCESSING', 'TEMPORARY']),
  status: z.enum(['ACTIVE', 'INACTIVE', 'UNDER_MAINTENANCE', 'DECOMMISSIONED']).default('ACTIVE'),
  address: z.string().optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  postalCode: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email("Invalid email address").optional(),
  totalCapacity: z.number().int().positive("Capacity must be a positive number"),
  currentOccupancy: z.number().int().min(0, "Occupancy cannot be negative").default(0),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format")
  }).optional(),
  emergencyContact: z.string().optional(),
  licenseExpiry: z.string().datetime("Invalid date format").optional()
})

// Extended facility type with count fields
interface FacilityWithCounts extends Facility {
  equipmentCount: number;
  maintenanceCount: number;
  staffCount: number;
}

// GET all facilities
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facilities = await prisma.facility.findMany()

    // Fetch counts separately
    const facilitiesWithCounts: FacilityWithCounts[] = await Promise.all(
      facilities.map(async (facility) => {
        const equipmentCount = await prisma.equipment.count({
          where: { facilityId: facility.id }
        })
        const maintenanceCount = await prisma.facilityMaintenance.count({
          where: { facilityId: facility.id }
        })
        const staffCount = await prisma.user.count({
          where: { facilityId: facility.id }
        })

        return {
          ...facility,
          equipmentCount,
          maintenanceCount,
          staffCount
        }
      })
    )

    return NextResponse.json(facilitiesWithCounts)
  } catch (error) {
    console.error('Facilities fetch error:', error)
    return NextResponse.json({
      error: 'Failed to fetch facilities',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST create a new facility
export async function POST (req: Request) {
  try {
    const data = await req.json()

    // Validate input
    const validatedData = FacilitySchema.parse(data)

    // Check if facility with same code already exists
    const existingFacility = await prisma.facility.findUnique({
      where: { code: validatedData.code }
    })

    if (existingFacility) {
      return NextResponse.json({
        error: 'Facility with this code already exists'
      }, { status: 400 })
    }

    // Create facility with explicit type casting
    const newFacility = await prisma.facility.create({
      data: {
        name: validatedData.name,
        code: validatedData.code,
        type: validatedData.type as FacilityType,
        status: validatedData.status as FacilityStatus,
        address: validatedData.address,
        city: validatedData.city,
        province: validatedData.province,
        postalCode: validatedData.postalCode,
        phone: validatedData.phone,
        email: validatedData.email,
        totalCapacity: validatedData.totalCapacity,
        currentOccupancy: validatedData.currentOccupancy,
        operatingHours: validatedData.operatingHours,
        emergencyContact: validatedData.emergencyContact,
        licenseExpiry: new Date(validatedData.licenseExpiry)
      }
    })

    return NextResponse.json(newFacility, { status: 201 })
  } catch (error) {
    console.error('Facility creation error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      error: 'Failed to create facility',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// PUT /api/facilities - Update a facility
export async function PUT(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = body

    const facility = await prisma.facility.update({
      where: { id },
      data: updateData,
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_FACILITY',
        entityType: 'FACILITY',
        entityId: id,
        performedBy: session.user.id,
        changes: updateData,
      },
    })

    return NextResponse.json(facility)
  } catch (error) {
    console.error('Error updating facility:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}