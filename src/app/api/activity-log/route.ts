// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const entityType = searchParams.get("entityType")
    const entityId = searchParams.get("entityId")
    const userId = searchParams.get("userId")
    const action = searchParams.get("action")
    const bodyId = searchParams.get("bodyId")
    const bodyAdmissionId = searchParams.get("bodyAdmissionId")
    const bodyReferralId = searchParams.get("bodyReferralId")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "20")
    const skip = (page - 1) * limit

    // Build filter object
    const where: any = {}
    if (entityType) where.entityType = entityType
    if (entityId) where.entityId = entityId
    if (userId) where.userId = userId
    if (action) where.action = action
    if (bodyId) where.bodyId = bodyId
    if (bodyAdmissionId) where.bodyAdmissionId = bodyAdmissionId
    if (bodyReferralId) where.bodyReferralId = bodyReferralId

    // Get total count for pagination
    const total = await prisma.activityLog.count({ where })

    // Get activity logs with pagination
    const activityLogs = await prisma.activityLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json({
      data: activityLogs,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching activity logs:", error)
    return NextResponse.json({ error: "Failed to fetch activity logs" }, { status: 500 })
  }
}

