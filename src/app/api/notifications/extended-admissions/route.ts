// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { extendedAdmissionService } from '@/services/extendedAdmissionService';
import { auth } from '@/auth';

// GET: Fetch bodies in extended admission (> 7 days)
export async function GET(request: Request) {
  try {
    const session = await auth();
    
    // Check for authentication
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only admins and pathologists can view this data
    if (!(session.user.role === 'ADMIN' || session.user.role === 'PATHOLOGIST')) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get facility filter from query params if provided
    const { searchParams } = new URL(request.url);
    const facilityId = searchParams.get('facilityId');

    let result;
    if (facilityId) {
      // Get extended admissions for a specific facility
      const groupedAdmissions = await extendedAdmissionService.groupAdmissionsByFacility();
      result = groupedAdmissions.find(group => group.facilityId === facilityId);
      
      if (!result) {
        return NextResponse.json({
          success: true,
          data: { 
            facilityId,
            bodies: [],
            facilityName: "Unknown",
            totalCount: 0
          }
        });
      }
    } else {
      // Get all extended admissions grouped by facility
      result = await extendedAdmissionService.groupAdmissionsByFacility();
    }

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Error fetching extended admissions:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch extended admissions' },
      { status: 500 }
    );
  }
}

// POST: Send notifications for extended admissions
export async function POST(request: Request) {
  try {
    const session = await auth();
    
    // Check for authentication and authorization
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only admins can trigger notifications
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { facilityId } = body;
    
    let result;

    if (facilityId) {
      // Send notifications for a specific facility
      const groupedAdmissions = await extendedAdmissionService.groupAdmissionsByFacility();
      const facilityGroup = groupedAdmissions.find(group => group.facilityId === facilityId);
      
      if (!facilityGroup) {
        return NextResponse.json(
          { success: false, error: 'Facility not found or no extended admissions' },
          { status: 404 }
        );
      }
      
      result = await extendedAdmissionService.sendFacilityNotifications(facilityGroup);
    } else {
      // Send notifications for all facilities
      result = await extendedAdmissionService.sendNotificationsForAllFacilities();
    }

    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Error sending extended admission notifications:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to send notifications' },
      { status: 500 }
    );
  }
} 