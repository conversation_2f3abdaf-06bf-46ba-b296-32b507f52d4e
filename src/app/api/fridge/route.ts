// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { z } from "zod"

// Schema for fridge validation based on Prisma schema
const fridgeSchema = z.object({
  fridgeNumber: z.string().min(1, { message: "Fridge number is required" }),
  barcode: z.string().min(1, { message: "Barcode is required" }),
  temperature: z.number(),
  status: z.enum(["AVAILABLE", "OCCUPIED", "MAINTENANCE", "OFFLINE"]).default("AVAILABLE"),
  capacity: z.number().min(1, { message: "Capacity must be at least 1" }),
  currentOccupancy: z.number().default(0),
  facilityId: z.string().min(1, { message: "Facility is required" }),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request body
    const validatedData = fridgeSchema.parse(body)

    // Check if fridge number already exists
    const existingFridge = await prisma.fridge.findFirst({
      where: {
        OR: [{ fridgeNumber: validatedData.fridgeNumber }, { barcode: validatedData.barcode }],
        facilityId: validatedData.facilityId,
      },
    })

    if (existingFridge) {
      return NextResponse.json(
        {
          error: "Fridge number or barcode already exists in this facility",
        },
        { status: 400 },
      )
    }

    // Create new fridge
    const fridge = await prisma.fridge.create({
      data: validatedData,
    })

    return NextResponse.json(fridge, { status: 201 })
  } catch (error) {
    console.error("Error creating fridge:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to create fridge" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const facilityId = searchParams.get("facilityId")
    const status = searchParams.get("status")
    const fridgeNumber = searchParams.get("fridgeNumber")
    const barcode = searchParams.get("barcode")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Build filter object
    const where: any = {}
    if (facilityId) where.facilityId = facilityId
    if (status) where.status = status
    if (fridgeNumber) where.fridgeNumber = fridgeNumber
    if (barcode) where.barcode = barcode

    // Get total count for pagination
    const total = await prisma.fridge.count({ where })

    // Get fridges with pagination
    const fridges = await prisma.fridge.findMany({
      where,
      include: {
        facility: true,
      },
      skip,
      take: limit,
      orderBy: {
        fridgeNumber: "asc",
      },
    })

    return NextResponse.json({
      data: fridges,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching fridges:", error)
    return NextResponse.json({ error: "Failed to fetch fridges" }, { status: 500 })
  }
}

