// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { z } from 'zod';

import { PathologyProcedureType, PathologyProcedureStatus, PathologyPriority } from '@/types/pathology';

// Validation schema for procedure creation
const createProcedureSchema = z.object({
  bodyId: z.string(),
  workflowId: z.string().optional(),
  procedureType: z.nativeEnum(PathologyProcedureType),
  status: z.nativeEnum(PathologyProcedureStatus).default(PathologyProcedureStatus.PENDING),
  assignedToId: z.string(),
  assignedToName: z.string(),
  requestedById: z.string(),
  requestedByName: z.string(),
  requestDate: z.date().default(() => new Date()),
  scheduledDate: z.date().optional(),
  completionDate: z.date().optional(),
  priority: z.nativeEnum(PathologyPriority),
  notes: z.string().optional(),
  findings: z.string().optional(),
  attachments: z.array(z.string()).optional(),
  specimens: z.array(z.string()).optional(),
  location: z.string().optional(),
});

// GET handler for fetching procedures
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const bodyId = searchParams.get('bodyId');
    const workflowId = searchParams.get('workflowId');

    if (!bodyId) {
      return NextResponse.json({ error: 'Body ID is required' }, { status: 400 });
    }

    // In production, fetch from database
    // For now, return mock data based on the workflowId and bodyId
    
    // Mock procedures for the given body
    const mockProcedures = [
      {
        id: 'p1',
        bodyId: 'b1',
        workflowId: 'w1',
        procedureType: PathologyProcedureType.AUTOPSY,
        status: PathologyProcedureStatus.COMPLETED,
        assignedToId: 'u1',
        assignedToName: 'Dr. Jane Smith',
        requestedById: 'u4',
        requestedByName: 'Dr. Robert Wilson',
        requestDate: new Date('2023-06-19T15:00:00Z'),
        scheduledDate: new Date('2023-06-20T10:00:00Z'),
        completionDate: new Date('2023-06-20T14:30:00Z'),
        priority: PathologyPriority.HIGH,
        notes: 'Full autopsy required',
        findings: 'Multiple internal injuries consistent with blunt force trauma',
        attachments: ['autopsy-image-1.jpg', 'autopsy-image-2.jpg'],
        specimens: ['sp1', 'sp2'],
      },
      {
        id: 'p2',
        bodyId: 'b1',
        workflowId: 'w1',
        procedureType: PathologyProcedureType.TOXICOLOGY,
        status: PathologyProcedureStatus.IN_PROGRESS,
        assignedToId: 'u3',
        assignedToName: 'Dr. Sarah Johnson',
        requestedById: 'u1',
        requestedByName: 'Dr. Jane Smith',
        requestDate: new Date('2023-06-20T14:35:00Z'),
        scheduledDate: new Date('2023-06-22T10:00:00Z'),
        priority: PathologyPriority.MEDIUM,
        notes: 'Check for presence of alcohol and common drugs',
        specimens: ['sp3'],
      }
    ];
    
    let filteredProcedures = mockProcedures;
    
    // Filter by bodyId
    if (bodyId && bodyId !== 'b1') {
      filteredProcedures = [];
    }
    
    // Filter by workflowId if provided
    if (workflowId && workflowId !== 'w1') {
      filteredProcedures = filteredProcedures.filter(p => p.workflowId === workflowId);
    }

    return NextResponse.json(filteredProcedures);

  } catch (error) {
    console.error('Error in GET /api/pathology/procedures:', error);
    return error;
  }
}

// POST handler for creating a new procedure
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    
    // Validate request body
    const validationResult = createProcedureSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    // Create procedure in database (mock for now)
    const newProcedure = {
      id: `p${Date.now()}`,
      ...validationResult.data,
    };

    // In production, this would save to the database
    console.log('Created new procedure:', newProcedure);

    /* // Create audit log
    await createAuditLog({
      action: AuditAction.CREATE,
      details: `Created new ${data.procedureType} procedure for body ${data.bodyId}`,
      performedBy: session.user.id || 'unknown',
      resourceType: 'PATHOLOGY_PROCEDURE',
      resourceId: newProcedure.id,
    }); */

    return NextResponse.json(newProcedure, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/pathology/procedures:', error);
    return error;
  }
}

// PUT handler for updating a procedure
export async function PUT(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Procedure ID is required' }, { status: 400 });
    }

    const data = await req.json();
    
    // In production, this would update the database
    console.log(`Updated procedure ${id} with:`, data);

    // Create audit log
    /* await createAuditLog({
      action: 'UPDATE_PATHOLOGY_PROCEDURE',
      details: `Updated pathology procedure ${id}`,
      performedBy: session.user.id || 'unknown',
      resourceType: 'PATHOLOGY_PROCEDURE',
      resourceId: id,
    }); */

    return NextResponse.json({ id, ...data, updated: true });

  } catch (error) {
    console.error('Error in PUT /api/pathology/procedures:', error);
    return error;
  }
} 