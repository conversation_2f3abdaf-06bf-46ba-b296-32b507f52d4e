// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { z } from 'zod';
import { createAuditLog } from '@/lib/audit';

// Helper function for error responses
const getErrorResponse = (status: number, message: string) => {
  return NextResponse.json({ error: message }, { status });
};

// Mock data for development
const mockWorkflows = [
  {
    id: 'w1',
    bodyId: 'b1',
    type: 'STANDARD',
    status: 'IN_PROGRESS',
    priority: 'MEDIUM',
    startDate: new Date('2023-06-20T10:00:00Z'),
    estimatedCompletion: new Date('2023-06-25T10:00:00Z'),
    steps: [
      {
        id: 's1',
        name: 'Initial Examination',
        status: 'COMPLETED',
        assignedToId: 'u1',
        assignedToName: 'Dr. <PERSON>',
        startDate: new Date('2023-06-20T10:00:00Z'),
        completionDate: new Date('2023-06-20T14:30:00Z'),
      },
      {
        id: 's2',
        name: 'Specimen Collection',
        status: 'COMPLETED',
        assignedToId: 'u2',
        assignedToName: 'Dr. John Doe',
        startDate: new Date('2023-06-21T09:00:00Z'),
        completionDate: new Date('2023-06-21T11:45:00Z'),
      },
      {
        id: 's3',
        name: 'Toxicology Analysis',
        status: 'IN_PROGRESS',
        assignedToId: 'u3',
        assignedToName: 'Dr. Sarah Johnson',
        startDate: new Date('2023-06-22T10:00:00Z'),
      }
    ],
    procedures: [
      {
        id: 'p1',
        bodyId: 'b1',
        procedureType: 'AUTOPSY',
        status: 'COMPLETED',
        assignedToId: 'u1',
        assignedToName: 'Dr. Jane Smith',
        requestedById: 'u4',
        requestedByName: 'Dr. Robert Wilson',
        requestDate: new Date('2023-06-19T15:00:00Z'),
        scheduledDate: new Date('2023-06-20T10:00:00Z'),
        completionDate: new Date('2023-06-20T14:30:00Z'),
        priority: 'HIGH',
        notes: 'Full autopsy required',
        findings: 'Multiple internal injuries consistent with blunt force trauma',
        attachments: ['autopsy-image-1.jpg', 'autopsy-image-2.jpg'],
      },
      {
        id: 'p2',
        bodyId: 'b1',
        procedureType: 'TOXICOLOGY',
        status: 'IN_PROGRESS',
        assignedToId: 'u3',
        assignedToName: 'Dr. Sarah Johnson',
        requestedById: 'u1',
        requestedByName: 'Dr. Jane Smith',
        requestDate: new Date('2023-06-20T14:35:00Z'),
        scheduledDate: new Date('2023-06-22T10:00:00Z'),
        priority: 'MEDIUM',
        notes: 'Check for presence of alcohol and common drugs',
      }
    ],
    reports: [
      {
        id: 'r1',
        bodyId: 'b1',
        procedureId: 'p1',
        reportType: 'Autopsy Report',
        status: 'completed',
        pathologistId: 'u1',
        pathologistName: 'Dr. Jane Smith',
        dateCreated: new Date('2023-06-20T16:00:00Z'),
        dateCompleted: new Date('2023-06-21T11:30:00Z'),
        findings: 'Multiple internal injuries including ruptured spleen and liver laceration. Fractures to ribs 4-7 on the right side. Cranial examination reveals subdural hematoma.',
        conclusion: 'Cause of death determined to be internal bleeding due to blunt force trauma consistent with a high-speed vehicular accident.',
        attachments: ['autopsy-report-1.pdf', 'detailed-images.zip'],
        isVerified: true,
        verifiedById: 'u4',
        verifiedByName: 'Dr. Robert Wilson',
        verificationDate: new Date('2023-06-21T14:20:00Z'),
      }
    ],
    specimens: [
      {
        id: 'sp1',
        bodyId: 'b1',
        procedureId: 'p1',
        type: 'Blood Sample',
        location: 'Cardiac',
        collectedById: 'u2',
        collectedByName: 'Dr. John Doe',
        dateCollected: new Date('2023-06-20T11:15:00Z'),
        storageLocation: 'Lab Refrigerator A, Shelf 2',
        status: 'STORED',
        description: '20ml blood sample for toxicology',
        weight: 20,
        preservationMethod: 'Refrigeration',
      },
      {
        id: 'sp2',
        bodyId: 'b1',
        procedureId: 'p1',
        type: 'Tissue Sample',
        location: 'Liver',
        collectedById: 'u2',
        collectedByName: 'Dr. John Doe',
        dateCollected: new Date('2023-06-20T11:45:00Z'),
        storageLocation: 'Lab Freezer B, Drawer 3',
        status: 'STORED',
        description: 'Liver tissue sample showing trauma',
        weight: 15,
        dimensions: '3cm x 2cm x 1cm',
        preservationMethod: 'Freezing',
      }
    ],
    createdById: 'u4',
    createdByName: 'Dr. Robert Wilson',
  }
];

// GET handler for fetching workflows
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const bodyId = searchParams.get('bodyId');

    if (!bodyId) {
      return NextResponse.json({ error: 'Body ID is required' }, { status: 400 });
    }

    // In production, fetch from database
    // For now, use mock data
    let workflows = mockWorkflows;
    
    if (bodyId && bodyId !== 'b1') {
      // If bodyId doesn't match our mock data, return empty array
      workflows = [];
    }

    return NextResponse.json(workflows);

  } catch (error) {
    console.error('Error in GET /api/pathology/workflows:', error);
    return getErrorResponse(500, 'Internal server error');
  }
}

// Validation schema for workflow creation
const createWorkflowSchema = z.object({
  bodyId: z.string(),
  type: z.string(),
  status: z.string(),
  priority: z.string(),
  startDate: z.date().optional().default(() => new Date()),
  estimatedCompletion: z.date().optional(),
  steps: z.array(z.any()).optional().default([]),
  procedures: z.array(z.any()).optional().default([]),
  reports: z.array(z.any()).optional().default([]),
  specimens: z.array(z.any()).optional().default([]),
  notes: z.string().optional(),
});

// POST handler for creating a new workflow
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    
    // Validate request body
    const validationResult = createWorkflowSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    // Create workflow in database (mock for now)
    const newWorkflow = {
      id: `w${Date.now()}`,
      ...validationResult.data,
      createdById: session.user.id || 'unknown',
      createdByName: session.user.name || 'Unknown User',
    };

    // In production, this would save to the database
    console.log('Created new workflow:', newWorkflow);

    // Create audit log
    await createAuditLog({
      userId: session.user.id,
      userName: session.user.name || 'Unknown User',
      action: 'CREATE',
      details: `Created new pathology workflow for body ${data.bodyId}`,
      severity: 'info',
      resourceType: 'PATHOLOGY_WORKFLOW',
      resourceId: newWorkflow.id,
    });

    return NextResponse.json(newWorkflow, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/pathology/workflows:', error);
    return getErrorResponse(500, 'Internal server error');
  }
}

// PUT handler for updating a workflow
export async function PUT(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Workflow ID is required' }, { status: 400 });
    }

    const data = await req.json();
    
    // In production, this would update the database
    console.log(`Updated workflow ${id} with:`, data);

    // Create audit log
    await createAuditLog({
      userId: session.user.id,
      userName: session.user.name || 'Unknown User',
      action: 'UPDATE',
      details: `Updated pathology workflow ${id}`,
      severity: 'info',
      resourceType: 'PATHOLOGY_WORKFLOW',
      resourceId: id,
    });

    return NextResponse.json({ id, ...data, updated: true });

  } catch (error) {
    console.error('Error in PUT /api/pathology/workflows:', error);
    return getErrorResponse(500, 'Internal server error');
  }
} 