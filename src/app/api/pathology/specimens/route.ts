// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { z } from 'zod';

// Validation schema for specimen creation
const createSpecimenSchema = z.object({
  bodyId: z.string(),
  workflowId: z.string().optional(),
  procedureId: z.string().optional(),
  type: z.string(),
  location: z.string(),
  collectedById: z.string(),
  collectedByName: z.string(),
  dateCollected: z.date().default(() => new Date()),
  storageLocation: z.string(),
  status: z.string().default('STORED'),
  description: z.string().optional(),
  weight: z.number().optional(),
  dimensions: z.string().optional(),
  preservationMethod: z.string().optional(),
  expiryDate: z.date().optional(),
  notes: z.string().optional(),
});

// GET handler for fetching specimens
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const bodyId = searchParams.get('bodyId');
    const procedureId = searchParams.get('procedureId');
    const workflowId = searchParams.get('workflowId');

    if (!bodyId) {
      return NextResponse.json({ error: 'Body ID is required' }, { status: 400 });
    }

    // In production, fetch from database
    // For now, return mock data based on the parameters
    
    // Mock specimens for the given body
    const mockSpecimens = [
      {
        id: 'sp1',
        bodyId: 'b1',
        procedureId: 'p1',
        workflowId: 'w1',
        type: 'Blood Sample',
        location: 'Cardiac',
        collectedById: 'u2',
        collectedByName: 'Dr. John Doe',
        dateCollected: new Date('2023-06-20T11:15:00Z'),
        storageLocation: 'Lab Refrigerator A, Shelf 2',
        status: 'STORED',
        description: '20ml blood sample for toxicology',
        weight: 20,
        preservationMethod: 'Refrigeration',
      },
      {
        id: 'sp2',
        bodyId: 'b1',
        procedureId: 'p1',
        workflowId: 'w1',
        type: 'Tissue Sample',
        location: 'Liver',
        collectedById: 'u2',
        collectedByName: 'Dr. John Doe',
        dateCollected: new Date('2023-06-20T11:45:00Z'),
        storageLocation: 'Lab Freezer B, Drawer 3',
        status: 'STORED',
        description: 'Liver tissue sample showing trauma',
        weight: 15,
        dimensions: '3cm x 2cm x 1cm',
        preservationMethod: 'Freezing',
      },
      {
        id: 'sp3',
        bodyId: 'b1',
        procedureId: 'p2',
        workflowId: 'w1',
        type: 'Blood Sample',
        location: 'Peripheral',
        collectedById: 'u3',
        collectedByName: 'Dr. Sarah Johnson',
        dateCollected: new Date('2023-06-22T10:30:00Z'),
        storageLocation: 'Lab Refrigerator A, Shelf 3',
        status: 'STORED',
        description: '10ml blood sample for toxicology',
        weight: 10,
        preservationMethod: 'Refrigeration',
      }
    ];
    
    let filteredSpecimens = mockSpecimens;
    
    // Filter by bodyId
    if (bodyId && bodyId !== 'b1') {
      filteredSpecimens = [];
    }
    
    // Filter by procedureId if provided
    if (procedureId) {
      filteredSpecimens = filteredSpecimens.filter(s => s.procedureId === procedureId);
    }
    
    // Filter by workflowId if provided
    if (workflowId) {
      filteredSpecimens = filteredSpecimens.filter(s => s.workflowId === workflowId);
    }

    return NextResponse.json(filteredSpecimens);

  } catch (error) {
    console.error('Error in GET /api/pathology/specimens:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler for creating a new specimen
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    
    // Validate request body
    const validationResult = createSpecimenSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    // Create specimen in database (mock for now)
    const newSpecimen = {
      id: `sp${Date.now()}`,
      ...validationResult.data,
    };

    // In production, this would save to the database
    console.log('Created new specimen:', newSpecimen);

    // Create audit log
    /* await createAuditLog({
      action: auditUserAction
      details: `Created new ${data.type} specimen from ${data.location} for body ${data.bodyId}`,
      performedBy: session.user.id || 'unknown',
      resourceType: 'PATHOLOGY_SPECIMEN',
      resourceId: newSpecimen.id,
    }); */

    return NextResponse.json(newSpecimen, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/pathology/specimens:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT handler for updating a specimen
export async function PUT(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Specimen ID is required' }, { status: 400 });
    }

    const data = await req.json();
    
    // In production, this would update the database
    console.log(`Updated specimen ${id} with:`, data);

    // Create audit log
    /* await createAuditLog({
      action: 'UPDATE_PATHOLOGY_SPECIMEN',
      details: `Updated pathology specimen ${id}`,
      performedBy: session.user.id || 'unknown',
      resourceType: 'PATHOLOGY_SPECIMEN',
      resourceId: id,
    }); */

    return NextResponse.json({ id, ...data, updated: true });

  } catch (error) {
    console.error('Error in PUT /api/pathology/specimens:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 