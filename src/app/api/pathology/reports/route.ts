// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { z } from 'zod';


// Validation schema for report creation
const createReportSchema = z.object({
  bodyId: z.string(),
  procedureId: z.string(),
  workflowId: z.string().optional(),
  reportType: z.string(),
  status: z.string().default('draft'),
  pathologistId: z.string(),
  pathologistName: z.string(),
  dateCreated: z.date().default(() => new Date()),
  dateCompleted: z.date().optional(),
  findings: z.string(),
  conclusion: z.string(),
  attachments: z.array(z.string()).default([]),
  signatures: z.object({
    pathologist: z.string().optional(),
    reviewer: z.string().optional(),
    timestamp: z.date().optional(),
  }).optional(),
  isVerified: z.boolean().default(false),
  verifiedById: z.string().optional(),
  verifiedByName: z.string().optional(),
  verificationDate: z.date().optional(),
});

// GET handler for fetching reports
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const bodyId = searchParams.get('bodyId');
    const procedureId = searchParams.get('procedureId');
    const workflowId = searchParams.get('workflowId');
    const reportType = searchParams.get('reportType');

    if (!bodyId) {
      return NextResponse.json({ error: 'Body ID is required' }, { status: 400 });
    }

    // In production, fetch from database
    // For now, return mock data based on the parameters
    
    // Mock reports for the given body
    const mockReports = [
      {
        id: 'r1',
        bodyId: 'b1',
        procedureId: 'p1',
        workflowId: 'w1',
        reportType: 'Autopsy Report',
        status: 'completed',
        pathologistId: 'u1',
        pathologistName: 'Dr. Jane Smith',
        dateCreated: new Date('2023-06-20T16:00:00Z'),
        dateCompleted: new Date('2023-06-21T11:30:00Z'),
        findings: 'Multiple internal injuries including ruptured spleen and liver laceration. Fractures to ribs 4-7 on the right side. Cranial examination reveals subdural hematoma.',
        conclusion: 'Cause of death determined to be internal bleeding due to blunt force trauma consistent with a high-speed vehicular accident.',
        attachments: ['autopsy-report-1.pdf', 'detailed-images.zip'],
        signatures: {
          pathologist: 'Dr. Jane Smith',
          reviewer: 'Dr. Robert Wilson',
          timestamp: new Date('2023-06-21T11:30:00Z'),
        },
        isVerified: true,
        verifiedById: 'u4',
        verifiedByName: 'Dr. Robert Wilson',
        verificationDate: new Date('2023-06-21T14:20:00Z'),
      },
      {
        id: 'r2',
        bodyId: 'b1',
        procedureId: 'p2',
        workflowId: 'w1',
        reportType: 'Toxicology Report',
        status: 'draft',
        pathologistId: 'u3',
        pathologistName: 'Dr. Sarah Johnson',
        dateCreated: new Date('2023-06-22T15:30:00Z'),
        findings: 'Preliminary toxicology screening indicates presence of alcohol (BAC 0.12%) and traces of benzodiazepines.',
        conclusion: 'Further detailed analysis required to confirm specific compounds and concentrations.',
        attachments: [],
        signatures: {
          pathologist: 'Dr. Sarah Johnson',
        },
        isVerified: false,
      }
    ];
    
    let filteredReports = mockReports;
    
    // Filter by bodyId
    if (bodyId && bodyId !== 'b1') {
      filteredReports = [];
    }
    
    // Filter by procedureId if provided
    if (procedureId) {
      filteredReports = filteredReports.filter(r => r.procedureId === procedureId);
    }
    
    // Filter by workflowId if provided
    if (workflowId) {
      filteredReports = filteredReports.filter(r => r.workflowId === workflowId);
    }
    
    // Filter by reportType if provided
    if (reportType) {
      filteredReports = filteredReports.filter(r => 
        r.reportType.toLowerCase().includes(reportType.toLowerCase())
      );
    }

    return NextResponse.json(filteredReports);

  } catch (error) {
    console.error('Error in GET /api/pathology/reports:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler for creating a new report
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    
    // Validate request body
    const validationResult = createReportSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    // Create report in database (mock for now)
    const newReport = {
      id: `r${Date.now()}`,
      ...validationResult.data,
    };

    // In production, this would save to the database
    console.log('Created new report:', newReport);

    // Create audit log
    /* await createAuditLog({
      action: AuditAction.CREATE,
      details: `Created new ${data.reportType} for body ${data.bodyId}`,
      performedBy: session.user.id || 'unknown',
      resourceType: 'PATHOLOGY_REPORT',
      resourceId: newReport.id,
    }); */

    return NextResponse.json(newReport, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/pathology/reports:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT handler for updating a report
export async function PUT(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    const data = await req.json();
    
    // In production, this would update the database
    console.log(`Updated report ${id} with:`, data);

    // Create audit log
    /* await createAuditLog({
      action: AuditAction.UPDATE,
      details: `Updated pathology report ${id}`,
      performedBy: session.user.id || 'unknown',
      resourceType: 'PATHOLOGY_REPORT',
      resourceId: id,
    }); */

    return NextResponse.json({ id, ...data, updated: true });

  } catch (error) {
    console.error('Error in PUT /api/pathology/reports:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 