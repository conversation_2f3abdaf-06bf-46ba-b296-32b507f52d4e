// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { z } from "zod"

// Schema for body release validation based on Prisma schema
const bodyReleaseSchema = z.object({
  facilityId: z.string().min(1, { message: "Facility is required" }),
  bodyId: z.string().min(1, { message: "Body ID is required" }),
  status: z.enum(["PENDING", "VERIFIED", "COMPLETED", "CANCELLED"]).default("PENDING"),

  // Release details
  releasedTo: z.string().min(1, { message: "Recipient name is required" }),
  relationship: z.string().min(1, { message: "Relationship to deceased is required" }),
  identificationDocument: z.string().min(1, { message: "Identification document is required" }),
  contactDetails: z.string().min(1, { message: "Contact details are required" }),
  notes: z.string().optional(),

  // Documentation
  photoEvidence: z.string().optional(),
  signatureImage: z.string().optional(),

  // Staff assignments
  verifiedById: z.string().optional(),
  releasedById: z.string().min(1, { message: "Released by staff is required" }),
})

// Extended schema that allows persalNumber but isn't used directly in database operations
const extendedBodyReleaseSchema = bodyReleaseSchema.extend({
  persalNumber: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request body
    const validatedData = bodyReleaseSchema.parse(body)

    // Use a transaction to ensure all operations succeed or fail together
    const bodyRelease = await prisma.$transaction(async (tx) => {
      // Create new body release
      const release = await tx.bodyRelease.create({
        data: {
          facilityId: validatedData.facilityId,
          bodyId: validatedData.bodyId,
          status: validatedData.status,
          releasedTo: validatedData.releasedTo,
          relationship: validatedData.relationship,
          identificationDocument: validatedData.identificationDocument,
          contactDetails: validatedData.contactDetails,
          notes: validatedData.notes,
          photoEvidence: validatedData.photoEvidence,
          signatureImage: validatedData.signatureImage,
          verifiedById: validatedData.verifiedById,
          releasedById: validatedData.releasedById,
          releaseDate: new Date(),
        },
        include: {
          facility: true,
          body: true,
          verifiedBy: true,
          releasedBy: true,
        },
      })

      return release
    })

    // Create activity log
    /* await prisma.activityLog.create({
      data: {
        activityType: "CREATE",
        entityType: "BODY_RELEASE",
        entityId: bodyRelease.id,
        details: JSON.stringify(bodyRelease),
        userId: validatedData.releasedById,
      },
    }) */

    // If status is COMPLETED or PENDING, update body status appropriately
    if (validatedData.status === "COMPLETED") {
      await prisma.body.update({
        where: { id: validatedData.bodyId },
        data: { status: "RELEASED" },
      })

      // Create additional activity log for body status change
      /* await prisma.activityLog.create({
        data: {
          activityType: "UPDATE",
          entity: "BODY",
          entityId: validatedData.bodyId,
          details: JSON.stringify({ status: "RELEASED" }),
          userId: validatedData.releasedById,

        },
      }) */
    }
    // Note: We don't need to update the body status to PENDING_RELEASE here
    // because the body should already be in PENDING_RELEASE status (set by the pathologist)
    // before the release process begins

      // Create additional activity log for body status change
      /* await prisma.activityLog.create({
        data: {
          activityType: "UPDATE",
          entityType: "BODY",
          entityId: validatedData.bodyId,
          details: JSON.stringify({ status: "PENDING_RELEASE" }),
          userId: validatedData.releasedById,
        },
      }) */

    return NextResponse.json(bodyRelease, { status: 201 })
  } catch (error) {
    console.error("Error creating body release:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to create body release" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const facilityId = searchParams.get("facilityId")
    const bodyId = searchParams.get("bodyId")
    const status = searchParams.get("status")
    const releasedById = searchParams.get("releasedById")
    const verifiedById = searchParams.get("verifiedById")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Build filter object
    const where: any = {}
    if (facilityId) where.facilityId = facilityId
    if (bodyId) where.bodyId = bodyId
    if (status) where.status = status
    if (releasedById) where.releasedById = releasedById
    if (verifiedById) where.verifiedById = verifiedById

    // Get total count for pagination
    const total = await prisma.bodyRelease.count({ where })

    // Get body releases with pagination
    const bodyReleases = await prisma.bodyRelease.findMany({
      where,
      include: {
        facility: true,
        body: true,
        verifiedBy: true,
        releasedBy: true,
      },
      skip,
      take: limit,
      orderBy: {
        releaseDate: "desc",
      },
    })

    return NextResponse.json({
      data: bodyReleases,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching body releases:", error)
    return NextResponse.json({ error: "Failed to fetch body releases" }, { status: 500 })
  }
}

