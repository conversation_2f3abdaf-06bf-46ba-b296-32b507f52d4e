// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/prisma';
import { ReleaseStatus } from '@prisma/client';
import { z } from 'zod';
import { bodyReleaseSchema } from '@/lib/schema/releaseSchema';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import moment from 'moment';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const release = await prisma.bodyRelease.findUnique({
      where: { id: params.id },
      include: {
        body: {
          include: {
            bodyTag: true,
            collection: true,
          }
        },
        facility: {
          select: {
            id: true,
            name: true,
            code: true,
          }
        },
        verifiedBy: {
          select: {
            id: true,
            name: true,
            role: true,
          }
        },
        releasedBy: {
          select: {
            id: true,
            name: true,
            role: true,
          }
        }
      }
    });

    if (!release) {
      return NextResponse.json(
        { error: 'Release not found' },
        { status: 404 }
      );
    }

    // Get activity log
    const activityLog = await prisma.adminAudit.findMany({
      where: {
        entityType: 'RELEASE',
        entityId: release.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        action: true,
        createdAt: true,
        changes: true,
        performedBy: true,
      }
    });

    // Transform the response
    const response = {
      ...release,
      _metadata: {
        currentStatus: release.status,
        isVerified: release.status === ReleaseStatus.VERIFIED,
        lastUpdated: release.updatedAt,
        activityCount: activityLog.length,
        hasPhotos: !!(release.photoEvidence || release.signatureImage)
      },
      activityLog: activityLog.map(log => ({
        id: log.id,
        action: log.action,
        timestamp: log.createdAt,
        user: log.performedBy || 'Unknown',
        details: JSON.stringify(log.changes)
      }))
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching release details:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    const body = await request.json();

    // Validate request body
    const validatedData = bodyReleaseSchema.parse(body);

    // Update the release record
    const updatedRelease = await prisma.bodyRelease.update({
      where: { id },
      data: {
        facilityId: validatedData.facilityId,
        status: validatedData.status,
        releasedTo: validatedData.releasedTo,
        relationship: validatedData.relationship,
        identificationDocument: validatedData.identificationDocument,
        contactDetails: validatedData.contactDetails,
        notes: validatedData.notes,
        photoEvidence: validatedData.photoEvidence,
        signatureImage: validatedData.signatureImage,
        verifiedById: validatedData.verifiedById,
        releasedById: validatedData.releasedById,
        updatedAt: new Date(),
        releaseDate: moment(validatedData.releaseDate).toDate(),
      },
      include: {
        facility: true,
        body: true,
        verifiedBy: true,
        releasedBy: true,
      },
    });

    // Update body status based on release status
    if (validatedData.status === "COMPLETED") {
      await prisma.body.update({
        where: { id: updatedRelease.bodyId },
        data: { status: "RELEASED" },
      });
    }
    // Note: We don't need to update the body status to PENDING_RELEASE here
    // because the body should already be in PENDING_RELEASE status (set by the pathologist)
    // before the release process begins

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        entityType: 'RELEASE',
        entityId: id,
        action: 'UPDATE',
        performedBy: session.user?.name || session.user?.email || 'Unknown',
        changes: JSON.stringify({
          previous: body.previousData || {},
          current: updatedRelease
        }),
      }
    });

    return NextResponse.json(updatedRelease);
  } catch (error) {
    console.error('Error updating release record:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }

    if (error instanceof PrismaClientKnownRequestError) {
      if (error.code === 'P2025') {
        return NextResponse.json({ error: 'Release record not found' }, { status: 404 });
      }
    }

    return NextResponse.json({ error: 'Failed to update release record' }, { status: 500 });
  }
}