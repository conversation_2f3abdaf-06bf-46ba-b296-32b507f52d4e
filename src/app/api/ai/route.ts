// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { z } from 'zod';
import { openai } from '@ai-sdk/openai';
import { generateText } from 'ai';
import { prisma } from '@/lib/prisma';

// Tool Schemas
const BodyAdmissionSchema = z.object({
  facilityId: z.string(),
  bodyId: z.string(),
  admissionType: z.enum(['INITIAL', 'POST_REFERRAL', 'TRANSFER']),
  bodyCondition: z.string().optional(),
  notes: z.string().optional()
});

const ReleaseRequestSchema = z.object({
  bodyId: z.string(),
  releasedTo: z.string(),
  relationship: z.string(),
  identificationDocument: z.string()
});

const ReferralSchema = z.object({
  bodyId: z.string(),
  referralType: z.enum(['LODOX', 'XRAY', 'SPECIMEN', 'OTHER']),
  notes: z.string().optional()
});

const CollectionFormSchema = z.object({
  employeeName: z.string().min(2, "Employee name is required"),
  collectionType: z.enum(['CRIME_SCENE', 'HOSPITAL', 'OTHER']),
  location: z.string().min(1, "Location is required"),
  bodyCondition: z.string().optional(),
  specialInstructions: z.string().optional()
});

const ReferralFormSchema = z.object({
  bodyId: z.string().min(1, "Body ID is required"),
  referralType: z.enum(['LODOX', 'XRAY', 'SPECIMEN', 'OTHER']),
  urgency: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  notes: z.string().optional()
});

const ReleaseFormSchema = z.object({
  bodyId: z.string().min(1, "Body ID is required"),
  releasedTo: z.string().min(2, "Released to name is required"),
  relationship: z.string().min(1, "Relationship is required"),
  identificationDocument: z.string().min(1, "Identification document is required")
});

// AI Tools
export async function createBodyAdmissionSuggestion(input: z.infer<typeof BodyAdmissionSchema>) {
  try {
    // Check facility exists
    const facility = await prisma.facility.findUnique({
      where: { id: input.facilityId }
    });
    if (!facility) {
      throw new Error('Facility not found');
    }

    // Use AI to generate enhanced admission details
    const result = await generateText({
      model: openai('gpt-4o'),
      prompt: `Enhance the body admission details for a pathology management system:
        - Facility: ${facility.name}
        - Admission Type: ${input.admissionType}
        - Current Body Condition: ${input.bodyCondition || 'Not specified'}
        
        Provide:
        1. Detailed condition assessment
        2. Recommended initial processing steps
        3. Potential medical considerations
        4. Suggested storage requirements`,
      max_tokens: 300
    });

    return {
      aiSuggestions: result.text,
      admissionData: input
    };
  } catch (error) {
    console.error('AI Admission Suggestion Error:', error);
    throw error;
  }
}

export async function analyzeBodyReleaseRisk(input: z.infer<typeof ReleaseRequestSchema>) {
  try {
    // Fetch body and related information
    const body = await prisma.body.findUnique({
      where: { id: input.bodyId },
      include: {
        admissions: true,
        collections: true
      }
    });

    if (!body) {
      throw new Error('Body not found');
    }

    // AI-powered risk assessment
    const result = await generateText({
      model: openai('gpt-4o'),
      prompt: `Perform a comprehensive risk assessment for body release:
        - Deceased Background: ${body.admissions.map(a => a.notes).join(', ') || 'No additional notes'}
        - Collection Details: ${body.collections.map(c => c.collectionType).join(', ')}
        - Released To: ${input.releasedTo}
        - Relationship: ${input.relationship}
        
        Assess:
        1. Potential legal or ethical release concerns
        2. Verification recommendations
        3. Additional documentation needed
        4. Sensitivity of release process`,
      max_tokens: 300
    });

    return {
      riskAssessment: result.text,
      releaseRequest: input
    };
  } catch (error) {
    console.error('AI Release Risk Assessment Error:', error);
    throw error;
  }
}

export async function generateReferralRecommendations(input: z.infer<typeof ReferralSchema>) {
  try {
    // Fetch body details
    const body = await prisma.body.findUnique({
      where: { id: input.bodyId },
      include: {
        admissions: true,
        collections: true
      }
    });

    if (!body) {
      throw new Error('Body not found');
    }

    // AI-powered referral insights
    const result = await generateText({
      model: openai('gpt-4o'),
      prompt: `Generate specialized referral recommendations:
        - Referral Type: ${input.referralType}
        - Body Background: ${body.admissions.map(a => a.notes).join(', ') || 'No admission notes'}
        - Collection Context: ${body.collections.map(c => c.collectionType).join(', ')}
        
        Provide:
        1. Specialized referral routing recommendations
        2. Potential additional tests or examinations
        3. Interdepartmental communication suggestions
        4. Priority and urgency assessment`,
      max_tokens: 300
    });

    return {
      referralInsights: result.text,
      referralRequest: input
    };
  } catch (error) {
    console.error('AI Referral Recommendation Error:', error);
    throw error;
  }
}

export async function predictBodyStorageNeeds() {
  try {
    // Fetch current facility occupancy and body statuses
    const facilities = await prisma.facility.findMany({
      include: {
        _count: {
          select: { 
            admissions: true,
            fridges: true 
          }
        }
      }
    });

    // AI-powered predictive analysis
    const result = await generateText({
      model: openai('gpt-4o'),
      prompt: `Analyze facility storage needs and predict future requirements:
        Facilities Data:
        ${facilities.map(f => `
        - Facility: ${f.name}
        - Total Admissions: ${f._count.admissions}
        - Available Fridges: ${f._count.fridges}
        - Facility Type: ${f.type}
        `).join('\n')}
        
        Provide:
        1. Storage capacity predictions
        2. Recommended fridge allocation strategy
        3. Potential capacity bottlenecks
        4. Optimization suggestions`,
      max_tokens: 300
    });

    return {
      storagePredictions: result.text,
      facilitiesData: facilities
    };
  } catch (error) {
    console.error('AI Storage Needs Prediction Error:', error);
    throw error;
  }
}

export async function generateCollectionAssistance(input: z.infer<typeof CollectionFormSchema>) {
  try {
    const result = await generateText({
      model: openai('gpt-4o'),
      prompt: `Provide intelligent assistance for body collection:
        - Employee: ${input.employeeName}
        - Collection Type: ${input.collectionType}
        - Location: ${input.location}
        - Body Condition: ${input.bodyCondition || 'Not specified'}
        - Special Instructions: ${input.specialInstructions || 'None'}

        Generate:
        1. Recommended collection protocol
        2. Potential safety precautions
        3. Documentation tips
        4. Preservation suggestions based on body condition`,
      max_tokens: 300
    });

    return {
      collectionGuidance: result.text,
      input
    };
  } catch (error) {
    console.error('AI Collection Assistance Error:', error);
    throw error;
  }
}

export async function generateReferralInsights(input: z.infer<typeof ReferralFormSchema>) {
  try {
    // Fetch body details for context
    const body = await prisma.body.findUnique({
      where: { id: input.bodyId },
      include: {
        admissions: true,
        collections: true
      }
    });

    const result = await generateText({
      model: openai('gpt-4o'),
      prompt: `Generate specialized referral insights:
        - Referral Type: ${input.referralType}
        - Urgency: ${input.urgency}
        - Body Background: ${body?.admissions.map(a => a.notes).join(', ') || 'No admission notes'}
        - Collection Context: ${body?.collections.map(c => c.collectionType).join(', ') || 'No collection details'}

        Provide:
        1. Recommended referral routing
        2. Potential additional examinations
        3. Interdepartmental communication strategy
        4. Priority assessment and handling`,
      max_tokens: 300
    });

    return {
      referralRecommendations: result.text,
      input,
      bodyContext: body
    };
  } catch (error) {
    console.error('AI Referral Insights Error:', error);
    throw error;
  }
}

export async function generateReleaseRecommendations(input: z.infer<typeof ReleaseFormSchema>) {
  try {
    // Fetch body details for comprehensive analysis
    const body = await prisma.body.findUnique({
      where: { id: input.bodyId },
      include: {
        admissions: true,
        collections: true,
        referrals: true
      }
    });

    const result = await generateText({
      model: openai('gpt-4o'),
      prompt: `Comprehensive body release risk assessment:
        - Released To: ${input.releasedTo}
        - Relationship: ${input.relationship}
        - Identification Document: ${input.identificationDocument}
        - Body History: 
          * Admissions: ${body?.admissions.map(a => a.notes).join(', ') || 'No admission notes'}
          * Collections: ${body?.collections.map(c => c.collectionType).join(', ') || 'No collection details'}
          * Referrals: ${body?.referrals.map(r => r.status).join(', ') || 'No referral history'}

        Generate:
        1. Detailed release risk assessment
        2. Verification recommendations
        3. Potential legal or ethical considerations
        4. Sensitivity and confidentiality guidance`,
      max_tokens: 300
    });

    return {
      releaseRecommendations: result.text,
      input,
      bodyContext: body
    };
  } catch (error) {
    console.error('AI Release Recommendations Error:', error);
    throw error;
  }
}

// Main Route Handler
export async function POST(req: Request) {
  try {
    const { tool, input } = await req.json();

    switch (tool) {
      case 'createBodyAdmissionSuggestion':
        const admissionInput = BodyAdmissionSchema.parse(input);
        return Response.json(await createBodyAdmissionSuggestion(admissionInput));
      
      case 'analyzeBodyReleaseRisk':
        const releaseInput = ReleaseRequestSchema.parse(input);
        return Response.json(await analyzeBodyReleaseRisk(releaseInput));
      
      case 'generateReferralRecommendations':
        const referralInput = ReferralSchema.parse(input);
        return Response.json(await generateReferralRecommendations(referralInput));
      
      case 'predictBodyStorageNeeds':
        return Response.json(await predictBodyStorageNeeds());
      
      case 'generateCollectionAssistance':
        const collectionInput = CollectionFormSchema.parse(input);
        return Response.json(await generateCollectionAssistance(collectionInput));
      
      case 'generateReferralInsights':
        const referralFormInput = ReferralFormSchema.parse(input);
        return Response.json(await generateReferralInsights(referralFormInput));
      
      case 'generateReleaseRecommendations':
        const releaseFormInput = ReleaseFormSchema.parse(input);
        return Response.json(await generateReleaseRecommendations(releaseFormInput));
      
      default:
        return Response.json({ error: 'Invalid tool selected' }, { status: 400 });
    }
  } catch (error) {
    console.error('AI Route Handler Error:', error);
    return Response.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}
