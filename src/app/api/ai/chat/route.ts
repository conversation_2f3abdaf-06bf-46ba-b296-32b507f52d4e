// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;
export const runtime = 'edge';

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('AI Chat Route Error:', error);
    return new Response(JSON.stringify({
      error: 'An unexpected error occurred during AI chat processing'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
