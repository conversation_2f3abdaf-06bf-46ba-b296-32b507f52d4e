// Force dynamic rendering for API routes
export const dynamic = "force-dynamic";


import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import {
  createBodyCollectionSchema,
  updateBodyCollectionSchema
} from '@/lib/schema/bodyCollectionSchema';
import { z } from 'zod'
import { BodyStatus, TagStatus } from '@prisma/client'

// GET /api/collections - Get all collections
export async function GET(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')

    // Build where clause based on filters
    const where: any = {}
    if (type) where.collectionType = type
    if (status) where.status = status
    if (dateFrom) where.createdAt = { gte: new Date(dateFrom) }
    if (dateTo) where.createdAt = { lte: new Date(dateTo) }

    const collections = await prisma.bodyCollection.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        body: {
          select: {
            id: true,
            trackingNumber: true,
            status: true,
            bodyTag: {
              select: {
                tagNumber: true,
                status: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json(collections)
  } catch (error) {
    console.error('Error fetching collections:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST /api/collections - Create a new collection
export async function POST(request: Request) {

  try {
    const session = await auth();
    if (!session || !['FIELD_EMPLOYEE', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({
        error: 'Unauthorized',
        details: 'User lacks required permissions'
      }, { status: 401 });
    }

    const body = await request.json();
    // Validate the request body against our schema
    let validatedData;
    try {
      validatedData = createBodyCollectionSchema.parse(body);
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json({
          error: 'Validation Error',
          details: validationError.errors.map(e => ({
            path: e.path.join('.'),
            message: e.message
          }))
        }, { status: 400 });
      }
      throw validationError; // Re-throw if it's not a ZodError
    }

    // Ensure bodyTagId is provided
    if (!validatedData.bodyTagId) {
      return NextResponse.json({
        error: 'Missing Body Tag',
        details: 'A body tag ID is required for collection'
      }, { status: 400 });
    }

    // Check if body tag exists and is available
    const existingTag = await prisma.bodyTag.findUnique({
      where: { id: validatedData.bodyTagId },
      select: { status: true, id: true, tagNumber: true, bodyId: true }
    });

    if (!existingTag) {
      return NextResponse.json({
        error: 'Invalid Body Tag',
        details: 'The selected body tag does not exist'
      }, { status: 400 });
    }

    if (existingTag.status !== 'GENERATED') {
      return NextResponse.json({
        error: 'Invalid Body Tag',
        details: `Body tag ${existingTag.tagNumber} is already in use or not available (status: ${existingTag.status})`
      }, { status: 400 });
    }

    // Check if the body tag is already associated with a body
    if (existingTag.bodyId) {
      return NextResponse.json({
        error: 'Invalid Body Tag',
        details: `Body tag ${existingTag.tagNumber} is already associated with a body`
      }, { status: 400 });
    }

    // Check if there's already a body with this tag ID
    const existingBody = await prisma.body.findUnique({
      where: { bodyTagId: validatedData.bodyTagId }
    });

    if (existingBody) {
      return NextResponse.json({
        error: 'Invalid Body Tag',
        details: `Body tag ${existingTag.tagNumber} is already associated with body ID: ${existingBody.id}`
      }, { status: 400 });
    }

    // Process the collection in a transaction to ensure data consistency
    try {
      const result = await prisma.$transaction(async (tx) => {
        // Generate a unique tracking number
        const trackingNumber = `TRK-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

        // 1. Create Body record
        const body = await tx.body.create({
          data: {
            trackingNumber,
            status: BodyStatus.COLLECTED,
            bodyTagId: validatedData.bodyTagId,
            metadata: {
              bodyCondition: validatedData.bodyCondition,
              bodyDescription: validatedData.bodyDescription,
              gpsCoords: validatedData.gpsCoords,
              persalNumber: validatedData.persalNumber,
              collectionDate: new Date().toISOString(),
            },
          }
        });

        // 2. Create Collection record
        const collection = await tx.bodyCollection.create({
          data: {
            bodyId: body.id,
            userId: session.user.id,
            name: validatedData.name,
            institution: validatedData.institution,
            vehicleReg: validatedData.vehicleReg,
            arrivalTime: new Date(validatedData.arrivalTime),
            gpsCoords: validatedData.gpsCoords,
            collectionType: validatedData.bodyCollectionType || validatedData.collectionType,
            bodyDescription: validatedData.bodyDescription,
            sceneDescription: validatedData.sceneDescription || null,
            weatherConditions: validatedData.weatherConditions || null,
            temperature: validatedData.temperature || null,
            collectionNotes: validatedData.notes || validatedData.collectionNotes || null,
            photos: validatedData.bodyPhotos?.filter(photo => photo !== undefined && photo !== null) || validatedData.photos || [],
            documents: validatedData.documents || [],
            handedOverBy: validatedData.handedOverBy || null,
            handedOverRole: validatedData.handedOverRole || null,
            handedOverContact: validatedData.handedOverContact || null,
            handoverNotes: validatedData.handoverNotes || null,
            status: BodyStatus.COLLECTED,
            barcodeValue: existingTag.tagNumber, // Use the actual tag number, not the ID
            metadata: {
              persalNumber: validatedData.persalNumber,
              bodyCondition: validatedData.bodyCondition,
              isComplete: validatedData.isComplete || false,
              notes: validatedData.notes || validatedData.collectionNotes,
              createdBy: session.user.name || session.user.email,
              createdById: session.user.id,
              trackingNumber
            }
          }
        });

        // 3. Update Body Tag status
        await tx.bodyTag.update({
          where: { id: validatedData.bodyTagId },
          data: {
            status: TagStatus.COLLECTED,
            bodyId: body.id,
            lastScannedAt: new Date(),
            lastScannedBy: session.user.id,
            collectionId: collection.id
          }
        });

        // 4. Create audit log entry
        await tx.auditLog.create({
          data: {
            action: 'CREATE_COLLECTION',
            details: `Created collection for body ${body.id} with tracking number ${trackingNumber}`,
            user: session.user.name || session.user.email || 'Unknown User',
            performedBy: session.user.id,
            resourceType: 'COLLECTION',
            resourceId: collection.id,
            severity: 'info'
          }
        });

        return {
          ...collection,
          body,
          trackingNumber,
          bodyTag: existingTag
        };
      });

      return NextResponse.json({
        data: result,
        success: true,
        message: 'Collection created successfully'
      }, { status: 201 });

    } catch (transactionError) {
      console.error('Transaction Error:', transactionError);
      return NextResponse.json({
        error: 'Failed to create collection',
        details: transactionError instanceof Error ? transactionError.message : 'Database transaction failed'
      }, { status: 500 });
    }

    // This return statement is unreachable due to the try/catch block above

  } catch (error) {
    console.error('Collection Creation Error:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation Error',
        success: false,
        details: error.errors.map(e => ({
          path: e.path.join('.'),
          message: e.message
        }))
      }, { status: 400 });
    }

    // Handle database errors
    if (error instanceof Error && error.message.includes('Prisma')) {
      return NextResponse.json({
        error: 'Database Error',
        success: false,
        message: 'Failed to create collection due to a database error',
        details: error.message
      }, { status: 500 });
    }

    // Handle all other errors
    return NextResponse.json({
      error: 'Internal Server Error',
      success: false,
      message: 'An unexpected error occurred while creating the collection',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT /api/collections - Update a collection
export async function PUT(request: Request) {
  try {
    const session = await auth()
    if (!session || !['FIELD_EMPLOYEE', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = body

    // Validate update data
    const validatedData = updateBodyCollectionSchema.parse({ id, ...updateData })

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Check if collection exists and user has permission
      const existingCollection = await tx.bodyCollection.findUnique({
        where: { id },
        include: { user: true }
      })

      if (!existingCollection) {
        throw new Error('Collection not found')
      }

      if (session.user.role !== 'ADMIN' && existingCollection.userId !== session.user.id) {
        throw new Error('Not authorized to update this collection')
      }

      // Update collection
      const collection = await tx.bodyCollection.update({
        where: { id },
        data: {
          ...validatedData,
          updatedAt: new Date()
        }
      })

      // Create audit log
      /* await tx.adminAudit.create({
        data: {
          action: 'UPDATE_COLLECTION',
          entityType: 'COLLECTION',
          entityId: id,
          performedBy: session.user.id,
          changes: validatedData
        }
      }) */

      return collection
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error updating collection:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    )
  }
}
