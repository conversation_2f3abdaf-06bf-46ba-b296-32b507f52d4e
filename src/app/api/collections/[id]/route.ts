// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { BodyStatus } from '@prisma/client';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const collection = await prisma.bodyCollection.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true,
            department: true,
            facility: {
              select: {
                name: true,
                code: true,
                type: true
              }
            }
          }
        },
        body: {
          include: {
            admissions: {
              orderBy: { createdAt: 'desc' },
              take: 1,
              include: {
                facility: true,
                assignedTo: {
                  select: {
                    id: true,
                    name: true,
                    role: true,
                    department: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!collection) {
      return NextResponse.json(
        { error: 'Collection not found' },
        { status: 404 }
      );
    }

    // Get activity log
    const activityLog = await prisma.adminAudit.findMany({
      where: {
        entityType: 'COLLECTION',
        entityId: collection.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        action: true,
        createdAt: true,
        changes: true,
        performedBy: true,
      }
    });

    // Transform the response
    const response = {
      ...collection,
      _metadata: {
        currentStatus: collection.body?.status || BodyStatus.COLLECTED,
        isAdmitted: collection.body?.admissions.length > 0,
        lastUpdated: collection.updatedAt,
        activityCount: activityLog.length,
        hasPhotos: collection.photos && collection.photos.length > 0
      },
      activityLog: activityLog.map(log => ({
        id: log.id,
        action: log.action,
        timestamp: log.createdAt,
        user: log.performedBy || 'Unknown',
        details: JSON.stringify(log.changes)
      }))
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching collection details:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session || !['FIELD_EMPLOYEE', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the collection ID from params
    const collectionId = params.id;

    // Get the existing collection to compare changes
    const existingCollection = await prisma.bodyCollection.findUnique({
      where: { id: collectionId },
      include: {
        body: true
      }
    });

    if (!existingCollection) {
      return NextResponse.json({ error: 'Collection not found' }, { status: 404 });
    }

    // Parse the request body
    const body = await request.json();
    console.log('Update request body:', body);

    // Note: The body should contain fields that match the BodyCollection model
    // For notes, use 'collectionNotes' field, not 'notes'

    // Handle the case where 'notes' is passed instead of 'collectionNotes'
    if (body.notes !== undefined && body.collectionNotes === undefined) {
      body.collectionNotes = body.notes;
      delete body.notes; // Remove the notes field to avoid Prisma errors
    }

    // Extract fields we don't want to directly update
    const { status, bodyTagId, ...updateData } = body;

    // Prepare metadata - merge with existing metadata
    const metadata = {
      ...(existingCollection.metadata as any || {}),
      ...(updateData.metadata || {}),
      lastUpdated: new Date().toISOString(),
      updatedBy: session.user.name || session.user.email,
      updatedById: session.user.id
    };

    // Log the data being sent to Prisma for debugging
    console.log('Updating collection with data:', {
      ...updateData,
      metadata,
      updatedAt: new Date()
    });

    // Update the collection
    const updatedCollection = await prisma.$transaction(async (tx) => {
      // Update the collection record
      const collection = await tx.bodyCollection.update({
        where: { id: collectionId },
        data: {
          ...updateData,
          metadata,
          updatedAt: new Date()
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              role: true,
              department: true,
              facility: {
                select: {
                  name: true,
                  code: true,
                  type: true
                }
              }
            }
          },
          body: {
            include: {
              admissions: {
                orderBy: { createdAt: 'desc' },
                take: 1,
                include: {
                  facility: true,
                  assignedTo: {
                    select: {
                      id: true,
                      name: true,
                      role: true,
                      department: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      // If there's a body associated with this collection, update its metadata too
      if (collection.body) {
        await tx.body.update({
          where: { id: collection.body.id },
          data: {
            metadata: {
              ...(collection.body.metadata as any || {}),
              lastUpdated: new Date().toISOString(),
              bodyDescription: updateData.bodyDescription || collection.bodyDescription,
              bodyCondition: metadata.bodyCondition
            }
          }
        });
      }

      // Create audit log
      await tx.adminAudit.create({
        data: {
          action: 'UPDATE_COLLECTION',
          entityType: 'COLLECTION',
          entityId: collectionId,
          performedBy: session.user.id,
          changes: {
            before: {
              name: existingCollection.name,
              institution: existingCollection.institution,
              vehicleReg: existingCollection.vehicleReg,
              bodyDescription: existingCollection.bodyDescription,
              collectionType: existingCollection.collectionType,
              photos: existingCollection.photos?.length || 0
            },
            after: {
              name: updateData.name,
              institution: updateData.institution,
              vehicleReg: updateData.vehicleReg,
              bodyDescription: updateData.bodyDescription,
              collectionType: updateData.collectionType,
              photos: updateData.photos?.length || 0
            }
          },
        },
      });

      return collection;
    });

    return NextResponse.json({
      data: updatedCollection,
      success: true,
      message: 'Collection updated successfully'
    });
  } catch (error) {
    console.error('Error updating collection:', error);

    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Collection not found', success: false },
        { status: 404 }
      );
    }

    // Check if it's a Prisma validation error
    if (error.name === 'PrismaClientValidationError') {
      console.error('Prisma validation error:', error.message);
      return NextResponse.json(
        {
          error: 'Validation Error',
          success: false,
          message: 'Invalid data format. Please check field names match the database schema.',
          details: error.message
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        success: false,
        message: error instanceof Error ? error.message : 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session || !['ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if collection exists
    const collection = await prisma.bodyCollection.findUnique({
      where: { id: params.id },
      include: {
        body: true
      }
    });

    if (!collection) {
      return NextResponse.json(
        { error: 'Collection not found' },
        { status: 404 }
      );
    }

    // Check if body has admissions
    if (collection.body) {
      const hasAdmissions = await prisma.bodyAdmission.count({
        where: { bodyId: collection.body.id }
      });

      if (hasAdmissions > 0) {
        return NextResponse.json(
          { error: 'Cannot delete collection with existing admissions' },
          { status: 400 }
        );
      }
    }

    // Start transaction
    await prisma.$transaction(async (tx) => {
      // Delete collection
      await tx.bodyCollection.delete({
        where: { id: params.id }
      });

      // Create audit log
      await tx.adminAudit.create({
        data: {
          action: 'DELETE_COLLECTION',
          entityType: 'COLLECTION',
          entityId: params.id,
          performedBy: session.user.id,
          changes: { id: params.id }
        }
      });
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting collection:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}