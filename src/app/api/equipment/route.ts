// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'

// GET /api/equipment - Get all equipment
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const equipment = await prisma.equipment.findMany({
      include: {
        facility: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return NextResponse.json(equipment)
  } catch (error) {
    console.error('Error fetching equipment:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST /api/equipment - Create new equipment
export async function POST(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      type,
      serialNumber,
      facilityId,
      location,
      status,
      purchaseDate,
      warrantyEnd,
    } = body

    // Basic validation
    if (!name || !type || !facilityId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check if serial number is unique if provided
    if (serialNumber) {
      const existing = await prisma.equipment.findUnique({
        where: { serialNumber },
      })
      if (existing) {
        return NextResponse.json(
          { error: 'Serial number already exists' },
          { status: 400 }
        )
      }
    }

    const equipment = await prisma.equipment.create({
      data: {
        name,
        type,
        serialNumber,
        facilityId,
        location,
        status: status || 'ACTIVE',
        purchaseDate,
        warrantyEnd,
      },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'CREATE_EQUIPMENT',
        entityType: 'EQUIPMENT',
        entityId: equipment.id,
        performedBy: session.user.id,
        changes: body,
      },
    })

    return NextResponse.json(equipment)
  } catch (error) {
    console.error('Error creating equipment:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT /api/equipment - Update equipment
export async function PUT(request: Request) {
  try {
    const session = await auth()
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = body

    // If updating serial number, check uniqueness
    if (updateData.serialNumber) {
      const existing = await prisma.equipment.findUnique({
        where: { serialNumber: updateData.serialNumber },
      })
      if (existing && existing.id !== id) {
        return NextResponse.json(
          { error: 'Serial number already exists' },
          { status: 400 }
        )
      }
    }

    const equipment = await prisma.equipment.update({
      where: { id },
      data: updateData,
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_EQUIPMENT',
        entityType: 'EQUIPMENT',
        entityId: id,
        performedBy: session.user.id,
        changes: updateData,
      },
    })

    return NextResponse.json(equipment)
  } catch (error) {
    console.error('Error updating equipment:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}