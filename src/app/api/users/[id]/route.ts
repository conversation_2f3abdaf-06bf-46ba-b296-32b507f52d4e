// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'

// GET /api/users/[id] - Get a specific user
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only allow users to view their own profile or admins to view any profile
    if (session.user.id !== params.id && session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        persalNumber: true,
        department: true,
        position: true,
        phoneNumber: true,
        address: true,
        emergencyContact: true,
        dateOfBirth: true,
        hireDate: true,
        facility: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PATCH /api/users/[id] - Update a specific user
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only allow users to update their own profile or admins to update any profile
    if (session.user.id !== params.id && session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      email,
      role,
      status,
      persalNumber,
      department,
      position,
      phoneNumber,
      address,
      emergencyContact,
      dateOfBirth,
      hireDate,
      facilityId,
    } = body

    // If not admin, restrict which fields can be updated
    const updateData = session.user.role === 'ADMIN'
      ? {
          name,
          email,
          role,
          status,
          persalNumber,
          department,
          position,
          phoneNumber,
          address,
          emergencyContact,
          dateOfBirth,
          hireDate,
          facilityId,
        }
      : {
          name,
          phoneNumber,
          address,
          emergencyContact,
        }

    // Remove undefined values
    Object.keys(updateData).forEach(
      key => updateData[key] === undefined && delete updateData[key]
    )

    const user = await prisma.user.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        persalNumber: true,
        department: true,
        position: true,
        phoneNumber: true,
        address: true,
        emergencyContact: true,
        dateOfBirth: true,
        hireDate: true,
        facility: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_USER',
        entityType: 'USER',
        entityId: params.id,
        performedBy: session.user.id,
        changes: updateData,
      },
    })

    return NextResponse.json(user)
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// DELETE /api/users/[id] - Delete a specific user
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: { id: true },
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Delete user
    await prisma.user.delete({
      where: { id: params.id },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'DELETE_USER',
        entityType: 'USER',
        entityId: params.id,
        performedBy: session.user.id,
        changes: { id: params.id },
      },
    })

    return NextResponse.json({ message: 'User deleted successfully' })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 