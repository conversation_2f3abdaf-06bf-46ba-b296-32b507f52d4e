// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { withPermissions } from '@/lib/permissions';
import { auditUserAction } from '@/lib/audit';

// Schema for validating role assignment requests
const RoleAssignmentSchema = z.object({
  roleIds: z.array(z.string())
});

// GET /api/users/[id]/roles - Get roles for a user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id;
    
    // Get user with their roles
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        roles: {
          include: {
            permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Format roles with their permissions
    const roles = user.roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        description: rp.permission.description,
        category: rp.permission.category
      }))
    }));

    return NextResponse.json(roles);
  } catch (error) {
    console.error('Error fetching user roles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user roles' },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id]/roles - Update roles for a user
export const PUT = withPermissions(
  async (req: Request, { params }: { params: { id: string } }) => {
    try {
      const userId = params.id;
      const body = await req.json();

      // Validate request body
      const { roleIds } = RoleAssignmentSchema.parse(body);

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { roles: true }
      });

      if (!user) {
        return new Response(JSON.stringify({ error: 'User not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Get current role IDs for audit log
      const currentRoleIds = user.roles.map(role => role.id);

      // Update user roles (disconnect all existing roles and connect new ones)
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          roles: {
            disconnect: currentRoleIds.map(id => ({ id })),
            connect: roleIds.map(id => ({ id }))
          }
        },
        include: {
          roles: true
        }
      });

      // Log the role change
      await auditUserAction({
        userId: user.id,
        userName: user.name || 'Unknown',
        action: 'UPDATE',
        resourceType: 'user_roles',
        resourceId: userId,
        details: `User roles updated from [${currentRoleIds.join(', ')}] to [${roleIds.join(', ')}]`
      });

      return new Response(JSON.stringify({
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        roles: updatedUser.roles.map(role => ({
          id: role.id,
          name: role.name
        }))
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('Error updating user roles:', error);
      
      if (error instanceof z.ZodError) {
        return new Response(JSON.stringify({
          error: 'Invalid request data',
          details: error.errors
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      return new Response(JSON.stringify({
        error: 'Failed to update user roles'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  },
  ['users.manage', 'roles.assign']
); 