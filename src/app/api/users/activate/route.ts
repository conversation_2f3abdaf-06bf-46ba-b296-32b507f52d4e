// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { logger } from '@/utils/logger';

// Input validation schema
const activationSchema = z.object({
  token: z.string().min(5, "Invalid activation token"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input
    const validationResult = activationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation Failed',
          details: validationResult.error.flatten().fieldErrors
        },
        { status: 400 }
      );
    }

    const { token, password } = validationResult.data;

    // Find invitation by token
    const invitation = await prisma.userInvitation.findUnique({
      where: { token },
    });

    if (!invitation) {
      logger.warn(`Account activation failed: Invalid token ${token.substring(0, 5)}...`);
      return NextResponse.json(
        { error: 'Invalid or expired activation token' },
        { status: 400 }
      );
    }

    // Check if invitation is expired
    if (invitation.expiresAt < new Date()) {
      logger.warn(`Account activation failed: Expired token for ${invitation.email}`);
      return NextResponse.json(
        { error: 'Activation token has expired' },
        { status: 400 }
      );
    }

    // Check if invitation has already been used
    if (invitation.status === 'ACCEPTED') {
      logger.warn(`Account activation failed: Token already used for ${invitation.email}`);
      return NextResponse.json(
        { error: 'This invitation has already been accepted' },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the user account
    const user = await prisma.user.create({
      data: {
        name: invitation.name,
        email: invitation.email,
        password: hashedPassword,
        role: invitation.role,
        status: 'ACTIVE',
        department: invitation.metadata?.department,
        position: invitation.metadata?.position,
        facilityId: invitation.facilityId,
      },
    });

    // Update invitation status
    await prisma.userInvitation.update({
      where: { id: invitation.id },
      data: {
        status: 'ACCEPTED',
        acceptedAt: new Date(),
        user: {
          connect: {
            id: user.id,
          },
        },
      },
    });

    // Create an audit log
    await prisma.adminAudit.create({
      data: {
        action: 'ACCOUNT_ACTIVATED',
        entityType: 'USER',
        entityId: user.id,
        performedBy: user.id,
        changes: { activationMethod: 'INVITATION' },
      },
    });

    // Create a notification for the user
    await prisma.notification.create({
      data: {
        userId: user.id,
        title: 'Welcome to GP Pathology',
        message: 'Your account has been successfully activated. Welcome to the platform!',
        type: 'WELCOME',
      },
    });

    logger.info(`Account activated successfully for ${user.email}`);
    return NextResponse.json(
      {
        success: true,
        message: 'Account activated successfully',
        userId: user.id,
      },
      { status: 200 }
    );

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error(`Error activating account: ${errorMessage}`, { error });
    return NextResponse.json(
      { error: 'Failed to activate account. Please try again or contact support.' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const token = request.nextUrl.searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'Activation token is required' },
        { status: 400 }
      );
    }

    // Find invitation by token
    const invitation = await prisma.userInvitation.findUnique({
      where: { token },
      include: {
        sentBy: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!invitation) {
      logger.warn(`Token validation failed: Invalid token ${token.substring(0, 5)}...`);
      return NextResponse.json(
        {
          valid: false,
          error: 'Invalid activation token'
        },
        { status: 400 }
      );
    }

    if (invitation.expiresAt < new Date()) {
      logger.warn(`Token validation failed: Expired token for ${invitation.email}`);
      return NextResponse.json(
        {
          valid: false,
          error: 'Activation token has expired',
          email: invitation.email,
        },
        { status: 400 }
      );
    }

    if (invitation.status === 'ACCEPTED') {
      logger.warn(`Token validation failed: Token already used for ${invitation.email}`);
      return NextResponse.json(
        {
          valid: false,
          error: 'This invitation has already been accepted',
          email: invitation.email,
        },
        { status: 400 }
      );
    }

    // Calculate expiry time in hours
    const now = new Date();
    const expiryHours = Math.round((invitation.expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60));

    logger.info(`Token validated successfully for ${invitation.email}`);
    return NextResponse.json({
      valid: true,
      email: invitation.email,
      name: invitation.name,
      role: invitation.role,
      facilityId: invitation.facilityId,
      department: invitation.metadata?.department,
      position: invitation.metadata?.position,
      invitedBy: invitation.sentBy?.name || 'Administrator',
      expiresIn: expiryHours > 0 ? `${expiryHours} hours` : 'less than an hour',
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error(`Error verifying activation token: ${errorMessage}`, { error });
    return NextResponse.json(
      {
        valid: false,
        error: 'Failed to verify activation token. Please try again or contact support.'
      },
      { status: 500 }
    );
  }
}