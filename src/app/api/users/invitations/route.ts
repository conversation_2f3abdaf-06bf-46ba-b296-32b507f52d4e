// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { z } from 'zod';
import crypto from 'crypto';
import { logInvitation } from '@/lib/logging';
import nodemailer from 'nodemailer';
import { createInvitationEmailTemplate } from '@/lib/email-templates';

// Input validation schema
const invitationSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  role: z.enum(['ADMIN', 'PATHOLOGIST', 'MORGUE_STAFF', 'SECURITY_STAFF', 'FIELD_WORKER', "GUEST"]),
  facilityId: z.string().optional(),
  message: z.string().max(500).optional(),
  metadata: z.object({
    position: z.string().optional(),
    department: z.string().optional(),
    employeeId: z.string().optional()
  }).optional(),
  expiresIn: z.number().optional().default(7 * 24 * 60 * 60 * 1000), // Default 7 days
});

// Create a transporter using SMTP
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: Number.parseInt(process.env.SMTP_PORT || "587"),
  secure: process.env.SMTP_SECURE === "true",
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
});

// GET /api/users/invitations - Get all invitations
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    const invitations = await prisma.userInvitation.findMany({
      where,
      include: {
        sentBy: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
    
    return NextResponse.json({
      total: invitations.length,
      data: invitations,
    });
  } catch (error) {
    console.error('Error fetching invitations:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/users/invitations - Create a new invitation
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate input
    const validationResult = invitationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation Failed', 
          details: validationResult.error.flatten().fieldErrors 
        }, 
        { status: 400 }
      );
    }

    const { 
      email, 
      name, 
      role, 
      facilityId, 
      message,
      metadata, 
      expiresIn 
    } = validationResult.data;

    // Check if user already exists with this email
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists with this email' },
        { status: 400 }
      );
    }

    // Check if there's an existing pending invitation
    const existingInvitation = await prisma.userInvitation.findFirst({
      where: { 
        email,
        status: 'PENDING'
      },
    });

    if (existingInvitation) {
      return NextResponse.json(
        { error: 'An invitation is already pending for this email' },
        { status: 400 }
      );
    }

    // Generate a unique token for this invitation
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + expiresIn);

    // Create the invitation record
    const invitation = await prisma.userInvitation.create({
      data: {
        email,
        name,
        role,
        token,
        status: 'PENDING',
        expiresAt,
        facilityId,
        metadata,
        sentById: session.user.id,
      },
    });

    // Send the invitation email
    const activationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/activate-account?token=${token}`;
    
    const htmlContent = createInvitationEmailTemplate({
      title: 'Join GP Pathology IT Platform',
      message: message || `You have been invited to join the GP Pathology IT platform as a ${role.replace('_', ' ').toLowerCase()}.`,
      ctaLink: activationUrl,
      recipientName: name,
      ctaText: 'Activate Your Account',
    });

    const mailOptions = {
      from: process.env.SMTP_FROM,
      to: email,
      subject: 'Invitation to Join GP Pathology IT Platform',
      html: htmlContent,
      text: `${message || `You have been invited to join the GP Pathology IT platform as a ${role.replace('_', ' ').toLowerCase()}.`}\n\nClick the link below to activate your account:\n${activationUrl}`,
    };

    await transporter.sendMail(mailOptions);
    await logInvitation(email);

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'CREATE_INVITATION',
        entityType: 'USER_INVITATION',
        entityId: invitation.id,
        performedBy: session.user.id,
        changes: {
          email,
          name,
          role,
          facilityId,
        },
      },
    });

    return NextResponse.json(invitation, { status: 201 });
  } catch (error) {
    console.error('Error creating invitation:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/users/invitations/:id - Cancel an invitation
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];
    
    if (!id) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      );
    }

    // Check if invitation exists
    const invitation = await prisma.userInvitation.findUnique({
      where: { id },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Only allow canceling pending invitations
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Only pending invitations can be canceled' },
        { status: 400 }
      );
    }

    // Cancel the invitation
    await prisma.userInvitation.update({
      where: { id },
      data: {
        status: 'CANCELED',
        canceledAt: new Date(),
      },
    });

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'CANCEL_INVITATION',
        entityType: 'USER_INVITATION',
        entityId: id,
        performedBy: session.user.id,
        changes: { status: 'CANCELED' },
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Invitation canceled successfully',
    });
  } catch (error) {
    console.error('Error canceling invitation:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 