// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Input validation schema
const verifyTokenSchema = z.object({
  token: z.string().min(5, 'Invalid token'),
});

// POST /api/users/invitations/verify - Verify an invitation token
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validationResult = verifyTokenSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation Failed', 
          details: validationResult.error.flatten().fieldErrors 
        }, 
        { status: 400 }
      );
    }

    const { token } = validationResult.data;

    // Find invitation by token
    const invitation = await prisma.userInvitation.findUnique({
      where: { token },
    });

    if (!invitation) {
      return NextResponse.json(
        { valid: false, error: 'Invalid invitation token' },
        { status: 200 }
      );
    }

    if (invitation.expiresAt < new Date()) {
      return NextResponse.json(
        { valid: false, error: 'Invitation token has expired' },
        { status: 200 }
      );
    }

    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { valid: false, error: 'This invitation is no longer valid' },
        { status: 200 }
      );
    }

    return NextResponse.json({
      valid: true,
      email: invitation.email,
      name: invitation.name,
      role: invitation.role,
    });
    
  } catch (error) {
    console.error('Error verifying invitation token:', error);
    return NextResponse.json(
      { valid: false, error: 'Failed to verify token' },
      { status: 500 }
    );
  }
}

// GET /api/users/invitations/verify - Verify an invitation token via query parameter
export async function GET(request: NextRequest) {
  try {
    const token = request.nextUrl.searchParams.get('token');
    
    if (!token) {
      return NextResponse.json(
        { valid: false, error: 'Token parameter is required' },
        { status: 400 }
      );
    }

    // Find invitation by token
    const invitation = await prisma.userInvitation.findUnique({
      where: { token },
    });

    if (!invitation) {
      return NextResponse.json(
        { valid: false, error: 'Invalid invitation token' },
        { status: 200 }
      );
    }

    if (invitation.expiresAt < new Date()) {
      return NextResponse.json(
        { valid: false, error: 'Invitation token has expired' },
        { status: 200 }
      );
    }

    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { valid: false, error: 'This invitation is no longer valid' },
        { status: 200 }
      );
    }

    return NextResponse.json({
      valid: true,
      email: invitation.email,
      name: invitation.name,
      role: invitation.role,
    });
    
  } catch (error) {
    console.error('Error verifying invitation token:', error);
    return NextResponse.json(
      { valid: false, error: 'Failed to verify token' },
      { status: 500 }
    );
  }
} 