// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';

// GET /api/users/invitations/[id] - Get a single invitation
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    const invitation = await prisma.userInvitation.findUnique({
      where: { id },
      include: {
        sentBy: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(invitation);
  } catch (error) {
    console.error('Error fetching invitation:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/users/invitations/[id] - Cancel an invitation
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Check if invitation exists
    const invitation = await prisma.userInvitation.findUnique({
      where: { id },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Only allow canceling pending invitations
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Only pending invitations can be canceled' },
        { status: 400 }
      );
    }

    // Cancel the invitation
    await prisma.userInvitation.update({
      where: { id },
      data: {
        status: 'CANCELED',
        canceledAt: new Date(),
      },
    });

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'CANCEL_INVITATION',
        entityType: 'USER_INVITATION',
        entityId: id,
        performedBy: session.user.id,
        changes: { status: 'CANCELED' },
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Invitation canceled successfully',
    });
  } catch (error) {
    console.error('Error canceling invitation:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// PATCH /api/users/invitations/[id] - Update invitation details
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    const body = await request.json();

    // Check if invitation exists
    const invitation = await prisma.userInvitation.findUnique({
      where: { id },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Only allow updating pending invitations
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Only pending invitations can be updated' },
        { status: 400 }
      );
    }

    // Fields that can be updated
    const allowedFields = ['name', 'role', 'facilityId', 'metadata'];
    const updates: Record<string, any> = {};

    // Filter allowed fields
    Object.keys(body).forEach(key => {
      if (allowedFields.includes(key)) {
        updates[key] = body[key];
      }
    });

    if (Object.keys(updates).length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Update the invitation
    const updatedInvitation = await prisma.userInvitation.update({
      where: { id },
      data: updates,
    });

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_INVITATION',
        entityType: 'USER_INVITATION',
        entityId: id,
        performedBy: session.user.id,
        changes: updates,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Invitation updated successfully',
      invitation: updatedInvitation,
    });
  } catch (error) {
    console.error('Error updating invitation:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 