// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import nodemailer from 'nodemailer';
import { createInvitationEmailTemplate } from '@/lib/email-templates';
import { logInvitation } from '@/lib/logging';

// Create a transporter using SMTP
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: Number.parseInt(process.env.SMTP_PORT || "587"),
  secure: process.env.SMTP_SECURE === "true",
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
});

// POST /api/users/invitations/[id]/resend - Resend an invitation
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Find the invitation
    const invitation = await prisma.userInvitation.findUnique({
      where: { id },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Check if invitation status is pending
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Only pending invitations can be resent' },
        { status: 400 }
      );
    }

    // Check if invitation is expired and update expiry
    const now = new Date();
    const isExpired = invitation.expiresAt < now;
    
    // Extend invitation expiry (7 days from now)
    const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    await prisma.userInvitation.update({
      where: { id },
      data: {
        expiresAt,
        resendCount: { increment: 1 },
        lastResendAt: now,
      },
    });

    // Send the invitation email
    const activationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/activate-account?token=${invitation.token}`;
    
    const message = `You have been invited to join the GP Pathology IT platform as a ${invitation.role.replace('_', ' ').toLowerCase()}.`;
    
    const htmlContent = createInvitationEmailTemplate({
      title: 'Join GP Pathology IT Platform',
      message: message,
      ctaLink: activationUrl,
      recipientName: invitation.name,
      ctaText: 'Activate Your Account',
    });

    const mailOptions = {
      from: process.env.SMTP_FROM,
      to: invitation.email,
      subject: isExpired ? 'Invitation Renewed: Join GP Pathology IT Platform' : 'Reminder: Join GP Pathology IT Platform',
      html: htmlContent,
      text: `${message}\n\nClick the link below to activate your account:\n${activationUrl}`,
    };

    await transporter.sendMail(mailOptions);
    await logInvitation(invitation.email);

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'RESEND_INVITATION',
        entityType: 'USER_INVITATION',
        entityId: id,
        performedBy: session.user.id,
        changes: { 
          expiresAt: expiresAt.toISOString(),
          previouslyExpired: isExpired
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Invitation resent successfully',
      expiresAt,
    });
  } catch (error) {
    console.error('Error resending invitation:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 