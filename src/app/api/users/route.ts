// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { UserRole } from '@prisma/client';

// User validation schema
const UserSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters').optional(),
  role: z.nativeEnum(UserRole),
  persalNumber: z.string().optional(),
  department: z.string().optional(),
  position: z.string().optional(),
  facilityId: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED']).optional().default('ACTIVE'),
})

// GET /api/users - Get all users with optional search
export async function GET(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')

    // Build where clause for search
    const whereClause = search ? {
      OR: [
        { name: { contains: search, mode: 'insensitive' as const } },
        { email: { contains: search, mode: 'insensitive' as const } },
        { department: { contains: search, mode: 'insensitive' as const } },
        { position: { contains: search, mode: 'insensitive' as const } },
        { persalNumber: { contains: search, mode: 'insensitive' as const } },
      ]
    } : {}

    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        persalNumber: true,
        department: true,
        position: true,
        facility: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [
        { name: 'asc' },
        { email: 'asc' }
      ],
      take: 50, // Limit results to prevent large responses
    })

    return NextResponse.json(users)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST /api/users - Create a new user
export async function POST(request: Request) {
  try {
    const session = await auth()
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate input
    const validationResult = UserSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation Failed', 
          details: validationResult.error.flatten().fieldErrors 
        }, 
        { status: 400 }
      )
    }

    const { 
      name, 
      email, 
      password, 
      role, 
      persalNumber, 
      department, 
      position, 
      facilityId,
      status 
    } = validationResult.data

    // Check for existing user
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists with this email' },
        { status: 400 }
      )
    }

    // Hash password if provided
    const hashedPassword = password 
      ? await bcrypt.hash(password, 10) 
      : undefined

    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: role || UserRole.FIELD_EMPLOYEE,
        persalNumber,
        department,
        position,
        facilityId,
        status,
      },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'CREATE_USER',
        entityType: 'USER',
        entityId: user.id,
        performedBy: session.user.id,
        changes: body,
      },
    })

    return NextResponse.json(user, { status: 201 })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT /api/users - Update a user
export async function PUT(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = body

    // Validate input (partial validation)
    const validationResult = UserSchema.partial().safeParse(updateData)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation Failed', 
          details: validationResult.error.flatten().fieldErrors 
        }, 
        { status: 400 }
      )
    }

    // Only admins can update other users
    if (session.user.id !== id && session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Hash password if provided
    if (updateData.password) {
      updateData.password = await bcrypt.hash(updateData.password, 10)
    }

    const user = await prisma.user.update({
      where: { id },
      data: updateData,
    })

    // Create audit log for admin actions
    if (session.user.role === 'ADMIN') {
      await prisma.adminAudit.create({
        data: {
          action: 'UPDATE_USER',
          entityType: 'USER',
          entityId: id,
          performedBy: session.user.id,
          changes: updateData,
        },
      })
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// DELETE /api/users - Delete a user
export async function DELETE(request: Request) {
  try {
    const session = await auth()
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('id')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' }, 
        { status: 400 }
      )
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    })

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' }, 
        { status: 404 }
      )
    }

    // Prevent deleting the last admin
    const adminCount = await prisma.user.count({
      where: { role: 'ADMIN' },
    })

    if (existingUser.role === 'ADMIN' && adminCount <= 1) {
      return NextResponse.json(
        { error: 'Cannot delete the last admin user' }, 
        { status: 400 }
      )
    }

    // Delete user
    const deletedUser = await prisma.user.delete({
      where: { id: userId },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'DELETE_USER',
        entityType: 'USER',
        entityId: userId,
        performedBy: session.user.id,
        changes: deletedUser,
      },
    })

    return NextResponse.json(
      { message: 'User deleted successfully', user: deletedUser },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}