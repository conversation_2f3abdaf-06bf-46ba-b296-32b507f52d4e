// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/server';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();
    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    const user = await verifyToken(token);
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    return NextResponse.json({ user });
  } catch (error) {
    console.error('Error verifying token:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 