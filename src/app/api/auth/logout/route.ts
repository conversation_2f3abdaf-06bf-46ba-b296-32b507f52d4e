import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { signOut } from '@/auth';
import { logger } from '@/utils/logger';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

interface LogoutResponse {
  success: boolean;
  error?: string;
}

export async function POST(_request: NextRequest): Promise<NextResponse<LogoutResponse>> {
  try {
    // Use NextAuth signOut to properly clear session
    await signOut();

    // Also clear all possible auth cookies to ensure clean state
    const cookieStore = cookies();

    // Clear all possible auth cookies
    cookieStore.delete('next-auth.session-token');
    cookieStore.delete('next-auth.csrf-token');
    cookieStore.delete('next-auth.callback-url');
    cookieStore.delete('auth-token');
    cookieStore.delete('token');
    cookieStore.delete('session');
    cookieStore.delete('refreshToken');
    cookieStore.delete('currentUser');

    logger.info('User logged out successfully');

    const response = NextResponse.json({ success: true });
    return response;
  } catch (error) {
    logger.error('Logout error:', { error });
    return NextResponse.json(
      { success: false, error: 'Logout failed' },
      { status: 500 }
    );
  }
}

// Handle GET requests for compatibility
export async function GET(request: NextRequest): Promise<NextResponse<LogoutResponse>> {
  return POST(request);
}
