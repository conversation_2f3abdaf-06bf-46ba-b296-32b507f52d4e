// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { authenticator } from "otplib";
import * as qrcode from "qrcode";

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || !session.user.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Generate a secret
    const secret = authenticator.generateSecret();
    
    // Store the secret (but don't mark MFA as enabled yet)
    await prisma.user.update({
      where: { id: user.id },
      data: {
        mfaSecret: secret,
        mfaVerified: false,
      },
    });
    
    // Generate the OTP auth URL for the QR code
    const otpauth = authenticator.keyuri(
      user.email, 
      "GP Pathology System", 
      secret
    );
    
    // Generate QR code as data URL
    const qrCodeDataURL = await qrcode.toDataURL(otpauth);
    
    return NextResponse.json({
      secret,
      qrCode: qrCodeDataURL,
    });
  } catch (error) {
    console.error("MFA Setup Error:", error);
    return NextResponse.json(
      { error: "Failed to setup MFA" },
      { status: 500 }
    );
  }
} 