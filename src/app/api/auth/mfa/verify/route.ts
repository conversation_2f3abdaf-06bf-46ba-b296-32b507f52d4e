// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { authenticator } from "otplib";
import { nanoid } from "nanoid";

// Helper function to generate backup codes
function generateBackupCodes(count = 10): string[] {
  return Array.from({ length: count }, () => nanoid(10).toUpperCase());
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || !session.user.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const data = await req.json();
    const { token } = data;
    
    if (!token) {
      return NextResponse.json(
        { error: "Token is required" },
        { status: 400 }
      );
    }
    
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user || !user.mfaSecret) {
      return NextResponse.json(
        { error: "User not found or MFA not initialized" },
        { status: 404 }
      );
    }
    
    // Verify the token
    const isValid = authenticator.verify({
      token,
      secret: user.mfaSecret,
    });
    
    if (!isValid) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 400 }
      );
    }
    
    // Generate backup codes
    const backupCodes = generateBackupCodes();
    
    // Update the user to enable MFA
    await prisma.user.update({
      where: { id: user.id },
      data: {
        mfaEnabled: true,
        mfaVerified: true,
        mfaBackupCodes: backupCodes,
      },
    });
    
    return NextResponse.json({
      success: true,
      backupCodes,
    });
  } catch (error) {
    console.error("MFA Verification Error:", error);
    return NextResponse.json(
      { error: "Failed to verify MFA token" },
      { status: 500 }
    );
  }
} 