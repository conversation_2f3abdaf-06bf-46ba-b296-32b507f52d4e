// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { nanoid } from "nanoid";

// Helper function to generate backup codes
function generateBackupCodes(count = 10): string[] {
  return Array.from({ length: count }, () => nanoid(10).toUpperCase());
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || !session.user.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    if (!user.mfaEnabled) {
      return NextResponse.json(
        { error: "MFA is not enabled for this user" },
        { status: 400 }
      );
    }
    
    // Generate new backup codes
    const backupCodes = generateBackupCodes();
    
    // Update the user with new backup codes
    await prisma.user.update({
      where: { id: user.id },
      data: {
        mfaBackupCodes: backupCodes,
      },
    });
    
    return NextResponse.json({
      success: true,
      backupCodes,
    });
  } catch (error) {
    console.error("MFA Regenerate Codes Error:", error);
    return NextResponse.json(
      { error: "Failed to regenerate backup codes" },
      { status: 500 }
    );
  }
} 