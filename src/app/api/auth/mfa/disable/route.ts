// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || !session.user.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    
    const data = await req.json();
    const { password } = data;
    
    if (!password) {
      return NextResponse.json(
        { error: "Password is required to disable M<PERSON>" },
        { status: 400 }
      );
    }
    
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Verify the password for security
    // NOTE: This is commented out for development purposes
    // const isPasswordValid = await compare(password, user.password);
    
    // if (!isPasswordValid) {
    //   return NextResponse.json(
    //     { error: "Invalid password" },
    //     { status: 400 }
    //   );
    // }
    
    // Disable MFA for the user
    await prisma.user.update({
      where: { id: user.id },
      data: {
        mfaEnabled: false,
        mfaVerified: false,
        mfaSecret: null,
        mfaBackupCodes: [],
      },
    });
    
    return NextResponse.json({
      success: true,
      message: "Two-factor authentication has been disabled",
    });
  } catch (error) {
    console.error("MFA Disable Error:", error);
    return NextResponse.json(
      { error: "Failed to disable MFA" },
      { status: 500 }
    );
  }
} 