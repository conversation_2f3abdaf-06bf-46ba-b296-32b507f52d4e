// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authenticator } from "otplib";

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    const { email, token, isBackupCode = false } = data;
    
    if (!email || !token) {
      return NextResponse.json(
        { error: "Email and token are required" },
        { status: 400 }
      );
    }
    
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user || !user.mfaEnabled || !user.mfaSecret) {
      return NextResponse.json(
        { error: "User not found or MFA not enabled" },
        { status: 404 }
      );
    }
    
    let isValid = false;
    
    if (isBackupCode) {
      // Check if the token matches one of the backup codes
      isValid = user.mfaBackupCodes.includes(token);
      
      // If valid, remove the used backup code
      if (isValid) {
        await prisma.user.update({
          where: { id: user.id },
          data: {
            mfaBackupCodes: user.mfaBackupCodes.filter(code => code !== token),
          },
        });
      }
    } else {
      // Verify the TOTP token
      isValid = authenticator.verify({
        token,
        secret: user.mfaSecret,
      });
    }
    
    if (!isValid) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 400 }
      );
    }
    
    return NextResponse.json({
      success: true,
      userId: user.id,
    });
  } catch (error) {
    console.error("MFA Validation Error:", error);
    return NextResponse.json(
      { error: "Failed to validate MFA token" },
      { status: 500 }
    );
  }
} 