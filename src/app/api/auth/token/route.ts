// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { getServerAuthToken } from '@/lib/auth/server';

export async function GET() {
  try {
    const token = getServerAuthToken();
    if (!token) {
      return NextResponse.json({ error: 'No token found' }, { status: 401 });
    }
    return NextResponse.json({ token });
  } catch (error) {
    console.error('Error getting token:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 