import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { logger } from "@/utils/logger";
import { cookies } from "next/headers";

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

interface SessionResponse {
  isAuthenticated: boolean;
  user?: {
    id: string;
    email: string;
    name?: string | null;
    role: string;
    image?: string | null;
    permissions?: string[] | null;
  };
  error?: string;
}

export async function GET(): Promise<NextResponse<SessionResponse>> {
  try {
    const session = await auth();

    if (!session || !session.user) {
      logger.info('Session check: No valid session found');

      // Clear all possible auth cookies to ensure clean state
      const cookieStore = cookies();
      cookieStore.delete('next-auth.session-token');
      cookieStore.delete('next-auth.csrf-token');
      cookieStore.delete('next-auth.callback-url');
      cookieStore.delete('auth-token');
      cookieStore.delete('token');
      cookieStore.delete('session');
      cookieStore.delete('refreshToken');
      cookieStore.delete('currentUser');

      return NextResponse.json({
        isAuthenticated: false,
        error: "Session expired or invalid"
      });
    }

    logger.info('Session check: Valid session', { userId: session.user.id });
    return NextResponse.json({
      isAuthenticated: true,
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role,
        image: session.user.image,
        permissions: session.user.permissions,
      }
    });
  } catch (error) {
    logger.error('Session check error', { error });

    // Clear all possible auth cookies on error
    const cookieStore = cookies();
    cookieStore.delete('next-auth.session-token');
    cookieStore.delete('next-auth.csrf-token');
    cookieStore.delete('next-auth.callback-url');
    cookieStore.delete('auth-token');
    cookieStore.delete('token');
    cookieStore.delete('session');
    cookieStore.delete('refreshToken');
    cookieStore.delete('currentUser');

    return NextResponse.json(
      {
        isAuthenticated: false,
        error: "An unexpected error occurred"
      },
      { status: 500 }
    );
  }
}
