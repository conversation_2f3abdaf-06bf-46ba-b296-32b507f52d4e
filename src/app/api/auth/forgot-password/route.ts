// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import * as z from "zod"



const schema = z.object({
  email: z.string().email(),
})

function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

export async function POST(req: Request) {
  try {
    const json = await req.json()
    const body = schema.parse(json)

    const user = await prisma.user.findUnique({
      where: { email: body.email },
    })

    if (!user) {
      // Return 200 even if user doesn't exist to prevent email enumeration
      return NextResponse.json({
        message: "If an account exists with that email, we've sent a verification code.",
      })
    }

    // Generate OTP
    const otp = generateOTP()
    const expires = new Date(Date.now() + 300000) // 5 minutes from now

    // Save the OTP
    await prisma.passwordReset.create({
      data: {
        token: otp,
        expires,
        userId: user.id,
      },
    })

    // Send email with OTP using Nodemailer
    await transporter.sendMail({
      ...mailConfig,
      to: user.email,
      subject: "Password Reset Verification Code",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333; text-align: center;">Password Reset Verification Code</h2>
          <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <p style="font-size: 24px; text-align: center; margin: 0;">Your verification code is:</p>
            <p style="font-size: 36px; font-weight: bold; text-align: center; margin: 10px 0; color: #007bff;">${otp}</p>
          </div>
          <p style="color: #666; text-align: center;">This code will expire in 5 minutes.</p>
          <p style="color: #666; text-align: center; font-size: 12px;">If you didn't request this code, you can safely ignore this email.</p>
        </div>
      `,
      text: `Your password reset verification code is: ${otp}\n\nThis code will expire in 5 minutes.\n\nIf you didn't request this code, you can safely ignore this email.`,
    })

    return NextResponse.json({
      message: "Verification code sent successfully",
      email: user.email,
    })
  } catch (error) {
    console.error("Password reset error:", error)
    return NextResponse.json(
      { message: "Failed to send verification code" },
      { status: 500 }
    )
  }
}