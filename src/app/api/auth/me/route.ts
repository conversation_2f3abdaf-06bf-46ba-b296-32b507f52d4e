import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/jwt';
import { prisma } from '@/lib/prisma';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(req: NextRequest) {
  try {
    console.log('GET /api/auth/me - Request received');
    
    const authHeader = req.headers.get('authorization');
    console.log('Authorization header present:', !!authHeader);
    
    if (!authHeader?.startsWith('Bearer ')) {
      console.log('Invalid token format');
      return NextResponse.json(
        { error: 'Unauthorized - Invalid token format' },
        { status: 401 }
      );
    }

    const token = authHeader.split(' ')[1];
    console.log('Token extracted, verifying...');
    
    const payload = await verifyToken(token);
    console.log('Token verification result:', !!payload);

    if (!payload) {
      console.log('Token verification failed');
      return NextResponse.json(
        { error: 'Unauthorized - Invalid or expired token' },
        { status: 401 }
      );
    }

    console.log('Looking up user:', payload.userId);
    
    // Get user from database to ensure they still exist and get latest data
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        emailVerified: true,
        lastLogin: true,
        image: true,
      },
    });

    console.log('User lookup result:', !!user);

    if (!user) {
      console.log('User not found in database');
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    console.log('Returning user data');
    return NextResponse.json({ 
      user,
      message: 'User data retrieved successfully'
    });
  } catch (error) {
    console.error('GET /api/auth/me Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 