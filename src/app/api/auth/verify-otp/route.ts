// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import * as z from "zod"

const schema = z.object({
  email: z.string().email(),
  otp: z.string().length(6),
})

export async function POST(req: Request) {
  try {
    const json = await req.json()
    const body = schema.parse(json)

    const user = await prisma.user.findUnique({
      where: { email: body.email },
      include: {
        passwordResets: {
          where: {
            expires: { gt: new Date() },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    })

    if (!user || user.passwordResets.length === 0) {
      return NextResponse.json(
        { message: "Invalid or expired verification code" },
        { status: 400 }
      )
    }

    const reset = user.passwordResets[0]
    
    if (reset.token !== body.otp) {
      return NextResponse.json(
        { message: "Invalid verification code" },
        { status: 400 }
      )
    }

    // Delete the used OTP
    await prisma.passwordReset.delete({
      where: { id: reset.id },
    })

    // Generate a temporary token for the reset password page
    const tempToken = crypto.randomUUID()
    await prisma.passwordReset.create({
      data: {
        userId: user.id,
        token: tempToken,
        expires: new Date(Date.now() + 300000), // 5 minutes
      },
    })

    return NextResponse.json({
      message: "Verification successful",
      token: tempToken,
    })
  } catch (error) {
    console.error("OTP verification error:", error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: error.issues[0].message },
        { status: 422 }
      )
    }
    return NextResponse.json(
      { message: "Failed to verify code" },
      { status: 500 }
    )
  }
}
