// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'

// GET /api/documents - Get all documents
export async function GET() {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const documents = await prisma.document.findMany({
      include: {
        facility: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      where: {
        // If not admin, only show documents from user's facility
        ...(session.user.role !== 'ADMIN' && {
          facilityId: session.user.facilityId,
        }),
      },
    })

    return NextResponse.json(documents)
  } catch (error) {
    console.error('Error fetching documents:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST /api/documents - Create a new document
export async function POST(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      title,
      type,
      fileUrl,
      version,
      facilityId,
      description,
      expiryDate,
    } = body

    // Basic validation
    if (!title || !type || !fileUrl || !facilityId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Only allow users to create documents for their facility unless admin
    if (session.user.role !== 'ADMIN' && facilityId !== session.user.facilityId) {
      return NextResponse.json(
        { error: 'Cannot create document for other facilities' },
        { status: 403 }
      )
    }

    const document = await prisma.document.create({
      data: {
        title,
        type,
        fileUrl,
        version,
        facilityId,
        description,
        expiryDate,
        uploadedBy: session.user.id,
      },
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'CREATE_DOCUMENT',
        entityType: 'DOCUMENT',
        entityId: document.id,
        performedBy: session.user.id,
        changes: body,
      },
    })

    return NextResponse.json(document)
  } catch (error) {
    console.error('Error creating document:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT /api/documents - Update a document
export async function PUT(request: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = body

    // Check if document exists and user has permission
    const existingDoc = await prisma.document.findUnique({
      where: { id },
    })

    if (!existingDoc) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    // Only allow updates if admin or document is from user's facility
    if (
      session.user.role !== 'ADMIN' &&
      existingDoc.facilityId !== session.user.facilityId
    ) {
      return NextResponse.json(
        { error: 'Cannot update documents from other facilities' },
        { status: 403 }
      )
    }

    const document = await prisma.document.update({
      where: { id },
      data: updateData,
    })

    // Create audit log
    await prisma.adminAudit.create({
      data: {
        action: 'UPDATE_DOCUMENT',
        entityType: 'DOCUMENT',
        entityId: id,
        performedBy: session.user.id,
        changes: updateData,
      },
    })

    return NextResponse.json(document)
  } catch (error) {
    console.error('Error updating document:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 