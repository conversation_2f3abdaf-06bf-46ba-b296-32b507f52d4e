// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get("id")
    const trackingNumber = searchParams.get("trackingNumber")
    const status = searchParams.get("status")
    const lastName = searchParams.get("lastName")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Build filter object
    const where: any = {}
    if (id) where.id = id
    if (trackingNumber) where.trackingNumber = trackingNumber
    if (status) where.status = status
    if (lastName) where.lastName = { contains: lastName, mode: "insensitive" }

    // Get total count for pagination
    const total = await prisma.body.count({ where })

    // Get bodies with pagination
    const bodies = await prisma.body.findMany({
      where,
      include: {
        bodyTag: true,
        collection: true,
        admissions: {
          take: 1,
          orderBy: {
            admissionDate: "desc",
          },
        },
        releases: {
          take: 1,
          orderBy: {
            releaseDate: "desc",
          },
        },
        referrals: {
          take: 1,
          orderBy: {
            referralDate: "desc",
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        updatedAt: "desc",
      },
    })

    return NextResponse.json({
      data: bodies,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching bodies:", error)
    return NextResponse.json({ error: "Failed to fetch bodies" }, { status: 500 })
  }
}

