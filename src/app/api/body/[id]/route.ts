// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { type NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id

    const body = await prisma.body.findUnique({
      where: { id },
      include: {
        bodyTag: true,
        collection: true,
        admissions: {
          include: {
            facility: true,
            assignedTo: true,
            createdBy: true,
          },
          orderBy: {
            admissionDate: "desc",
          },
        },
        releases: {
          include: {
            facility: true,
            verifiedBy: true,
            releasedBy: true,
          },
          orderBy: {
            releaseDate: "desc",
          },
        },
        referrals: {
          include: {
            referredBy: true,
            assignedTo: true,
          },
          orderBy: {
            referralDate: "desc",
          },
        },
      },
    })

    if (!body) {
      return NextResponse.json({ error: "Body not found" }, { status: 404 })
    }

    return NextResponse.json(body)
  } catch (error) {
    console.error("Error fetching body:", error)
    return NextResponse.json({ error: "Failed to fetch body" }, { status: 500 })
  }
}

