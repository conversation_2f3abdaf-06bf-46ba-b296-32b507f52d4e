// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { streamText } from 'ai';
import { auth } from '@/auth' 
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { NextResponse } from 'next/server';

export const runtime = 'edge';

const chatRequestSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string()
  })),
  context: z.object({
    documentContent: z.string(),
    documentTitle: z.string(),
    selectedText: z.string()
  })
});

export async function POST(req: Request) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { messages, context } = chatRequestSchema.parse(body);

    const systemMessage = `You are an AI assistant helping with a medical pathology document titled "${context.documentTitle}". 
    The document content is: "${context.documentContent}".
    ${context.selectedText ? `The user has selected the following text: "${context.selectedText}"` : ''}
    
    Please help answer questions about this document, explain medical terms, and provide relevant insights.
    Keep responses clear, accurate, and focused on the medical context.`;

    const stream = await streamText({
      model: openai('gpt-4o-mini'),
      messages: [
        { role: 'system' as const, content: systemMessage },
        ...messages.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        }))
      ],
      temperature: 0.7,
    });

    return stream.toDataStreamResponse()

  } catch (error) {
    console.error('Chat API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
