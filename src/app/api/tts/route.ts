// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { DeepgramTTS, type TTSOptions } from '@/lib/tts/deepgram';

export const runtime = 'edge';

type TTSProvider = 'deepgram' | 'web';

interface TTSRequest extends TTSOptions {
  text: string;
  provider?: TTSProvider;
  lang?: string;
  modelName?: string;
  quantized?: boolean;
}

export async function POST(req: Request) {
  let body: TTSRequest;

  try {
    // Parse and validate request body
    body = await req.json().catch(() => ({})) as TTSRequest;

    // Input validation
    if (!body.text?.trim()) {
      return NextResponse.json(
        { error: 'Text is required and cannot be empty' },
        { status: 400 }
      );
    }

    if (body.text.length > 5000) {
      return NextResponse.json(
        { error: 'Text must be 5000 characters or less' },
        { status: 400 }
      );
    }

    // Validate numeric parameters
    if (body.rate !== undefined && (isNaN(body.rate) || body.rate < 0.5 || body.rate > 2.0)) {
      return NextResponse.json(
        { error: 'Rate must be between 0.5 and 2.0' },
        { status: 400 }
      );
    }

    if (body.pitch !== undefined && (isNaN(body.pitch) || body.pitch < 0.5 || body.pitch > 2.0)) {
      return NextResponse.json(
        { error: 'Pitch must be between 0.5 and 2.0' },
        { status: 400 }
      );
    }

    // Handle different providers
    switch (body.provider) {
      case 'web':
        return NextResponse.json({
          provider: 'web',
          text: body.text,
          options: {
            voice: body.voice,
            rate: body.rate,
            pitch: body.pitch,
            lang: body.lang
          }
        });

      case 'transformers':
        const transformerTTS = TransformerTTS.getInstance();
        const transformerAudio = await transformerTTS.textToSpeech(body.text, {
          modelName: body.modelName,
          quantized: body.quantized,
          rate: body.rate,
          pitch: body.pitch
        });

        return new NextResponse(transformerAudio, {
          headers: {
            'Content-Type': 'audio/wav',
            'Content-Length': transformerAudio.byteLength.toString(),
            'Cache-Control': 'public, max-age=3600'
          }
        });

      case 'deepgram':
      default:
        const deepgramTTS = DeepgramTTS.getInstance();
        const deepgramAudio = await deepgramTTS.textToSpeech(body.text, {
          voice: body.voice,
          rate: body.rate,
          pitch: body.pitch,
          model: body.model,
          encoding: body.encoding,
          container: body.container
        });

        const contentType = body.container === 'wav' ? 'audio/wav' : 'audio/mpeg';

        return new NextResponse(deepgramAudio, {
          headers: {
            'Content-Type': contentType,
            'Content-Length': deepgramAudio.byteLength.toString(),
            'Cache-Control': 'public, max-age=3600'
          }
        });
    }
  } catch (error: any) {
    console.error('TTS API Error:', error);

    // Determine fallback provider
    const currentProvider = body?.provider || 'deepgram';
    let fallbackProvider: TTSProvider | null = null;

    if (currentProvider === 'deepgram') {
      fallbackProvider = 'web';
    }

    if (fallbackProvider) {
      return NextResponse.json({
        error: `${currentProvider} TTS failed. Try using ${fallbackProvider} instead.`,
        provider: fallbackProvider,
        fallback: true
      }, { status: 503 });
    }

    // Handle specific error types
    if (error?.message?.includes('Rate limit exceeded')) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    if (error?.code) {
      return NextResponse.json(
        { 
          error: error.message || 'Failed to generate audio',
          details: error.details
        },
        { status: parseInt(error.code) || 500 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate audio' },
      { status: 500 }
    );
  }
}
