// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from "@/auth";

// GET /api/permissions - List all permissions
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const permissions = await prisma.permission.findMany({
      include: {
        _count: {
          select: {
            roles: true
          }
        }
      }
    });

    return NextResponse.json(permissions);
  } catch (error) {
    console.error('Error fetching permissions:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/permissions - Create a new permission
export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { name, description, category } = await req.json();

    if (!name || !category) {
      return NextResponse.json({ error: 'Name and category are required' }, { status: 400 });
    }

    // Check if permission already exists
    const existingPermission = await prisma.permission.findUnique({
      where: { name }
    });

    if (existingPermission) {
      return NextResponse.json({ error: 'Permission already exists' }, { status: 400 });
    }

    // Create permission
    const permission = await prisma.permission.create({
      data: {
        name,
        description,
        category
      }
    });

    return NextResponse.json(permission);
  } catch (error) {
    console.error('Error creating permission:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 