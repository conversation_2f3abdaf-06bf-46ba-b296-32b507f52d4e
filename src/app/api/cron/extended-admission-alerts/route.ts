// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { extendedAdmissionService } from '@/services/extendedAdmissionService';

// Environment variable check to prevent unauthorized access
const API_SECRET = process.env.CRON_API_SECRET;

export async function GET(request: Request) {
  try {
    // Verify the request is authorized with the correct secret
    const { searchParams } = new URL(request.url);
    const secret = searchParams.get('secret');

    if (!API_SECRET || secret !== API_SECRET) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Run the notification service
    const result = await extendedAdmissionService.sendNotificationsForAllFacilities();

    // Log the result
    console.log(`Extended admission alerts sent: ${result.totalBodies} bodies across ${result.facilitiesProcessed} facilities`);

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('Error in extended admission alerts cron job:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to process extended admission alerts',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
} 