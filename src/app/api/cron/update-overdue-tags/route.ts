import { NextRequest, NextResponse } from 'next/server';
import { updateOverdueBodyTags } from '@/lib/utils/bodyTagStatusUpdater';

/**
 * POST /api/cron/update-overdue-tags
 * Cron job endpoint to automatically update overdue body tags
 * This should be called daily by a cron service
 */
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (optional)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Running scheduled overdue body tags update...');
    
    const result = await updateOverdueBodyTags();
    
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      updated: result.updated,
      overdueTagIds: result.overdueTagIds,
      message: `Successfully updated ${result.updated} body tags to OVERDUE status`
    };

    console.log('Scheduled overdue update completed:', response);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in scheduled overdue update:', error);
    
    const errorResponse = {
      success: false,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to update overdue body tags'
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * GET /api/cron/update-overdue-tags
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({
    service: 'Overdue Body Tags Updater',
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
}
