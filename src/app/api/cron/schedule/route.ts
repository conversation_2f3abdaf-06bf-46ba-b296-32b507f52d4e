// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import { auth } from '@/auth';

// Define the scheduled jobs
const scheduledJobs = [
  {
    id: 'extended-admission-alerts',
    name: 'Extended Admission Alerts',
    description: 'Sends alerts for bodies admitted longer than 7 days',
    schedule: 'Daily at 09:00 AM',
    endpoint: '/api/cron/extended-admission-alerts',
    lastRun: new Date().toISOString(),
    nextRun: new Date(new Date().setHours(9, 0, 0, 0) + (new Date().getHours() >= 9 ? 86400000 : 0)).toISOString(),
    status: 'active'
  },
  // Add more scheduled jobs here as needed
];

export async function GET(request: Request) {
  try {
    const session = await auth();
    
    // Check for authentication and authorization
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Return the list of scheduled jobs
    return NextResponse.json({
      success: true,
      data: {
        scheduledJobs,
        systemTime: new Date().toISOString()
      }
    });
  } catch (error: any) {
    console.error('Error in cron job schedule endpoint:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to retrieve cron job schedule',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
} 