// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { EmailTemplateType } from '@/lib/email';
import { getEmailTemplate, getBatchExtendedBodyAdmissionTemplate } from '@/services/emailTemplates';
import { v4 as uuidv4 } from 'uuid';

// Configure email transporter
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.example.com',
  port: process.env.SMTP_PORT ? parseInt(process.env.SMTP_PORT) : 587,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER || '<EMAIL>',
    pass: process.env.SMTP_PASSWORD || 'password',
  },
});

// In-memory store for batch processing status (in production use Redis or database)
const batchStatus = new Map<string, {
  total: number;
  sent: number;
  failed: number;
  inProgress: boolean;
  startTime: Date;
  endTime?: Date;
  errors: string[];
}>();

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { recipients, templateType, templateData, priority = 'NORMAL' } = body;

    if (!recipients || !recipients.length || !templateType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate a batch ID
    const batchId = uuidv4();

    // Initialize batch status
    batchStatus.set(batchId, {
      total: recipients.length,
      sent: 0,
      failed: 0,
      inProgress: true,
      startTime: new Date(),
      errors: [],
    });

    // Process batch asynchronously
    processBatch(batchId, recipients, templateType, templateData, priority);

    return NextResponse.json({
      success: true,
      data: {
        batchId,
        total: recipients.length,
        message: 'Batch email processing started',
      },
    });
  } catch (error: any) {
    console.error('Error processing batch emails:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to process batch emails' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const batchId = searchParams.get('batchId');

  if (!batchId) {
    return NextResponse.json(
      { success: false, error: 'Batch ID is required' },
      { status: 400 }
    );
  }

  const status = batchStatus.get(batchId);
  if (!status) {
    return NextResponse.json(
      { success: false, error: 'Batch not found' },
      { status: 404 }
    );
  }

  return NextResponse.json({
    success: true,
    data: {
      batchId,
      ...status,
      duration: status.endTime 
        ? Math.floor((status.endTime.getTime() - status.startTime.getTime()) / 1000) 
        : Math.floor((new Date().getTime() - status.startTime.getTime()) / 1000),
    },
  });
}

// Process emails in batch
async function processBatch(
  batchId: string,
  recipients: string[],
  templateType: EmailTemplateType,
  templateData: any,
  priority: string
) {
  try {
    // For EXTENDED_BODY_ADMISSION, use specialized batch template
    let template;
    if (templateType === 'EXTENDED_BODY_ADMISSION') {
      template = getBatchExtendedBodyAdmissionTemplate(templateData);
    } else {
      template = getEmailTemplate(templateType, templateData);
    }

    const status = batchStatus.get(batchId);
    if (!status) return;

    // Configure email priority headers
    const priorityHeader = 
      priority === 'HIGH' ? 'high' : 
      priority === 'LOW' ? 'low' : 'normal'; // Default is normal

    // Send emails to all recipients
    for (const recipient of recipients) {
      try {
        await transporter.sendMail({
          from: process.env.EMAIL_FROM || '<EMAIL>',
          to: recipient,
          subject: template.subject,
          text: template.text,
          html: template.html,
          priority: priorityHeader,
        });
        
        // Update sent count
        status.sent += 1;
      } catch (error: any) {
        // Update failed count and log errors
        status.failed += 1;
        status.errors.push(`Failed to send to ${recipient}: ${error.message}`);
        console.error(`Failed to send to ${recipient}:`, error);
      }
    }

    // Mark batch as complete
    status.inProgress = false;
    status.endTime = new Date();
    batchStatus.set(batchId, status);

    // Log completion
    console.log(`Batch ${batchId} completed: ${status.sent}/${status.total} emails sent`);
  } catch (error) {
    console.error('Error in batch processing:', error);
    const status = batchStatus.get(batchId);
    if (status) {
      status.inProgress = false;
      status.endTime = new Date();
      status.errors.push(`Batch processing error: ${error}`);
      batchStatus.set(batchId, status);
    }
  }
} 