// Force dynamic rendering
export const dynamic = "force-dynamic";
// Force dynamic rendering

import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { EmailTemplateType } from '@/lib/email';
import { getEmailTemplate } from '@/services/emailTemplates';

// Configure email transporter (in production use environment variables)
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.example.com',
  port: process.env.SMTP_PORT ? parseInt(process.env.SMTP_PORT) : 587,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER || '<EMAIL>',
    pass: process.env.SMTP_PASSWORD || 'password',
  },
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { to, subject, text, html, templateType, templateData, ...options } = body;

    // If using template, generate email content from template
    let emailHtml = html;
    let emailSubject = subject;
    let emailText = text;

    if (templateType && templateData) {
      const template = getEmailTemplate(templateType as EmailTemplateType, templateData);
      emailHtml = template.html;
      emailSubject = template.subject;
      emailText = template.text;
    }

    // Ensure we have the required fields
    if (!to || (!emailHtml && !emailText) || !emailSubject) {
      return NextResponse.json(
        { success: false, error: 'Missing required email fields' },
        { status: 400 }
      );
    }

    // Send the email
    const info = await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to,
      subject: emailSubject,
      text: emailText,
      html: emailHtml,
      ...options,
    });

    // Log for development
    if (process.env.NODE_ENV === 'development') {
      console.log('Email sent:', info);
    }

    return NextResponse.json({
      success: true,
      messageId: info.messageId,
    });
  } catch (error: any) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to send email' },
      { status: 500 }
    );
  }
} 