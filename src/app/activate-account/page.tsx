'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ear<PERSON><PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Stack,
  FormControl,
  FormLabel,
  Input,
  FormHelperText,
  CircularProgress,
  Divider,
  Alert,
  Box,
  Card,
  IconButton,
} from '@mui/joy';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAccountActivation } from '@/hooks/useAccountActivation';
import { CheckCircle, AlertTriangle, Key, Shield, Eye, EyeOff, RefreshCw, Clock, User, Mail, Building, Briefcase } from 'lucide-react';

// Form validation schema with password strength requirements
const activationSchema = z.object({
  token: z.string().min(5, 'Invalid activation token'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

type ActivationFormValues = z.infer<typeof activationSchema>;

export default function ActivateAccountPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');

  const [isSuccess, setIsSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const { loading, error, tokenInfo, validateToken, activateAccount, resetState } = useAccountActivation();

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<ActivationFormValues>({
    resolver: zodResolver(activationSchema),
    defaultValues: {
      token: token || '',
      password: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  });

  const password = watch('password');

  // Validate token on page load if token is provided
  useEffect(() => {
    if (token) {
      setValue('token', token);
      validateToken(token);
    }
  }, [token, validateToken, setValue]);

  const onSubmit = async (data: ActivationFormValues) => {
    // Ensure all required fields are present
    const activationData = {
      token: data.token,
      password: data.password,
      confirmPassword: data.confirmPassword
    };

    const result = await activateAccount(activationData);
    if (result) {
      setIsSuccess(true);
      // Redirect to login page after a delay
      setTimeout(() => {
        router.push('/auth/login');
      }, 3000);
    }
  };

  const handleRetry = () => {
    resetState();
    if (token) {
      validateToken(token);
    }
  };

  const handleManualEntry = () => {
    resetState();
    setValue('token', '');
  };

  // Password strength indicator
  const getPasswordStrength = (password: string) => {
    if (!password) return 0;

    let strength = 0;
    if (password.length >= 8) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    return strength;
  };

  const passwordStrength = getPasswordStrength(password);

  const getStrengthColor = (strength: number) => {
    if (strength <= 2) return 'danger';
    if (strength <= 3) return 'warning';
    if (strength <= 4) return 'primary';
    return 'success';
  };

  const getStrengthText = (strength: number) => {
    if (strength <= 2) return 'Weak';
    if (strength <= 3) return 'Fair';
    if (strength <= 4) return 'Good';
    return 'Strong';
  };

  if (isSuccess) {
    return (
      <div className="flex min-h-screen justify-center items-center p-4 bg-gray-50">
        <Sheet
          sx={{
            width: 400,
            mx: 'auto',
            my: 4,
            py: 3,
            px: 2,
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            borderRadius: 'md',
            boxShadow: 'lg',
          }}
          variant="outlined"
        >
          <Stack spacing={3} alignItems="center">
            <Box sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              bgcolor: 'success.100',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <CheckCircle color="green" size={48} />
            </Box>
            <Typography level="h4" component="h1">
              Account Activated!
            </Typography>
            <Typography textAlign="center">
              Your account has been successfully activated. You can now log in to access the GP Pathology platform.
            </Typography>
            <CircularProgress size="sm" />
            <Typography level="body-sm" textAlign="center">
              Redirecting to login page...
            </Typography>
            <Button
              component={Link}
              href="/auth/login"
              variant="outlined"
              size="sm"
            >
              Go to Login Now
            </Button>
          </Stack>
        </Sheet>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen justify-center items-center p-4 bg-gray-50">
      <Sheet
        sx={{
          width: 450,
          mx: 'auto',
          my: 4,
          py: 3,
          px: 3,
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          borderRadius: 'md',
          boxShadow: 'lg',
        }}
        variant="outlined"
      >
        <div>
          <Typography level="h4" component="h1">
            Activate Your Account
          </Typography>
          <Typography level="body-sm">
            Complete your registration to access the GP Pathology platform
          </Typography>
        </div>

        <Divider />

        {loading ? (
          <Stack alignItems="center" spacing={2} sx={{ py: 4 }}>
            <CircularProgress />
            <Typography level="body-sm">Validating your invitation...</Typography>
          </Stack>
        ) : error ? (
          <>
            <Alert
              variant="soft"
              color="danger"
              startDecorator={<AlertTriangle />}
              sx={{ mb: 2 }}
            >
              {error}
            </Alert>
            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="outlined"
                color="neutral"
                startDecorator={<RefreshCw size={16} />}
                onClick={handleRetry}
              >
                Try Again
              </Button>
              <Button
                variant="solid"
                color="primary"
                onClick={handleManualEntry}
              >
                Enter Token Manually
              </Button>
            </Stack>
          </>
        ) : tokenInfo && tokenInfo.valid ? (
          <>
            <Alert
              variant="soft"
              color="success"
              sx={{ mb: 2 }}
            >
              Welcome, {tokenInfo.name}! Please create your password to complete your account setup.
            </Alert>

            {/* User Information Card */}
            <Card variant="outlined" sx={{ mb: 2, p: 2 }}>
              <Typography level="title-sm" sx={{ mb: 1 }}>
                Your Account Information
              </Typography>
              <Stack spacing={1}>
                <Stack direction="row" spacing={1} alignItems="center">
                  <User size={16} />
                  <Typography level="body-sm">
                    <strong>Name:</strong> {tokenInfo.name}
                  </Typography>
                </Stack>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Mail size={16} />
                  <Typography level="body-sm">
                    <strong>Email:</strong> {tokenInfo.email}
                  </Typography>
                </Stack>
                {tokenInfo.role && (
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Shield size={16} />
                    <Typography level="body-sm">
                      <strong>Role:</strong> {tokenInfo.role}
                    </Typography>
                  </Stack>
                )}
                {tokenInfo.department && (
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Building size={16} />
                    <Typography level="body-sm">
                      <strong>Department:</strong> {tokenInfo.department}
                    </Typography>
                  </Stack>
                )}
                {tokenInfo.position && (
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Briefcase size={16} />
                    <Typography level="body-sm">
                      <strong>Position:</strong> {tokenInfo.position}
                    </Typography>
                  </Stack>
                )}
                {tokenInfo.expiresIn && (
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Clock size={16} />
                    <Typography level="body-sm">
                      <strong>Expires:</strong> In {tokenInfo.expiresIn}
                    </Typography>
                  </Stack>
                )}
              </Stack>
            </Card>

            <form onSubmit={handleSubmit(onSubmit)}>
              <Stack spacing={2}>
                <Controller
                  name="token"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="hidden"
                    />
                  )}
                />

                <Controller
                  name="password"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.password}>
                      <FormLabel>Password</FormLabel>
                      <Input
                        {...field}
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter a secure password"
                        startDecorator={<Key size={16} />}
                        endDecorator={
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            variant="plain"
                            color="neutral"
                            size="sm"
                          >
                            {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                          </IconButton>
                        }
                      />
                      {errors.password && (
                        <FormHelperText>{errors.password.message}</FormHelperText>
                      )}
                      {password && (
                        <Box sx={{ mt: 1 }}>
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Box
                              sx={{
                                height: 4,
                                flexGrow: 1,
                                borderRadius: 'sm',
                                bgcolor: 'neutral.200',
                                position: 'relative',
                                overflow: 'hidden',
                              }}
                            >
                              <Box
                                sx={{
                                  height: '100%',
                                  width: `${(passwordStrength / 5) * 100}%`,
                                  bgcolor: getStrengthColor(passwordStrength),
                                  transition: 'width 0.3s',
                                }}
                              />
                            </Box>
                            <Typography level="body-xs" color={getStrengthColor(passwordStrength)}>
                              {getStrengthText(passwordStrength)}
                            </Typography>
                          </Stack>
                        </Box>
                      )}
                    </FormControl>
                  )}
                />

                <Controller
                  name="confirmPassword"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.confirmPassword}>
                      <FormLabel>Confirm Password</FormLabel>
                      <Input
                        {...field}
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder="Confirm your password"
                        startDecorator={<Shield size={16} />}
                        endDecorator={
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            variant="plain"
                            color="neutral"
                            size="sm"
                          >
                            {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                          </IconButton>
                        }
                      />
                      {errors.confirmPassword && (
                        <FormHelperText>{errors.confirmPassword.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />

                <Button
                  type="submit"
                  loading={isSubmitting}
                  sx={{ mt: 1 }}
                  fullWidth
                >
                  Activate Account
                </Button>
              </Stack>
            </form>
          </>
        ) : (
          <>
            {tokenInfo?.error && (
              <Alert
                variant="soft"
                color="danger"
                startDecorator={<AlertTriangle />}
                sx={{ mb: 2 }}
              >
                {tokenInfo.error}
              </Alert>
            )}

            <Typography level="body-sm" sx={{ mb: 2 }}>
              Please enter the activation token from your invitation email to continue.
            </Typography>

            <form onSubmit={handleSubmit((data) => validateToken(data.token))}>
              <Stack spacing={2}>
                <Controller
                  name="token"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.token}>
                      <FormLabel>Activation Token</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter your activation token"
                      />
                      {errors.token && (
                        <FormHelperText>{errors.token.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />

                <Button
                  type="submit"
                  loading={loading}
                  fullWidth
                >
                  Validate Token
                </Button>
              </Stack>
            </form>

            <Typography level="body-xs" textAlign="center" sx={{ mt: 2 }}>
              If you don't have an activation token, please contact your administrator.
            </Typography>
          </>
        )}
      </Sheet>
    </div>
  );
}