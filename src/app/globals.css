/* Add these styles to the end of the file */

/* Mermaid Diagram Styles */
.mermaid-container {
  margin: 2rem 0;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: auto;
}

.mermaid-diagram {
  display: flex;
  justify-content: center;
  width: 100%;
}

.mermaid-diagram svg {
  max-width: 100%;
  height: auto;
}

/* Dark mode support */
.dark-mode .mermaid-container {
  background-color: #2d2d2d;
  color: #e0e0e0;
}

/* Loading spinner for mermaid diagrams */
.mermaid-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #666;
}

.mermaid-loading-spinner {
  margin-top: 1rem;
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: mermaid-spinner 1s ease-in-out infinite;
}

.dark-mode .mermaid-loading-spinner {
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top-color: #60a5fa;
}

@keyframes mermaid-spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.mermaid-error {
  padding: 1rem;
  border: 1px solid #f56565;
  border-radius: 8px;
  background-color: #fff5f5;
  margin: 1rem 0;
}

.dark-mode .mermaid-error {
  background-color: #3d1d1d;
  border-color: #9b2c2c;
}

/* Add these styles to the existing mermaid styles */

.direct-mermaid-container {
  margin-top: 2rem;
  border-top: 1px dashed #ccc;
  padding-top: 1rem;
}

.direct-mermaid-container::before {
  content: "Mermaid Diagrams";
  display: block;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #666;
}

.dark-mode .direct-mermaid-container {
  border-top-color: #555;
}

.dark-mode .direct-mermaid-container::before {
  color: #aaa;
} 