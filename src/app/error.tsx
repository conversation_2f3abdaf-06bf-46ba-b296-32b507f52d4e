'use client' // Error components must be Client Components

import { useEffect } from 'react'
import { 
  Button,
  Typography,
  Box
} from '@mui/joy';
import { useRouter } from 'next/navigation';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to your error reporting service
    console.error('Error occurred:', error);
  }, [error])

  const router = useRouter();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        textAlign: 'center',
        p: 2,
      }}
    >
      <Typography level="h1" sx={{ mb: 2 }}>500</Typography>
      <Typography level="h2" sx={{ mb: 4 }}>Something went wrong!</Typography>
      <Typography sx={{ mb: 4 }}>
        We apologize for the inconvenience. Please try again later.
      </Typography>
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button
          onClick={() => reset()}
          variant="outlined"
          color="neutral"
        >
          Try again
        </Button>
        <Button
          onClick={() => router.push('/dashboard')}
          variant="solid"
          color="primary"
        >
          Go to Dashboard
        </Button>
      </Box>
    </Box>
  )
}
