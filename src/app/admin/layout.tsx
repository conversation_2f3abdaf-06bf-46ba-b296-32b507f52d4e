"use client";
// Force dynamic rendering for all admin pages
export const dynamic = "force-dynamic";

/*DashboardLayout
- This component is a layout component that wraps the dashboard pages.
- It's a "blank" layout that doesn't have any specific styles or components because:
  * It will contain the dashboard pages that will have their own styles and components.
  * These Pages have their own Nested Layouts
- It will be used to wrap the dashboard pages.
*/

// Import dynamic configuration for all admin pages

import * as React from "react";
import { Box, Sheet, Drawer } from "@mui/joy";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import { CssVarsProvider } from "@mui/joy/styles";
import CssBaseline from "@mui/joy/CssBaseline";
import Layout from "@/components/admin/AdminLayout";
import { usePathname } from "next/navigation";


export default function AdminLayout(props: { children: React.ReactNode }) {
  //REFS

  //CONSTANTS
  const pathname = usePathname();

  //STATES
  const [addTableOpen, setAddTableOpen] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [editTableOpen, setEditTableOpen] = React.useState(false);

  //AI
  //-- allows for automated assisted navigation
  //-- form autocomplete and suggestions
  //useAIForm()

  //RENDERS
  return (
      <LocalizationProvider dateAdapter={AdapterMoment}>
        <CssVarsProvider disableTransitionOnChange>
          <CssBaseline />
          {drawerOpen && (
            <Layout.AdminSidebar
              onClose={() => setDrawerOpen(false)}
              navItems={[
                {
                  label: "Overview",
                  icon: "mdi:chart-bar",
                  link: `/admin`,
                  isActive: pathname === "/admin",
                },
                {
                  label: "Staff",
                  icon: "mdi:account",
                  link: `/dashboard/staff`,
                  isActive: pathname === "/dashboard/staff",
                },
                {
                  label: "Collections",
                  icon: "mdi:folder",
                  link: `/dashboard/collections`,
                  isActive: pathname === "/dashboard/collections",
                },
                {
                  label: "Mailer",
                  icon: "mdi:email",
                  link: `/dashboard/mailer`,
                  isActive: pathname === "/dashboard/mailer",
                },
                {
                  label: "Settings",
                  icon: "mdi:cog",
                  link: `/dashboard/settings`,
                  isActive: pathname === "/dashboard/settings",
                },
                {
                  label: "Query Logs",
                  icon: "mdi:database",
                  link: `/dashboard/query-logs`,
                  isActive: pathname === "/dashboard/query-logs",
                }
              ]}
            />
          )}

          <Layout.Root
            sx={{
              display: "flex",
              flexDirection: "row",
              gap: 0,
              overflow: "hidden",
            }}
          >
            <Layout.AdminSidebar
              sx={{
                height: "100%",
                overflowY: "auto",
              }}
              navItems={[
                {
                  label: "Dashboard",
                  icon: "mdi:view-dashboard",
                  link: `/admin`,
                  isActive: pathname === "/admin",
                },
                {
                  label: "User Management",
                  icon: "mdi:account-group",
                  link: `/admin/users`,
                  isActive: pathname === "/admin/users",
                  subItems: [
                    {
                      label: "All Users",
                      icon: "mdi:account-multiple",
                      link: `/admin/users/all`,
                      isActive: pathname === "/admin/users/all",
                    },
                    {
                      label: "Invitations",
                      icon: "mdi:account-plus",
                      link: `/admin/users/invitations`,
                      isActive: pathname === "/admin/users/invitations",
                    },
                    {
                      label: "Roles & Permissions",
                      icon: "mdi:shield-account",
                      link: `/admin/rbac`,
                      isActive: pathname === "/admin/rbac",
                    }
                  ]
                },
                {
                  label: "Facilities",
                  icon: "mdi:building",
                  link: `/admin/facilities`,
                  isActive: pathname === "/admin/facilities",
                  subItems: [

                  ]
                },
                {
                  label: "Mailer",
                  icon: "mdi:email",
                  link: `/admin/mailer`,
                  isActive: pathname === "/admin/mailer",
                  subItems: [
                    {
                      label: "Compose",
                      icon: "mdi:email-send",
                      link: `/admin/mailer`,
                      isActive: pathname === "/admin/mailer/compose",
                    },
                    {
                      label: "Templates",
                      icon: "mdi:file-document-outline",
                      link: `/admin/mailer/templates`,
                      isActive: pathname === "/admin/mailer/templates",
                    },
                    {
                      label: "Automation",
                      icon: "mdi:robot",
                      link: `/admin/mailer/automation`,
                      isActive: pathname === "/admin/mailer/automation",
                    }
                  ]
                },
                {
                  label: "Settings",
                  icon: "mdi:cog",
                  link: `/admin/settings`,
                  isActive: pathname.startsWith("/admin/settings"),
                  subItems: [
                    {
                      label: "General",
                      icon: "mdi:tune",
                      link: `/admin/settings/general`,
                      isActive: pathname === "/admin/settings/general",
                    },
                    {
                      label: "Security",
                      icon: "mdi:security",
                      link: `/admin/settings/security`,
                      isActive: pathname === "/admin/settings/security",
                    },
                    {
                      label: "Integrations",
                      icon: "mdi:puzzle",
                      link: `/admin/settings/integrations`,
                      isActive: pathname === "/admin/settings/integrations",
                    }
                  ]
                },
                {
                  label: "Tools",
                  icon: "mdi:tools",
                  link: `/admin/tools`,
                  isActive: pathname.startsWith("/admin/tools"),
                  subItems: [
                    {
                      label: "Body Tag Management",
                      icon: "mdi:tag-multiple",
                      link: `/admin/tools/body-tags`,
                      isActive: pathname === "/admin/tools/body-tags",
                    }
                  ]
                },
                {
                  label: "Audit Logs",
                  icon: "mdi:file-document-edit",
                  link: `/admin/audit`,
                  isActive: pathname === "/admin/audit",
                }
              ]}
            />

            <Box
              sx={{
                display: "flex",
                flex: 1,
                height: "100vh",
                flexDirection: "column",
                overflow: "hidden",
              }}
            >
              <Box
                sx={{
                  display: "block",
                  flex: 1,
                  height: "calc(100vh - 64px)",
                }}
              >
                {props.children}
              </Box>
            </Box>
          </Layout.Root>

          <Drawer
            open={addTableOpen}
            onClose={() => setAddTableOpen(false)}
            sx={{
              "--Drawer-horizontalSize": "clamp(500px, 40%, 100%)",
              ".MuiDrawer-content": {
                "&::-webkit-scrollbar": {
                  width: "6px",
                  height: "6px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "rgba(0, 0, 0, 0.2)",
                },
                "&::-webkit-scrollbar-thumb:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.3)",
                },
                "&::-webkit-scrollbar-track": {
                  backgroundColor: "rgba(0, 0, 0, 0.1)",
                },
              },
            }}
          >
            <Sheet
              sx={{
                mt: "48px",
                height: "100vh",
              }}
            ></Sheet>
          </Drawer>

          <Drawer
            open={editTableOpen}
            onClose={() => {
              setEditTableOpen(false);
            }}
            sx={{
              "--Drawer-horizontalSize": "clamp(500px, 40%, 100%)",
              ".MuiDrawer-content": {
                "&::-webkit-scrollbar": {
                  width: "6px",
                  height: "6px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "rgba(0, 0, 0, 0.2)",
                },
                "&::-webkit-scrollbar-thumb:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.3)",
                },
                "&::-webkit-scrollbar-track": {
                  backgroundColor: "rgba(0, 0, 0, 0.1)",
                },
              },
            }}
          >
            <Sheet
              sx={{
                mt: "48px",
                height: "100vh",
              }}
            ></Sheet>
          </Drawer>
        </CssVarsProvider>
      </LocalizationProvider>
  );
}
