"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import {
  Box,
  Typography,
  Card,
  Chip,
  Tab,
  Tab<PERSON>ist,
  TabPanel,
  Tabs,
  Table,
  Ava<PERSON>,
  <PERSON>ack,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import { useState } from "react";

// Mock data - replace with actual data fetching
const mockStaffMember = {
  id: 1,
  name: "Dr. <PERSON>",
  email: "<EMAIL>",
  role: "PATHOLOGIST",
  persalNumber: "PS001",
  employeeId: "EMP001",
  department: "Pathology",
  status: "active",
  lastLogin: "2024-01-08 14:30:00",
  phoneNumber: "+27 123 456 789",
  supervisor: "Dr. <PERSON>",
  permissions: ["VIEW_REPORTS", "EDIT_CASES", "MANAGE_SPECIMENS"],
  institution: "GP Pathology Main Branch",
  vehicleReg: "GP 123-456",
  image: null,
};

const mockActivityLog = [
  {
    id: 1,
    action: "LOGIN",
    timestamp: "2024-01-08 14:30:00",
    details: "Logged in from IP ***********",
    location: "Main Office",
  },
  {
    id: 2,
    action: "VIEW_CASE",
    timestamp: "2024-01-08 14:35:00",
    details: "Accessed case #12345",
    location: "Pathology Lab",
  },
];

const mockAssignedCases = [
  {
    id: 1,
    caseNumber: "CASE-2024-001",
    status: "In Progress",
    assignedDate: "2024-01-05",
    priority: "High",
  },
  {
    id: 2,
    caseNumber: "CASE-2024-002",
    status: "Pending Review",
    assignedDate: "2024-01-07",
    priority: "Medium",
  },
];

export default function StaffProfilePage({ params }) {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <Box sx={{ p: 4 }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "flex-start",
          mb: 4,
        }}
      >
        <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
          <Avatar
            size="lg"
            sx={{ "--Avatar-size": "64px" }}
          >
            {mockStaffMember.name[0]}
          </Avatar>
          <Box>
            <Typography level="h1">{mockStaffMember.name}</Typography>
            <Typography level="body-sm">{mockStaffMember.email}</Typography>
            <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
              <Chip
                size="sm"
                variant="soft"
                color="primary"
              >
                {mockStaffMember.role.replace("_", " ")}
              </Chip>
              <Chip
                size="sm"
                variant="soft"
                color={mockStaffMember.status === "active" ? "success" : "warning"}
              >
                {mockStaffMember.status}
              </Chip>
            </Box>
          </Box>
        </Box>
        <Button
          variant="outlined"
          color="neutral"
          startDecorator={<Icon icon="mdi:pencil" />}
        >
          Edit Profile
        </Button>
      </Box>

      {/* Main Content */}
      <Tabs
        value={activeTab}
        onChange={(event, value) => setActiveTab(value)}
        sx={{ bgcolor: "background.surface" }}
      >
        <TabList>
          <Tab>Overview</Tab>
          <Tab>Activity Log</Tab>
          <Tab>Assigned Cases</Tab>
          <Tab>Permissions</Tab>
        </TabList>

        <TabPanel value={0}>
          <Card variant="outlined">
            <Stack spacing={4} sx={{ p: 2 }}>
              <Box>
                <Typography level="title-md" sx={{ mb: 2 }}>
                  Personal Information
                </Typography>
                <Stack spacing={2}>
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Typography level="body-sm" sx={{ width: 200 }}>
                      Employee ID
                    </Typography>
                    <Typography>{mockStaffMember.employeeId}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Typography level="body-sm" sx={{ width: 200 }}>
                      Persal Number
                    </Typography>
                    <Typography>{mockStaffMember.persalNumber}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Typography level="body-sm" sx={{ width: 200 }}>
                      Phone Number
                    </Typography>
                    <Typography>{mockStaffMember.phoneNumber}</Typography>
                  </Box>
                </Stack>
              </Box>

              <Divider />

              <Box>
                <Typography level="title-md" sx={{ mb: 2 }}>
                  Employment Details
                </Typography>
                <Stack spacing={2}>
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Typography level="body-sm" sx={{ width: 200 }}>
                      Department
                    </Typography>
                    <Typography>{mockStaffMember.department}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Typography level="body-sm" sx={{ width: 200 }}>
                      Institution
                    </Typography>
                    <Typography>{mockStaffMember.institution}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Typography level="body-sm" sx={{ width: 200 }}>
                      Supervisor
                    </Typography>
                    <Typography>{mockStaffMember.supervisor}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Typography level="body-sm" sx={{ width: 200 }}>
                      Vehicle Registration
                    </Typography>
                    <Typography>{mockStaffMember.vehicleReg}</Typography>
                  </Box>
                </Stack>
              </Box>
            </Stack>
          </Card>
        </TabPanel>

        <TabPanel value={1}>
          <Card variant="outlined">
            <Table>
              <thead>
                <tr>
                  <th>Action</th>
                  <th>Timestamp</th>
                  <th>Details</th>
                  <th>Location</th>
                </tr>
              </thead>
              <tbody>
                {mockActivityLog.map((activity) => (
                  <tr key={activity.id}>
                    <td>
                      <Chip
                        size="sm"
                        variant="soft"
                        color="neutral"
                      >
                        {activity.action}
                      </Chip>
                    </td>
                    <td>{activity.timestamp}</td>
                    <td>{activity.details}</td>
                    <td>{activity.location}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Card>
        </TabPanel>

        <TabPanel value={2}>
          <Card variant="outlined">
            <Table>
              <thead>
                <tr>
                  <th>Case Number</th>
                  <th>Status</th>
                  <th>Assigned Date</th>
                  <th>Priority</th>
                </tr>
              </thead>
              <tbody>
                {mockAssignedCases.map((case_) => (
                  <tr key={case_.id}>
                    <td>{case_.caseNumber}</td>
                    <td>
                      <Chip
                        size="sm"
                        variant="soft"
                        color={case_.status === "In Progress" ? "primary" : "warning"}
                      >
                        {case_.status}
                      </Chip>
                    </td>
                    <td>{case_.assignedDate}</td>
                    <td>
                      <Chip
                        size="sm"
                        variant="soft"
                        color={case_.priority === "High" ? "danger" : "primary"}
                      >
                        {case_.priority}
                      </Chip>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Card>
        </TabPanel>

        <TabPanel value={3}>
          <Card variant="outlined">
            <Box sx={{ p: 2 }}>
              <Typography level="title-md" sx={{ mb: 2 }}>
                Assigned Permissions
              </Typography>
              <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                {mockStaffMember.permissions.map((permission) => (
                  <Chip
                    key={permission}
                    size="sm"
                    variant="soft"
                    color="primary"
                  >
                    {permission}
                  </Chip>
                ))}
              </Box>
            </Box>
          </Card>
        </TabPanel>
      </Tabs>
    </Box>
  );
} 