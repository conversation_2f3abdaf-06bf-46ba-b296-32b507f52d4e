"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from "react";
import {
  Box,
  Typography,
  Card,
  Table,
  Button,
  IconButton,
  Chip,
  Select,
  Option,
  Modal,
  ModalDialog,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Avatar,
} from "@mui/joy";
import { Icon } from "@iconify/react";

// Mock data - replace with actual data fetching
const mockSchedules = [
  {
    id: 1,
    staffMember: {
      name: "Dr. <PERSON>",
      role: "PATHOLOGIST",
      department: "Pathology",
    },
    shift: "Morning",
    startTime: "07:00",
    endTime: "15:00",
    date: "2024-01-10",
    status: "Scheduled",
    location: "Main Lab",
  },
  {
    id: 2,
    staffMember: {
      name: "<PERSON>",
      role: "MORGUE_STAFF",
      department: "Morgue",
    },
    shift: "Afternoon",
    startTime: "15:00",
    endTime: "23:00",
    date: "2024-01-10",
    status: "On Duty",
    location: "Morgue Facility",
  },
];

const shiftColors = {
  Morning: "success",
  Afternoon: "primary",
  Night: "warning",
};

const statusColors = {
  Scheduled: "neutral",
  "On Duty": "success",
  "On Leave": "warning",
  Absent: "danger",
};

export default function StaffSchedulesPage() {
  const [schedules, setSchedules] = useState(mockSchedules);
  const [openModal, setOpenModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState("all");
  const [selectedShift, setSelectedShift] = useState("all");

  const handleAddSchedule = () => {
    setOpenModal(true);
  };

  const filteredSchedules = schedules.filter((schedule) => {
    if (
      selectedDepartment !== "all" &&
      schedule.staffMember.department !== selectedDepartment
    )
      return false;
    if (selectedShift !== "all" && schedule.shift !== selectedShift) return false;
    return true;
  });

  return (
    <Box sx={{ p: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 4,
        }}
      >
        <Typography level="h1">Staff Schedules</Typography>
        <Box sx={{ display: "flex", gap: 2 }}>
          <Button
            variant="outlined"
            color="neutral"
            startDecorator={<Icon icon="mdi:calendar-export" />}
          >
            Export Schedule
          </Button>
          <Button
            startDecorator={<Icon icon="mdi:plus" />}
            onClick={handleAddSchedule}
          >
            Add Schedule
          </Button>
        </Box>
      </Box>

      <Card variant="outlined">
        <Box
          sx={{
            p: 2,
            display: "flex",
            gap: 2,
            borderBottom: "1px solid",
            borderColor: "divider",
          }}
        >
          <Select
            placeholder="Filter by Department"
            value={selectedDepartment}
            onChange={(_, value) => setSelectedDepartment(value)}
            sx={{ minWidth: 200 }}
          >
            <Option value="all">All Departments</Option>
            <Option value="Pathology">Pathology</Option>
            <Option value="Morgue">Morgue</Option>
            <Option value="Security">Security</Option>
          </Select>
          <Select
            placeholder="Filter by Shift"
            value={selectedShift}
            onChange={(_, value) => setSelectedShift(value)}
            sx={{ minWidth: 200 }}
          >
            <Option value="all">All Shifts</Option>
            <Option value="Morning">Morning</Option>
            <Option value="Afternoon">Afternoon</Option>
            <Option value="Night">Night</Option>
          </Select>
        </Box>

        <Table
          sx={{
            "& th": {
              backgroundColor: "var(--joy-palette-background-level1)",
            },
          }}
        >
          <thead>
            <tr>
              <th>Staff Member</th>
              <th>Date</th>
              <th>Shift</th>
              <th>Time</th>
              <th>Location</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredSchedules.map((schedule) => (
              <tr key={schedule.id}>
                <td>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1.5 }}>
                    <Avatar size="sm">{schedule.staffMember.name[0]}</Avatar>
                    <Box>
                      <Typography fontWeight="md">
                        {schedule.staffMember.name}
                      </Typography>
                      <Typography level="body-xs">
                        {schedule.staffMember.role.replace("_", " ")}
                      </Typography>
                    </Box>
                  </Box>
                </td>
                <td>{schedule.date}</td>
                <td>
                  <Chip
                    size="sm"
                    variant="soft"
                    color={shiftColors[schedule.shift]}
                  >
                    {schedule.shift}
                  </Chip>
                </td>
                <td>
                  {schedule.startTime} - {schedule.endTime}
                </td>
                <td>{schedule.location}</td>
                <td>
                  <Chip
                    size="sm"
                    variant="soft"
                    color={statusColors[schedule.status]}
                  >
                    {schedule.status}
                  </Chip>
                </td>
                <td>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <IconButton size="sm" variant="plain">
                      <Icon icon="mdi:pencil" />
                    </IconButton>
                    <IconButton size="sm" variant="plain" color="danger">
                      <Icon icon="mdi:delete" />
                    </IconButton>
                  </Box>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      </Card>

      <Modal open={openModal} onClose={() => setOpenModal(false)}>
        <ModalDialog
          aria-labelledby="schedule-modal-dialog-title"
          sx={{ maxWidth: 500 }}
        >
          <Typography id="schedule-modal-dialog-title" level="h2">
            Add Schedule
          </Typography>
          <Stack spacing={3} sx={{ mt: 2 }}>
            <FormControl>
              <FormLabel>Staff Member</FormLabel>
              <Select placeholder="Select staff member">
                <Option value="1">Dr. John Doe - Pathologist</Option>
                <Option value="2">Sarah Wilson - Morgue Staff</Option>
              </Select>
            </FormControl>
            <FormControl>
              <FormLabel>Date</FormLabel>
              <Input type="date" />
            </FormControl>
            <FormControl>
              <FormLabel>Shift</FormLabel>
              <Select defaultValue="Morning">
                <Option value="Morning">Morning (07:00 - 15:00)</Option>
                <Option value="Afternoon">Afternoon (15:00 - 23:00)</Option>
                <Option value="Night">Night (23:00 - 07:00)</Option>
              </Select>
            </FormControl>
            <FormControl>
              <FormLabel>Location</FormLabel>
              <Select>
                <Option value="Main Lab">Main Lab</Option>
                <Option value="Morgue Facility">Morgue Facility</Option>
                <Option value="Security Post">Security Post</Option>
              </Select>
            </FormControl>
            <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                color="neutral"
                onClick={() => setOpenModal(false)}
              >
                Cancel
              </Button>
              <Button onClick={() => setOpenModal(false)}>Add Schedule</Button>
            </Box>
          </Stack>
        </ModalDialog>
      </Modal>
    </Box>
  );
} 