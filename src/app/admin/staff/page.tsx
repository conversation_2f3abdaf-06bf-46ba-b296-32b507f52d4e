"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from "react";
import {
  Box,
  Typography,
  Card,
  Table,
  Button,
  IconButton,
  Chip,
  Input,
  Select,
  Option,
  Stack,
  Modal,
  ModalDialog,
  FormControl,
  FormLabel,
  Avatar,
} from "@mui/joy";
import { Icon } from "@iconify/react";

// Mock data - replace with actual data fetching
const mockStaff = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    email: "<EMAIL>",
    role: "PATHOLOGIST",
    persalNumber: "PS001",
    employeeId: "EMP001",
    department: "Pathology",
    status: "active",
    lastLogin: "2024-01-08 14:30:00",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "MORGUE_STAFF",
    persalNumber: "PS002",
    employeeId: "EMP002",
    department: "Morgue",
    status: "active",
    lastLogin: "2024-01-08 13:15:00",
  },
];

const roleColors = {
  PATHOLOGIST: "primary",
  MORGUE_STAFF: "neutral",
  SECURITY_STAFF: "warning",
  FIELD_EMPLOYEE: "success",
  ADMIN: "danger",
};

const StaffTable = ({ staff, onEdit, onDelete }) => (
  <Table
    aria-label="Staff members table"
    sx={{
      "& th": {
        backgroundColor: "var(--joy-palette-background-level1)",
      },
    }}
  >
    <thead>
      <tr>
        <th>Name</th>
        <th>Role</th>
        <th>Employee ID</th>
        <th>Department</th>
        <th>Status</th>
        <th>Last Login</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {staff.map((member) => (
        <tr key={member.id}>
          <td>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1.5 }}>
              <Avatar size="sm">{member.name[0]}</Avatar>
              <Box>
                <Typography fontWeight="md">{member.name}</Typography>
                <Typography level="body-xs">{member.email}</Typography>
              </Box>
            </Box>
          </td>
          <td>
            <Chip
              size="sm"
              variant="soft"
              color={roleColors[member.role] || "neutral"}
            >
              {member.role.replace("_", " ")}
            </Chip>
          </td>
          <td>
            <Typography level="body-sm">{member.employeeId}</Typography>
            <Typography level="body-xs">Persal: {member.persalNumber}</Typography>
          </td>
          <td>{member.department}</td>
          <td>
            <Chip
              size="sm"
              variant="soft"
              color={member.status === "active" ? "success" : "warning"}
            >
              {member.status}
            </Chip>
          </td>
          <td>
            <Typography level="body-xs">{member.lastLogin}</Typography>
          </td>
          <td>
            <Box sx={{ display: "flex", gap: 1 }}>
              <IconButton
                size="sm"
                variant="plain"
                color="neutral"
                onClick={() => onEdit(member)}
              >
                <Icon icon="mdi:pencil" />
              </IconButton>
              <IconButton
                size="sm"
                variant="plain"
                color="danger"
                onClick={() => onDelete(member)}
              >
                <Icon icon="mdi:delete" />
              </IconButton>
            </Box>
          </td>
        </tr>
      ))}
    </tbody>
  </Table>
);

export default function StaffManagementPage() {
  const [staff, setStaff] = useState(mockStaff);
  const [openModal, setOpenModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRole, setSelectedRole] = useState("all");
  const [selectedDepartment, setSelectedDepartment] = useState("all");

  const handleEdit = (member) => {
    // Implement edit functionality
    console.log("Edit member:", member);
  };

  const handleDelete = (member) => {
    // Implement delete functionality
    console.log("Delete member:", member);
  };

  const filteredStaff = staff.filter((member) => {
    if (selectedRole !== "all" && member.role !== selectedRole) return false;
    if (
      selectedDepartment !== "all" &&
      member.department !== selectedDepartment
    )
      return false;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        member.name.toLowerCase().includes(query) ||
        member.email.toLowerCase().includes(query) ||
        member.employeeId.toLowerCase().includes(query)
      );
    }
    return true;
  });

  return (
    <Box sx={{ p: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 4,
        }}
      >
        <Typography level="h1">Staff Management</Typography>
        <Button
          size="lg"
          startDecorator={<Icon icon="mdi:plus" />}
          onClick={() => setOpenModal(true)}
        >
          Add Staff Member
        </Button>
      </Box>

      <Card variant="outlined" sx={{ mb: 4 }}>
        <Box
          sx={{
            p: 2,
            display: "flex",
            gap: 2,
            borderBottom: "1px solid",
            borderColor: "divider",
          }}
        >
          <Input
            placeholder="Search staff..."
            startDecorator={<Icon icon="mdi:magnify" />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            sx={{ flex: 1 }}
          />
          <Select
            placeholder="Filter by Role"
            value={selectedRole}
            onChange={(_, value) => setSelectedRole(value)}
            sx={{ minWidth: 180 }}
          >
            <Option value="all">All Roles</Option>
            <Option value="PATHOLOGIST">Pathologist</Option>
            <Option value="MORGUE_STAFF">Morgue Staff</Option>
            <Option value="SECURITY_STAFF">Security Staff</Option>
            <Option value="FIELD_EMPLOYEE">Field Employee</Option>
            <Option value="ADMIN">Admin</Option>
          </Select>
          <Select
            placeholder="Filter by Department"
            value={selectedDepartment}
            onChange={(_, value) => setSelectedDepartment(value)}
            sx={{ minWidth: 180 }}
          >
            <Option value="all">All Departments</Option>
            <Option value="Pathology">Pathology</Option>
            <Option value="Morgue">Morgue</Option>
            <Option value="Security">Security</Option>
            <Option value="Field">Field</Option>
          </Select>
        </Box>

        <Box sx={{ p: 2 }}>
          <StaffTable
            staff={filteredStaff}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        </Box>
      </Card>

      <Modal open={openModal} onClose={() => setOpenModal(false)}>
        <ModalDialog
          aria-labelledby="staff-modal-dialog-title"
          sx={{ maxWidth: 600 }}
        >
          <Typography id="staff-modal-dialog-title" level="h2">
            Add Staff Member
          </Typography>
          <Stack spacing={3} sx={{ mt: 2 }}>
            <FormControl>
              <FormLabel>Full Name</FormLabel>
              <Input autoFocus required />
            </FormControl>
            <FormControl>
              <FormLabel>Email</FormLabel>
              <Input type="email" required />
            </FormControl>
            <FormControl>
              <FormLabel>Role</FormLabel>
              <Select defaultValue="FIELD_EMPLOYEE">
                <Option value="PATHOLOGIST">Pathologist</Option>
                <Option value="MORGUE_STAFF">Morgue Staff</Option>
                <Option value="SECURITY_STAFF">Security Staff</Option>
                <Option value="FIELD_EMPLOYEE">Field Employee</Option>
                <Option value="ADMIN">Admin</Option>
              </Select>
            </FormControl>
            <FormControl>
              <FormLabel>Department</FormLabel>
              <Input required />
            </FormControl>
            <FormControl>
              <FormLabel>Employee ID</FormLabel>
              <Input required />
            </FormControl>
            <FormControl>
              <FormLabel>Persal Number</FormLabel>
              <Input required />
            </FormControl>
            <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                color="neutral"
                onClick={() => setOpenModal(false)}
              >
                Cancel
              </Button>
              <Button onClick={() => setOpenModal(false)}>Add Staff Member</Button>
            </Box>
          </Stack>
        </ModalDialog>
      </Modal>
    </Box>
  );
} 