'use client';

import React from 'react';
import { Box, Typography, Breadcrumbs, Link, Divider } from '@mui/joy';
import { usePathname } from 'next/navigation';
import NextLink from 'next/link';
import { Icon } from '@iconify/react';

export default function ToolsLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  
  // Extract the current tool name from the pathname
  const toolName = pathname.split('/').pop();
  
  // Format the tool name for display
  const formatToolName = (name: string | undefined) => {
    if (!name) return 'Tools';
    
    // Convert kebab-case to Title Case
    return name
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Breadcrumbs navigation */}
      <Breadcrumbs 
        size="sm" 
        sx={{ mb: 2 }}
        separator={<Icon icon="mdi:chevron-right" fontSize="small" />}
      >
        <Link
          component={NextLink}
          href="/admin"
          underline="hover"
          color="neutral"
          fontSize="sm"
          startDecorator={<Icon icon="mdi:view-dashboard" />}
        >
          Admin
        </Link>
        <Link
          component={NextLink}
          href="/admin/tools"
          underline="hover"
          color="neutral"
          fontSize="sm"
          startDecorator={<Icon icon="mdi:tools" />}
        >
          Tools
        </Link>
        <Typography color="primary" fontWeight="lg" fontSize="sm">
          {formatToolName(toolName)}
        </Typography>
      </Breadcrumbs>
      
      <Divider sx={{ mb: 3 }} />
      
      {/* Page content */}
      {children}
    </Box>
  );
}
