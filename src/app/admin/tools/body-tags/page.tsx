'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Divider,
  <PERSON>rid,
  <PERSON>
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import { BodyTagGenerator } from '@/components/Forms/BodyTag/BodyTagGenerator';
import { BodyTagDebug } from '@/components/Debug/BodyTagDebug';

export default function BodyTagsManagementPage() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    count?: number;
  } | null>(null);

  const handleRemoveGeneratedTags = async () => {
    if (!confirm('Are you sure you want to remove all generated body tags? This action cannot be undone.')) {
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/body-tags/remove-generated', {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok) {
        setResult({
          success: true,
          message: data.message,
          count: data.count
        });
      } else {
        setResult({
          success: false,
          message: data.error || 'Failed to remove generated body tags'
        });
      }
    } catch (error) {
      console.error('Error removing generated body tags:', error);
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user is authorized (admin only)
  const isAuthorized = session?.user?.role === 'ADMIN';

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography level="h2">Body Tag Management</Typography>
          <Chip
            variant="soft"
            color="primary"
            startDecorator={<Icon icon="mdi:tools" />}
          >
            Administration Tool
          </Chip>
        </Box>

        <Grid container spacing={3}>
          <Grid xs={12} md={8}>
            {/* Debug Component - Remove this after fixing */}
            <BodyTagDebug />

            {/* Body Tag Generation Component */}
            <BodyTagGenerator
              variant="card"
              showTitle={true}
            />

            {/* Body Tag Cleanup Card */}
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Icon icon="mdi:tag-multiple" style={{ fontSize: '24px', color: 'var(--joy-palette-primary-500)' }} />
                    <Typography level="title-lg">
                      Body Tag Cleanup Tool
                    </Typography>
                  </Box>
                  <Typography>
                    This tool allows you to remove all body tags with status "GENERATED" that are not associated with any body, collection, or admission.
                  </Typography>
                  <Alert
                    color="warning"
                    startDecorator={<Icon icon="mdi:alert-circle" />}
                    variant="soft"
                    sx={{ mb: 2 }}
                  >
                    <Typography fontWeight="lg">Warning</Typography>
                    <Typography>
                      This action cannot be undone. Only use this tool if you are sure you want to remove all generated body tags.
                    </Typography>
                  </Alert>
                  <Divider />
                  {!isAuthorized ? (
                    <Alert color="danger" startDecorator={<Icon icon="mdi:lock" />}>
                      You do not have permission to use this tool. Only administrators can remove generated body tags.
                    </Alert>
                  ) : (
                    <Button
                      color="danger"
                      startDecorator={<Icon icon="mdi:trash" />}
                      onClick={handleRemoveGeneratedTags}
                      loading={isLoading}
                      disabled={isLoading}
                      size="lg"
                    >
                      Remove All Generated Body Tags
                    </Button>
                  )}
                </Stack>
              </CardContent>
            </Card>

            {result && (
              <Alert
                color={result.success ? 'success' : 'danger'}
                startDecorator={
                  result.success ?
                    <Icon icon="mdi:check-circle" /> :
                    <Icon icon="mdi:alert-circle" />
                }
                variant="soft"
                sx={{ mb: 3 }}
              >
                <Stack spacing={1}>
                  <Typography fontWeight="lg">
                    {result.success ? 'Success' : 'Error'}
                  </Typography>
                  <Typography>
                    {result.message}
                  </Typography>
                  {result.success && result.count !== undefined && (
                    <Typography>
                      Removed {result.count} generated body tags.
                    </Typography>
                  )}
                </Stack>
              </Alert>
            )}
          </Grid>

          <Grid xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography level="title-lg" sx={{ mb: 2 }}>
                  About Body Tags
                </Typography>
                <Stack spacing={2}>
                  <Typography>
                    Body tags are used to track bodies throughout the collection, admission, and release process.
                  </Typography>
                  <Typography>
                    Tags with status "GENERATED" are newly created tags that have not yet been assigned to a body.
                  </Typography>
                  <Typography>
                    Removing generated tags helps keep the system clean and prevents confusion when selecting tags for new collections.
                  </Typography>
                  <Divider />
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Icon icon="mdi:information-outline" style={{ color: 'var(--joy-palette-primary-500)' }} />
                    <Typography level="body-sm">
                      Only tags with status "GENERATED" that are not associated with any body, collection, or admission will be removed.
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </motion.div>
    </Box>
  );
}
