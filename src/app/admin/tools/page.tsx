'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";







import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  AspectRatio,
  Button
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import Link from 'next/link';

// Define the available admin tools
const adminTools = [
  {
    id: 'body-tags',
    title: 'Body Tag Management',
    description: 'Generate new body tags for facilities and manage existing tags, including removing unused generated tags.',
    icon: 'mdi:tag-multiple',
    color: '#e91e63',
    link: '/admin/tools/body-tags'
  },
  // Add more tools here as they are developed
];

export default function AdminToolsPage() {
  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography level="h2" sx={{ mb: 3 }}>
          Administration Tools
        </Typography>

        <Typography level="body-lg" sx={{ mb: 4 }}>
          These tools help you manage and maintain the system. Use them with caution as some actions cannot be undone.
        </Typography>

        <Grid container spacing={3}>
          {adminTools.map((tool) => (
            <Grid key={tool.id} xs={12} sm={6} md={4}>
              <motion.div
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
              >
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <AspectRatio
                      ratio="1"
                      sx={{
                        width: 60,
                        borderRadius: '50%',
                        bgcolor: `${tool.color}20`,
                        mb: 2
                      }}
                    >
                      <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Icon
                          icon={tool.icon}
                          style={{
                            fontSize: '30px',
                            color: tool.color
                          }}
                        />
                      </Box>
                    </AspectRatio>

                    <Typography level="title-lg" sx={{ mb: 1 }}>
                      {tool.title}
                    </Typography>

                    <Typography level="body-sm" sx={{ mb: 3, minHeight: '60px' }}>
                      {tool.description}
                    </Typography>

                    <Button
                      component={Link}
                      href={tool.link}
                      variant="soft"
                      color="primary"
                      fullWidth
                      endDecorator={<Icon icon="mdi:arrow-right" />}
                    >
                      Open Tool
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </motion.div>
    </Box>
  );
}
