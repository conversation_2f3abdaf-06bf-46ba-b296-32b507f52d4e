"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState } from "react";
import {
  Box,
  Typography,
  <PERSON>,
  Chip,
  Button,
  Stack,
  Avatar,
  Grid,
  List,
  ListItem,
  ListItemContent,
  ListDivider,
  Tabs,
  Tab<PERSON>ist,
  Tab,
  TabPanel,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import MapView from "../../../../components/collections/MapView";
import CommunicationLog from "../../../../components/collections/CommunicationLog";
import DocumentManager from "../../../../components/collections/DocumentManager";
import NotificationSystem from "../../../../components/collections/NotificationSystem";

// Mock data - replace with actual data fetching
const mockRequest = {
  id: 1,
  caseNumber: "COL-2024-001",
  location: {
    name: "City Hospital",
    address: "123 Main St, City Center",
    coordinates: { lat: -26.2041, lng: 28.0473 },
  },
  assignedTo: {
    id: 1,
    name: "<PERSON>",
    status: "en_route" as const,
    phone: "+27 123 456 789",
    vehicle: "GP 123-456",
    coordinates: { lat: -26.2051, lng: 28.0463 }, // Current location
  },
  status: "IN_PROGRESS" as const,
  priority: "HIGH" as const,
  requestedAt: "2024-01-10 09:30:00",
  expectedPickupTime: "2024-01-10 11:00:00",
  collectionType: "HOSPITAL" as const,
  details: "Deceased in hospital morgue, ready for collection",
  contactPerson: {
    name: "Dr. Sarah Wilson",
    phone: "+27 987 654 321",
    role: "Hospital Administrator",
  },
  timeline: [
    {
      time: "2024-01-10 09:30:00",
      status: "REQUEST_CREATED" as const,
      details: "Collection request created by Admin",
    },
    {
      time: "2024-01-10 09:35:00",
      status: "ASSIGNED" as const,
      details: "Assigned to John Smith",
    },
    {
      time: "2024-01-10 09:45:00",
      status: "ACCEPTED" as const,
      details: "Field worker accepted the request",
    },
    {
      time: "2024-01-10 10:00:00",
      status: "EN_ROUTE" as const,
      details: "Field worker en route to location",
    },
  ],
  messages: [
    {
      id: 1,
      sender: {
        id: "admin1",
        name: "Admin",
        role: "ADMIN" as const,
      },
      content: "Please confirm when you arrive at the location",
      timestamp: "2024-01-10 09:35:00",
      status: "READ" as const,
    },
    {
      id: 2,
      sender: {
        id: "fw1",
        name: "John Smith",
        role: "FIELD_WORKER" as const,
      },
      content: "I'm on my way, ETA 15 minutes",
      timestamp: "2024-01-10 09:45:00",
      status: "READ" as const,
    },
  ],
  documents: [
    {
      id: 1,
      name: "Hospital Release Form.pdf",
      type: "application/pdf" as const,
      category: "medical" as const,
      uploadedBy: "Dr. Sarah Wilson",
      uploadedAt: "2024-01-10 09:30:00",
      size: "1.2 MB",
      status: "VERIFIED" as const,
      url: "#",
      metadata: {
        version: "1.0",
        pageCount: 2,
      },
    },
    {
      id: 2,
      name: "ID Document.jpg",
      type: "image/jpeg" as const,
      category: "identification" as const,
      uploadedBy: "John Smith",
      uploadedAt: "2024-01-10 09:45:00",
      size: "800 KB",
      status: "PENDING" as const,
      url: "#",
      metadata: {
        dimensions: "1920x1080",
        format: "JPEG",
      },
    },
  ],
};

const statusColors = {
  PENDING: "neutral",
  IN_PROGRESS: "primary",
  COMPLETED: "success",
  CANCELLED: "danger",
};

const priorityColors = {
  HIGH: "danger",
  MEDIUM: "warning",
  LOW: "neutral",
};

const timelineStatusColors = {
  REQUEST_CREATED: "neutral",
  ASSIGNED: "primary",
  ACCEPTED: "info",
  EN_ROUTE: "warning",
  ARRIVED: "success",
  COMPLETED: "success",
  CANCELLED: "danger",
};

// Mock notifications data
const mockNotifications = [
  {
    id: 1,
    title: "Field Worker En Route",
    body: "John Smith is heading to the collection location",
    type: "STATUS_UPDATE" as const,
    priority: "MEDIUM",
    status: "DELIVERED",
    timestamp: "2024-01-10 09:40:00",
    metadata: {
      collectionId: "COL-2024-001",
      location: "City Hospital",
    },
  },
  {
    id: 2,
    title: "Document Verification Required",
    body: "New identification document needs verification",
    type: "DOCUMENT" as const,
    priority: "HIGH",
    status: "PENDING",
    timestamp: "2024-01-10 09:45:00",
    metadata: {
      collectionId: "COL-2024-001",
      documentId: "2",
      actionRequired: true,
    },
  },
];

interface PageProps {
  params: {
    id: string;
  };
}

export default function CollectionRequestDetailPage({ params }: PageProps) {
  const [activeTab, setActiveTab] = useState(0);

  const handleSendMessage = (content: string) => {
    console.log("Sending message:", content);
  };

  const handleUploadDocument = (file: File) => {
    console.log("Uploading document:", file);
  };

  const handleDeleteDocument = (id: number) => {
    console.log("Deleting document:", id);
  };

  const handleVerifyDocument = (id: number) => {
    console.log("Verifying document:", id);
  };

  const handleMarkAsRead = (id: number) => {
    console.log("Marking notification as read:", id);
  };

  const handleDeleteNotification = (id: number) => {
    console.log("Deleting notification:", id);
  };

  const handleCreateTemplate = (template: any) => {
    console.log("Creating notification template:", template);
  };

  return (
    <Box sx={{ p: 4 }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "flex-start",
          mb: 4,
        }}
      >
        <Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center", mb: 1 }}>
            <Typography level="h1">Collection Request</Typography>
            <Chip>{mockRequest.caseNumber}</Chip>
          </Box>
          <Box sx={{ display: "flex", gap: 1 }}>
            <Chip
              size="sm"
              variant="soft"
              color={statusColors[mockRequest.status]}
            >
              {mockRequest.status.replace("_", " ")}
            </Chip>
            <Chip
              size="sm"
              variant="soft"
              color={priorityColors[mockRequest.priority]}
            >
              Priority: {mockRequest.priority}
            </Chip>
            <Chip size="sm" variant="soft">
              {mockRequest.collectionType.replace("_", " ")}
            </Chip>
          </Box>
        </Box>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            variant="outlined"
            color="danger"
            startDecorator={<Icon icon="mdi:close" />}
          >
            Cancel Request
          </Button>
          <Button
            variant="outlined"
            color="neutral"
            startDecorator={<Icon icon="mdi:pencil" />}
          >
            Edit Request
          </Button>
        </Box>
      </Box>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        {/* Location Details */}
        <Grid xs={12} md={6}>
          <Card variant="outlined">
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:map-marker" />}
              sx={{ mb: 2 }}
            >
              Location Details
            </Typography>
            <Stack spacing={2}>
              <Box>
                <Typography level="body-sm">Location Name</Typography>
                <Typography fontWeight="md">
                  {mockRequest.location.name}
                </Typography>
              </Box>
              <Box>
                <Typography level="body-sm">Address</Typography>
                <Typography fontWeight="md">
                  {mockRequest.location.address}
                </Typography>
              </Box>
              <Box>
                <Typography level="body-sm">Coordinates</Typography>
                <Typography fontWeight="md">
                  {mockRequest.location.coordinates.lat},{" "}
                  {mockRequest.location.coordinates.lng}
                </Typography>
              </Box>
            </Stack>
          </Card>
        </Grid>

        {/* Contact Details */}
        <Grid xs={12} md={6}>
          <Card variant="outlined">
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:account" />}
              sx={{ mb: 2 }}
            >
              Contact Person
            </Typography>
            <Stack spacing={2}>
              <Box>
                <Typography level="body-sm">Name</Typography>
                <Typography fontWeight="md">
                  {mockRequest.contactPerson.name}
                </Typography>
              </Box>
              <Box>
                <Typography level="body-sm">Role</Typography>
                <Typography fontWeight="md">
                  {mockRequest.contactPerson.role}
                </Typography>
              </Box>
              <Box>
                <Typography level="body-sm">Phone</Typography>
                <Typography fontWeight="md">
                  {mockRequest.contactPerson.phone}
                </Typography>
              </Box>
            </Stack>
          </Card>
        </Grid>

        {/* Field Worker Details */}
        <Grid xs={12} md={6}>
          <Card variant="outlined">
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:account-hard-hat" />}
              sx={{ mb: 2 }}
            >
              Assigned Field Worker
            </Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
              <Avatar size="lg">{mockRequest.assignedTo.name[0]}</Avatar>
              <Box>
                <Typography fontWeight="md">
                  {mockRequest.assignedTo.name}
                </Typography>
                <Chip
                  size="sm"
                  variant="soft"
                  color="primary"
                  sx={{ mt: 0.5 }}
                >
                  {mockRequest.assignedTo.status.replace("_", " ")}
                </Chip>
              </Box>
            </Box>
            <Stack spacing={2}>
              <Box>
                <Typography level="body-sm">Phone</Typography>
                <Typography fontWeight="md">
                  {mockRequest.assignedTo.phone}
                </Typography>
              </Box>
              <Box>
                <Typography level="body-sm">Vehicle</Typography>
                <Typography fontWeight="md">
                  {mockRequest.assignedTo.vehicle}
                </Typography>
              </Box>
            </Stack>
          </Card>
        </Grid>

        {/* Timeline */}
        <Grid xs={12} md={6}>
          <Card variant="outlined">
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:timeline" />}
              sx={{ mb: 2 }}
            >
              Request Timeline
            </Typography>
            <List>
              {mockRequest.timeline.map((event, index) => (
                <React.Fragment key={index}>
                  <ListItem>
                    <ListItemContent>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                        }}
                      >
                        <Box>
                          <Chip
                            size="sm"
                            variant="soft"
                            color={timelineStatusColors[event.status]}
                          >
                            {event.status.replace("_", " ")}
                          </Chip>
                          <Typography level="body-sm" sx={{ mt: 1 }}>
                            {event.details}
                          </Typography>
                        </Box>
                        <Typography level="body-xs">{event.time}</Typography>
                      </Box>
                    </ListItemContent>
                  </ListItem>
                  {index < mockRequest.timeline.length - 1 && <ListDivider />}
                </React.Fragment>
              ))}
            </List>
          </Card>
        </Grid>

        {/* Add Notifications Grid Item */}
        <Grid xs={12}>
          <NotificationSystem
            notifications={mockNotifications}
            onMarkAsRead={handleMarkAsRead}
            onDelete={handleDeleteNotification}
            onCreateTemplate={handleCreateTemplate}
          />
        </Grid>
      </Grid>

      {/* Tabs for Map, Communication, and Documents */}
      <Tabs
        value={activeTab}
        onChange={(event: React.SyntheticEvent, value: number) => setActiveTab(value)}
        sx={{ bgcolor: "background.surface", my: 2 }}
      >
        <TabList>
          <Tab>Location Tracking</Tab>
          <Tab>Communication</Tab>
          <Tab>Documents</Tab>
        </TabList>

        <TabPanel value={0}>
          <MapView
            locations={[
              {
                name: mockRequest.location.name,
                coordinates: mockRequest.location.coordinates,
                type: mockRequest.collectionType,
              },
            ]}
            fieldWorkers={[
              {
                name: mockRequest.assignedTo.name,
                coordinates: mockRequest.assignedTo.coordinates,
                status: mockRequest.assignedTo.status,
              },
            ]}
          />
        </TabPanel>

        <TabPanel value={1}>
          <CommunicationLog
            messages={mockRequest.messages}
            onSendMessage={handleSendMessage}
          />
        </TabPanel>

        <TabPanel value={2}>
          <DocumentManager
            documents={mockRequest.documents}
            onUpload={handleUploadDocument}
            onDelete={handleDeleteDocument}
            onVerify={handleVerifyDocument}
          />
        </TabPanel>
      </Tabs>

      {/* Additional Details */}
      <Card variant="outlined">
        <Typography
          level="title-md"
          startDecorator={<Icon icon="mdi:information" />}
          sx={{ mb: 2 }}
        >
          Additional Details
        </Typography>
        <Typography>{mockRequest.details}</Typography>
      </Card>
    </Box>
  );
} 