"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useEffect, useState } from "react";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Box,
  Typography,
  Card,
  Table,
  Button,
  IconButton,
  Chip,
  Select,
  Option,
  Modal,
  ModalDialog,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Textarea,
  Divider,
} from "@mui/joy";
import { Icon } from "@iconify/react";

// Zod schema based on BodyCollection model and collection request requirements
const CollectionRequestSchema = z.object({
  collectionType: z.enum(["HOSPITAL", "CRIME_SCENE", "OTHER"]),
  locationName: z.string().min(1, "Location name is required"),
  address: z.string().min(1, "Address is required"),
  priority: z.enum(["HIGH", "MEDIUM", "LOW"]),
  assignedToId: z.string().min(1, "Field worker must be assigned"),
  expectedPickupTime: z.string().datetime(),
  details: z.string().optional(),
});

type CollectionRequestFormData = z.infer<typeof CollectionRequestSchema>;

// Mock data - replace with actual data fetching
const mockCollectionRequests = [
  {
    id: 1,
    caseNumber: "COL-2024-001",
    location: {
      name: "City Hospital",
      address: "123 Main St, City Center",
      coordinates: { lat: -26.2041, lng: 28.0473 },
    },
    assignedTo: {
      id: 1,
      name: "John Smith",
      status: "available",
    },
    status: "PENDING",
    priority: "HIGH",
    requestedAt: "2024-01-10 09:30:00",
    expectedPickupTime: "2024-01-10 11:00:00",
    collectionType: "HOSPITAL",
    details: "Deceased in hospital morgue, ready for collection",
  },
];

const statusColors = {
  PENDING: "neutral",
  IN_PROGRESS: "primary",
  COMPLETED: "success",
  CANCELLED: "danger",
};

const priorityColors = {
  HIGH: "danger",
  MEDIUM: "warning",
  LOW: "neutral",
};

const fieldWorkerStatuses = {
  available: "success",
  en_route: "primary",
  busy: "warning",
  offline: "neutral",
};

export default function CollectionRequestsPage() {
  const [requests, setRequests] = useState(mockCollectionRequests);
  const [openModal, setOpenModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedPriority, setSelectedPriority] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [fieldWorkers, setFieldWorkers] = useState([])

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CollectionRequestFormData>({
    resolver: zodResolver(CollectionRequestSchema),
    defaultValues: {
      collectionType: "HOSPITAL",
      priority: "MEDIUM",
    },
  });

  const handleCreateRequest = () => {
    setOpenModal(true);
  };

  const onSubmit = (data: CollectionRequestFormData) => {
    // TODO: Implement actual request creation logic
    console.log("Collection Request Data:", data);
    setOpenModal(false);
    reset();
  };

  useEffect(() => {
    const getFieldWorkers = async () => {
      try {
        const response = await fetch('/api/users?role=FIELD_WORKER')
        const data = await response.json()
        setFieldWorkers(data.users)
      } catch (error) {
        console.error('Error fetching field workers:', error)
      }
    }

    getFieldWorkers()
  }, [])

  const filteredRequests = requests.filter((request) => {
    if (selectedStatus !== "all" && request.status !== selectedStatus)
      return false;
    if (selectedPriority !== "all" && request.priority !== selectedPriority)
      return false;
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      return (
        request.caseNumber.toLowerCase().includes(searchLower) ||
        request.location.name.toLowerCase().includes(searchLower) ||
        request.assignedTo.name.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  return (
    <Box sx={{ p: 4 }}>
      <Stack direction="row" spacing={2} sx={{ mb: 4 }} alignItems="center">
        <Typography level="h2">Collection Requests</Typography>
        <Button
          startDecorator={<Icon icon="mdi:plus" />}
          onClick={handleCreateRequest}
        >
          New Request
        </Button>
      </Stack>

      <Card>
        <Box sx={{ p: 2 }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Input
              placeholder="Search requests..."
              startDecorator={<Icon icon="mdi:magnify" />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              sx={{ width: 300 }}
            />
            <Select
              placeholder="Status"
              value={selectedStatus}
              onChange={(_, value) => setSelectedStatus(value || "all")}
              sx={{ width: 150 }}
            >
              <Option value="all">All Status</Option>
              <Option value="PENDING">Pending</Option>
              <Option value="IN_PROGRESS">In Progress</Option>
              <Option value="COMPLETED">Completed</Option>
              <Option value="CANCELLED">Cancelled</Option>
            </Select>
            <Select
              placeholder="Priority"
              value={selectedPriority}
              onChange={(_, value) => setSelectedPriority(value || "all")}
              sx={{ width: 150 }}
            >
              <Option value="all">All Priority</Option>
              <Option value="HIGH">High</Option>
              <Option value="MEDIUM">Medium</Option>
              <Option value="LOW">Low</Option>
            </Select>
          </Stack>
        </Box>
        <Divider />
        <Table>
          <thead>
            <tr>
              <th>Case Number</th>
              <th>Location</th>
              <th>Type</th>
              <th>Assigned To</th>
              <th>Status</th>
              <th>Priority</th>
              <th>Expected Pickup</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredRequests.map((request) => (
              <tr key={request.id}>
                <td>{request.caseNumber}</td>
                <td>
                  <Stack spacing={0.5}>
                    <Typography level="body-sm">{request.location.name}</Typography>
                    <Typography level="body-xs" color="neutral">
                      {request.location.address}
                    </Typography>
                  </Stack>
                </td>
                <td>
                  <Chip
                    variant="soft"
                    size="sm"
                    color={request.collectionType === "CRIME_SCENE" ? "danger" : "primary"}
                  >
                    {request.collectionType.replace("_", " ")}
                  </Chip>
                </td>
                <td>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Chip
                      variant="soft"
                      size="sm"
                      color={fieldWorkerStatuses[request.assignedTo.status]}
                    >
                      {request.assignedTo.status}
                    </Chip>
                    <Typography level="body-sm">{request.assignedTo.name}</Typography>
                  </Stack>
                </td>
                <td>
                  <Chip
                    variant="soft"
                    size="sm"
                    color={statusColors[request.status]}
                  >
                    {request.status}
                  </Chip>
                </td>
                <td>
                  <Chip
                    variant="soft"
                    size="sm"
                    color={priorityColors[request.priority]}
                  >
                    {request.priority}
                  </Chip>
                </td>
                <td>
                  <Typography level="body-sm">
                    {new Date(request.expectedPickupTime).toLocaleString()}
                  </Typography>
                </td>
                <td>
                  <Stack direction="row" spacing={1}>
                    <IconButton
                      variant="plain"
                      color="neutral"
                      size="sm"
                      onClick={() => {/* TODO: Implement view details */}}
                    >
                      <Icon icon="mdi:eye" />
                    </IconButton>
                    <IconButton
                      variant="plain"
                      color="primary"
                      size="sm"
                      onClick={() => {/* TODO: Implement edit */}}
                    >
                      <Icon icon="mdi:pencil" />
                    </IconButton>
                    <IconButton
                      variant="plain"
                      color="danger"
                      size="sm"
                      onClick={() => {/* TODO: Implement delete */}}
                    >
                      <Icon icon="mdi:delete" />
                    </IconButton>
                  </Stack>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      </Card>

      <Modal open={openModal} onClose={() => setOpenModal(false)}>
        <ModalDialog
          aria-labelledby="collection-request-dialog-title"
          sx={{ maxWidth: 600 }}
        >
          <Typography id="collection-request-dialog-title" level="h2">
            Create Collection Request
          </Typography>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={3} sx={{ mt: 2 }}>
              <FormControl error={!!errors.collectionType}>
                <FormLabel>Collection Type</FormLabel>
                <Controller
                  name="collectionType"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onChange={(_, value) => field.onChange(value)}
                     {...field}>
                      <Option value="HOSPITAL">Hospital</Option>
                      <Option value="CRIME_SCENE">Crime Scene</Option>
                      <Option value="OTHER">Other</Option>
                    </Select>
                  )}
                />
                {errors.collectionType && (
                  <Typography level="body-xs" color="danger">
                    {errors.collectionType.message}
                  </Typography>
                )}
              </FormControl>

              <FormControl error={!!errors.locationName}>
                <FormLabel>Location Name</FormLabel>
                <Controller
                  name="locationName"
                  control={control}
                  render={({ field }) => <Input {...field} required />}
                />
                {errors.locationName && (
                  <Typography level="body-xs" color="danger">
                    {errors.locationName.message}
                  </Typography>
                )}
              </FormControl>

              <FormControl error={!!errors.address}>
                <FormLabel>Address</FormLabel>
                <Controller
                  name="address"
                  control={control}
                  render={({ field }) => (
                    <Textarea {...field} minRows={2} required />
                  )}
                />
                {errors.address && (
                  <Typography level="body-xs" color="danger">
                    {errors.address.message}
                  </Typography>
                )}
              </FormControl>

              <FormControl error={!!errors.priority}>
                <FormLabel>Priority</FormLabel>
                <Controller
                  name="priority"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onChange={(_, value) => field.onChange(value)}
                     {...field}>
                      <Option value="HIGH">High</Option>
                      <Option value="MEDIUM">Medium</Option>
                      <Option value="LOW">Low</Option>
                    </Select>
                  )}
                />
                {errors.priority && (
                  <Typography level="body-xs" color="danger">
                    {errors.priority.message}
                  </Typography>
                )}
              </FormControl>

              <FormControl error={!!errors.assignedToId}>
                <FormLabel>Assign To</FormLabel>
                <Controller
                  name="assignedToId"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onChange={(_, value) => field.onChange(value)}
                     {...field}
                    >
                      {fieldWorkers
                        .map(user => (
                          <Option key={user.id} value={user.id}>
                            {user.name}
                          </Option>
                        ))}
                    </Select>
                  )}
                />
                {errors.assignedToId && (
                  <Typography level="body-xs" color="danger">
                    {errors.assignedToId.message}
                  </Typography>
                )}
              </FormControl>

              <FormControl error={!!errors.expectedPickupTime}>
                <FormLabel>Expected Pickup Time</FormLabel>
                <Controller
                  name="expectedPickupTime"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} type="datetime-local" required />
                  )}
                />
                {errors.expectedPickupTime && (
                  <Typography level="body-xs" color="danger">
                    {errors.expectedPickupTime.message}
                  </Typography>
                )}
              </FormControl>

              <FormControl>
                <FormLabel>Additional Details</FormLabel>
                <Controller
                  name="details"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      minRows={3}
                      placeholder="Enter any additional information or special instructions..."
                    />
                  )}
                />
              </FormControl>

              <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
                <Button
                  variant="outlined"
                  color="neutral"
                  onClick={() => setOpenModal(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Create Request</Button>
              </Box>
            </Stack>
          </form>
        </ModalDialog>
      </Modal>
    </Box>
  );
} 