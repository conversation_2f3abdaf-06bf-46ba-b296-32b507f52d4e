"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from "react";
import {
  <PERSON>,
  Typography,
  <PERSON>ton,
  Card,
  Table,
  IconButton,
  Chip,
  Grid,
  Select,
  Option,
} from "@mui/joy";
import { Icon } from "@iconify/react";

// Mock data - replace with actual data fetching
const mockTimeEntries = [
  {
    id: 1,
    project: "Project Alpha",
    task: "Implementation",
    user: "John Doe",
    date: "2024-01-08",
    hours: 6,
    status: "Approved",
  },
  {
    id: 2,
    project: "Project Beta",
    task: "Code Review",
    user: "<PERSON>",
    date: "2024-01-08",
    hours: 4,
    status: "Pending",
  },
];

const TimeEntryTable = ({ entries }) => (
  <Table
    aria-label="Time entries table"
    sx={{
      "& th": {
        backgroundColor: "var(--joy-palette-background-level1)",
      },
    }}
  >
    <thead>
      <tr>
        <th>Date</th>
        <th>Project</th>
        <th>Task</th>
        <th>User</th>
        <th>Hours</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {entries.map((entry) => (
        <tr key={entry.id}>
          <td>{entry.date}</td>
          <td>{entry.project}</td>
          <td>{entry.task}</td>
          <td>{entry.user}</td>
          <td>{entry.hours}h</td>
          <td>
            <Chip
              size="sm"
              variant="soft"
              color={entry.status === "Approved" ? "success" : "warning"}
            >
              {entry.status}
            </Chip>
          </td>
          <td>
            <Box sx={{ display: "flex", gap: 1 }}>
              <IconButton size="sm" variant="plain">
                <Icon icon="mdi:pencil" />
              </IconButton>
              <IconButton size="sm" variant="plain" color="danger">
                <Icon icon="mdi:delete" />
              </IconButton>
            </Box>
          </td>
        </tr>
      ))}
    </tbody>
  </Table>
);

const TimeStats = () => (
  <Grid container spacing={2} sx={{ mb: 4 }}>
    <Grid xs={12} md={3}>
      <Card variant="soft" sx={{ p: 2 }}>
        <Typography level="body-xs">Total Hours This Week</Typography>
        <Typography level="h2">32h</Typography>
      </Card>
    </Grid>
    <Grid xs={12} md={3}>
      <Card variant="soft" sx={{ p: 2 }}>
        <Typography level="body-xs">Average Daily Hours</Typography>
        <Typography level="h2">6.4h</Typography>
      </Card>
    </Grid>
    <Grid xs={12} md={3}>
      <Card variant="soft" sx={{ p: 2 }}>
        <Typography level="body-xs">Pending Approvals</Typography>
        <Typography level="h2">3</Typography>
      </Card>
    </Grid>
    <Grid xs={12} md={3}>
      <Card variant="soft" sx={{ p: 2 }}>
        <Typography level="body-xs">Projects Active</Typography>
        <Typography level="h2">4</Typography>
      </Card>
    </Grid>
  </Grid>
);

export default function TimeTrackingPage() {
  const [timeEntries, setTimeEntries] = useState(mockTimeEntries);
  const [selectedProject, setSelectedProject] = useState("all");

  return (
    <Box sx={{ p: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 4,
        }}
      >
        <Typography level="h1">Time Tracking</Typography>
        <Button
          startDecorator={<Icon icon="mdi:plus" />}
          size="lg"
          color="primary"
        >
          Log Time
        </Button>
      </Box>

      <TimeStats />

      <Box
        sx={{
          display: "flex",
          gap: 2,
          mb: 3,
        }}
      >
        <Select
          placeholder="Filter by Project"
          value={selectedProject}
          onChange={(_, value) => setSelectedProject(value)}
          sx={{ minWidth: 200 }}
        >
          <Option value="all">All Projects</Option>
          <Option value="alpha">Project Alpha</Option>
          <Option value="beta">Project Beta</Option>
        </Select>

        <Select placeholder="Filter by User" sx={{ minWidth: 200 }}>
          <Option value="all">All Users</Option>
          <Option value="john">John Doe</Option>
          <Option value="jane">Jane Smith</Option>
        </Select>

        <Button
          variant="outlined"
          color="neutral"
          startDecorator={<Icon icon="mdi:download" />}
        >
          Export
        </Button>
      </Box>

      <Card variant="outlined">
        <TimeEntryTable entries={timeEntries} />
      </Card>
    </Box>
  );
} 