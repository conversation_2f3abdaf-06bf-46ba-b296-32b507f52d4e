"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { Box, Typography, Grid, Card } from "@mui/joy";
import { Icon } from "@iconify/react";

export default function WorkManagementPage() {
  return (
    <Box sx={{ p: 4 }}>
      <Typography level="h1" sx={{ mb: 4 }}>
        Work Management
      </Typography>

      <Grid container spacing={3}>
        <Grid xs={12} md={4}>
          <Card
            variant="outlined"
            sx={{
              p: 3,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 2,
            }}
          >
            <Icon icon="mdi:folder" width={40} height={40} />
            <Typography level="h3">Projects</Typography>
            <Typography>Manage and track all ongoing projects</Typography>
          </Card>
        </Grid>

        <Grid xs={12} md={4}>
          <Card
            variant="outlined"
            sx={{
              p: 3,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 2,
            }}
          >
            <Icon icon="mdi:checkbox-marked" width={40} height={40} />
            <Typography level="h3">Tasks</Typography>
            <Typography>Track and assign tasks across teams</Typography>
          </Card>
        </Grid>

        <Grid xs={12} md={4}>
          <Card
            variant="outlined"
            sx={{
              p: 3,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 2,
            }}
          >
            <Icon icon="mdi:clock" width={40} height={40} />
            <Typography level="h3">Time Tracking</Typography>
            <Typography>Monitor time spent on projects and tasks</Typography>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
} 