"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from "react";
import {
  <PERSON>,
  Typography,
  <PERSON>ton,
  Card,
  Chip,
  IconButton,
  Grid,
  Avatar,
} from "@mui/joy";
import { Icon } from "@iconify/react";

// Mock data - replace with actual data fetching
const mockTasks = {
  todo: [
    {
      id: 1,
      title: "Review Project Proposal",
      priority: "High",
      assignee: "John D.",
      dueDate: "2024-01-10",
    },
    {
      id: 2,
      title: "Update Documentation",
      priority: "Medium",
      assignee: "<PERSON>",
      dueDate: "2024-01-15",
    },
  ],
  inProgress: [
    {
      id: 3,
      title: "Implement New Feature",
      priority: "High",
      assignee: "<PERSON>",
      dueDate: "2024-01-12",
    },
  ],
  completed: [
    {
      id: 4,
      title: "Initial Setup",
      priority: "Medium",
      assignee: "Jane S.",
      dueDate: "2024-01-05",
    },
  ],
};

const TaskCard = ({ task }) => (
  <Card
    variant="outlined"
    sx={{
      mb: 2,
      cursor: "pointer",
      "&:hover": { bgcolor: "background.level1" },
    }}
  >
    <Typography level="title-sm">{task.title}</Typography>
    <Box sx={{ mt: 1, display: "flex", justifyContent: "space-between" }}>
      <Chip
        size="sm"
        variant="soft"
        color={task.priority === "High" ? "danger" : "primary"}
      >
        {task.priority}
      </Chip>
      <Avatar size="sm">{task.assignee[0]}</Avatar>
    </Box>
    <Box sx={{ mt: 1, display: "flex", justifyContent: "space-between" }}>
      <Typography level="body-xs">Due: {task.dueDate}</Typography>
      <IconButton size="sm" variant="plain">
        <Icon icon="mdi:dots-vertical" />
      </IconButton>
    </Box>
  </Card>
);

const Column = ({ title, tasks, icon }) => (
  <Card
    variant="outlined"
    sx={{
      p: 2,
      height: "100%",
      bgcolor: "background.level1",
    }}
  >
    <Box sx={{ display: "flex", alignItems: "center", mb: 2, gap: 1 }}>
      <Icon icon={icon} width={20} height={20} />
      <Typography level="title-lg">{title}</Typography>
      <Chip size="sm">{tasks.length}</Chip>
    </Box>
    <Box sx={{ minHeight: 400 }}>
      {tasks.map((task) => (
        <TaskCard key={task.id} task={task} />
      ))}
    </Box>
  </Card>
);

export default function TasksPage() {
  const [tasks, setTasks] = useState(mockTasks);

  return (
    <Box sx={{ p: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 4,
        }}
      >
        <Typography level="h1">Tasks</Typography>
        <Button
          startDecorator={<Icon icon="mdi:plus" />}
          size="lg"
          color="primary"
        >
          Add Task
        </Button>
      </Box>

      <Grid container spacing={2} sx={{ height: "calc(100vh - 200px)" }}>
        <Grid xs={12} md={4}>
          <Column
            title="To Do"
            tasks={tasks.todo}
            icon="mdi:clipboard-text-outline"
          />
        </Grid>
        <Grid xs={12} md={4}>
          <Column
            title="In Progress"
            tasks={tasks.inProgress}
            icon="mdi:progress-clock"
          />
        </Grid>
        <Grid xs={12} md={4}>
          <Column
            title="Completed"
            tasks={tasks.completed}
            icon="mdi:checkbox-marked-circle-outline"
          />
        </Grid>
      </Grid>
    </Box>
  );
} 