"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from "react";
import {
  Box,
  Typography,
  Button,
  Table,
  Sheet,
  IconButton,
  Chip,
} from "@mui/joy";
import { Icon } from "@iconify/react";

// Mock data - replace with actual data fetching
const mockProjects = [
  {
    id: 1,
    name: "Project Alpha",
    status: "In Progress",
    lead: "John Doe",
    deadline: "2024-01-15",
    team: 5,
  },
  {
    id: 2,
    name: "Project Beta",
    status: "Planning",
    lead: "<PERSON>",
    deadline: "2024-02-28",
    team: 3,
  },
];

export default function ProjectsPage() {
  const [projects, setProjects] = useState(mockProjects);

  return (
    <Box sx={{ p: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 4,
        }}
      >
        <Typography level="h1">Projects</Typography>
        <Button
          startDecorator={<Icon icon="mdi:plus" />}
          size="lg"
          color="primary"
        >
          New Project
        </Button>
      </Box>

      <Sheet variant="outlined" sx={{ borderRadius: "sm" }}>
        <Table
          aria-label="Projects table"
          sx={{
            "& th": {
              backgroundColor: "var(--joy-palette-background-level1)",
            },
          }}
        >
          <thead>
            <tr>
              <th>Project Name</th>
              <th>Status</th>
              <th>Project Lead</th>
              <th>Deadline</th>
              <th>Team Size</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {projects.map((project) => (
              <tr key={project.id}>
                <td>
                  <Typography fontWeight="lg">{project.name}</Typography>
                </td>
                <td>
                  <Chip
                    variant="soft"
                    color={
                      project.status === "In Progress" ? "primary" : "neutral"
                    }
                  >
                    {project.status}
                  </Chip>
                </td>
                <td>{project.lead}</td>
                <td>{project.deadline}</td>
                <td>{project.team} members</td>
                <td>
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <IconButton size="sm" variant="plain">
                      <Icon icon="mdi:pencil" />
                    </IconButton>
                    <IconButton size="sm" variant="plain" color="danger">
                      <Icon icon="mdi:delete" />
                    </IconButton>
                  </Box>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      </Sheet>
    </Box>
  );
} 