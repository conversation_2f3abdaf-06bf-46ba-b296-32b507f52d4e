'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";








import { useState } from 'react';
import Tabs from '@mui/joy/Tabs';
import TabList from '@mui/joy/TabList';
import Tab from '@mui/joy/Tab';
import TabPanel from '@mui/joy/TabPanel';
import { EmailComposer } from './components/EmailComposer';
import { TemplateDesigner } from './components/TemplateDesigner';
import { AutomationManager } from './components/AutomationManager';
import { AnalyticsDashboard } from './components/AnalyticsDashboard';

export default function MailerPage() {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <Tabs
        value={activeTab}
        onChange={(event, value) => setActiveTab(value as number)}
        sx={{
          bgcolor: 'background.surface',
          height: '100vh',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
         }}
      >
        <TabList>
          <Tab>Compose Email</Tab>
          <Tab>Templates</Tab>
          <Tab>Automation</Tab>
          <Tab>Analytics</Tab>
        </TabList>

        <TabPanel value={0}>
          <EmailComposer />
        </TabPanel>

        <TabPanel value={1}>
          <TemplateDesigner />
        </TabPanel>

        <TabPanel value={2}>
          <AutomationManager />
        </TabPanel>

        <TabPanel value={3}>
          <AnalyticsDashboard />
        </TabPanel>
      </Tabs>
  );
}
