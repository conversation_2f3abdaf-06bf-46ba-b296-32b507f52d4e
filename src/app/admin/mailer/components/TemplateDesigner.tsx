'use client';

import { useState, useCallback } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import Card from '@mui/joy/Card';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import Button from '@mui/joy/Button';
import Input from '@mui/joy/Input';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';
import Textarea from '@mui/joy/Textarea';
import Grid from '@mui/joy/Grid';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Alert from '@mui/joy/Alert';
import Sheet from '@mui/joy/Sheet';
import { EmailTemplateType } from '@/lib/email';
import { DraggableBlock } from './template-designer/DraggableBlock';
import { TemplateBlock, BlockType } from './template-designer/types';
import { generateHandlebarsTemplate } from './template-designer/templateGenerator';
import { BlockLibrary } from './template-designer/BlockLibrary';

const defaultBlocks: TemplateBlock[] = [
  {
    id: 'header',
    type: BlockType.Header,
    content: 'Welcome to {{companyName}}',
    styles: {
      textAlign: 'center',
      fontSize: '24px',
      color: '#333',
    },
  },
];

export function TemplateDesigner() {
  const [blocks, setBlocks] = useState<TemplateBlock[]>(defaultBlocks);
  const [selectedBlock, setSelectedBlock] = useState<TemplateBlock | null>(null);
  const [previewData, setPreviewData] = useState<Record<string, any>>({
    companyName: 'Our Company',
    userName: 'John Doe',
    message: 'Welcome to our platform!',
    items: ['Item 1', 'Item 2', 'Item 3'],
  });
  const [error, setError] = useState<string | null>(null);
  const [templateType, setTemplateType] = useState<EmailTemplateType>('WELCOME');

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setBlocks((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  }, []);

  const handleAddBlock = (blockType: BlockType) => {
    const newBlock: TemplateBlock = {
      id: `block-${Date.now()}`,
      type: blockType,
      content: '',
      styles: getDefaultStylesForBlock(blockType),
    };
    setBlocks((prev) => [...prev, newBlock]);
  };

  const handleUpdateBlock = (blockId: string, updates: Partial<TemplateBlock>) => {
    setBlocks((prev) =>
      prev.map((block) =>
        block.id === blockId ? { ...block, ...updates } : block
      )
    );
  };

  const handleDeleteBlock = (blockId: string) => {
    setBlocks((prev) => prev.filter((block) => block.id !== blockId));
  };

  const handlePreview = () => {
    try {
      const template = generateHandlebarsTemplate(blocks);
      // TODO: Implement preview logic with handlebars
      console.log('Generated template:', template);
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handleSave = async () => {
    try {
      const template = generateHandlebarsTemplate(blocks);
      // TODO: Implement save logic
      console.log('Saving template:', template);
    } catch (error: any) {
      setError(error.message);
    }
  };

  return (
    <Grid container spacing={2} sx={{ height: '100%' }}>
      <Grid xs={3}>
        <Card variant="outlined" sx={{ p: 2, height: '100%' }}>
          <BlockLibrary onAddBlock={handleAddBlock} />
        </Card>
      </Grid>

      <Grid xs={6}>
        <Card variant="outlined" sx={{ p: 2, height: '100%' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography level="h3">Template Editor</Typography>
            <FormControl size="sm">
              <Select
                value={templateType}
                onChange={(_, value) => value && setTemplateType(value as EmailTemplateType)}
              >
                <Option value="WELCOME">Welcome Email</Option>
                <Option value="OTP">OTP Verification</Option>
                <Option value="COLLECTION">Collection Notification</Option>
              </Select>
            </FormControl>
          </Box>

          {error && (
            <Alert color="danger" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Sheet
            variant="outlined"
            sx={{
              height: 'calc(100% - 100px)',
              overflowY: 'auto',
              p: 2,
              borderRadius: 'sm',
            }}
          >
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={blocks.map((block) => block.id)}
                strategy={verticalListSortingStrategy}
              >
                {blocks.map((block) => (
                  <DraggableBlock
                    key={block.id}
                    block={block}
                    isSelected={selectedBlock?.id === block.id}
                    onClick={() => setSelectedBlock(block)}
                    onUpdate={(updates) => handleUpdateBlock(block.id, updates)}
                    onDelete={() => handleDeleteBlock(block.id)}
                  />
                ))}
              </SortableContext>
            </DndContext>
          </Sheet>

          <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
            <Button
              variant="outlined"
              color="neutral"
              onClick={handlePreview}
            >
              Preview
            </Button>
            <Button onClick={handleSave}>
              Save Template
            </Button>
          </Box>
        </Card>
      </Grid>

      <Grid xs={3}>
        <Card variant="outlined" sx={{ p: 2, height: '100%' }}>
          <Typography level="h3" sx={{ mb: 2 }}>
            {selectedBlock ? 'Block Settings' : 'Preview Data'}
          </Typography>

          {selectedBlock ? (
            <BlockSettings
              block={selectedBlock}
              onUpdate={(updates) => handleUpdateBlock(selectedBlock.id, updates)}
            />
          ) : (
            <FormControl>
              <FormLabel>Sample Data (JSON):</FormLabel>
              <Textarea
                sx={{ fontFamily: 'monospace' }}
                minRows={10}
                value={JSON.stringify(previewData, null, 2)}
                onChange={(e) => {
                  try {
                    setPreviewData(JSON.parse(e.target.value));
                  } catch {
                    // Invalid JSON, ignore
                  }
                }}
              />
            </FormControl>
          )}
        </Card>
      </Grid>
    </Grid>
  );
}

function BlockSettings({
  block,
  onUpdate,
}: {
  block: TemplateBlock;
  onUpdate: (updates: Partial<TemplateBlock>) => void;
}) {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <FormControl>
        <FormLabel>Content:</FormLabel>
        <Textarea
          value={block.content}
          onChange={(e) => onUpdate({ content: e.target.value })}
          minRows={3}
        />
      </FormControl>

      <FormControl>
        <FormLabel>Font Size:</FormLabel>
        <Select
          value={block.styles.fontSize || '16px'}
          onChange={(_, value) =>
            value &&
            onUpdate({
              styles: { ...block.styles, fontSize: value },
            })
          }
        >
          <Option value="12px">Small</Option>
          <Option value="16px">Medium</Option>
          <Option value="24px">Large</Option>
          <Option value="32px">Extra Large</Option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel>Text Align:</FormLabel>
        <Select
          value={block.styles.textAlign || 'left'}
          onChange={(_, value) =>
            value &&
            onUpdate({
              styles: { ...block.styles, textAlign: value },
            })
          }
        >
          <Option value="left">Left</Option>
          <Option value="center">Center</Option>
          <Option value="right">Right</Option>
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel>Color:</FormLabel>
        <Input
          type="color"
          value={block.styles.color || '#000000'}
          onChange={(e) =>
            onUpdate({
              styles: { ...block.styles, color: e.target.value },
            })
          }
        />
      </FormControl>
    </Box>
  );
}

function getDefaultStylesForBlock(blockType: BlockType): Record<string, string> {
  switch (blockType) {
    case BlockType.Header:
      return {
        fontSize: '24px',
        fontWeight: 'bold',
        color: '#333',
        textAlign: 'center',
      };
    case BlockType.Text:
      return {
        fontSize: '16px',
        color: '#666',
        textAlign: 'left',
      };
    case BlockType.Image:
      return {
        width: '100%',
        maxWidth: '600px',
        margin: '0 auto',
      };
    case BlockType.Button:
      return {
        backgroundColor: '#007bff',
        color: '#ffffff',
        padding: '10px 20px',
        borderRadius: '4px',
        textAlign: 'center',
        textDecoration: 'none',
        display: 'inline-block',
      };
    case BlockType.Divider:
      return {
        borderTop: '1px solid #eee',
        margin: '20px 0',
      };
    default:
      return {};
  }
}
