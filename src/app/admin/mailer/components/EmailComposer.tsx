'use client';

import { useState, useEffect } from 'react';
import Card from '@mui/joy/Card';
import Input from '@mui/joy/Input';
import Button from '@mui/joy/Button';
import Textarea from '@mui/joy/Textarea';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Alert from '@mui/joy/Alert';
import CircularProgress from '@mui/joy/CircularProgress';
import Tabs from '@mui/joy/Tabs';
import TabList from '@mui/joy/TabList';
import Tab from '@mui/joy/Tab';
import TabPanel from '@mui/joy/TabPanel';
import { EmailTemplate, EmailTemplateType } from '@/types/email';

interface EmailData {
  to: string;
  subject: string;
  templateType?: EmailTemplateType;
  templateData?: Record<string, any>;
  htmlContent?: string;
}

export function EmailComposer() {
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [previewHtml, setPreviewHtml] = useState<string>('');
  const [activeTab, setActiveTab] = useState(0);

  const [emailData, setEmailData] = useState<EmailData>({
    to: '',
    subject: '',
    templateData: {},
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/email/templates');
      const data = await response.json();
      if (data.success) {
        setTemplates(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch templates:', error);
    }
  };

  const handleTemplateSelect = async (templateType: EmailTemplateType) => {
    try {
      const response = await fetch(`/api/email/templates?type=${templateType}`);
      const data = await response.json();
      if (data.success) {
        const template = data.data;
        setSelectedTemplate(template);
        setEmailData(prev => ({
          ...prev,
          templateType,
          subject: template.subject,
          templateData: {}
        }));
      }
    } catch (error) {
      console.error('Failed to fetch template:', error);
    }
  };

  const handlePreview = async () => {
    if (!selectedTemplate) return;

    try {
      const response = await fetch('/api/email/preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateType: selectedTemplate.type,
          templateData: emailData.templateData
        })
      });

      const data = await response.json();
      if (data.success) {
        setPreviewHtml(data.data.html);
        setActiveTab(1); // Switch to preview tab
      }
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  };

  const handleSendEmail = async () => {
    try {
      setSending(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/email/send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(emailData)
      });

      const data = await response.json();
      if (data.success) {
        setSuccess('Email sent successfully!');
        // Reset form
        setEmailData({
          to: '',
          subject: '',
          templateData: {},
        });
        setSelectedTemplate(null);
        setPreviewHtml('');
        setActiveTab(0);
      } else {
        throw new Error(data.error);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to send email');
    } finally {
      setSending(false);
    }
  };

  return (
    <Card variant="outlined" sx={{ p: 3 }}>
      {error && (
        <Alert color="danger" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Tabs value={activeTab} onChange={(_, value) => setActiveTab(value)}>
        <TabList>
          <Tab>Compose</Tab>
          <Tab>Preview</Tab>
        </TabList>

        <TabPanel value={0}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControl>
              <FormLabel>To:</FormLabel>
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={emailData.to}
                onChange={e => setEmailData(prev => ({ ...prev, to: e.target.value }))}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Template:</FormLabel>
              <Select
                value={selectedTemplate?.type || ''}
                onChange={(_, value) => value && handleTemplateSelect(value as EmailTemplateType)}
                placeholder="Select a template"
              >
                {templates.map(template => (
                  <Option key={template.id} value={template.type}>
                    {template.name}
                  </Option>
                ))}
              </Select>
            </FormControl>

            <FormControl>
              <FormLabel>Subject:</FormLabel>
              <Input
                placeholder="Email subject"
                value={emailData.subject}
                onChange={e => setEmailData(prev => ({ ...prev, subject: e.target.value }))}
              />
            </FormControl>

            {selectedTemplate && (
              <TemplateDataForm
                template={selectedTemplate}
                data={emailData.templateData || {}}
                onChange={data => setEmailData(prev => ({ ...prev, templateData: data }))}
              />
            )}

            {!selectedTemplate && (
              <FormControl>
                <FormLabel>Content:</FormLabel>
                <Textarea
                  minRows={8}
                  placeholder="Email content..."
                  value={emailData.htmlContent || ''}
                  onChange={e => setEmailData(prev => ({ ...prev, htmlContent: e.target.value }))}
                />
              </FormControl>
            )}

            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="outlined"
                color="neutral"
                onClick={() => {
                  setEmailData({
                    to: '',
                    subject: '',
                    templateData: {},
                  });
                  setSelectedTemplate(null);
                  setPreviewHtml('');
                }}
              >
                Clear
              </Button>
              {selectedTemplate && (
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={handlePreview}
                >
                  Preview
                </Button>
              )}
              <Button
                onClick={handleSendEmail}
                disabled={sending}
                startDecorator={sending && <CircularProgress size="sm" />}
              >
                {sending ? 'Sending...' : 'Send Email'}
              </Button>
            </Box>
          </Box>
        </TabPanel>

        <TabPanel value={1}>
          {previewHtml ? (
            <Box sx={{ 
              border: '1px solid', 
              borderColor: 'divider',
              borderRadius: 'sm',
              p: 2,
              maxHeight: '600px',
              overflow: 'auto'
            }}>
              <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
            </Box>
          ) : (
            <Typography level="body-sm" sx={{ color: 'text.secondary' }}>
              No preview available. Select a template and click Preview to see how your email will look.
            </Typography>
          )}
        </TabPanel>
      </Tabs>
    </Card>
  );
}

function TemplateDataForm({
  template,
  data,
  onChange
}: {
  template: EmailTemplate;
  data: Record<string, any>;
  onChange: (data: Record<string, any>) => void;
}) {
  // Extract variables from template content using regex
  const variables = Array.from(
    new Set(template.content.match(/{{([^{}]+)}}/g)?.map(v => v.replace(/[{}]/g, '').trim()) || [])
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography level="body-sm" sx={{ color: 'text.secondary' }}>
        Template: {template.name}
      </Typography>
      <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
        {template.description}
      </Typography>

      {variables.map(variable => {
        // Skip Handlebars helpers
        if (variable.startsWith('#') || variable.startsWith('/')) return null;

        const fieldName = variable.split(' ')[0]; // Get the variable name without any helpers
        const label = fieldName
          .split(/(?=[A-Z])/)
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');

        return (
          <FormControl key={fieldName}>
            <FormLabel>{label}:</FormLabel>
            <Input
              value={data[fieldName] || ''}
              onChange={e => onChange({ ...data, [fieldName]: e.target.value })}
              placeholder={`Enter ${label.toLowerCase()}`}
            />
          </FormControl>
        );
      })}
    </Box>
  );
}
