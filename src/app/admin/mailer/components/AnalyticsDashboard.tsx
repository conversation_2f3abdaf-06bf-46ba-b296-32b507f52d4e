'use client';

import { useEffect, useState } from 'react';
import Card from '@mui/joy/Card';
import Typography from '@mui/joy/Typography';
import Box from '@mui/joy/Box';
import Table from '@mui/joy/Table';
import Chip from '@mui/joy/Chip';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';
import { EmailTemplateType } from '@/types/email';
import { BarChart } from '@mui/x-charts/BarChart';

interface MetricSummary {
  templateType: EmailTemplateType;
  sent: number;
  failed: number;
  opened: number;
  clicked: number;
}

export function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState<number>(7);
  const [metrics, setMetrics] = useState<MetricSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchMetrics();
  }, [timeRange]);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/email/metrics?days=${timeRange}`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error);
      }

      // Process metrics into summary format
      const summaryMap = new Map<EmailTemplateType, MetricSummary>();
      
      data.data.forEach((metric: any) => {
        const current = summaryMap.get(metric.templateType) || {
          templateType: metric.templateType,
          sent: 0,
          failed: 0,
          opened: 0,
          clicked: 0
        };

        current[metric.status.toLowerCase()] = metric._count;
        summaryMap.set(metric.templateType, current);
      });

      setMetrics(Array.from(summaryMap.values()));
      setError(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const chartData = {
    series: [
      {
        data: metrics.map(m => m.sent),
        label: 'Sent',
        color: '#4CAF50'
      },
      {
        data: metrics.map(m => m.failed),
        label: 'Failed',
        color: '#f44336'
      },
      {
        data: metrics.map(m => m.opened),
        label: 'Opened',
        color: '#2196F3'
      },
      {
        data: metrics.map(m => m.clicked),
        label: 'Clicked',
        color: '#FF9800'
      }
    ],
    xAxis: {
      data: metrics.map(m => m.templateType),
      scaleType: 'band' as const,
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography level="h4">Email Analytics</Typography>
        <Select
          value={timeRange}
          onChange={(_, value) => setTimeRange(value as number)}
          sx={{ minWidth: 150 }}
        >
          <Option value={7}>Last 7 days</Option>
          <Option value={30}>Last 30 days</Option>
          <Option value={90}>Last 90 days</Option>
        </Select>
      </Box>

      {error && (
        <Card variant="soft" color="danger" sx={{ mb: 2 }}>
          <Typography>{error}</Typography>
        </Card>
      )}

      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 2, mb: 4 }}>
        <Card>
          <Typography level="h2">
            {metrics.reduce((sum, m) => sum + m.sent, 0)}
          </Typography>
          <Typography level="body-sm">Total Sent</Typography>
        </Card>
        <Card>
          <Typography level="h2" color="danger">
            {metrics.reduce((sum, m) => sum + m.failed, 0)}
          </Typography>
          <Typography level="body-sm">Failed</Typography>
        </Card>
        <Card>
          <Typography level="h2" color="success">
            {metrics.reduce((sum, m) => sum + m.opened, 0)}
          </Typography>
          <Typography level="body-sm">Opened</Typography>
        </Card>
        <Card>
          <Typography level="h2" color="primary">
            {metrics.reduce((sum, m) => sum + m.clicked, 0)}
          </Typography>
          <Typography level="body-sm">Clicked</Typography>
        </Card>
      </Box>

      <Card sx={{ mb: 4, height: 400 }}>
        <BarChart
          dataset={metrics.map(m => ({
            ...m,
            [m.templateType]: m.sent,
            type: m.templateType
          }))}
          series={chartData.series}
          xAxis={[chartData.xAxis]}
          height={350}
        />
      </Card>

      <Card>
        <Table>
          <thead>
            <tr>
              <th>Template Type</th>
              <th>Sent</th>
              <th>Failed</th>
              <th>Open Rate</th>
              <th>Click Rate</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {metrics.map((metric) => {
              const totalSent = metric.sent;
              const openRate = totalSent ? ((metric.opened / totalSent) * 100).toFixed(1) : '0';
              const clickRate = totalSent ? ((metric.clicked / totalSent) * 100).toFixed(1) : '0';
              
              return (
                <tr key={metric.templateType}>
                  <td>{metric.templateType}</td>
                  <td>{metric.sent}</td>
                  <td>{metric.failed}</td>
                  <td>{openRate}%</td>
                  <td>{clickRate}%</td>
                  <td>
                    <Chip
                      variant="soft"
                      color={metric.failed > 0 ? 'warning' : 'success'}
                    >
                      {metric.failed > 0 ? 'Issues Detected' : 'Healthy'}
                    </Chip>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </Table>
      </Card>
    </Box>
  );
}
