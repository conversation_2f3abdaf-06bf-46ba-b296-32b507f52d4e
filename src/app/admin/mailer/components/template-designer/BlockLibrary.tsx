'use client';

import Box from '@mui/joy/Box';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import ListItemButton from '@mui/joy/ListItemButton';
import ListItemContent from '@mui/joy/ListItemContent';
import Typography from '@mui/joy/Typography';
import { BlockType, BlockLibraryItem } from './types';

const blocks: BlockLibraryItem[] = [
  {
    type: BlockType.Header,
    label: 'Header',
    icon: 'H',
    description: 'Large text for sections',
    template: 'Welcome to {{companyName}}',
  },
  {
    type: BlockType.Text,
    label: 'Text',
    icon: 'T',
    description: 'Regular paragraph text',
    template: 'Hello {{userName}}, {{message}}',
  },
  {
    type: BlockType.Image,
    label: 'Image',
    icon: '🖼️',
    description: 'Insert an image',
    template: '{{imageUrl}}',
  },
  {
    type: BlockType.Button,
    label: 'Button',
    icon: '⚡',
    description: 'Clickable button',
    template: '{{buttonText}}',
  },
  {
    type: BlockType.Divider,
    label: 'Divider',
    icon: '—',
    description: 'Horizontal line separator',
    template: '',
  },
  {
    type: BlockType.List,
    label: 'List',
    icon: '•',
    description: 'Bulleted or numbered list',
    template: '{{#each items}}\n- {{this}}\n{{/each}}',
  },
  {
    type: BlockType.Conditional,
    label: 'Conditional',
    icon: '?',
    description: 'Show content conditionally',
    template: '{{#if condition}}Content{{/if}}',
  },
  {
    type: BlockType.Loop,
    label: 'Loop',
    icon: '↻',
    description: 'Repeat content for each item',
    template: '{{#each items}}\n{{this}}\n{{/each}}',
  },
];

interface BlockLibraryProps {
  onAddBlock: (type: BlockType) => void;
}

export function BlockLibrary({ onAddBlock }: BlockLibraryProps) {
  return (
    <List
      sx={{
        '--List-gap': '0.5rem',
        '--ListItem-radius': 'var(--joy-radius-sm)',
      }}
    >
      {blocks.map((block) => (
        <ListItem key={block.type}>
          <ListItemButton
            onClick={() => onAddBlock(block.type)}
            sx={{
              boxShadow: 'sm',
              bgcolor: 'background.surface',
            }}
          >
            <Box
              sx={{
                width: 24,
                height: 24,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 'sm',
                bgcolor: 'background.level1',
                mr: 1.5,
              }}
            >
              {block.icon}
            </Box>
            <ListItemContent>
              <Typography level="title-sm">{block.label}</Typography>
              <Typography level="body-xs">{block.description}</Typography>
            </ListItemContent>
          </ListItemButton>
        </ListItem>
      ))}
    </List>
  );
}
