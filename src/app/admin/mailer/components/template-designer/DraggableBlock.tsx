'use client';

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Box from '@mui/joy/Box';
import IconButton from '@mui/joy/IconButton';
import Typography from '@mui/joy/Typography';
import { TemplateBlock, BlockType } from './types';

interface DraggableBlockProps {
  block: TemplateBlock;
  isSelected: boolean;
  onClick: () => void;
  onUpdate: (updates: Partial<TemplateBlock>) => void;
  onDelete: () => void;
}

export function DraggableBlock({
  block,
  isSelected,
  onClick,
  onUpdate,
  onDelete,
}: DraggableBlockProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: block.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    ...block.styles,
  };

  const renderBlockContent = () => {
    switch (block.type) {
      case BlockType.Header:
      case BlockType.Text:
        return <Typography sx={block.styles}>{block.content}</Typography>;
      
      case BlockType.Image:
        return (
          <img
            src={block.content || 'https://via.placeholder.com/400x200'}
            alt="Template"
            style={block.styles}
          />
        );
      
      case BlockType.Button:
        return (
          <button style={block.styles as any}>
            {block.content || 'Click me'}
          </button>
        );
      
      case BlockType.Divider:
        return <hr style={block.styles} />;
      
      case BlockType.List:
        return (
          <ul style={block.styles as any}>
            {block.content.split('\n').map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        );
      
      case BlockType.Conditional:
        return (
          <Box sx={{ border: '1px dashed', p: 1, borderRadius: 'sm' }}>
            <Typography level="body-xs" sx={{ mb: 1 }}>
              If {block.conditions?.variable} {block.conditions?.operator} {block.conditions?.value}:
            </Typography>
            <Typography>{block.content}</Typography>
          </Box>
        );
      
      case BlockType.Loop:
        return (
          <Box sx={{ border: '1px dashed', p: 1, borderRadius: 'sm' }}>
            <Typography level="body-xs" sx={{ mb: 1 }}>
              For each {block.loop?.itemName} in {block.loop?.variable}:
            </Typography>
            <Typography>{block.content}</Typography>
          </Box>
        );
      
      default:
        return null;
    }
  };

  return (
    <Box
      ref={setNodeRef}
      sx={{
        position: 'relative',
        p: 2,
        mb: 1,
        border: '1px solid',
        borderColor: isSelected ? 'primary.500' : 'divider',
        borderRadius: 'sm',
        cursor: 'move',
        bgcolor: 'background.surface',
        opacity: isDragging ? 0.5 : 1,
        '&:hover': {
          '& .actions': {
            opacity: 1,
          },
        },
      }}
      onClick={onClick}
      {...attributes}
      {...listeners}
    >
      <Box
        className="actions"
        sx={{
          position: 'absolute',
          top: 4,
          right: 4,
          opacity: isSelected ? 1 : 0,
          transition: 'opacity 0.2s',
          display: 'flex',
          gap: 0.5,
        }}
      >
        <IconButton
          size="sm"
          variant="plain"
          color="danger"
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
        >
          ×
        </IconButton>
      </Box>

      {renderBlockContent()}
    </Box>
  );
}
