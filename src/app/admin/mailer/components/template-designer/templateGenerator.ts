import { TemplateBlock, BlockType } from './types';

const escapeHtml = (text: string): string => {
  const escapedText = text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
  return escapedText;
};

interface ResponsiveStyles {
  base: Record<string, string>;
  mobile?: Record<string, string>;
}

const getDefaultStylesForBlock = (blockType: BlockType): ResponsiveStyles => {
  switch (blockType) {
    case BlockType.Header:
      return {
        base: { 
          fontSize: '28px', 
          color: '#333',
          marginBottom: '20px',
          fontWeight: '600',
          lineHeight: '1.3'
        },
        mobile: {
          fontSize: '22px'
        }
      };
    case BlockType.Text:
      return {
        base: { 
          fontSize: '16px', 
          color: '#333',
          marginBottom: '16px',
          lineHeight: '1.6'
        },
        mobile: {
          fontSize: '14px'
        }
      };
    case BlockType.Image:
      return {
        base: { 
          width: '100%', 
          height: 'auto',
          display: 'block',
          margin: '20px auto'
        }
      };
    case BlockType.Button:
      return {
        base: { 
          backgroundColor: '#007bff', 
          color: '#fff', 
          padding: '12px 24px', 
          textDecoration: 'none', 
          display: 'inline-block',
          borderRadius: '4px',
          fontWeight: '600',
          textAlign: 'center',
          margin: '16px 0'
        },
        mobile: {
          width: '100%',
          padding: '10px 20px'
        }
      };
    case BlockType.Divider:
      return {
        base: { 
          width: '100%', 
          height: '1px', 
          backgroundColor: '#eee',
          margin: '24px 0',
          border: 'none'
        }
      };
    case BlockType.List:
      return {
        base: { 
          fontSize: '16px', 
          color: '#333',
          marginLeft: '20px',
          marginBottom: '16px'
        },
        mobile: {
          fontSize: '14px',
          marginLeft: '16px'
        }
      };
    default:
      return { base: {} };
  }
};

const generateBlockHtml = ({ block }: { block: TemplateBlock }): string => {
  const defaultStyles = getDefaultStylesForBlock(block.type);
  const customStyles = Object.entries(block.styles)
    .map(([key, value]) => `${key}: ${value}`)
    .join('; ');
  
  const baseStyles = `${Object.entries(defaultStyles.base)
    .map(([key, value]) => `${key}: ${value}`)
    .join('; ')}; ${customStyles}`;
  switch (block.type) {
    case BlockType.Header:
      return `<h1 style="${baseStyles}" class="mobile-text-center">{{{safeHtml content}}}</h1>`;

    case BlockType.Text:
      return `<p style="${baseStyles}">{{{safeHtml content}}}</p>`;

    case BlockType.Image:
      return `<img src="{{{content}}}" style="${baseStyles}" alt="${block.alt || 'Template Image'}" class="mobile-full-width" loading="lazy" />`;

    case BlockType.Button:
      return `<a href="${block.url || '#'}" style="${baseStyles}" class="mobile-full-width mobile-text-center" target="_blank" rel="noopener noreferrer">{{{safeHtml content}}}</a>`;

    case BlockType.Divider:
      return `<hr style="${baseStyles}" />`;

    case BlockType.List:
      return `<ul style="${baseStyles}">
        {{#each ${block.loop?.variable || 'items'}}}
          <li style="margin-bottom: 8px;">{{{safeHtml this}}}</li>
        {{/each}}
      </ul>`;

    case BlockType.Conditional:
      if (!block.conditions) return '{{{safeHtml content}}}';
      const { variable, operator, value } = block.conditions;
      let condition = '';
      
      switch (operator) {
        case 'equals':
          condition = `(eq ${variable} '${value}')`;
          break;
        case 'notEquals':
          condition = `(not (eq ${variable} '${value}'))`;
          break;
        case 'contains':
          condition = `(includes ${variable} '${value}')`;
          break;
        case 'notContains':
          condition = `(not (includes ${variable} '${value}'))`;
          break;
      }

      return `{{#if ${condition}}}
        {{{safeHtml content}}}
      {{/if}}`;

    case BlockType.Loop:
      if (!block.loop) return '{{{safeHtml content}}}';
      const { variable: loopVariable , itemName } = block.loop;
      return `{{#each ${loopVariable} as |${itemName}|}}
        {{{safeHtml ../content}}}
      {{/each}}`;

    default:
      return '{{{safeHtml content}}}';
  }
}

export function generateHandlebarsTemplate(blocks: TemplateBlock[]): string {
  const template = blocks
    .map((block) => generateBlockHtml({ block }))
    .join('\n');

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <title>{{subject}}</title>
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
  <style>
    :root {
      color-scheme: light dark;
      supported-color-schemes: light dark;
    }
    
    body {
      margin: 0;
      padding: 0;
      width: 100% !important;
      min-width: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .email-container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }

    img {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 0 auto;
    }

    a {
      color: #007bff;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    @media only screen and (max-width: 480px) {
      .email-container {
        padding: 10px !important;
      }

      .mobile-full-width {
        width: 100% !important;
      }

      .mobile-text-center {
        text-align: center !important;
      }

      .mobile-padding {
        padding: 10px !important;
      }
    }

    @media (prefers-color-scheme: dark) {
      body {
        background-color: #1a1a1a !important;
        color: #ffffff !important;
      }

      .email-container {
        background-color: #1a1a1a !important;
      }

      a {
        color: #66b3ff !important;
      }

      hr {
        border-color: #444 !important;
      }
    }

    [data-ogsc] .dark-img {
      display: block !important;
    }

    [data-ogsc] .light-img {
      display: none !important;
    }
  </style>
  <!--[if mso]>
  <style type="text/css">
    body, table, td {
      font-family: Arial, sans-serif !important;
    }
  </style>
  <![endif]-->
</head>
<body>
  <!--[if mso]>
  <div style="max-width: 600px; margin: 0 auto;">
  <![endif]-->
  <div class="email-container">
    ${template}
  </div>
  <!--[if mso]>
  </div>
  <![endif]-->
</body>
</html>
`.trim();
}
