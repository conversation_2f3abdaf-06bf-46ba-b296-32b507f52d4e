export enum BlockType {
  Header = 'header',
  Text = 'text',
  Image = 'image',
  Button = 'button',
  Divider = 'divider',
  List = 'list',
  Conditional = 'conditional',
  Loop = 'loop',
}

export interface TemplateBlock {
  id: string;
  type: BlockType;
  content: string;
  styles: Record<string, string>;
  conditions?: {
    variable: string;
    operator: 'equals' | 'notEquals' | 'contains' | 'notContains';
    value: string;
  };
  loop?: {
    variable: string;
    itemName: string;
  };
  alt?: string;
  url?: string;
  children?: TemplateBlock[];
  parent?: TemplateBlock;
  responsiveStyles?: ResponsiveStyles;
  className?: string;
  isHidden?: boolean;
}

export interface ResponsiveStyles {
  [key: string]: Record<string, string>;
}

export interface BlockLibraryItem {
  type: BlockType;
  label: string;
  icon: string;
  description: string;
  template: string;
}
