'use client';

import { useState } from 'react';
import Card from '@mui/joy/Card';
import Input from '@mui/joy/Input';
import Button from '@mui/joy/Button';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Alert from '@mui/joy/Alert';
import CircularProgress from '@mui/joy/CircularProgress';
import IconButton from '@mui/joy/IconButton';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import ListItemContent from '@mui/joy/ListItemContent';
import ListItemButton from '@mui/joy/ListItemButton';
import Switch from '@mui/joy/Switch';
import Divider from '@mui/joy/Divider';
import { EmailTemplateType } from '@/lib/email';

interface AutomationRule {
  id: string;
  name: string;
  event: string;
  templateType: EmailTemplateType;
  conditions: Array<{
    field: string;
    operator: string;
    value: string;
  }>;
  enabled: boolean;
}

const defaultRule: AutomationRule = {
  id: '',
  name: '',
  event: '',
  templateType: 'WELCOME',
  conditions: [],
  enabled: true
};

export function AutomationManager() {
  const [rules, setRules] = useState<AutomationRule[]>([]);
  const [selectedRule, setSelectedRule] = useState<AutomationRule | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSaveRule = async () => {
    if (!selectedRule) return;

    try {
      setSaving(true);
      setError(null);
      // TODO: Implement rule saving logic
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulated save

      setRules(prev => {
        const index = prev.findIndex(r => r.id === selectedRule.id);
        if (index === -1) {
          return [...prev, { ...selectedRule, id: Date.now().toString() }];
        }
        return prev.map(r => r.id === selectedRule.id ? selectedRule : r);
      });

      setSelectedRule(null);
      setIsEditing(false);
    } catch (error: any) {
      setError(error.message || 'Failed to save rule');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    try {
      // TODO: Implement rule deletion logic
      setRules(prev => prev.filter(r => r.id !== ruleId));
    } catch (error: any) {
      setError(error.message || 'Failed to delete rule');
    }
  };

  const handleToggleRule = async (ruleId: string, enabled: boolean) => {
    try {
      // TODO: Implement rule toggle logic
      setRules(prev => prev.map(r => 
        r.id === ruleId ? { ...r, enabled } : r
      ));
    } catch (error: any) {
      setError(error.message || 'Failed to toggle rule');
    }
  };

  return (
    <Box sx={{ display: 'flex', gap: 2, height: '100%' }}>
      <Card variant="outlined" sx={{ width: 300, p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography level="h3">Rules</Typography>
          <Button
            size="sm"
            onClick={() => {
              setSelectedRule({ ...defaultRule });
              setIsEditing(true);
            }}
          >
            New Rule
          </Button>
        </Box>

        <List>
          {rules.map(rule => (
            <ListItem key={rule.id}>
              <ListItemButton
                selected={selectedRule?.id === rule.id}
                onClick={() => {
                  setSelectedRule(rule);
                  setIsEditing(false);
                }}
              >
                <ListItemContent>
                  <Typography level="title-sm">{rule.name}</Typography>
                  <Typography level="body-sm" sx={{ color: 'text.secondary' }}>
                    {rule.event}
                  </Typography>
                </ListItemContent>
                <Switch
                  checked={rule.enabled}
                  onChange={e => handleToggleRule(rule.id, e.target.checked)}
                  onClick={e => e.stopPropagation()}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Card>

      <Card variant="outlined" sx={{ flex: 1, p: 3 }}>
        {error && (
          <Alert color="danger" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {selectedRule ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography level="h3">
                {isEditing ? (selectedRule.id ? 'Edit Rule' : 'New Rule') : 'Rule Details'}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {!isEditing ? (
                  <>
                    <Button
                      variant="outlined"
                      color="danger"
                      onClick={() => handleDeleteRule(selectedRule.id)}
                    >
                      Delete
                    </Button>
                    <Button
                      onClick={() => setIsEditing(true)}
                    >
                      Edit
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      variant="outlined"
                      color="neutral"
                      onClick={() => {
                        setSelectedRule(null);
                        setIsEditing(false);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSaveRule}
                      disabled={saving}
                      startDecorator={saving && <CircularProgress size="sm" />}
                    >
                      {saving ? 'Saving...' : 'Save'}
                    </Button>
                  </>
                )}
              </Box>
            </Box>

            {isEditing ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <FormControl>
                  <FormLabel>Rule Name:</FormLabel>
                  <Input
                    value={selectedRule.name}
                    onChange={e => setSelectedRule(prev => prev ? { ...prev, name: e.target.value } : null)}
                    placeholder="Enter rule name..."
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Event:</FormLabel>
                  <Select
                    value={selectedRule.event}
                    onChange={(_, value) => value && setSelectedRule(prev => prev ? { ...prev, event: value } : null)}
                  >
                    <Option value="user.created">User Created</Option>
                    <Option value="document.uploaded">Document Uploaded</Option>
                    <Option value="collection.ready">Collection Ready</Option>
                    <Option value="results.available">Results Available</Option>
                  </Select>
                </FormControl>

                <FormControl>
                  <FormLabel>Template:</FormLabel>
                  <Select
                    value={selectedRule.templateType}
                    onChange={(_, value) => value && setSelectedRule(prev => prev ? { ...prev, templateType: value as EmailTemplateType } : null)}
                  >
                    <Option value="WELCOME">Welcome Email</Option>
                    <Option value="DOCUMENT">Document Update</Option>
                    <Option value="COLLECTION">Collection Notification</Option>
                    <Option value="RESULTS_READY">Results Ready</Option>
                  </Select>
                </FormControl>

                <Typography level="title-md" sx={{ mt: 2 }}>
                  Conditions
                </Typography>
                
                {selectedRule.conditions.map((condition, index) => (
                  <Box key={index} sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
                    <FormControl sx={{ flex: 1 }}>
                      <Input
                        placeholder="Field"
                        value={condition.field}
                        onChange={e => {
                          const newConditions = [...selectedRule.conditions];
                          newConditions[index].field = e.target.value;
                          setSelectedRule(prev => prev ? { ...prev, conditions: newConditions } : null);
                        }}
                      />
                    </FormControl>
                    <FormControl sx={{ flex: 1 }}>
                      <Select
                        value={condition.operator}
                        onChange={(_, value) => {
                          if (!value) return;
                          const newConditions = [...selectedRule.conditions];
                          newConditions[index].operator = value;
                          setSelectedRule(prev => prev ? { ...prev, conditions: newConditions } : null);
                        }}
                      >
                        <Option value="equals">Equals</Option>
                        <Option value="contains">Contains</Option>
                        <Option value="greater_than">Greater Than</Option>
                        <Option value="less_than">Less Than</Option>
                      </Select>
                    </FormControl>
                    <FormControl sx={{ flex: 1 }}>
                      <Input
                        placeholder="Value"
                        value={condition.value}
                        onChange={e => {
                          const newConditions = selectedRule.conditions.filter((_, i) => i !== index);
                          newConditions[index].value = e.target.value;
                          setSelectedRule(prev => prev ? { ...prev, conditions: newConditions } : null);
                        }}
                      />
                    </FormControl>
                    <IconButton
                      variant="outlined"
                      color="danger"
                      onClick={() => {
                        const newConditions = selectedRule.conditions.filter((_, i) => i !== index);
                        setSelectedRule(prev => prev ? { ...prev, conditions: newConditions } : null);
                      }}
                    >
                      ×
                    </IconButton>
                  </Box>
                ))}

                <Button
                  variant="outlined"
                  onClick={() => {
                    const newConditions = [...selectedRule.conditions, { field: '', operator: 'equals', value: '' }];
                    setSelectedRule(prev => prev ? { ...prev, conditions: newConditions } : null);
                  }}
                >
                  Add Condition
                </Button>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box>
                  <Typography level="body-sm" sx={{ color: 'text.secondary' }}>Rule Name</Typography>
                  <Typography>{selectedRule.name}</Typography>
                </Box>
                <Divider />
                <Box>
                  <Typography level="body-sm" sx={{ color: 'text.secondary' }}>Event</Typography>
                  <Typography>{selectedRule.event}</Typography>
                </Box>
                <Divider />
                <Box>
                  <Typography level="body-sm" sx={{ color: 'text.secondary' }}>Template</Typography>
                  <Typography>{selectedRule.templateType}</Typography>
                </Box>
                <Divider />
                <Box>
                  <Typography level="body-sm" sx={{ color: 'text.secondary' }}>Conditions</Typography>
                  <List>
                    {selectedRule.conditions.map((condition, index) => (
                      <ListItem key={index}>
                        <Typography>
                          {condition.field} {condition.operator} {condition.value}
                        </Typography>
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Box>
            )}
          </Box>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
            <Typography level="body-lg" sx={{ color: 'text.secondary' }}>
              Select a rule to view or edit its details
            </Typography>
          </Box>
        )}
      </Card>
    </Box>
  );
}
