"use client"; // Error components must be Client Components

import { useEffect } from "react";
import { Button, Typography, Stack, Box } from "@mui/joy";
import { Icon } from "@iconify/react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        width: "100%",
        padding: "1rem",
      }}
    >
      <Stack direction="column" alignItems="center" spacing={2}>
        <Icon
          icon="bi:exclamation-triangle-fill"
          color="var(--joy-palette-primary-400)"
          width="100"
          height="100"
        />
        <Typography level="title-lg">An error occurred</Typography>
        <Typography level="body-md">{error.message}</Typography>
        <Button onClick={reset} variant="soft" color="primary">
          Reload
        </Button>
      </Stack>
    </Box>
  );
}
