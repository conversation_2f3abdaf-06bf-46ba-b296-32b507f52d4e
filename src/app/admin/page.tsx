"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState } from 'react';
import {
  Box,
  Grid,
  Typography,
  IconButton,
  CircularProgress,
  Card,
  CardContent,
  Tabs,
  TabList,
  Tab,
  TabPanel,
  Select,
  Option,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@mui/joy/styles';
import { SystemOverviewWidget, FacilityMetricsWidget, UserAnalyticsWidget, SystemAlertsWidget } from '@/components/AdminDashboard';

const AdminDashboardPage = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'tabs'>('grid');
  const [activeTab, setActiveTab] = useState<string>('overview');
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      <AnimatePresence mode="wait">
        <motion.div
          key="admin-dashboard"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Dashboard Header */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            mb: 3,
            flexWrap: 'wrap',
            gap: 2
          }}>
            <Box>
              <Typography level="h2" component="h1">Admin Dashboard</Typography>
              <Typography level="body-md" color="neutral">
                System overview and management
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Select 
                placeholder="View Mode" 
                defaultValue="grid"
                onChange={(_, value) => value && setViewMode(value as 'grid' | 'tabs')}
                startDecorator={<Icon icon="mdi:view-dashboard-outline" />}
                size="sm"
              >
                <Option value="grid">Grid View</Option>
                <Option value="tabs">Tab View</Option>
              </Select>
              
              <IconButton 
                variant="soft" 
                color="primary"
                component={motion.button}
                whileHover={{ rotate: 180 }}
                transition={{ duration: 0.3 }}
              >
                <Icon icon="mdi:refresh" />
              </IconButton>
            </Box>
          </Box>

          {viewMode === 'grid' ? (
            /* Grid View Layout */
            <Grid container spacing={3}>
              {/* System Overview Widget - Full Width */}
              <Grid xs={12}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  <SystemOverviewWidget />
                </motion.div>
              </Grid>
              
              {/* Two Column Layout for Metrics and User Analytics */}
              <Grid xs={12} md={7}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <FacilityMetricsWidget />
                </motion.div>
              </Grid>
              
              <Grid xs={12} md={5}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <SystemAlertsWidget />
                </motion.div>
              </Grid>
              
              {/* User Analytics Widget - Full Width */}
              <Grid xs={12}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <UserAnalyticsWidget />
                </motion.div>
              </Grid>
            </Grid>
          ) : (
            /* Tab View Layout */
            <Card>
              <Tabs
                aria-label="Dashboard tabs"
                value={activeTab}
                onChange={(event, value) => setActiveTab(value as string)}
                sx={{ borderRadius: 'lg' }}
              >
                <CardContent sx={{ p: 2 }}>
                  <TabList 
                    variant="soft" 
                    color="primary" 
                    sx={{ 
                      borderRadius: 'md',
                      p: 0.5,
                      overflow: 'auto',
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'center',
                      gap: 1
                    }}
                  >
                    <Tab 
                      value="overview" 
                      variant={activeTab === 'overview' ? 'solid' : 'plain'}
                      color={activeTab === 'overview' ? 'primary' : 'neutral'}
                      sx={{ flex: 1, maxWidth: 200 }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Icon icon="mdi:view-dashboard-outline" />
                        <Typography level="body-md">System Overview</Typography>
                      </Box>
                    </Tab>
                    <Tab 
                      value="facilities" 
                      variant={activeTab === 'facilities' ? 'solid' : 'plain'}
                      color={activeTab === 'facilities' ? 'primary' : 'neutral'}
                      sx={{ flex: 1, maxWidth: 200 }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Icon icon="mdi:hospital-building" />
                        <Typography level="body-md">Facility Metrics</Typography>
                      </Box>
                    </Tab>
                    <Tab 
                      value="users" 
                      variant={activeTab === 'users' ? 'solid' : 'plain'}
                      color={activeTab === 'users' ? 'primary' : 'neutral'}
                      sx={{ flex: 1, maxWidth: 200 }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Icon icon="mdi:account-group" />
                        <Typography level="body-md">User Analytics</Typography>
                      </Box>
                    </Tab>
                    <Tab 
                      value="alerts" 
                      variant={activeTab === 'alerts' ? 'solid' : 'plain'}
                      color={activeTab === 'alerts' ? 'primary' : 'neutral'}
                      sx={{ flex: 1, maxWidth: 200 }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Icon icon="mdi:bell-outline" />
                        <Typography level="body-md">System Alerts</Typography>
                      </Box>
                    </Tab>
                  </TabList>
                </CardContent>
                
                <TabPanel value="overview" sx={{ p: 2 }}>
                  <SystemOverviewWidget />
                </TabPanel>
                
                <TabPanel value="facilities" sx={{ p: 2 }}>
                  <FacilityMetricsWidget />
                </TabPanel>
                
                <TabPanel value="users" sx={{ p: 2 }}>
                  <UserAnalyticsWidget />
                </TabPanel>
                
                <TabPanel value="alerts" sx={{ p: 2 }}>
                  <SystemAlertsWidget />
                </TabPanel>
              </Tabs>
            </Card>
          )}
          
          {/* Admin Dashboard Footer - Quick Stats Card */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Box sx={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                gap: 4, 
                justifyContent: 'space-around',
                py: 1
              }}>
                <QuickStatItem
                  icon="mdi:account-check"
                  label="Active Users"
                  value={32}
                  color="primary"
                />
                <QuickStatItem
                  icon="mdi:server"
                  label="Server Status"
                  value="Optimal"
                  color="success"
                />
                <QuickStatItem
                  icon="mdi:clock-time-eight"
                  label="Avg Response"
                  value="1.2s"
                  color="info"
                />
                <QuickStatItem
                  icon="mdi:alert-circle"
                  label="Critical Issues"
                  value={2}
                  color="warning"
                />
                <QuickStatItem
                  icon="mdi:chart-timeline-variant"
                  label="System Load"
                  value="42%"
                  color="neutral"
                />
              </Box>
            </CardContent>
          </Card>
          
        </motion.div>
      </AnimatePresence>
    </Box>
  );
};

interface QuickStatItemProps {
  icon: string;
  label: string;
  value: string | number;
  color: 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'neutral';
}

const QuickStatItem = ({ icon, label, value, color }: QuickStatItemProps) => {
  return (
    <Box sx={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: 1.5
    }}>
      <Box sx={{ 
        bgcolor: `${color}.softBg`, 
        color: `${color}.solidBg`,
        p: 1,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Icon icon={icon} width={20} height={20} />
      </Box>
      <Box>
        <Typography level="body-xs" color="neutral">{label}</Typography>
        <Typography level="title-lg">{value}</Typography>
      </Box>
    </Box>
  );
};

export default AdminDashboardPage;