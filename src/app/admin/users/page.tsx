"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from "react";
import { Box, Typography, Button, Stack, IconButton, CircularProgress, Alert } from "@mui/joy";
import { Icon } from "@iconify/react";
import dynamicImport from "next/dynamic";
import { Suspense } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import UserStatsPanel from "./(components)/UserStatsPanel";

// Dynamically import UserList with no SSR
const UserList = dynamicImport(
  () => import("@/components/Users/<USER>"),
  {
    ssr: false,
    loading: () => (
      <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
        <CircularProgress />
      </Box>
    )
  }
);

// Dynamically import UserImport component
const UserImport = dynamicImport(
  () => import("@/components/Users/<USER>"),
  {
    ssr: false
  }
);

export default function UsersPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const router = useRouter();

  const handleError = (message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000);
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // Add refresh logic here
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      handleError("Failed to refresh users");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Stack spacing={2}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography level="h4">User Management</Typography>
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              variant="soft"
              color="neutral"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <Icon icon={isLoading ? "mdi:loading" : "mdi:refresh"} />
            </IconButton>
            <Button
              variant="outlined"
              startDecorator={<Icon icon="mdi:file-import" />}
              onClick={() => setIsImportModalOpen(true)}
            >
              Import
            </Button>
            <Link href="/admin/users/add">
              <Button
                startDecorator={<Icon icon="mdi:account-plus" />}
                onClick={() => router.push("/admin/users/add")}
              >
                Add User
              </Button>
            </Link>
          </Box>
        </Stack>

        <UserStatsPanel />

        {error && (
          <Alert color="danger" variant="soft">
            {error}
          </Alert>
        )}

        <Suspense fallback={
          <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
            <CircularProgress />
          </Box>
        }>
          <UserList onError={handleError} />
        </Suspense>
      </Stack>

      <UserImport 
        open={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onSuccess={handleRefresh}
      />
    </Box>
  );
}
