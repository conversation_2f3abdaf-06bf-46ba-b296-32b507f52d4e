"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from "react";
import { Box, Typo<PERSON>, <PERSON>ton, <PERSON>ack, Ta<PERSON>, <PERSON>b<PERSON><PERSON>, Tab, TabPanel } from "@mui/joy";
import { Icon } from "@iconify/react";
import RolesTable from "../(components)/roles/RolesTable";
import CreateRoleDialog from "../(components)/roles/CreateRoleDialog";
import PermissionsOverview from "../(components)/roles/PermissionsOverview";

export default function RolesPage() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  return (
    <Box sx={{ p: 2 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography level="h2">Role Management</Typography>
        <Button
          startDecorator={<Icon icon="mdi:shield-plus" />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Role
        </Button>
      </Stack>

      <Tabs
        value={activeTab}
        sx={{ bgcolor: 'background.surface' }}
      >
        <TabList>
          <Tab>Roles</Tab>
          <Tab>Permissions Overview</Tab>
        </TabList>
      
      
      <Box sx={{ mt: 2 }}>
        <TabPanel value={activeTab}>
          <RolesTable />
        </TabPanel>
        <TabPanel value={activeTab}>
          <PermissionsOverview />
        </TabPanel>
      </Box>
      </Tabs>

      <CreateRoleDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
      />
    </Box>
  );
}
