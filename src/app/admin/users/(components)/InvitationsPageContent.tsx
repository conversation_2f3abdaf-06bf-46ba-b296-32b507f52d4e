'use client';

import { useState } from 'react';
import { InvitationForm, InvitationsList } from './index';
import {
  Stack,
  Drawer,
  IconButton,
  Typography,
  Box
} from '@mui/joy';
import { X } from 'lucide-react';

export function InvitationsPageContent() {
  const [open, setOpen] = useState(false);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 4,
        p: 4,
      }}
    >
      <Drawer
        anchor="right"
        size="lg"
        open={open}
        onClose={() => setOpen(false)}
        /* slotProps={{
          content: {
            sx: { width: 450, p: 0 }
          }
        }} */
      >
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}
        >
          <Typography level="h4">Add New Invitation</Typography>
          <IconButton
            variant="plain"
            color="neutral"
            onClick={() => setOpen(false)}
          >
            <X size={18} />
          </IconButton>
        </Stack>
        <InvitationForm onComplete={() => setOpen(false)} />
      </Drawer>

      <InvitationsList onNewInvitation={() => setOpen(true)} />
    </Box>
  );
} 