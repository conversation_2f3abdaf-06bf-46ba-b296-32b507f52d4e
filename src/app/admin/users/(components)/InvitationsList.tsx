'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import {
  Sheet,
  Typography,
  Table,
  IconButton,
  Tooltip,
  Button,
  Stack,
  Chip,
  Input,
  Select,
  Option,
  Box,
  Divider,
  LinearProgress,
} from '@mui/joy';
import { 
  RefreshCw, 
  Search, 
  SendHorizonal, 
  Trash2, 
  X, 
  CheckCircle2, 
  Clock, 
  AlertCircle,
  Plus
} from 'lucide-react';
import { useInvitations } from '@/hooks/useInvitations';
import { formatDistanceToNow } from 'date-fns';

interface InvitationsListProps {
  onNewInvitation?: () => void;
}

export default function InvitationsList({ onNewInvitation }: InvitationsListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const initialLoadDone = useRef(false);
  
  const { 
    loading, 
    invitations, 
    fetchInvitations, 
    resendInvitation, 
    cancelInvitation 
  } = useInvitations();

  // Handle search input with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch invitations when filters change or on initial load
  useEffect(() => {
    if (!initialLoadDone.current) {
      fetchInvitations();
      initialLoadDone.current = true;
      return;
    }

    fetchInvitations(statusFilter || undefined, debouncedSearch || undefined);
  }, [fetchInvitations, statusFilter, debouncedSearch]);

  const handleResend = async (id: string) => {
    await resendInvitation(id);
  };

  const handleCancel = async (id: string) => {
    if (window.confirm('Are you sure you want to cancel this invitation?')) {
      await cancelInvitation(id);
    }
  };

  const handleRefresh = useCallback(() => {
    fetchInvitations(statusFilter || undefined, debouncedSearch || undefined);
  }, [fetchInvitations, statusFilter, debouncedSearch]);

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Chip color="primary" startDecorator={<Clock size={14} />}>Pending</Chip>;
      case 'ACCEPTED':
        return <Chip color="success" startDecorator={<CheckCircle2 size={14} />}>Accepted</Chip>;
      case 'EXPIRED':
        return <Chip color="warning" startDecorator={<AlertCircle size={14} />}>Expired</Chip>;
      case 'CANCELED':
        return <Chip color="danger" startDecorator={<X size={14} />}>Canceled</Chip>;
      default:
        return <Chip>{status}</Chip>;
    }
  };

  const formatRole = (role: string) => {
    return role.replace('_', ' ').toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
  };

  const isExpired = (expiresAt: string) => new Date(expiresAt) < new Date();

  return (
    <Sheet
      sx={{
        borderRadius: 'md',
        p: 3,
        boxShadow: 'sm',
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
        <Typography level="h4" component="h2">
          User Invitations
        </Typography>
        
        {onNewInvitation && (
          <Button
            variant="outlined"
            color="primary"
            startDecorator={<Plus size={16} />}
            onClick={onNewInvitation}
          >
            New Invitation
          </Button>
        )}
      </Stack>
      
      <Divider sx={{ my: 2 }} />
      
      <Stack direction="row" spacing={2} sx={{ mb: 3 }} justifyContent="space-between">
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ flex: 1 }}>
          <Input
            placeholder="Search by email or name"
            startDecorator={<Search size={16} />}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ flex: 1 }}
          />
          
          <Select
            placeholder="Filter by status"
            value={statusFilter}
            onChange={(_, value) => setStatusFilter(value)}
            sx={{ minWidth: 180 }}
          >
            <Option value={null}>All</Option>
            <Option value="PENDING">Pending</Option>
            <Option value="ACCEPTED">Accepted</Option>
            <Option value="EXPIRED">Expired</Option>
            <Option value="CANCELED">Canceled</Option>
          </Select>
        </Stack>
        
        <Button
          variant="outlined"
          color="neutral"
          startDecorator={<RefreshCw size={16} />}
          onClick={handleRefresh}
        >
          Refresh
        </Button>
      </Stack>
      
      {loading && <LinearProgress sx={{ my: 2 }} />}
      
      {invitations.length === 0 ? (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Typography level="body-lg">No invitations found</Typography>
          <Typography level="body-sm" color="neutral">
            {searchTerm || statusFilter
              ? 'Try adjusting your search or filters'
              : 'Send your first invitation to get started'}
          </Typography>
        </Box>
      ) : (
        <Table
          sx={{
            '& thead th:first-of-type': {
              width: '40%',
            },
          }}
          aria-label="Invitations table"
        >
          <thead>
            <tr>
              <th>Recipient</th>
              <th>Role</th>
              <th>Status</th>
              <th>Expires</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {invitations.map((invitation) => (
              <tr key={invitation.id}>
                <td>
                  <Stack>
                    <Typography level="body-sm" fontWeight="bold">
                      {invitation.name}
                    </Typography>
                    <Typography level="body-xs">{invitation.email}</Typography>
                  </Stack>
                </td>
                <td>
                  <Typography level="body-sm">
                    {formatRole(invitation.role)}
                  </Typography>
                </td>
                <td>
                  {getStatusChip(invitation.status)}
                </td>
                <td>
                  <Typography level="body-sm">
                    {invitation.status === 'PENDING' 
                      ? isExpired(invitation.expiresAt)
                        ? <Typography color="danger">Expired</Typography>
                        : formatDistanceToNow(new Date(invitation.expiresAt), { addSuffix: true })
                      : '—'}
                  </Typography>
                </td>
                <td>
                  <Stack direction="row" spacing={1}>
                    {invitation.status === 'PENDING' && (
                      <>
                        <Tooltip title="Resend invitation">
                          <IconButton
                            size="sm"
                            variant="outlined"
                            color="primary"
                            onClick={() => handleResend(invitation.id)}
                          >
                            <SendHorizonal size={16} />
                          </IconButton>
                        </Tooltip>
                        
                        <Tooltip title="Cancel invitation">
                          <IconButton
                            size="sm"
                            variant="outlined"
                            color="danger"
                            onClick={() => handleCancel(invitation.id)}
                          >
                            <Trash2 size={16} />
                          </IconButton>
                        </Tooltip>
                      </>
                    )}
                  </Stack>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </Sheet>
  );
} 