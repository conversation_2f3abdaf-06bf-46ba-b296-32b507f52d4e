"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  ModalClose,
  Typography,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Select,
  Option,
  Snackbar,
  Grid,
  Box,
  Divider,
} from "@mui/joy";
import { UserRole, UserStatus } from "@prisma/client";
import { z } from "zod";
import FacilitySelector from "@/components/Shared/FacilitySelector";
import PersonIcon from '@mui/icons-material/Person';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import WorkIcon from '@mui/icons-material/Work';
import PhoneIcon from '@mui/icons-material/Phone';
import HomeIcon from '@mui/icons-material/Home';
import ContactEmergencyIcon from '@mui/icons-material/ContactEmergency';
import BadgeIcon from '@mui/icons-material/Badge';
import AssignmentIndIcon from '@mui/icons-material/AssignmentInd';
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";

// Validation schema
const CreateUserSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
  role: z.nativeEnum(UserRole),
  status: z.nativeEnum(UserStatus).optional().default('ACTIVE'),
  department: z.string().optional(),
  facilityId: z.string().optional(),
  persalNumber: z.string().optional(),
  phoneNumber: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional(),
  dateOfBirth: z.date().optional().nullable(),
  hireDate: z.date().optional().nullable(),
  position: z.string().optional(),
});

interface CreateUserDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function CreateUserDialog({ 
  open, 
  onClose, 
}: CreateUserDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    role: UserRole.FIELD_EMPLOYEE,
    status: UserStatus.ACTIVE,
    department: "",
    facilityId: "",
    persalNumber: "",
    phoneNumber: "",
    address: "",
    emergencyContact: "",
    dateOfBirth: null as Date | null,
    hireDate: null as Date | null,
    position: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string | null>(null);

  const validateForm = () => {
    try {
      CreateUserSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMap = error.flatten().fieldErrors;
        const mappedErrors: { [key: string]: string } = {};
        
        Object.keys(errorMap).forEach((key) => {
          mappedErrors[key] = errorMap[key]?.[0] || "Invalid input";
        });
        
        setErrors(mappedErrors);
      }
      return false;
    }
  };

  const handleFacilityChange = (facilityId: string | null) => {
    setFormData(prev => ({
      ...prev,
      facilityId: facilityId || "",
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          dateOfBirth: formData.dateOfBirth ? formData.dateOfBirth.toISOString() : null,
          hireDate: formData.hireDate ? formData.hireDate.toISOString() : null,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to create user');
      }

      setSnackbarMessage('User created successfully');
      
      // Reset form after successful submission
      setFormData({
        name: "",
        email: "",
        password: "",
        role: UserRole.FIELD_EMPLOYEE,
        status: UserStatus.ACTIVE,
        department: "",
        facilityId: "",
        persalNumber: "",
        phoneNumber: "",
        address: "",
        emergencyContact: "",
        dateOfBirth: null,
        hireDate: null,
        position: "",
      });

      // Close dialog after a short delay to show success message
      setTimeout(onClose, 1500);
    } catch (error) {
      console.error('User creation error:', error);
      setSnackbarMessage(
        error instanceof Error 
          ? error.message 
          : 'An unexpected error occurred'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Modal open={open} onClose={onClose}>
        <ModalDialog 
          sx={{
            width: "100%",
            maxWidth: "80vw",
            height: "100%",
            maxHeight: "90vh",
            overflowY: 'auto',
            borderRadius: 'lg',
            boxShadow: 'lg',
            backgroundColor: 'background.surface',
          }}
          variant="outlined"
        >
          <ModalClose />
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 2, 
              mb: 3,
              borderBottom: '1px solid',
              borderColor: 'divider',
              pb: 2,
            }}
          >
            <AssignmentIndIcon color="primary" fontSize="large" />
            <Typography level="h4" component="h1">Create New User</Typography>
          </Box>

          <form onSubmit={handleSubmit}>
            <Grid 
              container 
              spacing={3} 
              sx={{ 
                flexGrow: 1, 
                px: 2,
                '& .MuiFormControl-root': { 
                  width: '100%' 
                } 
              }}
            >
              {/* Personal Information Section */}
              <Grid xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography level="title-md" startDecorator={<PersonIcon color="primary" />} sx={{ mb: 2 }}>
                    Personal Details
                  </Typography>
                  <Divider />
                </Box>

                <Stack spacing={2}>
                  <FormControl error={!!errors.name} required>
                    <FormLabel>Full Name</FormLabel>
                    <Input
                      startDecorator={<PersonIcon />}
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                      error={!!errors.name}
                      placeholder="Enter full name"
                    />
                    {errors.name && (
                      <Typography level="body-sm" color="danger">
                        {errors.name}
                      </Typography>
                    )}
                  </FormControl>

                  <FormControl error={!!errors.email} required>
                    <FormLabel>Email Address</FormLabel>
                    <Input
                      type="email"
                      startDecorator={<EmailIcon />}
                      value={formData.email}
                      onChange={(e) =>
                        setFormData({ ...formData, email: e.target.value })
                      }
                      error={!!errors.email}
                      placeholder="Enter email address"
                    />
                    {errors.email && (
                      <Typography level="body-sm" color="danger">
                        {errors.email}
                      </Typography>
                    )}
                  </FormControl>

                  <FormControl error={!!errors.password} required>
                    <FormLabel>Password</FormLabel>
                    <Input
                      type="password"
                      startDecorator={<LockIcon />}
                      value={formData.password}
                      onChange={(e) =>
                        setFormData({ ...formData, password: e.target.value })
                      }
                      error={!!errors.password}
                      placeholder="Create a strong password"
                    />
                    {errors.password && (
                      <Typography level="body-sm" color="danger">
                        {errors.password}
                      </Typography>
                    )}
                  </FormControl>

                  <LocalizationProvider dateAdapter={AdapterMoment}>
                    <FormControl>
                      <FormLabel>Date of Birth</FormLabel>
                      <DatePicker
                        name="dateOfBirth"
                        value={formData.dateOfBirth}
                        onChange={(newValue) => 
                          setFormData({ ...formData, dateOfBirth: newValue })
                        }
                      />
                    </FormControl>
                  </LocalizationProvider>
                </Stack>
              </Grid>

              {/* Professional Information Section */}
              <Grid xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography level="title-md" startDecorator={<WorkIcon color="primary" />} sx={{ mb: 2 }}>
                    Professional Details
                  </Typography>
                  <Divider />
                </Box>

                <Stack spacing={2}>
                  <FormControl>
                    <FormLabel>Position</FormLabel>
                    <Input
                      startDecorator={<BadgeIcon />}
                      value={formData.position}
                      onChange={(e) =>
                        setFormData({ ...formData, position: e.target.value })
                      }
                      placeholder="Enter job position"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel>Persal Number</FormLabel>
                    <Input
                      startDecorator={<AssignmentIndIcon />}
                      value={formData.persalNumber}
                      onChange={(e) =>
                        setFormData({ ...formData, persalNumber: e.target.value })
                      }
                      placeholder="Enter persal number"
                    />
                  </FormControl>

                  <LocalizationProvider dateAdapter={AdapterMoment}>
                    <FormControl>
                      <FormLabel>Hire Date</FormLabel>
                      <DatePicker
                        value={formData.hireDate}
                        onChange={(newValue) => 
                          setFormData({ ...formData, hireDate: newValue })
                        }
                        placeholder="Select hire date"
                      />
                    </FormControl>
                  </LocalizationProvider>

                  <FormControl>
                    <FormLabel>Role</FormLabel>
                    <Select
                      startDecorator={<WorkIcon />}
                      value={formData.role}
                      onChange={(_, value) =>
                        setFormData({ ...formData, role: value as UserRole })
                      }
                      placeholder="Select user role"
                    >
                      {Object.values(UserRole).map((role) => (
                        <Option key={role} value={role}>
                          {role.replace(/_/g, ' ')}
                        </Option>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl>
                    <FormLabel>Status</FormLabel>
                    <Select
                      startDecorator={<WorkIcon />}
                      value={formData.status}
                      onChange={(_, value) =>
                        setFormData({ ...formData, status: value as UserStatus })
                      }
                      placeholder="Select user status"
                    >
                      {Object.values(UserStatus).map((status) => (
                        <Option key={status} value={status}>
                          {status}
                        </Option>
                      ))}
                    </Select>
                  </FormControl>
                </Stack>
              </Grid>

              {/* Contact and Additional Information Section */}
              <Grid xs={12}>
                <Box sx={{ mb: 2 }}>
                  <Typography level="title-md" startDecorator={<ContactEmergencyIcon color="primary" />} sx={{ mb: 2 }}>
                    Contact Information
                  </Typography>
                  <Divider />
                </Box>

                <Grid container spacing={2}>
                  <Grid xs={12} md={6}>
                    <Stack spacing={2}>
                      <FormControl>
                        <FormLabel>Phone Number</FormLabel>
                        <Input
                          type="tel"
                          startDecorator={<PhoneIcon />}
                          value={formData.phoneNumber}
                          onChange={(e) =>
                            setFormData({ ...formData, phoneNumber: e.target.value })
                          }
                          placeholder="Enter phone number"
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel>Address</FormLabel>
                        <Input
                          startDecorator={<HomeIcon />}
                          value={formData.address}
                          onChange={(e) =>
                            setFormData({ ...formData, address: e.target.value })
                          }
                          placeholder="Enter full address"
                        />
                      </FormControl>
                    </Stack>
                  </Grid>

                  <Grid xs={12} md={6}>
                    <Stack spacing={2}>
                      <FormControl>
                        <FormLabel>Emergency Contact</FormLabel>
                        <Input
                          startDecorator={<ContactEmergencyIcon />}
                          value={formData.emergencyContact}
                          onChange={(e) =>
                            setFormData({ ...formData, emergencyContact: e.target.value })
                          }
                          placeholder="Enter emergency contact details"
                        />
                      </FormControl>

                      <FacilitySelector
                        value={formData.facilityId}
                        onChange={handleFacilityChange}
                        label="Assigned Facility"
                        required={false}
                      />
                    </Stack>
                  </Grid>
                </Grid>
              </Grid>

              {/* Submit Button */}
              <Grid xs={12}>
                <Button 
                  type="submit" 
                  loading={loading}
                  color="primary"
                  size="lg"
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  Create User
                </Button>
              </Grid>
            </Grid>
          </form>
        </ModalDialog>
      </Modal>

      <Snackbar
        open={!!snackbarMessage}
        color={snackbarMessage?.includes('successfully') ? 'success' : 'danger'}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        autoHideDuration={3000}
        onClose={() => setSnackbarMessage(null)}
      >
        {snackbarMessage}
      </Snackbar>
    </>
  );
}
