'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  Option,
  Button,
  Stack,
  FormHelperText,
  Divider,
} from '@mui/joy';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useInvitations, InvitationData } from '@/hooks/useInvitations';
import { Mail, Send, X } from 'lucide-react';

// Form validation schema
const invitationSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  role: z.enum(['ADMIN', 'PATHOLOGIST', 'MORGUE_STAFF', 'SECURITY_STAFF', 'USER'], {
    errorMap: () => ({ message: 'Please select a role' }),
  }),
  facilityId: z.string().optional(),
  message: z.string().max(500, 'Message must be less than 500 characters').optional(),
  metadata: z.object({
    position: z.string().optional(),
    department: z.string().optional(),
  }).optional(),
});

type InvitationFormValues = z.infer<typeof invitationSchema>;

interface Facility {
  id: string;
  name: string;
}

// Default message templates
const MESSAGE_TEMPLATES = {
  ADMIN: "You've been invited to join as an administrator. You'll have access to manage users, facilities, and system settings.",
  PATHOLOGIST: "You've been invited to join as a pathologist. You'll have access to all pathology records and reporting tools.",
  MORGUE_STAFF: "You've been invited to join as morgue staff. You'll have access to body management and tracking features.",
  SECURITY_STAFF: "You've been invited to join as security staff. You'll have access to facility access logs and security features.",
  USER: "You've been invited to join our platform. Welcome aboard!",
};

interface InvitationFormProps {
  onComplete?: () => void;
}

export default function InvitationForm({ onComplete }: InvitationFormProps) {
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const { sendInvitation } = useInvitations();
  
  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<InvitationFormValues>({
    resolver: zodResolver(invitationSchema),
    defaultValues: {
      email: '',
      name: '',
      role: 'USER',
      message: '',
      metadata: {
        position: '',
        department: '',
      },
    },
  });
  
  // Watch role to update message template
  const watchRole = watch('role');
  
  // Load facilities
  useEffect(() => {
    const fetchFacilities = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/facilities');
        if (!response.ok) throw new Error('Failed to load facilities');
        const data = await response.json();
        setFacilities(data);
      } catch (error) {
        console.error('Error loading facilities:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchFacilities();
  }, []);
  
  // Update message template when role changes
  useEffect(() => {
    if (watchRole) {
      setValue('message', MESSAGE_TEMPLATES[watchRole]);
    }
  }, [watchRole, setValue]);
  
  const onSubmit = async (data: InvitationFormValues) => {
    try {
      await sendInvitation(data as InvitationData);
      reset();
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Error sending invitation:', error);
    }
  };
  
  return (
    <Sheet
      sx={{
        borderRadius: 'md',
        p: 3,
        boxShadow: 'sm',
      }}
    >
      <Typography level="h4" component="h2" sx={{ mb: 2 }}>
        Invite New User
      </Typography>
      
      <Divider sx={{ my: 2 }} />
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={2}>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <FormControl error={!!errors.email} sx={{ flex: 1 }}>
                  <FormLabel>Email Address *</FormLabel>
                  <Input
                    {...field}
                    type="email"
                    placeholder="<EMAIL>"
                    startDecorator={<Mail size={16} />}
                    error={!!errors.email}
                  />
                  {errors.email && (
                    <FormHelperText>{errors.email.message}</FormHelperText>
                  )}
                </FormControl>
              )}
            />
            
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <FormControl error={!!errors.name} sx={{ flex: 1 }}>
                  <FormLabel>Full Name *</FormLabel>
                  <Input
                    {...field}
                    placeholder="John Doe"
                    error={!!errors.name}
                  />
                  {errors.name && (
                    <FormHelperText>{errors.name.message}</FormHelperText>
                  )}
                </FormControl>
              )}
            />
          </Stack>
          
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            <Controller
              name="role"
              control={control}
              render={({ field }) => (
                <FormControl error={!!errors.role} sx={{ flex: 1 }}>
                  <FormLabel>User Role *</FormLabel>
                  <Select
                    {...field}
                    onChange={(event, value) => {
                      field.onChange(event);
                      setValue('role', value);
                    }}
                    placeholder="Select a role"
                  >
                    <Option value="ADMIN">Administrator</Option>
                    <Option value="PATHOLOGIST">Pathologist</Option>
                    <Option value="MORGUE_STAFF">Morgue Staff</Option>
                    <Option value="SECURITY_STAFF">Security Staff</Option>
                    <Option value="USER">Standard User</Option>
                  </Select>
                  {errors.role && (
                    <FormHelperText>{errors.role.message}</FormHelperText>
                  )}
                </FormControl>
              )}
            />
            
            <Controller
              name="facilityId"
              control={control}
              render={({ field }) => (
                <FormControl sx={{ flex: 1 }}>
                  <FormLabel>Facility (Optional)</FormLabel>
                  <Select
                    {...field}
                    onChange={(event, value) => {
                      field.onChange(event);
                      setValue('facilityId', value);
                    }}
                    placeholder="Select a facility"
                    disabled={isLoading}
                  >
                    {facilities.map((facility) => (
                      <Option key={facility.id} value={facility.id}>
                        {facility.name}
                      </Option>
                    ))}
                  </Select>
                  {isLoading && (
                    <FormHelperText>Loading facilities...</FormHelperText>
                  )}
                </FormControl>
              )}
            />
          </Stack>
          
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            <Controller
              name="metadata.position"
              control={control}
              render={({ field }) => (
                <FormControl sx={{ flex: 1 }}>
                  <FormLabel>Position (Optional)</FormLabel>
                  <Input
                    {...field}
                    placeholder="e.g. Senior Pathologist"
                  />
                </FormControl>
              )}
            />
            
            <Controller
              name="metadata.department"
              control={control}
              render={({ field }) => (
                <FormControl sx={{ flex: 1 }}>
                  <FormLabel>Department (Optional)</FormLabel>
                  <Input
                    {...field}
                    placeholder="e.g. Forensic Department"
                  />
                </FormControl>
              )}
            />
          </Stack>
          
          <Controller
            name="message"
            control={control}
            render={({ field }) => (
              <FormControl error={!!errors.message}>
                <FormLabel>Invitation Message (Optional)</FormLabel>
                <Textarea
                  {...field}
                  minRows={3}
                  placeholder="Enter a personalized message to include in the invitation email"
                  error={!!errors.message}
                />
                <FormHelperText>
                  {errors.message ? errors.message.message : 'This message will be included in the invitation email.'}
                </FormHelperText>
              </FormControl>
            )}
          />
          
          <Stack direction="row" spacing={2} justifyContent="flex-end" sx={{ mt: 3 }}>
            <Button
              variant="outlined"
              color="neutral"
              startDecorator={<X size={16} />}
              onClick={() => reset()}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              loading={isSubmitting}
              startDecorator={<Send size={16} />}
            >
              Send Invitation
            </Button>
          </Stack>
        </Stack>
      </form>
    </Sheet>
  );
} 