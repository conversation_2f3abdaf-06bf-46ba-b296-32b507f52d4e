"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Modal<PERSON>ialog,
  ModalClose,
  Typography,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Select,
  Option,
  Alert,
} from "@mui/joy";
import { User } from "@prisma/client";

interface EditUserDialogProps {
  open: boolean;
  onClose: () => void;
  user: User;
  onUserUpdated: (user: User) => Promise<void> | void;
}

export default function EditUserDialog({
  open,
  onClose,
  user,
  onUserUpdated,
}: EditUserDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    status: "active" as "active" | "inactive",
    role: "",
  });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        status: user.status as "active" | "inactive",
        role: user.role,
      });
    }
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to update user');
      }

      const updatedUser = await response.json();
      await onUserUpdated(updatedUser);
      onClose();
    } catch (error) {
      console.error('User update failed:', error);
      setError('Failed to update user. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog>
        <ModalClose />
        <Typography level="h4">Edit User</Typography>
        {error && <Alert color="danger">{error}</Alert>}
        <form onSubmit={handleSubmit}>
          <Stack spacing={2} sx={{ mt: 2 }}>
            <FormControl required>
              <FormLabel>Name</FormLabel>
              <Input
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
              />
            </FormControl>
            <FormControl required>
              <FormLabel>Email</FormLabel>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) =>
                  setFormData({ ...formData, email: e.target.value })
                }
              />
            </FormControl>
            <FormControl required>
              <FormLabel>Status</FormLabel>
              <Select
                value={formData.status}
                onChange={(_, value) =>
                  setFormData({ ...formData, status: value as "active" | "inactive" })
                }
              >
                <Option value="active">Active</Option>
                <Option value="inactive">Inactive</Option>
              </Select>
            </FormControl>
            <FormControl required>
              <FormLabel>Role</FormLabel>
              <Select
                value={formData.role}
                onChange={(_, value) =>
                  setFormData({ ...formData, role: value as string })
                }
              >
                <Option value="USER">User</Option>
                <Option value="ADMIN">Admin</Option>
                <Option value="STAFF">Staff</Option>
              </Select>
            </FormControl>
            <Button type="submit" loading={isLoading}>
              Update User
            </Button>
          </Stack>
        </form>
      </ModalDialog>
    </Modal>
  );
}
