"use client";

import { useState } from "react";
import {
  <PERSON>,
  Sheet,
  <PERSON>po<PERSON>,
  IconButton,
  <PERSON>,
  <PERSON>u,
  MenuI<PERSON>,
  Dropdown,
  MenuButton,
  Toolt<PERSON>,
  Stack,
  Modal,
  Box,
  Button,
  Alert,
} from "@mui/joy";
import { Icon } from "@iconify/react";

import EditRoleDialog from "./EditRoleDialog";
import ModalDialog from '@mui/joy/ModalDialog';

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  isSystem: boolean;
}

const MOCK_ROLES: Role[] = [
  {
    id: "1",
    name: "Super Admin",
    description: "Full system access",
    permissions: ["*"],
    userCount: 2,
    isSystem: true,
  },
  {
    id: "2",
    name: "Staff",
    description: "Limited system access",
    permissions: ["users.view", "content.edit"],
    userCount: 5,
    isSystem: true,
  },
  {
    id: "3",
    name: "Guest",
    description: "Read-only access",
    permissions: ["content.view"],
    userCount: 10,
    isSystem: false,
  },
];

export default function RolesTable() {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setEditDialogOpen(true);
  };

  const handleDeleteRole = (role: Role) => {
    setSelectedRole(role);
    setDeleteDialogOpen(true);
  };

  const handleRoleUpdated = (updatedRole: Role) => {
    // TODO: Implement API call to update role
    console.log("Role updated:", updatedRole);
  };

  const handleRoleDeleted = (roleId: string) => {
    // TODO: Implement API call to delete role
    console.log("Role deleted:", roleId);
  };

  return (
    <Sheet variant="outlined" sx={{ borderRadius: "sm", overflow: "auto" }}>
      <Table stickyHeader hoverRow>
        <thead>
          <tr>
            <th>Role Name</th>
            <th>Description</th>
            <th>Users</th>
            <th>Permissions</th>
            <th style={{ width: 50 }}></th>
          </tr>
        </thead>
        <tbody>
          {MOCK_ROLES.map((role) => (
            <tr key={role.id}>
              <td>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography>{role.name}</Typography>
                  {role.isSystem && (
                    <Tooltip title="System role - cannot be deleted">
                      <Icon icon="mdi:information" width="20" height="20" />
                    </Tooltip>
                  )}
                </Stack>
              </td>
              <td>{role.description}</td>
              <td>
                <Chip variant="soft" size="sm">
                  {role.userCount} users
                </Chip>
              </td>
              <td>
                <Chip
                  variant="soft"
                  color={role.permissions.includes("*") ? "danger" : "primary"}
                  startDecorator={
                    <Icon icon="mdi:shield-check" width="20" height="20" />
                  }
                >
                  {role.permissions.length} permissions
                </Chip>
              </td>
              <td>
                <Dropdown>
                  <MenuButton
                    slots={{ root: IconButton }}
                    slotProps={{ root: { variant: "plain", color: "neutral" } }}
                  >
                    <Icon icon="mdi:dots-vertical" width="24" height="24" />
                  </MenuButton>
                  <Menu placement="bottom-end">
                    <MenuItem onClick={() => handleEditRole(role)}>
                      <Icon icon="mdi:shield-edit" width="24" height="24" />
                      Edit Role
                    </MenuItem>
                    {!role.isSystem && (
                      <MenuItem
                        color="danger"
                        onClick={() => handleDeleteRole(role)}
                      >
                        <Icon icon="mdi:shield-remove" width="24" height="24" />
                        Delete Role
                      </MenuItem>
                    )}
                  </Menu>
                </Dropdown>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>

      {selectedRole && (
        <>
          <EditRoleDialog
            open={editDialogOpen}
            onClose={() => setEditDialogOpen(false)}
            role={selectedRole}
            onRoleUpdated={handleRoleUpdated}
          />
        </>
      )}
    </Sheet>
  );
}

interface DeleteRoleDialogProps {
  open: boolean;
  onClose: () => void;
  role: Role;
  onRoleDeleted: (roleId: string) => void;
}

const DeleteRoleDialog = ({ open, onClose, role, onRoleDeleted }: DeleteRoleDialogProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    setLoading(true);
    setError(null);
    try {
      // TODO: Implement actual API call to delete role
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulating API call
      onRoleDeleted(role.id);
      onClose();
    } catch (err) {
      setError("Failed to delete role. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog
        variant="outlined"
        role="alertdialog"
        aria-labelledby="alert-dialog-modal-title"
        aria-describedby="alert-dialog-modal-description"
      >
        <Typography id="alert-dialog-modal-title" level="h2">
          Delete Role
        </Typography>
        <Typography id="alert-dialog-modal-description">
          Are you sure you want to delete the role "{role?.name}"? This action cannot be undone.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', pt: 2 }}>
          <Button variant="plain" color="neutral" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="solid" color="danger" onClick={handleDelete} loading={loading}>
            Delete Role
          </Button>
        </Box>
        {error && (
          <Alert color="danger" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </ModalDialog>
    </Modal>
  );
};
