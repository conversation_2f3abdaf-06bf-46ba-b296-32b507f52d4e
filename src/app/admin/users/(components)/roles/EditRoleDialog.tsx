"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ModalClose,
  Ty<PERSON>graphy,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Textarea,
  Divider,
  Alert,
  CircularProgress,
} from "@mui/joy";
import { Icon } from '@iconify/react';
import PermissionSelector from "./PermissionSelector";

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  isSystem: boolean;
}

interface EditRoleDialogProps {
  open: boolean;
  onClose: () => void;
  role: Role;
  onRoleUpdated: (updatedRole: Role) => void;
}

export default function EditRoleDialog({
  open,
  onClose,
  role,
  onRoleUpdated,
}: EditRoleDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        description: role.description,
        permissions: role.permissions,
      });
    }
  }, [role]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // TODO: Implement actual API call to update role
      const updatedRole = { ...role, ...formData };
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulating API call
      onRoleUpdated(updatedRole);
      onClose();
    } catch (err) {
      setError("Failed to update role. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const isFormChanged = () => {
    return (
      formData.name !== role.name ||
      formData.description !== role.description ||
      JSON.stringify(formData.permissions) !== JSON.stringify(role.permissions)
    );
  };

  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog sx={{ maxWidth: 600 }}>
        <ModalClose />
        <Typography level="h4">Edit Role</Typography>
        <Typography level="body-sm" mb={2}>
          Modify role details and permissions
        </Typography>

        {role.isSystem && (
          <Alert
            startDecorator={<Icon icon="mdi:shield-lock" />}
            color="warning"
            sx={{ mb: 2 }}
          >
            This is a system role. Some settings cannot be modified.
          </Alert>
        )}

        {error && (
          <Alert color="danger" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Stack spacing={2}>
            <FormControl required>
              <FormLabel>Role Name</FormLabel>
              <Input
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                disabled={role.isSystem}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Description</FormLabel>
              <Textarea
                minRows={2}
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
              />
            </FormControl>

            <Divider>Permissions</Divider>

            <PermissionSelector
              selectedPermissions={formData.permissions}
              onChange={(permissions) =>
                setFormData({ ...formData, permissions })
              }
              disabled={role.isSystem && role.permissions.includes("*")}
            />

            <Button 
              type="submit" 
              sx={{ mt: 2 }}
              disabled={role.isSystem && role.permissions.includes("*") || !isFormChanged() || loading}
              startDecorator={loading && <CircularProgress size="sm" />}
            >
              {loading ? "Updating..." : "Update Role"}
            </Button>
          </Stack>
        </form>
      </ModalDialog>
    </Modal>
  );
}
