"use client";

import {
  List,
  <PERSON>Item,
  List<PERSON>temDecorator,
  ListItemContent,
  Typography,
  Checkbox,
  Sheet,
  Stack,
  Input,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import { useState, useMemo } from "react";

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

const ALL_PERMISSIONS: Permission[] = [
  // User Management
  {
    id: "users.view",
    name: "View Users",
    description: "Can view user list and details",
    category: "User Management",
  },
  {
    id: "users.create",
    name: "Create Users",
    description: "Can create new users",
    category: "User Management",
  },
  {
    id: "users.edit",
    name: "Edit Users",
    description: "Can edit user details",
    category: "User Management",
  },
  {
    id: "users.delete",
    name: "Delete Users",
    description: "Can delete users",
    category: "User Management",
  },
  // Role Management
  {
    id: "roles.view",
    name: "View Roles",
    description: "Can view roles and their permissions",
    category: "Role Management",
  },
  {
    id: "roles.create",
    name: "Create Roles",
    description: "Can create new roles",
    category: "Role Management",
  },
  {
    id: "roles.edit",
    name: "Edit Roles",
    description: "Can edit role details and permissions",
    category: "Role Management",
  },
  {
    id: "roles.delete",
    name: "Delete Roles",
    description: "Can delete roles",
    category: "Role Management",
  },
  // Content Management
  {
    id: "content.view",
    name: "View Content",
    description: "Can view content items",
    category: "Content Management",
  },
  {
    id: "content.create",
    name: "Create Content",
    description: "Can create new content",
    category: "Content Management",
  },
  {
    id: "content.edit",
    name: "Edit Content",
    description: "Can edit existing content",
    category: "Content Management",
  },
  {
    id: "content.delete",
    name: "Delete Content",
    description: "Can delete content",
    category: "Content Management",
  },
  // Settings
  {
    id: "settings.view",
    name: "View Settings",
    description: "Can view system settings",
    category: "Settings",
  },
  {
    id: "settings.edit",
    name: "Edit Settings",
    description: "Can modify system settings",
    category: "Settings",
  },
];

interface PermissionSelectorProps {
  selectedPermissions: string[];
  onChange: (permissions: string[]) => void;
  disabled?: boolean;
}

export default function PermissionSelector({
  selectedPermissions,
  onChange,
  disabled = false,
}: PermissionSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const groupedPermissions = useMemo(() => {
    const filtered = ALL_PERMISSIONS.filter(
      (permission) =>
        permission.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permission.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permission.category.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return filtered.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  }, [searchQuery]);

  const handleTogglePermission = (permissionId: string) => {
    if (disabled) return;
    
    onChange(
      selectedPermissions.includes(permissionId)
        ? selectedPermissions.filter((id) => id !== permissionId)
        : [...selectedPermissions, permissionId]
    );
  };

  const handleToggleCategory = (category: string) => {
    if (disabled) return;

    const categoryPermissions = ALL_PERMISSIONS.filter(
      (p) => p.category === category
    ).map((p) => p.id);

    const allSelected = categoryPermissions.every((id) =>
      selectedPermissions.includes(id)
    );

    onChange(
      allSelected
        ? selectedPermissions.filter((id) => !categoryPermissions.includes(id))
        : [...new Set([...selectedPermissions, ...categoryPermissions])]
    );
  };

  return (
    <Stack spacing={2}>
      <Input
        startDecorator={<Icon icon="mdi:magnify" />}
        placeholder="Search permissions..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />

      <Sheet variant="outlined" sx={{ borderRadius: "sm", overflow: "auto", maxHeight: 400 }}>
        {Object.entries(groupedPermissions).map(([category, permissions]) => (
          <Stack key={category}>
            <ListItem
              sx={{ 
                bgcolor: "background.level1",
                borderBottom: "1px solid",
                borderColor: "divider",
              }}
            >
              <ListItemContent>
                <Typography level="title-sm">{category}</Typography>
              </ListItemContent>
              <Checkbox
                checked={permissions.every((p) =>
                  selectedPermissions.includes(p.id)
                )}
                indeterminate={
                  permissions.some((p) => selectedPermissions.includes(p.id)) &&
                  !permissions.every((p) => selectedPermissions.includes(p.id))
                }
                onChange={() => handleToggleCategory(category)}
                disabled={disabled}
              />
            </ListItem>
            <List size="sm">
              {permissions.map((permission) => (
                <ListItem key={permission.id}>
                  <ListItemDecorator>
                    <Icon
                      icon={selectedPermissions.includes(permission.id) ? "mdi:shield-check" : "mdi:shield-alert"}
                      color={
                        selectedPermissions.includes(permission.id)
                          ? "var(--joy-palette-success-400)"
                          : "var(--joy-palette-neutral-400)"
                      }
                    />
                  </ListItemDecorator>
                  <ListItemContent>
                    <Typography level="body-sm">{permission.name}</Typography>
                    <Typography level="body-xs" color="neutral">
                      {permission.description}
                    </Typography>
                  </ListItemContent>
                  <Checkbox
                    checked={selectedPermissions.includes(permission.id)}
                    onChange={() => handleTogglePermission(permission.id)}
                    disabled={disabled}
                  />
                </ListItem>
              ))}
            </List>
          </Stack>
        ))}
      </Sheet>

      <Typography level="body-xs" textAlign="right">
        {selectedPermissions.length} permissions selected
      </Typography>
    </Stack>
  );
}
