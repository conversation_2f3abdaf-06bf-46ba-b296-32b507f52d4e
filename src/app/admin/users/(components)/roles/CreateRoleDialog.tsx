"use client";

import { useState } from "react";
import {
  Modal,
  ModalDialog,
  ModalClose,
  Typography,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Textarea,
  Divider,
} from "@mui/joy";
import PermissionSelector from "./PermissionSelector";

interface CreateRoleDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function CreateRoleDialog({ open, onClose }: CreateRoleDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement role creation
    console.log("Create role:", formData);
    onClose();
  };

  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog sx={{ maxWidth: 600 }}>
        <ModalClose />
        <Typography level="h4">Create New Role</Typography>
        <Typography level="body-sm" mb={2}>
          Define a new role and its permissions
        </Typography>

        <form onSubmit={handleSubmit}>
          <Stack spacing={2}>
            <FormControl required>
              <FormLabel>Role Name</FormLabel>
              <Input
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                placeholder="e.g., Content Editor"
              />
            </FormControl>

            <FormControl>
              <FormLabel>Description</FormLabel>
              <Textarea
                minRows={2}
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="Describe the purpose and scope of this role"
              />
            </FormControl>

            <Divider>Permissions</Divider>

            <PermissionSelector
              selectedPermissions={formData.permissions}
              onChange={(permissions) =>
                setFormData({ ...formData, permissions })
              }
            />

            <Button type="submit" sx={{ mt: 2 }}>
              Create Role
            </Button>
          </Stack>
        </form>
      </ModalDialog>
    </Modal>
  );
}
