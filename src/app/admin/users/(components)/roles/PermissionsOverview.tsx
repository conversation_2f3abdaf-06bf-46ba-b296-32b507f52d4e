"use client";

import {
  Table,
  Sheet,
  Typography,
  Stack,
  Input,
  Tooltip,
} from "@mui/joy";
import { 
  Search as SearchIcon, 
  Check as CheckIcon, 
  Close as CloseIcon, 
  Info as InfoIcon 
} from "@mui/icons-material";
import { useState, useMemo } from "react";

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  isSystem: boolean;
}

interface Permission {
  id: string;
  name: string;
  category: string;
}

const MOCK_ROLES: Role[] = [
  {
    id: "1",
    name: "Super Admin",
    description: "Full system access",
    permissions: ["*"],
    isSystem: true,
  },
  {
    id: "2",
    name: "Staff",
    description: "Limited system access",
    permissions: ["users.view", "content.edit", "content.view"],
    isSystem: true,
  },
  {
    id: "3",
    name: "Content Editor",
    description: "Content management access",
    permissions: ["content.view", "content.edit", "content.create"],
    isSystem: false,
  },
  {
    id: "4",
    name: "Guest",
    description: "Read-only access",
    permissions: ["content.view"],
    isSystem: false,
  },
];

const ALL_PERMISSIONS: Permission[] = [
  {
    id: "users.view",
    name: "View Users",
    category: "User Management",
  },
  {
    id: "users.create",
    name: "Create Users",
    category: "User Management",
  },
  {
    id: "content.view",
    name: "View Content",
    category: "Content Management",
  },
  {
    id: "content.edit",
    name: "Edit Content",
    category: "Content Management",
  },
  {
    id: "content.create",
    name: "Create Content",
    category: "Content Management",
  },
];

export default function PermissionsOverview() {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredPermissions = useMemo(() => {
    return ALL_PERMISSIONS.filter(
      (permission) =>
        permission.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permission.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  const hasPermission = (role: Role, permissionId: string) => {
    return role.permissions.includes("*") || role.permissions.includes(permissionId);
  };

  return (
    <Stack spacing={2}>
      <Input
        startDecorator={<SearchIcon />}
        placeholder="Search permissions..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />

      <Sheet variant="outlined" sx={{ borderRadius: "sm", overflow: "auto" }}>
        <Table stickyHeader>
          <thead>
            <tr>
              <th style={{ minWidth: 200 }}>Permission</th>
              {MOCK_ROLES.map((role) => (
                <th key={role.id}>
                  <Stack direction="row" spacing={0.5} alignItems="center">
                    {role.name}
                    {role.isSystem && (
                      <Tooltip title="System role">
                        <InfoIcon fontSize="small" />
                      </Tooltip>
                    )}
                  </Stack>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredPermissions.map((permission) => (
              <tr key={permission.id}>
                <td>
                  <Stack spacing={0.5}>
                    <Typography>{permission.name}</Typography>
                    <Typography level="body-xs" color="neutral">
                      {permission.category}
                    </Typography>
                  </Stack>
                </td>
                {MOCK_ROLES.map((role) => (
                  <td key={role.id}>
                    {hasPermission(role, permission.id) ? (
                      <CheckIcon color="success" />
                    ) : (
                      <CloseIcon color="disabled" />
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </Table>
      </Sheet>
    </Stack>
  );
}
