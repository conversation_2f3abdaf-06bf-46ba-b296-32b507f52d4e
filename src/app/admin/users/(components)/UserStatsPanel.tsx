"use client";

import { Grid, <PERSON>, Typography, AspectRatio } from "@mui/joy";
import { Icon } from "@iconify/react";

export default function UserStatsPanel() {
  return (
    <Grid container spacing={2}>
      <Grid xs={12} sm={6} md={3}>
        <Card>
          <AspectRatio ratio="1" sx={{ width: 40 }}>
            <Icon icon="mdi:account-multiple" width={24} />
          </AspectRatio>
          <Typography level="h2">150</Typography>
          <Typography level="body-sm">Total Users</Typography>
        </Card>
      </Grid>
      <Grid xs={12} sm={6} md={3}>
        <Card>
          <AspectRatio ratio="1" sx={{ width: 40 }}>
            <Icon icon="mdi:account-check" width={24} />
          </AspectRatio>
          <Typography level="h2">120</Typography>
          <Typography level="body-sm">Active Users</Typography>
        </Card>
      </Grid>
      <Grid xs={12} sm={6} md={3}>
        <Card>
          <AspectRatio ratio="1" sx={{ width: 40 }}>
            <Icon icon="mdi:account-clock" width={24} />
          </AspectRatio>
          <Typography level="h2">30</Typography>
          <Typography level="body-sm">Inactive Users</Typography>
        </Card>
      </Grid>
      <Grid xs={12} sm={6} md={3}>
        <Card>
          <AspectRatio ratio="1" sx={{ width: 40 }}>
            <Icon icon="mdi:shield-account" width={24} />
          </AspectRatio>
          <Typography level="h2">5</Typography>
          <Typography level="body-sm">Admin Users</Typography>
        </Card>
      </Grid>
    </Grid>
  );
}
