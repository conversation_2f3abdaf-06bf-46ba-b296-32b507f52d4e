"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Modal<PERSON>ialog,
  ModalClose,
  <PERSON><PERSON><PERSON>,
  Button,
  Stack,
  List,
  ListItem,
  ListItemDecorator,
  Box,
  Checkbox,
  Alert,
  CircularProgress,
  Divider,
  Avatar,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import { usePermission } from "@/lib/rbac/usePermission";

// Define the Role interface to match what's returned from the API
interface Role {
  id: string;
  name: string;
  description?: string;
  permissions?: {
    id: string;
    name: string;
    description?: string;
    category: string;
  }[];
}

interface EditUserRolesDialogProps {
  open: boolean;
  onClose: () => void;
  onUserUpdated: () => void;
  userId: string;
  userName: string | null;
}

export default function EditUserRolesDialog({
  open,
  onClose,
  onUserUpdated,
  userId,
  userName,
}: EditUserRolesDialogProps) {
  const [roles, setRoles] = useState<Role[]>([]);
  const [userRoles, setUserRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  // Use the permission hook to check for access to role assignment
  const { hasPermission, isLoading: permissionsLoading } = usePermission();
  const canAssignRoles = hasPermission(['users.manage', 'roles.assign']);

  // Fetch roles and user's current roles
  useEffect(() => {
    if (open && userId) {
      fetchRoles();
      fetchUserRoles();
    }
  }, [open, userId]);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/roles');
      if (!response.ok) throw new Error('Failed to fetch roles');
      const data = await response.json();
      setRoles(data);
    } catch (err) {
      setError('Failed to load roles. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserRoles = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/${userId}/roles`);
      if (!response.ok) throw new Error('Failed to fetch user roles');
      const data = await response.json();
      setUserRoles(data);
    } catch (err) {
      setError('Failed to load user roles. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleRole = (role: Role) => {
    setUserRoles(prev => 
      prev.some(r => r.id === role.id)
        ? prev.filter(r => r.id !== role.id)
        : [...prev, role]
    );
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      
      const response = await fetch(`/api/users/${userId}/roles`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roleIds: userRoles.map(r => r.id) })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user roles');
      }
      
      onUserUpdated();
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update roles';
      setError(errorMessage);
      console.error(err);
    } finally {
      setIsSaving(false);
    }
  };

  // Show access denied if user doesn't have permission
  if (!permissionsLoading && !canAssignRoles) {
    return (
      <Modal open={open} onClose={onClose}>
        <ModalDialog>
          <ModalClose />
          <Typography level="h4" color="danger">Access Denied</Typography>
          <Typography>
            You don't have permission to manage user roles.
          </Typography>
          <Button onClick={onClose} sx={{ mt: 2 }}>Close</Button>
        </ModalDialog>
      </Modal>
    );
  }

  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog sx={{ width: '100%', maxWidth: '600px', height: '100%', maxHeight: '80vh', overflow: 'auto' }}>
        <ModalClose />
        <Typography level="h4" mb={2}>
          Edit User Roles
        </Typography>

        {error && (
          <Alert color="danger" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress size="lg" />
          </Box>
        ) : (
          <Stack spacing={2}>
            <Stack direction="row" spacing={1} alignItems="center">
              <Avatar>
                {userName?.charAt(0) || 'U'}
              </Avatar>
              <Box>
                <Typography level="title-lg">
                  {userName || 'Unknown User'}
                </Typography>
              </Box>
            </Stack>

            <Divider>Available Roles</Divider>

            <List size="sm">
              {roles.map((role) => (
                <ListItem key={role.id}>
                  <ListItemDecorator>
                    <Icon
                      icon="mdi:shield-outline"
                      style={{
                        color: "var(--joy-palette-primary-500)"
                      }}
                    />
                  </ListItemDecorator>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography level="title-sm">{role.name}</Typography>
                    {role.description && (
                      <Typography level="body-xs" color="neutral">
                        {role.description}
                      </Typography>
                    )}
                    {role.permissions && role.permissions.length > 0 && (
                      <Stack direction="row" spacing={0.5} sx={{ mt: 0.5, flexWrap: 'wrap' }}>
                        {role.permissions.slice(0, 3).map(permission => (
                          <Typography
                            key={permission.id}
                            level="body-xs"
                            sx={{
                              fontSize: '10px',
                              px: 1,
                              py: 0.25,
                              borderRadius: 'sm',
                              bgcolor: 'background.level1',
                            }}
                          >
                            {permission.name}
                          </Typography>
                        ))}
                        {role.permissions.length > 3 && (
                          <Typography
                            level="body-xs"
                            sx={{
                              fontSize: '10px',
                              px: 1,
                              py: 0.25,
                              borderRadius: 'sm',
                              bgcolor: 'background.level1',
                            }}
                          >
                            +{role.permissions.length - 3} more
                          </Typography>
                        )}
                      </Stack>
                    )}
                  </Box>
                  <Checkbox
                    checked={userRoles.some(r => r.id === role.id)}
                    onChange={() => handleToggleRole(role)}
                    disabled={isSaving}
                  />
                </ListItem>
              ))}
            </List>

            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Button
                variant="outlined"
                color="neutral"
                onClick={onClose}
                disabled={isSaving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                loading={isSaving}
              >
                Save Changes
              </Button>
            </Stack>
          </Stack>
        )}
      </ModalDialog>
    </Modal>
  );
}
