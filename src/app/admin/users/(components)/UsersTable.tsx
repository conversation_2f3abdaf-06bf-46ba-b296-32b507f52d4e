"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  Sheet,
  <PERSON><PERSON><PERSON>,
  <PERSON>conButton,
  <PERSON>,
  <PERSON>u,
  MenuI<PERSON>,
  Dropdown,
  MenuButton,
  CircularProgress,
  Alert,
  Modal,
  Input,
  Box,
  ModalDialog,
  Button,
  Stack,
  Avatar,
  Tooltip,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import EditUserDialog from "./EditUserDialog";
import EditUserRolesDialog from "./EditUserRolesDialog";
import { UserTableProps } from "../types";
import { AnimatePresence } from "framer-motion";
import { format } from "date-fns";
import { UserRole, UserStatus } from "@prisma/client";

interface User {
  id: string;
  name: string | null;
  email: string;
  role: UserRole;
  status: UserStatus;
  department?: string | null;
  facilityId?: string | null;
  facility?: {
    name: string;
    code: string;
  } | null;
  emailVerified: Date | null;
  image?: string | null;
  persalNumber?: string | null;
  phoneNumber?: string | null;
  position?: string | null;
  lastLogin?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  // Additional fields needed for EditUserDialog
  address?: string;
  emergencyContact?: string;
  password?: string;
  dateOfBirth?: Date;
  hireDate?: Date;
  mfaEnabled?: boolean;
  mfaSecret?: string;
  mfaVerified?: boolean;
  mfaBackupCodes?: string[];
}

const getRoleColor = (role: UserRole) => {
  switch (role) {
    case "ADMIN":
      return "danger";
    case "PATHOLOGIST":
      return "success";
    case "MORGUE_STAFF":
      return "primary";
    case "SECURITY_STAFF":
      return "warning";
    case "FIELD_EMPLOYEE":
      return "neutral";
    default:
      return "neutral";
  }
};

const getStatusColor = (status: UserStatus) => {
  switch (status) {
    case "ACTIVE":
      return "success";
    case "INACTIVE":
      return "neutral";
    case "SUSPENDED":
      return "danger";
    default:
      return "neutral";
  }
};

export default function UsersTable({ filter }: UserTableProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [rolesDialogOpen, setRolesDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState<UserRole | "ALL">("ALL");
  const [statusFilter, setStatusFilter] = useState<UserStatus | "ALL">("ALL");

  useEffect(() => {
    fetchUsers();
  }, [filter]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/users?filter=${filter}`);
      if (!response.ok) throw new Error("Failed to fetch users");
      const data = await response.json();
      setUsers(data);
      setError(null);
    } catch (err) {
      console.error("Error fetching users:", err);
      setError("Failed to fetch users. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditDialogOpen(true);
  };

  const handleEditRoles = (user: User) => {
    setSelectedUser(user);
    setRolesDialogOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  const handleUserUpdated = () => {
    fetchUsers();
    setEditDialogOpen(false);
    setRolesDialogOpen(false);
  };

  const handleUserDeleted = () => {
    fetchUsers();
    setDeleteDialogOpen(false);
  };

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.persalNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.department?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRole = roleFilter === "ALL" || user.role === roleFilter;
    const matchesStatus = statusFilter === "ALL" || user.status === statusFilter;

    return matchesSearch && matchesRole && matchesStatus;
  });

  return (
    <AnimatePresence mode="wait">
      <Sheet
        variant="outlined"
        sx={{
          display: "flex",
          flexDirection: "column",
          borderRadius: "sm",
          overflow: "hidden",
          height: "100%",
          width: "100%",
        }}
      >
        {/* Filters */}
        <Box sx={{ p: 2, borderBottom: "1px solid", borderColor: "divider" }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Input
              placeholder="Search users..."
              startDecorator={<Icon icon="mdi:magnify" />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              sx={{ width: 300 }}
            />
            <Dropdown>
              <MenuButton variant="outlined" color="neutral" size="sm">
                Role: {roleFilter}
              </MenuButton>
              <Menu>
                <MenuItem onClick={() => setRoleFilter("ALL")}>All Roles</MenuItem>
                {Object.values(UserRole).map((role) => (
                  <MenuItem key={role} onClick={() => setRoleFilter(role)}>
                    {role}
                  </MenuItem>
                ))}
              </Menu>
            </Dropdown>
            <Dropdown>
              <MenuButton variant="outlined" color="neutral" size="sm">
                Status: {statusFilter}
              </MenuButton>
              <Menu>
                <MenuItem onClick={() => setStatusFilter("ALL")}>All Statuses</MenuItem>
                {Object.values(UserStatus).map((status) => (
                  <MenuItem key={status} onClick={() => setStatusFilter(status)}>
                    {status}
                  </MenuItem>
                ))}
              </Menu>
            </Dropdown>
          </Stack>
        </Box>

        <Box sx={{ overflow: "auto", maxHeight: "calc(100vh - 200px)", width: "100%" }}>
          <Table
            stickyHeader
            hoverRow
            sx={{
              /* "--TableCell-headBackground": "rgba(0 0 0 / 0.05)",
              "--TableRow-hoverBackground": "rgba(0 0 0 / 0.05)", */
              width: "100%",
              height: "100%",
            }}
          >
            {loading ? (
              <CircularProgress size="lg" />
            ) : error ? (
              <Alert color="danger">{error}</Alert>
            ) : (
              <>
                <thead>
                  <tr>
                    <th style={{ width: 250 }}>User</th>
                    <th>Department</th>
                    <th>Facility</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Last Active</th>
                    <th style={{ width: 50 }}></th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id}>
                      <td>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Avatar
                            src={user.image || undefined}
                            alt={user.name || ""}
                            size="sm"
                          />
                          <Stack>
                            <Typography level="body-sm">{user.name}</Typography>
                            <Typography level="body-xs" color="neutral">
                              {user.email}
                            </Typography>
                            {user.persalNumber && (
                              <Typography level="body-xs" color="primary">
                                ID: {user.persalNumber}
                              </Typography>
                            )}
                          </Stack>
                        </Stack>
                      </td>
                      <td>
                        <Typography level="body-sm">{user.department || "—"}</Typography>
                      </td>
                      <td>
                        {user.facility ? (
                          <Stack>
                            <Typography level="body-sm">{user.facility.name}</Typography>
                            <Typography level="body-xs" color="neutral">
                              {user.facility.code}
                            </Typography>
                          </Stack>
                        ) : (
                          "—"
                        )}
                      </td>
                      <td>
                        <Chip
                          variant="soft"
                          size="sm"
                          color={getRoleColor(user.role)}
                        >
                          {user.role}
                        </Chip>
                      </td>
                      <td>
                        <Chip
                          variant="soft"
                          size="sm"
                          color={getStatusColor(user.status)}
                        >
                          {user.status}
                        </Chip>
                      </td>
                      <td>
                        <Tooltip title={user.lastLogin ? format(new Date(user.lastLogin), "PPpp") : "Never"}>
                          <Typography level="body-sm">
                            {user.lastLogin
                              ? format(new Date(user.lastLogin), "PP")
                              : "Never"}
                          </Typography>
                        </Tooltip>
                      </td>
                      <td>
                        <Dropdown>
                          <MenuButton
                            slots={{ root: IconButton }}
                            slotProps={{
                              root: { variant: "plain", color: "neutral" },
                            }}
                          >
                            <Icon icon="mdi:dots-vertical" />
                          </MenuButton>
                          <Menu placement="bottom-end">
                            <MenuItem onClick={() => handleEditUser(user)}>
                              <Icon icon="mdi:account-edit" />
                              Edit User
                            </MenuItem>
                            <MenuItem onClick={() => handleEditRoles(user)}>
                              <Icon icon="mdi:shield-account" />
                              Edit Roles
                            </MenuItem>
                            <MenuItem
                              onClick={() => handleDeleteUser(user)}
                              color="danger"
                            >
                              <Icon icon="mdi:account-remove" />
                              Delete User
                            </MenuItem>
                          </Menu>
                        </Dropdown>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </>
            )}
          </Table>
        </Box>

        {selectedUser && (
          <>
            <EditUserDialog
              open={editDialogOpen}
              onClose={() => setEditDialogOpen(false)}
              onUserUpdated={handleUserUpdated}
              user={selectedUser as any}
            />
            <EditUserRolesDialog
              open={rolesDialogOpen}
              onClose={() => setRolesDialogOpen(false)}
              onUserUpdated={handleUserUpdated}
              userId={selectedUser?.id || ""}
              userName={selectedUser?.name}
            />
            <DeleteUserDialog
              open={deleteDialogOpen}
              onClose={() => setDeleteDialogOpen(false)}
              onUserDeleted={handleUserDeleted}
              user={selectedUser as User}
            />
          </>
        )}
      </Sheet>
    </AnimatePresence>
  );
}

const DeleteUserDialog: React.FC<{
  open: boolean;
  onClose: () => void;
  onUserDeleted: () => void;
  user: User;
}> = ({ open, onClose, onUserDeleted, user }) => {
  const [confirmText, setConfirmText] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (confirmText.toLowerCase() !== user.name?.toLowerCase()) {
      setError("The entered name doesn't match. Please try again.");
      return;
    }
    setLoading(true);
    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Failed to delete user");
      onUserDeleted();
      onClose();
    } catch (err) {
      setError("Failed to delete user. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog variant="outlined" role="alertdialog">
        <Typography level="h2">Delete User</Typography>
        <Typography>
          This action cannot be undone. To confirm, please type "{user.name}"
          below:
        </Typography>
        <Input
          value={confirmText}
          onChange={(e) => setConfirmText(e.target.value)}
          error={!!error}
          placeholder={user.name || ""}
        />
        {error && (
          <Alert color="danger" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
        <Box
          sx={{ display: "flex", gap: 1, justifyContent: "flex-end", mt: 2 }}
        >
          <Button variant="plain" color="neutral" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="solid"
            color="danger"
            onClick={handleDelete}
            loading={loading}
          >
            Delete User
          </Button>
        </Box>
      </ModalDialog>
    </Modal>
  );
};
