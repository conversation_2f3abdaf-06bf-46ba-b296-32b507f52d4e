import { create } from "zustand";
import { devtools } from "zustand/middleware";
import {
  User,
  Role,
  UserStats,
  CreateUserData,
  UpdateUserData,
  CreateRoleData,
  UpdateRoleData,
  UserFilter,
} from "./types";

interface UserState {
  // Users
  users: User[];
  filteredUsers: User[];
  selectedUser: User | null;
  userStats: UserStats;
  isLoadingUsers: boolean;
  userError: string | null;
  currentFilter: UserFilter;

  // Roles
  roles: Role[];
  selectedRole: Role | null;
  isLoadingRoles: boolean;
  roleError: string | null;

  // User Actions
  fetchUsers: () => Promise<void>;
  createUser: (data: CreateUserData) => Promise<User>;
  updateUser: (userId: string, data: UpdateUserData) => Promise<User>;
  deleteUser: (userId: string) => Promise<void>;
  updateUserRoles: (userId: string, roleId: string, permissions?: string[]) => Promise<User>;
  setSelectedUser: (user: User | null) => void;
  setUserFilter: (filter: UserFilter) => void;

  // Role Actions
  fetchRoles: () => Promise<void>;
  createRole: (data: CreateRoleData) => Promise<Role>;
  updateRole: (roleId: string, data: UpdateRoleData) => Promise<Role>;
  deleteRole: (roleId: string) => Promise<void>;
  setSelectedRole: (role: Role | null) => void;
}

export const useUserStore = create<UserState>()(
  devtools(
    (set, get) => ({
      // Initial state
      users: [],
      filteredUsers: [],
      selectedUser: null,
      userStats: { total: 0, active: 0, inactive: 0, admins: 0 },
      isLoadingUsers: false,
      userError: null,
      currentFilter: "all",

      roles: [],
      selectedRole: null,
      isLoadingRoles: false,
      roleError: null,

      // User Actions
      fetchUsers: async () => {
        set({ isLoadingUsers: true, userError: null });
        try {
          const response = await fetch("/api/users");
          if (!response.ok) throw new Error("Failed to fetch users");
          
          const users = await response.json();
          const stats = {
            total: users.length,
            active: users.filter((u: User) => u.status === "active").length,
            inactive: users.filter((u: User) => u.status === "inactive").length,
            admins: users.filter((u: User) => u.role === "ADMIN").length,
          };
          
          set({ 
            users,
            userStats: stats,
            filteredUsers: get().currentFilter === "all" 
              ? users 
              : users.filter((u: User) => u.status === get().currentFilter),
            isLoadingUsers: false 
          });
        } catch (error) {
          set({ 
            userError: error instanceof Error ? error.message : "An error occurred",
            isLoadingUsers: false 
          });
        }
      },

      createUser: async (data: CreateUserData) => {
        set({ userError: null });
        try {
          const response = await fetch("/api/users", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data),
          });
          
          if (!response.ok) throw new Error("Failed to create user");
          
          const newUser = await response.json();
          set((state) => ({ 
            users: [...state.users, newUser],
            filteredUsers: state.currentFilter === "all" || newUser.status === state.currentFilter
              ? [...state.filteredUsers, newUser]
              : state.filteredUsers
          }));
          
          return newUser;
        } catch (error) {
          const message = error instanceof Error ? error.message : "Failed to create user";
          set({ userError: message });
          throw new Error(message);
        }
      },

      updateUser: async (userId: string, data: UpdateUserData) => {
        set({ userError: null });
        try {
          const response = await fetch(`/api/users/${userId}`, {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data),
          });
          
          if (!response.ok) throw new Error("Failed to update user");
          
          const updatedUser = await response.json();
          set((state) => ({
            users: state.users.map((u) => (u.id === userId ? updatedUser : u)),
            filteredUsers: state.currentFilter === "all"
              ? state.users.map((u) => (u.id === userId ? updatedUser : u))
              : state.users
                  .map((u) => (u.id === userId ? updatedUser : u))
                  .filter((u) => u.status === state.currentFilter),
            selectedUser: state.selectedUser?.id === userId ? updatedUser : state.selectedUser,
          }));
          
          return updatedUser;
        } catch (error) {
          const message = error instanceof Error ? error.message : "Failed to update user";
          set({ userError: message });
          throw new Error(message);
        }
      },

      deleteUser: async (userId: string) => {
        set({ userError: null });
        try {
          const response = await fetch(`/api/users/${userId}`, {
            method: "DELETE",
          });
          
          if (!response.ok) throw new Error("Failed to delete user");
          
          set((state) => ({
            users: state.users.filter((u) => u.id !== userId),
            filteredUsers: state.filteredUsers.filter((u) => u.id !== userId),
            selectedUser: state.selectedUser?.id === userId ? null : state.selectedUser,
          }));
        } catch (error) {
          const message = error instanceof Error ? error.message : "Failed to delete user";
          set({ userError: message });
          throw new Error(message);
        }
      },

      updateUserRoles: async (userId: string, roleId: string, permissions?: string[]) => {
        set({ userError: null });
        try {
          const response = await fetch(`/api/users/${userId}/roles`, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ roleId, permissions }),
          });
          
          if (!response.ok) throw new Error("Failed to update user roles");
          
          const updatedUser = await response.json();
          set((state) => ({
            users: state.users.map((u) => (u.id === userId ? updatedUser : u)),
            filteredUsers: state.filteredUsers.map((u) => (u.id === userId ? updatedUser : u)),
            selectedUser: state.selectedUser?.id === userId ? updatedUser : state.selectedUser,
          }));
          
          return updatedUser;
        } catch (error) {
          const message = error instanceof Error ? error.message : "Failed to update user roles";
          set({ userError: message });
          throw new Error(message);
        }
      },

      setSelectedUser: (user: User | null) => set({ selectedUser: user }),

      setUserFilter: (filter: UserFilter) => {
        const { users } = get();
        set({
          currentFilter: filter,
          filteredUsers: filter === "all"
            ? users
            : users.filter((u) => u.status === filter),
        });
      },

      // Role Actions
      fetchRoles: async () => {
        set({ isLoadingRoles: true, roleError: null });
        try {
          const response = await fetch("/api/roles");
          if (!response.ok) throw new Error("Failed to fetch roles");
          
          const roles = await response.json();
          set({ roles, isLoadingRoles: false });
        } catch (error) {
          set({ 
            roleError: error instanceof Error ? error.message : "An error occurred",
            isLoadingRoles: false 
          });
        }
      },

      createRole: async (data: CreateRoleData) => {
        set({ roleError: null });
        try {
          const response = await fetch("/api/roles", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data),
          });
          
          if (!response.ok) throw new Error("Failed to create role");
          
          const newRole = await response.json();
          set((state) => ({ roles: [...state.roles, newRole] }));
          
          return newRole;
        } catch (error) {
          const message = error instanceof Error ? error.message : "Failed to create role";
          set({ roleError: message });
          throw new Error(message);
        }
      },

      updateRole: async (roleId: string, data: UpdateRoleData) => {
        set({ roleError: null });
        try {
          const response = await fetch(`/api/roles/${roleId}`, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data),
          });
          
          if (!response.ok) throw new Error("Failed to update role");
          
          const updatedRole = await response.json();
          set((state) => ({
            roles: state.roles.map((r) => (r.id === roleId ? updatedRole : r)),
            selectedRole: state.selectedRole?.id === roleId ? updatedRole : state.selectedRole,
          }));
          
          return updatedRole;
        } catch (error) {
          const message = error instanceof Error ? error.message : "Failed to update role";
          set({ roleError: message });
          throw new Error(message);
        }
      },

      deleteRole: async (roleId: string) => {
        set({ roleError: null });
        try {
          const response = await fetch(`/api/roles/${roleId}`, {
            method: "DELETE",
          });
          
          if (!response.ok) throw new Error("Failed to delete role");
          
          set((state) => ({
            roles: state.roles.filter((r) => r.id !== roleId),
            selectedRole: state.selectedRole?.id === roleId ? null : state.selectedRole,
          }));
        } catch (error) {
          const message = error instanceof Error ? error.message : "Failed to delete role";
          set({ roleError: message });
          throw new Error(message);
        }
      },

      setSelectedRole: (role: Role | null) => set({ selectedRole: role }),
    }),
    { name: "user-management-store" }
  )
);
