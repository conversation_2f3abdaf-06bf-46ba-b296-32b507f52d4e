export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: "active" | "inactive";
  lastLogin?: Date;
  createdAt: Date;
  permissions?: string[];
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: string[];
  isSystem: boolean;
  _count?: {
    users: number;
  };
}

export interface UserStats {
  total: number;
  active: number;
  inactive: number;
  admins: number;
}

export interface UserTableProps{
  filter: UserFilter;
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  role: string;
  sendWelcomeEmail?: boolean;
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  status?: "active" | "inactive";
  role?: string;
}

export interface CreateRoleData {
  name: string;
  description?: string;
  permissions: string[];
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  permissions?: string[];
}

export type UserFilter = "all" | "active" | "inactive";
