"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from "react";
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON>, Stack, IconButton } from "@mui/joy";
import { Icon } from "@iconify/react";
import UsersTable from "../(components)/UsersTable";
import CreateUserDialog from "../(components)/CreateUserDialog";
import UserStatsPanel from "../(components)/UserStatsPanel";
import { AnimatePresence, motion } from "framer-motion";
import dynamicImport from "next/dynamic";

// Dynamically import UserImport component
const UserImport = dynamicImport(
  () => import("@/components/Users/<USER>"),
  {
    ssr: false
  }
);

export default function AllUsersPage() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [showStats, setShowStats] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);

  const variants = {
    hidden: { opacity: 0, y: -20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { type: "spring", stiffness: 260, damping: 20 }
    },
    exit: { opacity: 0, y: 20, scale: 0.95 }
  };

  const handleCreateUser = () => {
    setCreateDialogOpen(true);
  };

  const handleToggleStats = () => {
    setShowStats(!showStats);
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // Add your refresh logic here
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulating API call
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AnimatePresence mode="wait">
      <Box sx={{ p: 2 }}>
        <Stack spacing={2} height="100%">
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography level="h4">All Users</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <IconButton
                variant="soft"
                color="neutral"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                <Icon icon={isLoading ? "mdi:loading" : "mdi:refresh"} />
              </IconButton>
              <IconButton
                variant="soft"
                color="neutral"
                size="sm"
                onClick={handleToggleStats}
              >
                <Icon icon={showStats ? "mdi:eye-off" : "mdi:eye"} />
              </IconButton>

              <Button
                startDecorator={<Icon icon="mdi:file-import" />}
                onClick={() => setIsImportModalOpen(true)}
              >
                Import Users
              </Button>
              <Button
                startDecorator={<Icon icon="mdi:account-plus" />}
                onClick={handleCreateUser}
              >
                Add User
              </Button>
            </Box>
          </Stack>

          <AnimatePresence>
            {showStats && (
              <motion.div
                variants={variants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <UserStatsPanel />
              </motion.div>
            )}
          </AnimatePresence>

          <UsersTable
            filter={activeTab === 0 ? "all" : activeTab === 1 ? "active" : "inactive"}
          />
        </Stack>

        <CreateUserDialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
        />
      </Box>

      <UserImport
        open={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onSuccess={() => setIsImportModalOpen(false)}
      />
    </AnimatePresence>
  );
}