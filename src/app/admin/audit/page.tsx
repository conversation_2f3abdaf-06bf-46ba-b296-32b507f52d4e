"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Card,
  Table,
  IconButton,
  Chip,
  Select,
  Option,
  Input,
  Button,
  Stack,
  CircularProgress,
  Sheet,
  Grid,
  Tooltip,
  Modal,
  ModalDialog,
  ModalClose,
  Divider,
  FormLabel,
  FormControl,
  Alert,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import { format } from "date-fns";
import { AuditAction, AuditSeverity } from "@/lib/audit";

interface AuditLog {
  id: string;
  timestamp: string;
  user: string;
  action: AuditAction;
  details: string;
  severity: AuditSeverity;
  ipAddress: string;
  userAgent: string;
  resourceType?: string;
  resourceId?: string;
}

interface PaginationData {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  limit: number;
}

const severityColorMap = {
  info: "primary",
  warning: "warning",
  error: "danger",
} as const;

const actionIcons = {
  LOGIN: "mdi:login",
  LOGOUT: "mdi:logout",
  CREATE: "mdi:plus-circle",
  UPDATE: "mdi:pencil",
  DELETE: "mdi:delete",
  VIEW: "mdi:eye",
  DOWNLOAD: "mdi:download",
  UPLOAD: "mdi:upload",
  EXPORT: "mdi:export",
  IMPORT: "mdi:import",
  ERROR: "mdi:alert-circle",
};

export default function AuditLogsPage() {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    totalCount: 0,
    totalPages: 1,
    currentPage: 1,
    limit: 10,
  });

  // Filter states
  const [selectedSeverity, setSelectedSeverity] = useState("all");
  const [selectedAction, setSelectedAction] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  // Modal states
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);

  // Fetch audit logs with filters
  const fetchAuditLogs = async (page = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query params
      const params = new URLSearchParams();
      params.append("page", page.toString());
      params.append("limit", pagination.limit.toString());
      
      if (selectedSeverity !== "all") {
        params.append("severity", selectedSeverity);
      }
      
      if (selectedAction !== "all") {
        params.append("action", selectedAction);
      }
      
      if (searchQuery) {
        params.append("search", searchQuery);
      }
      
      if (startDate) {
        params.append("startDate", startDate);
      }
      
      if (endDate) {
        params.append("endDate", endDate);
      }

      const response = await fetch(`/api/admin/audit-logs?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch audit logs");
      }
      
      const data = await response.json();
      setAuditLogs(data.auditLogs);
      setPagination(data.pagination);
    } catch (err) {
      console.error("Error fetching audit logs:", err);
      setError("Failed to load audit logs. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAuditLogs(1);
  }, [selectedSeverity, selectedAction, searchQuery, startDate, endDate]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchAuditLogs(1);
  };

  const handlePageChange = (event: React.MouseEvent | null, value: number) => {
    fetchAuditLogs(value);
  };

  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log);
    setDetailModalOpen(true);
  };

  const exportToCSV = () => {
    try {
      // Create CSV content
      const headers = ["Timestamp", "User", "Action", "Details", "Severity", "IP Address", "Resource Type", "Resource ID"];
      const csvContent = [
        headers.join(","),
        ...auditLogs.map(log => [
          log.timestamp,
          `"${log.user.replace(/"/g, '""')}"`, // Escape quotes in CSV
          log.action,
          `"${log.details.replace(/"/g, '""')}"`,
          log.severity,
          log.ipAddress,
          log.resourceType || "",
          log.resourceId || ""
        ].join(","))
      ].join("\n");
      
      // Create blob and download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", `audit-logs-${new Date().toISOString().slice(0, 10)}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.error("Error exporting to CSV:", err);
      setError("Failed to export data to CSV.");
    }
  };

  // Custom pagination component
  const CustomPagination = () => {
    const pages = [];
    const maxVisiblePages = 5;
    const { currentPage, totalPages } = pagination;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    // Previous button
    pages.push(
      <Button
        key="prev"
        variant="plain"
        color="neutral"
        size="sm"
        disabled={currentPage === 1}
        onClick={(e) => handlePageChange(e, currentPage - 1)}
      >
        <Icon icon="mdi:chevron-left" />
      </Button>
    );
    
    // First page
    if (startPage > 1) {
      pages.push(
        <Button
          key="1"
          variant={currentPage === 1 ? "solid" : "plain"}
          color={currentPage === 1 ? "primary" : "neutral"}
          size="sm"
          onClick={(e) => handlePageChange(e, 1)}
        >
          1
        </Button>
      );
      
      if (startPage > 2) {
        pages.push(
          <Button key="ellipsis1" variant="plain" color="neutral" size="sm" disabled>
            ...
          </Button>
        );
      }
    }
    
    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          variant={currentPage === i ? "solid" : "plain"}
          color={currentPage === i ? "primary" : "neutral"}
          size="sm"
          onClick={(e) => handlePageChange(e, i)}
        >
          {i}
        </Button>
      );
    }
    
    // Last page
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(
          <Button key="ellipsis2" variant="plain" color="neutral" size="sm" disabled>
            ...
          </Button>
        );
      }
      
      pages.push(
        <Button
          key={totalPages}
          variant={currentPage === totalPages ? "solid" : "plain"}
          color={currentPage === totalPages ? "primary" : "neutral"}
          size="sm"
          onClick={(e) => handlePageChange(e, totalPages)}
        >
          {totalPages}
        </Button>
      );
    }
    
    // Next button
    pages.push(
      <Button
        key="next"
        variant="plain"
        color="neutral"
        size="sm"
        disabled={currentPage === totalPages}
        onClick={(e) => handlePageChange(e, currentPage + 1)}
      >
        <Icon icon="mdi:chevron-right" />
      </Button>
    );
    
    return (
      <Stack direction="row" spacing={1} justifyContent="center">
        {pages}
      </Stack>
    );
  };

  return (
    <Box sx={{ p: 4 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 4 }}>
        <Typography level="h1">
          Audit Logs
        </Typography>
      </Stack>

      <Card variant="outlined" sx={{ mb: 3 }}>
        <Stack
          direction={{ xs: "column", md: "row" }}
          spacing={2}
          sx={{ p: 2, borderBottom: "1px solid", borderColor: "divider" }}
        >
          <Grid container spacing={2}>
            <Grid xs={12} md={6} lg={3}>
              <Select
                placeholder="Severity"
                value={selectedSeverity}
                onChange={(_, value) => setSelectedSeverity(value as string)}
                sx={{ width: "100%" }}
              >
                <Option value="all">All Severities</Option>
                <Option value="info">Info</Option>
                <Option value="warning">Warning</Option>
                <Option value="error">Error</Option>
              </Select>
            </Grid>

            <Grid xs={12} md={6} lg={3}>
              <Select
                placeholder="Action"
                value={selectedAction}
                onChange={(_, value) => setSelectedAction(value as string)}
                sx={{ width: "100%" }}
              >
                <Option value="all">All Actions</Option>
                <Option value="LOGIN">Login</Option>
                <Option value="LOGOUT">Logout</Option>
                <Option value="CREATE">Create</Option>
                <Option value="UPDATE">Update</Option>
                <Option value="DELETE">Delete</Option>
                <Option value="VIEW">View</Option>
                <Option value="ERROR">Error</Option>
              </Select>
            </Grid>

            <Grid xs={12} md={6} lg={3}>
              <Input
                type="date"
                placeholder="Start Date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                slotProps={{
                  input: {
                    max: endDate || undefined,
                  },
                }}
                sx={{ width: "100%" }}
              />
            </Grid>

            <Grid xs={12} md={6} lg={3}>
              <Input
                type="date"
                placeholder="End Date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                slotProps={{
                  input: {
                    min: startDate || undefined,
                  },
                }}
                sx={{ width: "100%" }}
              />
            </Grid>

            <Grid xs={12} md={8}>
              <form onSubmit={handleSearch}>
                <Input
                  placeholder="Search logs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  startDecorator={<Icon icon="mdi:magnify" />}
                  endDecorator={
                    searchQuery && (
                      <IconButton
                        variant="plain"
                        color="neutral"
                        onClick={() => setSearchQuery("")}
                      >
                        <Icon icon="mdi:close" />
                      </IconButton>
                    )
                  }
                  sx={{ width: "100%" }}
                />
              </form>
            </Grid>

            <Grid xs={12} md={4}>
              <Button
                variant="outlined"
                color="neutral"
                startDecorator={<Icon icon="mdi:download" />}
                onClick={exportToCSV}
                sx={{ width: "100%" }}
              >
                Export to CSV
              </Button>
            </Grid>
          </Grid>
        </Stack>

        {error && (
          <Alert color="danger" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}

        {isLoading ? (
          <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
            <CircularProgress size="lg" />
          </Box>
        ) : auditLogs.length === 0 ? (
          <Box sx={{ textAlign: "center", p: 4 }}>
            <Icon icon="mdi:file-search-outline" style={{ fontSize: 48, opacity: 0.5, marginBottom: 16 }} />
            <Typography level="body-lg">No audit logs found</Typography>
            <Typography level="body-sm" color="neutral">
              Try adjusting your filters or search criteria
            </Typography>
          </Box>
        ) : (
          <Sheet
            variant="plain"
            sx={{
              overflowX: "auto",
              maxHeight: "600px",
              overflowY: "auto",
            }}
          >
            <Table
              aria-label="Audit logs table"
              sx={{
                "& th": {
                  backgroundColor: "var(--joy-palette-background-level1)",
                  position: "sticky",
                  top: 0,
                  zIndex: 1,
                },
              }}
            >
              <thead>
                <tr>
                  <th style={{ width: 180 }}>Timestamp</th>
                  <th style={{ width: 120 }}>User</th>
                  <th style={{ width: 100 }}>Action</th>
                  <th>Details</th>
                  <th style={{ width: 100 }}>Severity</th>
                  <th style={{ width: 120 }}>IP Address</th>
                  <th style={{ width: 80 }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {auditLogs.map((log) => (
                  <tr key={log.id}>
                    <td>
                      <Typography level="body-sm">
                        {format(new Date(log.timestamp), "yyyy-MM-dd HH:mm:ss")}
                      </Typography>
                    </td>
                    <td>{log.user}</td>
                    <td>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Icon icon={actionIcons[log.action] || "mdi:information"} />
                        {log.action}
                      </Box>
                    </td>
                    <td>
                      <Typography 
                        level="body-sm" 
                        sx={{ 
                          overflow: "hidden", 
                          textOverflow: "ellipsis", 
                          whiteSpace: "nowrap",
                          maxWidth: "30ch"
                        }}
                      >
                        {log.details}
                      </Typography>
                    </td>
                    <td>
                      <Chip
                        size="sm"
                        variant="soft"
                        color={severityColorMap[log.severity] || "neutral"}
                      >
                        {log.severity}
                      </Chip>
                    </td>
                    <td>
                      <Typography level="body-sm">
                        {log.ipAddress}
                      </Typography>
                    </td>
                    <td>
                      <Tooltip title="View Details">
                        <IconButton 
                          size="sm" 
                          variant="plain" 
                          onClick={() => handleViewDetails(log)}
                        >
                          <Icon icon="mdi:information" />
                        </IconButton>
                      </Tooltip>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Sheet>
        )}

        <Box sx={{ display: "flex", justifyContent: "center", py: 2 }}>
          <CustomPagination />
        </Box>
      </Card>

      {/* Detail Modal */}
      <Modal open={detailModalOpen} onClose={() => setDetailModalOpen(false)}>
        <ModalDialog
          aria-labelledby="audit-log-detail-modal"
          size="lg"
          sx={{ maxWidth: 600 }}
        >
          <ModalClose />
          <Typography id="audit-log-detail-modal" level="title-lg">
            Audit Log Details
          </Typography>
          <Divider sx={{ my: 2 }}/>
          
          {selectedLog && (
            <Stack spacing={2}>
              <Grid container spacing={2}>
                <Grid xs={6}>
                  <FormControl>
                    <FormLabel>Timestamp</FormLabel>
                    <Typography>
                      {format(new Date(selectedLog.timestamp), "yyyy-MM-dd HH:mm:ss")}
                    </Typography>
                  </FormControl>
                </Grid>
                <Grid xs={6}>
                  <FormControl>
                    <FormLabel>User</FormLabel>
                    <Typography>{selectedLog.user}</Typography>
                  </FormControl>
                </Grid>
                <Grid xs={6}>
                  <FormControl>
                    <FormLabel>Action</FormLabel>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Icon icon={actionIcons[selectedLog.action] || "mdi:information"} />
                      <Typography>{selectedLog.action}</Typography>
                    </Box>
                  </FormControl>
                </Grid>
                <Grid xs={6}>
                  <FormControl>
                    <FormLabel>Severity</FormLabel>
                    <Chip
                      size="sm"
                      variant="soft"
                      color={severityColorMap[selectedLog.severity] || "neutral"}
                    >
                      {selectedLog.severity}
                    </Chip>
                  </FormControl>
                </Grid>
                <Grid xs={12}>
                  <FormControl>
                    <FormLabel>Details</FormLabel>
                    <Sheet
                      variant="outlined"
                      sx={{
                        p: 2,
                        borderRadius: 'sm',
                        fontFamily: 'monospace',
                        fontSize: 'sm',
                        maxHeight: '150px',
                        overflowY: 'auto',
                      }}
                    >
                      {selectedLog.details}
                    </Sheet>
                  </FormControl>
                </Grid>
                {selectedLog.resourceType && (
                  <Grid xs={6}>
                    <FormControl>
                      <FormLabel>Resource Type</FormLabel>
                      <Typography>{selectedLog.resourceType}</Typography>
                    </FormControl>
                  </Grid>
                )}
                {selectedLog.resourceId && (
                  <Grid xs={6}>
                    <FormControl>
                      <FormLabel>Resource ID</FormLabel>
                      <Typography sx={{ wordBreak: 'break-all' }}>{selectedLog.resourceId}</Typography>
                    </FormControl>
                  </Grid>
                )}
                <Grid xs={12}>
                  <FormControl>
                    <FormLabel>IP Address</FormLabel>
                    <Typography>{selectedLog.ipAddress}</Typography>
                  </FormControl>
                </Grid>
                <Grid xs={12}>
                  <FormControl>
                    <FormLabel>User Agent</FormLabel>
                    <Sheet
                      variant="outlined"
                      sx={{
                        p: 2,
                        borderRadius: 'sm',
                        fontSize: 'xs',
                        maxHeight: '100px',
                        overflowY: 'auto',
                      }}
                    >
                      {selectedLog.userAgent}
                    </Sheet>
                  </FormControl>
                </Grid>
              </Grid>
            </Stack>
          )}
        </ModalDialog>
      </Modal>
    </Box>
  );
} 