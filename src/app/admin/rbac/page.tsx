'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";







import { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  <PERSON>b<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>b<PERSON><PERSON>l,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider
} from '@mui/joy';
import { Icon } from '@iconify/react';
import RoleManagement from '@/components/RBAC/RoleManagement';
import PermissionsGuide from '@/components/RBAC/PermissionsGuide';

export default function RBACPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const handleError = (message: string) => {
    setError(message);
    // Auto-hide error after 5 seconds
    setTimeout(() => setError(null), 5000);
  };

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      <Stack spacing={3}>
        <Typography level="h2">Access Control Management</Typography>

        {error && (
          <Alert color="danger" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* <Stack direction="row" spacing={2} useFlexGap>
          <Card variant="soft" sx={{ flexGrow: 1 }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <AspectRatio
                ratio={1}
                variant="solid"
                color="primary"
                sx={{
                  width: 64,
                  borderRadius: 'md',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Icon icon="mdi:shield-account" width={32} height={32} />
              </AspectRatio>
              <Box>
                <Typography level="title-lg">Role Management</Typography>
                <Typography level="body-sm">
                  Create and manage roles with specific permissions
                </Typography>
              </Box>
            </Stack>
          </Card>

          <Card variant="soft" sx={{ flexGrow: 1 }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <AspectRatio
                ratio={1}
                variant="solid"
                color="success"
                sx={{
                  width: 64,
                  borderRadius: 'md',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Icon icon="mdi:account-key" width={32} height={32} />
              </AspectRatio>
              <Box>
                <Typography level="title-lg">User Assignment</Typography>
                <Typography level="body-sm">
                  Assign roles and permissions to users
                </Typography>
              </Box>
            </Stack>
          </Card>
        </Stack> */}

        <Card>
          <Tabs
            value={activeTab}
            onChange={(event, value) => setActiveTab(value as number)}
            sx={{ bgcolor: 'background.surface' }}
          >
            <TabList>
              <Tab>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Icon icon="mdi:shield-outline" />
                  <span>Roles</span>
                </Stack>
              </Tab>
              <Tab>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Icon icon="mdi:help-circle-outline" />
                  <span>Help & Guidelines</span>
                </Stack>
              </Tab>
            </TabList>

            <Divider />

            <TabPanel value={0}>
              <RoleManagement onError={handleError} />
            </TabPanel>

            <TabPanel value={2}>
              <Box sx={{ maxWidth: 800, mx: 'auto' }}>
                <PermissionsGuide />
              </Box>
            </TabPanel>
          </Tabs>
        </Card>
      </Stack>
    </Box>
  );
} 
