'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useEffect, useState } from 'react';
import { Box, Typography, Card, CardContent, Grid, Stack, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b<PERSON>ist, Tab, TabPanel, tabClasses, LinearProgress, Divider, Link as JoyLink, CircularProgress, AspectRatio } from '@mui/joy';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import EquipmentManagement from '@/components/Facilities/EquipmentManagement';
import MaintenanceSchedule from '@/components/Facilities/MaintenanceSchedule';
import FacilityDashboard from '@/components/Facilities/FacilityDashboard';
import ShiftManagement from '@/components/Facilities/ShiftManagement';
import ErrorBoundary from '@/components/ErrorBoundary';

interface FacilityDetails {
  id: string;
  name: string;
  code: string;
  type: 'MORGUE' | 'STORAGE' | 'PROCESSING' | 'TEMPORARY';
  status: 'ACTIVE' | 'INACTIVE' | 'UNDER_MAINTENANCE' | 'DECOMMISSIONED';

  // Contact and Location
  address: string;
  city: string;
  province: string;
  postalCode: string;
  phone: string;
  email: string;

  // Capacity
  totalCapacity: number;
  currentOccupancy: number;

  // Operations
  operatingHours?: {
    open: string;
    close: string;
  };
  emergencyContact?: string;
  licenseExpiry?: string;

  // Stats (might come from analytics endpoint)
  equipmentCount?: number;
  staffCount?: number;
  activeEquipment?: number;
  recentActivities?: {
    date: string;
    action: string;
    user: string;
  }[];

  // Timestamps
  createdAt: string;
  updatedAt: string;
}

export default function FacilityDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [facility, setFacility] = useState<FacilityDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    const fetchFacility = async () => {
      try {
        // Use the admin API endpoint
        const response = await fetch(`/api/admin/facilities/${params.id}`);
        if (!response.ok) throw new Error('Failed to fetch facility details');
        const data = await response.json();

        // Set the facility data
        setFacility(data);

        // Also fetch analytics data
        try {
          const analyticsResponse = await fetch(`/api/admin/facilities/${params.id}/analytics`);
          if (analyticsResponse.ok) {
            const analyticsData = await analyticsResponse.json();
            // Update facility with analytics data
            setFacility(prev => {
              if (!prev) return null;
              return {
                ...prev,
                equipmentCount: analyticsData.equipmentData?.total || 0,
                activeEquipment: analyticsData.equipmentData?.functional || 0,
                staffCount: analyticsData.staffCount || 0,
              };
            });
          }
        } catch (analyticsErr) {
          console.error('Failed to load analytics data', analyticsErr);
        }
      } catch (err) {
        setError('Failed to load facility details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFacility();
  }, [params.id]);

  const handleError = (message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000);
  };

  if (isLoading) {
    return (
      <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
        <CircularProgress size="lg" />
        <Typography level="body-sm" color="neutral">
          Loading facility details...
        </Typography>
      </Box>
    );
  }

  if (error || !facility) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography level="h4" color="danger">
          {error || 'Facility not found'}
        </Typography>
        <Button
          onClick={() => router.back()}
          startDecorator={<Icon icon="mdi:arrow-left" />}
          sx={{ mt: 2 }}
        >
          Go Back
        </Button>
      </Box>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      style={{ overflow: 'auto', height: '100%' }}
    >
      <Box sx={{ p: 3 }}>
        <Stack spacing={3}>
          {/* Header with Breadcrumb */}
          <Box sx={{ mb: 2 }}>
            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
              <Link href="/admin/facilities" passHref>
                <JoyLink level="body-sm" startDecorator={<Icon icon="mdi:arrow-left" />}>
                  Facilities
                </JoyLink>
              </Link>
              <Icon icon="mdi:chevron-right" width={16} />
              <Typography level="body-sm" color="neutral">Details</Typography>
            </Stack>

            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              justifyContent="space-between"
              alignItems={{ xs: 'flex-start', sm: 'center' }}
              spacing={2}
              sx={{ mb: 1 }}
            >
              <Stack direction="row" spacing={2} alignItems="center">
                <AspectRatio
                  ratio="1"
                  sx={{
                    width: 42,
                    borderRadius: 'md',
                    bgcolor: 'primary.softBg',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Icon icon="mdi:domain" width={24} style={{ color: 'var(--joy-palette-primary-500)' }} />
                </AspectRatio>
                <Box>
                  <Typography level="h4">{facility.name}</Typography>
                  <Typography level="body-sm" color="neutral">
                    {facility.code} · {facility.type.replace('_', ' ')}
                  </Typography>
                </Box>
                <Chip
                  variant="soft"
                  color={
                    facility.status === 'ACTIVE'
                      ? 'success'
                      : facility.status === 'UNDER_MAINTENANCE'
                      ? 'warning'
                      : facility.status === 'INACTIVE'
                      ? 'neutral'
                      : 'danger'
                  }
                  startDecorator={
                    facility.status === 'ACTIVE'
                      ? <Icon icon="mdi:check-circle" />
                      : facility.status === 'UNDER_MAINTENANCE'
                      ? <Icon icon="mdi:tools" />
                      : facility.status === 'INACTIVE'
                      ? <Icon icon="mdi:pause-circle" />
                      : <Icon icon="mdi:close-circle" />
                  }
                >
                  {facility.status.replace('_', ' ')}
                </Chip>
              </Stack>

              <Stack direction="row" spacing={1}>
                <Link href={`/admin/facilities/${params.id}/staff`} passHref>
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:account-group" />}
                    size="sm"
                  >
                    Staff
                  </Button>
                </Link>
                <Link href={`/admin/facilities/${params.id}/analytics`} passHref>
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:chart-bar" />}
                    size="sm"
                  >
                    Analytics
                  </Button>
                </Link>
                <Link href={`/admin/facilities/${params.id}/config`} passHref>
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:cog" />}
                    size="sm"
                  >
                    Configure
                  </Button>
                </Link>
                <Link href={`/admin/facilities/${params.id}/edit`} passHref>
                  <Button
                    variant="solid"
                    color="primary"
                    startDecorator={<Icon icon="mdi:pencil" />}
                    size="sm"
                  >
                    Edit
                  </Button>
                </Link>
              </Stack>
            </Stack>
          </Box>

          {/* Dashboard Metrics */}
          <Grid container spacing={2}>
            {/* Capacity Card */}
            <Grid xs={12} sm={6} lg={3}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardContent>
                  <Stack spacing={1}>
                    <Typography level="title-sm" color="neutral">Occupancy</Typography>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Typography level="h3">
                        {facility.currentOccupancy}/{facility.totalCapacity}
                      </Typography>
                      <Chip
                        size="sm"
                        variant="soft"
                        color={
                          facility.currentOccupancy / facility.totalCapacity > 0.9 ? 'danger' :
                          facility.currentOccupancy / facility.totalCapacity > 0.7 ? 'warning' : 'success'
                        }
                      >
                        {Math.round((facility.currentOccupancy / facility.totalCapacity) * 100)}%
                      </Chip>
                    </Stack>
                    <LinearProgress
                      determinate
                      value={(facility.currentOccupancy / facility.totalCapacity) * 100}
                      color={
                        facility.currentOccupancy / facility.totalCapacity > 0.9 ? 'danger' :
                        facility.currentOccupancy / facility.totalCapacity > 0.7 ? 'warning' : 'success'
                      }
                      sx={{ mt: 1 }}
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>

            {/* Staff Card */}
            <Grid xs={12} sm={6} lg={3}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardContent>
                  <Stack spacing={1}>
                    <Typography level="title-sm" color="neutral">Staff</Typography>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Typography level="h3">{facility.staffCount || 0}</Typography>
                      <Icon icon="mdi:account-group" width={24} style={{ color: 'var(--joy-palette-primary-500)' }} />
                    </Stack>
                    <Typography level="body-sm" sx={{ mt: 1 }}>
                      <Link href={`/admin/facilities/${params.id}/staff`} passHref>
                        <JoyLink level="body-sm">
                          Manage staff <Icon icon="mdi:arrow-right" width={16} />
                        </JoyLink>
                      </Link>
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>

            {/* Equipment Card */}
            <Grid xs={12} sm={6} lg={3}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardContent>
                  <Stack spacing={1}>
                    <Typography level="title-sm" color="neutral">Equipment</Typography>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Typography level="h3">{facility.activeEquipment || 0}/{facility.equipmentCount || 0}</Typography>
                      <Icon icon="mdi:medical-bag" width={24} style={{ color: 'var(--joy-palette-primary-500)' }} />
                    </Stack>
                    <Typography level="body-sm" color="neutral">
                      {facility.equipmentCount ?
                        `${Math.round((facility.activeEquipment || 0) / facility.equipmentCount * 100)}% operational` :
                        'No equipment data'
                      }
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>

            {/* Last Updated Card */}
            <Grid xs={12} sm={6} lg={3}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardContent>
                  <Stack spacing={1}>
                    <Typography level="title-sm" color="neutral">Last Updated</Typography>
                    <Typography level="h3">
                      {facility.updatedAt ? new Date(facility.updatedAt).toLocaleDateString() : 'N/A'}
                    </Typography>
                    <Typography level="body-sm" color="neutral">
                      {facility.updatedAt ?
                        `at ${new Date(facility.updatedAt).toLocaleTimeString()}` :
                        'No update time available'
                      }
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Tabs */}
          <Card variant="outlined">
            <Tabs
              value={activeTab}
              onChange={(event, value) => setActiveTab(value as number)}
              sx={{
                borderRadius: 'md',
                '--Tab-indicatorThickness': '3px',
                '--Tab-indicatorColor': 'var(--joy-palette-primary-500)',
                overflow: 'auto'
              }}
            >
              <TabList
                variant="plain"
                sx={{
                  pt: 1,
                  px: 2,
                  justifyContent: { xs: 'flex-start', md: 'center' },
                  [`& .${tabClasses.root}`]: {
                    bgcolor: 'transparent',
                    boxShadow: 'none',
                    fontWeight: 'md',
                    '&:hover': {
                      bgcolor: 'transparent',
                      color: 'primary.500'
                    },
                    '&.Mui-selected': {
                      color: 'primary.500',
                      fontWeight: 'lg'
                    }
                  }
                }}
              >
                <Tab>
                  <Icon icon="mdi:information-outline" />
                  <Typography level="body-sm">Overview</Typography>
                </Tab>
                <Tab>
                  <Icon icon="mdi:medical-bag" />
                  <Typography level="body-sm">Equipment</Typography>
                </Tab>
                <Tab>
                  <Icon icon="mdi:tools" />
                  <Typography level="body-sm">Maintenance</Typography>
                </Tab>
                <Tab>
                  <Icon icon="mdi:calendar-clock" />
                  <Typography level="body-sm">Shifts</Typography>
                </Tab>
                <Tab>
                  <Icon icon="mdi:chart-bar" />
                  <Typography level="body-sm">Analytics</Typography>
                </Tab>
              </TabList>
              <Divider />

              <AnimatePresence mode="wait">
                {/* Overview Tab */}
                <TabPanel value={0}>
                  <motion.div
                    key="overview"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ErrorBoundary componentName="Facility Overview" onError={(error) => handleError(error.message)}>
                      <Grid container spacing={2}>
                        {/* Facility Information */}
                        <Grid xs={12} md={8}>
                          <Card>
                            <CardContent>
                              <Typography level="title-lg" sx={{ mb: 2 }}>
                                Facility Information
                              </Typography>
                              <Stack spacing={2}>
                                <Stack direction="row" justifyContent="space-between">
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Type</Typography>
                                    <Typography level="body-md">{facility.type.replace('_', ' ')}</Typography>
                                  </Box>
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Code</Typography>
                                    <Typography level="body-md">{facility.code}</Typography>
                                  </Box>
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Operating Since</Typography>
                                    <Typography level="body-md">{facility.createdAt ? new Date(facility.createdAt).toLocaleDateString() : 'N/A'}</Typography>
                                  </Box>
                                </Stack>

                                <Divider />

                                <Typography level="title-sm">Location</Typography>
                                <Stack direction="row" spacing={2} flexWrap="wrap">
                                  <Box sx={{ minWidth: '200px', flex: 1 }}>
                                    <Typography level="body-sm" color="neutral">Address</Typography>
                                    <Typography level="body-md">{facility.address || 'N/A'}</Typography>
                                  </Box>
                                  <Box sx={{ minWidth: '140px' }}>
                                    <Typography level="body-sm" color="neutral">City</Typography>
                                    <Typography level="body-md">{facility.city || 'N/A'}</Typography>
                                  </Box>
                                  <Box sx={{ minWidth: '140px' }}>
                                    <Typography level="body-sm" color="neutral">Province</Typography>
                                    <Typography level="body-md">{facility.province || 'N/A'}</Typography>
                                  </Box>
                                  <Box sx={{ minWidth: '120px' }}>
                                    <Typography level="body-sm" color="neutral">Postal Code</Typography>
                                    <Typography level="body-md">{facility.postalCode || 'N/A'}</Typography>
                                  </Box>
                                </Stack>

                                <Divider />

                                <Typography level="title-sm">Capacity Details</Typography>
                                <LinearProgress
                                  determinate
                                  value={(facility.currentOccupancy / facility.totalCapacity) * 100}
                                  color={
                                    facility.currentOccupancy / facility.totalCapacity > 0.9 ? 'danger' :
                                    facility.currentOccupancy / facility.totalCapacity > 0.7 ? 'warning' : 'success'
                                  }
                                  sx={{ my: 1, height: 8, borderRadius: 4 }}
                                />
                                <Stack direction="row" spacing={2} justifyContent="space-between">
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Total Capacity</Typography>
                                    <Typography level="body-md">{facility.totalCapacity}</Typography>
                                  </Box>
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Current Occupancy</Typography>
                                    <Typography level="body-md">{facility.currentOccupancy}</Typography>
                                  </Box>
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Available</Typography>
                                    <Typography level="body-md">{facility.totalCapacity - facility.currentOccupancy}</Typography>
                                  </Box>
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Utilization</Typography>
                                    <Typography level="body-md">{Math.round((facility.currentOccupancy / facility.totalCapacity) * 100)}%</Typography>
                                  </Box>
                                </Stack>
                              </Stack>
                            </CardContent>
                          </Card>
                        </Grid>

                        {/* Contact Information */}
                        <Grid xs={12} md={4}>
                          <Stack spacing={2}>
                            <Card>
                              <CardContent>
                                <Typography level="title-lg" sx={{ mb: 2 }}>
                                  Contact Information
                                </Typography>
                                <Stack spacing={2}>
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Email</Typography>
                                    <Typography level="body-md">{facility.email || 'N/A'}</Typography>
                                  </Box>
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Phone</Typography>
                                    <Typography level="body-md">{facility.phone || 'N/A'}</Typography>
                                  </Box>
                                  <Box>
                                    <Typography level="body-sm" color="neutral">Emergency Contact</Typography>
                                    <Typography level="body-md">{facility.emergencyContact || 'N/A'}</Typography>
                                  </Box>
                                </Stack>
                              </CardContent>
                            </Card>

                            <Card>
                              <CardContent>
                                <Typography level="title-lg" sx={{ mb: 2 }}>
                                  Operating Hours
                                </Typography>
                                {facility.operatingHours ? (
                                  <Stack spacing={2}>
                                    <Stack direction="row" justifyContent="space-between">
                                      <Box>
                                        <Typography level="body-sm" color="neutral">Opens At</Typography>
                                        <Typography level="body-md">{facility.operatingHours.open}</Typography>
                                      </Box>
                                      <Box>
                                        <Typography level="body-sm" color="neutral">Closes At</Typography>
                                        <Typography level="body-md">{facility.operatingHours.close}</Typography>
                                      </Box>
                                    </Stack>
                                    {facility.licenseExpiry && (
                                      <Box>
                                        <Typography level="body-sm" color="neutral">License Expires</Typography>
                                        <Typography level="body-md">{new Date(facility.licenseExpiry).toLocaleDateString()}</Typography>
                                      </Box>
                                    )}
                                  </Stack>
                                ) : (
                                  <Typography level="body-sm" color="neutral">
                                    No operating hours data available
                                  </Typography>
                                )}
                              </CardContent>
                            </Card>
                          </Stack>
                        </Grid>
                      </Grid>
                    </ErrorBoundary>
                  </motion.div>
                </TabPanel>

                {/* Equipment Tab */}
                <TabPanel value={1}>
                  <motion.div
                    key="equipment"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <EquipmentManagement facilityId={params.id} onError={handleError} />
                  </motion.div>
                </TabPanel>

                {/* Maintenance Tab */}
                <TabPanel value={2}>
                  <motion.div
                    key="maintenance"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <MaintenanceSchedule facilityId={params.id} onError={handleError} />
                  </motion.div>
                </TabPanel>

                {/* Shifts Tab */}
                <TabPanel value={3}>
                  <motion.div
                    key="shifts"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ShiftManagement facilityId={params.id} onError={handleError} />
                  </motion.div>
                </TabPanel>

                {/* Analytics Tab */}
                <TabPanel value={4}>
                  <motion.div
                    key="analytics"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <FacilityDashboard facilityId={params.id} onError={handleError} />
                  </motion.div>
                </TabPanel>
              </AnimatePresence>
            </Tabs>
          </Card>
        </Stack>
      </Box>
    </motion.div>
  );
}