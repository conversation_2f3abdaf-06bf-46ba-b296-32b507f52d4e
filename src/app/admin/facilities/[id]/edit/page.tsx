'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Option,
  Grid,
  Stack,
  Divider,
  Alert,
  CircularProgress,
  IconButton,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import { FacilityTypeEnum, FacilityStatusEnum } from '@/lib/schema/facilitySchema';

interface FacilityFormData {
  id: string;
  name: string;
  code: string;
  type: string;
  status: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  phone: string;
  email: string;
  totalCapacity: number;
  currentOccupancy: number;
  operatingHours: {
    open: string;
    close: string;
  };
  emergencyContact: string;
}

export default function EditFacilityPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [facility, setFacility] = useState<FacilityFormData>({
    id: '',
    name: '',
    code: '',
    type: 'STORAGE',
    status: 'ACTIVE',
    address: '',
    city: '',
    province: '',
    postalCode: '',
    phone: '',
    email: '',
    totalCapacity: 100,
    currentOccupancy: 0,
    operatingHours: {
      open: '08:00',
      close: '17:00',
    },
    emergencyContact: '',
  });

  useEffect(() => {
    const fetchFacility = async () => {
      try {
        const response = await fetch(`/api/admin/facilities/${params.id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch facility');
        }

        const data = await response.json();

        // Format the data appropriately for the form
        setFacility({
          id: data.id,
          name: data.name,
          code: data.code,
          type: data.type,
          status: data.status,
          address: data.address || '',
          city: data.city || '',
          province: data.province || '',
          postalCode: data.postalCode || '',
          phone: data.phone || '',
          email: data.email || '',
          totalCapacity: data.totalCapacity,
          currentOccupancy: data.currentOccupancy,
          operatingHours: data.operatingHours || { open: '08:00', close: '17:00' },
          emergencyContact: data.emergencyContact || '',
        });
      } catch (err: any) {
        setError(err.message || 'Failed to load facility data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFacility();
  }, [params.id]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFacility(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (name: string, value: string | null) => {
    if (value) {
      setFacility(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleTimeChange = (field: 'open' | 'close', value: string) => {
    setFacility(prev => ({
      ...prev,
      operatingHours: {
        ...prev.operatingHours,
        [field]: value,
      },
    }));
  };

  const handleNumberChange = (name: string, value: string) => {
    const numValue = parseInt(value);
    if (!isNaN(numValue)) {
      setFacility(prev => ({
        ...prev,
        [name]: numValue,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/facilities/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(facility),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update facility');
      }

      setSuccess(true);
      // Navigate back to the facility page after a brief delay
      setTimeout(() => {
        router.push(`/admin/facilities/${params.id}`);
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'An error occurred while updating the facility');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Stack spacing={3}>
          {/* Header */}
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Stack direction="row" spacing={2} alignItems="center">
              <IconButton
                variant="outlined"
                color="neutral"
                size="sm"
                onClick={() => router.push(`/admin/facilities/${params.id}`)}
              >
                <Icon icon="mdi:arrow-left" />
              </IconButton>
              <Typography level="h4">Edit Facility</Typography>
            </Stack>
          </Stack>

          {/* Status Messages */}
          {error && (
            <Alert color="danger" variant="soft">
              {error}
            </Alert>
          )}
          {success && (
            <Alert color="success" variant="soft">
              Facility updated successfully! Redirecting...
            </Alert>
          )}

          {/* Form */}
          <Card>
            <CardContent>
              <form onSubmit={handleSubmit}>
                <Grid container spacing={2}>
                  <Grid xs={12}>
                    <Typography level="title-md">Basic Information</Typography>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Facility Name</FormLabel>
                      <Input
                        name="name"
                        value={facility.name}
                        onChange={handleInputChange}
                        placeholder="Enter facility name"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Facility Code</FormLabel>
                      <Input
                        name="code"
                        value={facility.code}
                        onChange={handleInputChange}
                        placeholder="Enter facility code"
                        disabled // Usually codes are not changed after creation
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Type</FormLabel>
                      <Select
                        value={facility.type}
                        onChange={(_, value) => handleSelectChange('type', value)}
                      >
                        {Object.values(FacilityTypeEnum.Values).map((type) => (
                          <Option key={type} value={type}>
                            {type.replace(/_/g, ' ')}
                          </Option>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Status</FormLabel>
                      <Select
                        value={facility.status}
                        onChange={(_, value) => handleSelectChange('status', value)}
                      >
                        {Object.values(FacilityStatusEnum.Values).map((status) => (
                          <Option key={status} value={status}>
                            {status.replace(/_/g, ' ')}
                          </Option>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid xs={12}>
                    <Typography level="title-md" sx={{ mt: 2 }}>
                      Contact and Location
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid xs={12}>
                    <FormControl>
                      <FormLabel>Address</FormLabel>
                      <Input
                        name="address"
                        value={facility.address}
                        onChange={handleInputChange}
                        placeholder="Enter facility address"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={4}>
                    <FormControl>
                      <FormLabel>City</FormLabel>
                      <Input
                        name="city"
                        value={facility.city}
                        onChange={handleInputChange}
                        placeholder="Enter city"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={4}>
                    <FormControl>
                      <FormLabel>Province/State</FormLabel>
                      <Input
                        name="province"
                        value={facility.province}
                        onChange={handleInputChange}
                        placeholder="Enter province/state"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={4}>
                    <FormControl>
                      <FormLabel>Postal Code</FormLabel>
                      <Input
                        name="postalCode"
                        value={facility.postalCode}
                        onChange={handleInputChange}
                        placeholder="Enter postal code"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Phone</FormLabel>
                      <Input
                        name="phone"
                        value={facility.phone}
                        onChange={handleInputChange}
                        placeholder="Enter phone number"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Email</FormLabel>
                      <Input
                        type="email"
                        name="email"
                        value={facility.email}
                        onChange={handleInputChange}
                        placeholder="Enter email address"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12}>
                    <Typography level="title-md" sx={{ mt: 2 }}>
                      Capacity & Operations
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Total Capacity</FormLabel>
                      <Input
                        type="number"
                        value={facility.totalCapacity}
                        onChange={(e) => handleNumberChange('totalCapacity', e.target.value)}
                        slotProps={{ input: { min: 1 } }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Current Occupancy</FormLabel>
                      <Input
                        type="number"
                        value={facility.currentOccupancy}
                        onChange={(e) => handleNumberChange('currentOccupancy', e.target.value)}
                        slotProps={{ input: { min: 0, max: facility.totalCapacity } }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Emergency Contact</FormLabel>
                      <Input
                        name="emergencyContact"
                        value={facility.emergencyContact}
                        onChange={handleInputChange}
                        placeholder="Emergency contact name/number"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={3}>
                    <FormControl>
                      <FormLabel>Opening Time</FormLabel>
                      <Input
                        type="time"
                        value={facility.operatingHours.open}
                        onChange={(e) => handleTimeChange('open', e.target.value)}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={3}>
                    <FormControl>
                      <FormLabel>Closing Time</FormLabel>
                      <Input
                        type="time"
                        value={facility.operatingHours.close}
                        onChange={(e) => handleTimeChange('close', e.target.value)}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sx={{ mt: 2 }}>
                    <Stack direction="row" spacing={2} justifyContent="flex-end">
                      <Button
                        variant="outlined"
                        color="neutral"
                        onClick={() => router.push(`/admin/facilities/${params.id}`)}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        loading={isSubmitting}
                        disabled={success}
                        startDecorator={<Icon icon="mdi:check" />}
                      >
                        Update Facility
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </CardContent>
          </Card>
        </Stack>
      </motion.div>
    </Box>
  );
}