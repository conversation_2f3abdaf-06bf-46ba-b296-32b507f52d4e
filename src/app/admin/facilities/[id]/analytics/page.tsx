'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Select,
  Option,
  Tab,
  TabList,
  Tabs,
  TabPanel,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import dynamicImport from 'next/dynamic';

// Force dynamic rendering
export const dynamic = "force-dynamic";

// Dynamically import Nivo charts
const ResponsiveLineChart = dynamicImport(() => import('@nivo/line').then(mod => mod.ResponsiveLine), { ssr: false });
const ResponsivePieChart = dynamicImport(() => import('@nivo/pie').then(mod => mod.ResponsivePie), { ssr: false });
// Types
interface FacilityAnalytics {
  name: string;
  type: string;
  capacityData: {
    current: number;
    total: number;
    percentage: number;
    history: Array<{ date: string; value: number }>;
  };
  casesData: {
    total: number;
    active: number;
    completed: number;
    byDepartment: Array<{ id: string; label: string; value: number; color: string }>;
    history: Array<{ date: string; active: number; completed: number }>;
  };
  activityData: {
    recentDays: Array<{ date: string; value: number }>;
    byHour: Array<{ hour: string; value: number }>;
    byStaff: Array<{ staff: string; role: string; value: number }>;
  };
  equipmentData: {
    total: number;
    functional: number;
    maintenance: number;
    usage: Array<{ equipment: string; usageHours: number }>;
  };
}

export default function FacilityAnalyticsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [analytics, setAnalytics] = useState<FacilityAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchAnalytics = async () => {
      setIsLoading(true);
      try {
        // API request to get analytics data
        const response = await fetch(`/api/admin/facilities/${params.id}/analytics?timeRange=${timeRange}`);
        if (!response.ok) throw new Error('Failed to fetch analytics data');
        const data = await response.json();
        setAnalytics(data);
      } catch (err) {
        console.error(err);
        setError('Failed to load facility analytics data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, [params.id, timeRange]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!analytics && !isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert color="danger">
          Failed to load analytics data. Please try again or contact support.
        </Alert>
        <Button
          sx={{ mt: 2 }}
          onClick={() => router.push(`/admin/facilities/${params.id}`)}
          startDecorator={<Icon icon="mdi:arrow-left" />}
        >
          Back to Facility
        </Button>
      </Box>
    );
  }

  const getCapacityColor = (percentage: number) => {
    if (percentage >= 90) return 'danger';
    if (percentage >= 75) return 'warning';
    if (percentage >= 50) return 'primary';
    return 'success';
  };

  return (
    <Box sx={{ p: 3 }}>
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
        >
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography level="h4">
                Analytics: {analytics?.name}
              </Typography>
              <Typography level="body-sm" color="neutral">
                Performance metrics and operational analytics
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Select
                value={timeRange}
                onChange={(_, value) => value && setTimeRange(value)}
                size="sm"
                startDecorator={<Icon icon="mdi:calendar" />}
              >
                <Option value="7d">Last 7 Days</Option>
                <Option value="30d">Last 30 Days</Option>
                <Option value="90d">Last Quarter</Option>
                <Option value="1y">Last Year</Option>
              </Select>
              <Button
                variant="outlined"
                color="neutral"
                onClick={() => router.push(`/admin/facilities/${params.id}`)}
                startDecorator={<Icon icon="mdi:arrow-left" />}
              >
                Back to Facility
              </Button>
            </Box>
          </Box>

          {/* Analytics Tabs */}
          <Card>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={activeTab}
                onChange={(_, value) => setActiveTab(value as string)}
                sx={{ bgcolor: 'background.body' }}
              >
                <TabList>
                  <Tab value="overview">Overview</Tab>
                  <Tab value="capacity">Capacity Utilization</Tab>
                  <Tab value="cases">Case Analytics</Tab>
                  <Tab value="activity">Staff Activity</Tab>
                  <Tab value="equipment">Equipment Usage</Tab>
                </TabList>
              </Tabs>
            </Box>

            {/* Overview Tab */}
            <TabPanel value="overview" sx={{ p: 0 }}>
              <Box sx={{ p: 3 }}>
                {/* KPI Cards */}
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid xs={12} sm={6} md={3}>
                    <Card variant="soft" sx={{ height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Typography level="title-md">Capacity Utilization</Typography>
                          <Icon icon="mdi:tune-vertical-variant" />
                        </Box>
                        <Box sx={{ textAlign: 'center', my: 2 }}>
                          <Typography level="h2" color={getCapacityColor(analytics?.capacityData.percentage || 0)}>
                            {analytics?.capacityData.percentage}%
                          </Typography>
                          <Typography level="body-sm" color="neutral">
                            {analytics?.capacityData.current} of {analytics?.capacityData.total} spaces
                          </Typography>
                        </Box>
                        <Box sx={{ width: '100%', height: 4, bgcolor: 'background.level3', borderRadius: 4, overflow: 'hidden' }}>
                          <Box
                            sx={{
                              width: `${analytics?.capacityData.percentage}%`,
                              height: '100%',
                              bgcolor: `${getCapacityColor(analytics?.capacityData.percentage || 0)}.solidBg`,
                            }}
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid xs={12} sm={6} md={3}>
                    <Card variant="soft" sx={{ height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Typography level="title-md">Active Cases</Typography>
                          <Icon icon="mdi:folder-open" />
                        </Box>
                        <Box sx={{ textAlign: 'center', my: 2 }}>
                          <Typography level="h2" color="primary">
                            {analytics?.casesData.active}
                          </Typography>
                          <Typography level="body-sm" color="neutral">
                            {((analytics?.casesData.active || 0) / (analytics?.casesData.total || 1) * 100).toFixed(0)}% of total cases
                          </Typography>
                        </Box>
                        <Chip
                          size="sm"
                          variant="soft"
                          color={analytics?.casesData.active > analytics?.casesData.completed ? 'warning' : 'success'}
                          startDecorator={<Icon icon={analytics?.casesData.active > analytics?.casesData.completed ? 'mdi:trending-up' : 'mdi:trending-down'} />}
                        >
                          {analytics?.casesData.active > analytics?.casesData.completed ? 'Above' : 'Below'} average
                        </Chip>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid xs={12} sm={6} md={3}>
                    <Card variant="soft" sx={{ height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Typography level="title-md">Staff Activity</Typography>
                          <Icon icon="mdi:account-group" />
                        </Box>
                        <Box sx={{ textAlign: 'center', my: 2 }}>
                          <Typography level="h2" color="success">
                            {analytics?.activityData.recentDays.reduce((sum, day) => sum + day.value, 0)}
                          </Typography>
                          <Typography level="body-sm" color="neutral">
                            Total activities in {timeRange === '7d' ? '7 days' : timeRange === '30d' ? '30 days' : timeRange === '90d' ? '90 days' : '1 year'}
                          </Typography>
                        </Box>
                        <Chip
                          size="sm"
                          variant="soft"
                          color="success"
                          startDecorator={<Icon icon="mdi:clock-outline" />}
                        >
                          Peak: {analytics?.activityData.byHour.reduce((max, current) => (current.value > max.value ? current : max), { hour: '', value: 0 }).hour}
                        </Chip>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid xs={12} sm={6} md={3}>
                    <Card variant="soft" sx={{ height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Typography level="title-md">Equipment Status</Typography>
                          <Icon icon="mdi:medical-bag" />
                        </Box>
                        <Box sx={{ textAlign: 'center', my: 2 }}>
                          <Typography level="h2" color={analytics?.equipmentData.functional / (analytics?.equipmentData.total || 1) * 100 > 90 ? 'success' : 'warning'}>
                            {analytics?.equipmentData.functional}/{analytics?.equipmentData.total}
                          </Typography>
                          <Typography level="body-sm" color="neutral">
                            Functional equipment
                          </Typography>
                        </Box>
                        <Chip
                          size="sm"
                          variant="soft"
                          color={analytics?.equipmentData.maintenance > 0 ? 'warning' : 'success'}
                          startDecorator={<Icon icon={analytics?.equipmentData.maintenance > 0 ? 'mdi:wrench' : 'mdi:check-circle'} />}
                        >
                          {analytics?.equipmentData.maintenance} in maintenance
                        </Chip>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Grid container spacing={3}>
                  {/* Capacity Trend */}
                  <Grid xs={12} md={6}>
                    <Card sx={{ height: '100%' }}>
                      <CardContent>
                        <Typography level="title-md">Capacity Utilization Trend</Typography>
                        <Box sx={{ height: 250 }}>
                          {analytics?.capacityData.history && (
                            <ResponsiveLineChart
                              data={[
                                {
                                  id: 'capacity',
                                  color: 'hsl(211, 70%, 50%)',
                                  data: analytics.capacityData.history.map(item => ({
                                    x: item.date,
                                    y: item.value
                                  }))
                                }
                              ]}
                              margin={{ top: 20, right: 20, bottom: 50, left: 50 }}
                              xScale={{ type: 'point' }}
                              yScale={{ type: 'linear', min: 0, max: 100 }}
                              axisBottom={{
                                tickSize: 5,
                                tickPadding: 5,
                                tickRotation: -45,
                                legendOffset: 36,
                                legendPosition: 'middle'
                              }}
                              axisLeft={{
                                tickSize: 5,
                                tickPadding: 5,
                                tickRotation: 0,
                                legend: 'Utilization %',
                                legendOffset: -40,
                                legendPosition: 'middle'
                              }}
                              pointSize={8}
                              useMesh={true}
                              curve="monotoneX"
                              colors={{ scheme: 'paired' }}
                              lineWidth={3}
                              enableArea={true}
                              areaOpacity={0.15}
                            />
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Case Distribution */}
                  <Grid xs={12} md={6}>
                    <Card sx={{ height: '100%' }}>
                      <CardContent>
                        <Typography level="title-md">Case Distribution by Department</Typography>
                        <Box sx={{ height: 250 }}>
                          {analytics?.casesData.byDepartment && (
                            <ResponsivePieChart
                              data={analytics.casesData.byDepartment}
                              margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                              innerRadius={0.5}
                              padAngle={0.7}
                              cornerRadius={3}
                              activeOuterRadiusOffset={8}
                              borderWidth={1}
                              borderColor={{ from: 'color', modifiers: [['darker', 0.2]] }}
                              arcLinkLabelsSkipAngle={10}
                              arcLinkLabelsTextColor="grey"
                              arcLinkLabelsThickness={2}
                              arcLinkLabelsColor={{ from: 'color' }}
                              arcLabelsSkipAngle={10}
                              arcLabelsTextColor={{ from: 'color', modifiers: [['darker', 2]] }}
                              colors={{ scheme: 'nivo' }}
                              legends={[
                                {
                                  anchor: 'bottom',
                                  direction: 'row',
                                  justify: false,
                                  translateX: 0,
                                  translateY: 40,
                                  itemsSpacing: 0,
                                  itemWidth: 100,
                                  itemHeight: 18,
                                  itemTextColor: '#999',
                                  itemDirection: 'left-to-right',
                                  itemOpacity: 1,
                                  symbolSize: 12,
                                  symbolShape: 'circle',
                                  effects: [
                                    {
                                      on: 'hover',
                                      style: {
                                        itemTextColor: '#000'
                                      }
                                    }
                                  ]
                                }
                              ]}
                            />
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>

            {/* Other tabs would be implemented similarly */}
            <TabPanel value="capacity" sx={{ p: 3 }}>
              <Typography level="title-md">Capacity Utilization Details</Typography>
              <Typography level="body-sm" color="neutral" sx={{ mb: 3 }}>
                Detailed capacity analytics would be shown here:
              </Typography>
              <ul>
                <li>Historical capacity trends</li>
                <li>Space utilization by type/category</li>
                <li>Forecasting and capacity planning</li>
                <li>Peak usage periods</li>
              </ul>
            </TabPanel>

            <TabPanel value="cases" sx={{ p: 3 }}>
              <Typography level="title-md">Case Analytics</Typography>
              <Typography level="body-sm" color="neutral" sx={{ mb: 3 }}>
                Detailed case analytics would be shown here:
              </Typography>
              <ul>
                <li>Case processing times and bottlenecks</li>
                <li>Case category distribution</li>
                <li>Case resolution rates</li>
                <li>Anomaly detection</li>
              </ul>
            </TabPanel>

            <TabPanel value="activity" sx={{ p: 3 }}>
              <Typography level="title-md">Staff Activity Analysis</Typography>
              <Typography level="body-sm" color="neutral" sx={{ mb: 3 }}>
                Detailed staff activity analytics would be shown here:
              </Typography>
              <ul>
                <li>Individual and team productivity metrics</li>
                <li>Activity types and distribution</li>
                <li>Workload balance analysis</li>
                <li>Peak activity periods</li>
              </ul>
            </TabPanel>

            <TabPanel value="equipment" sx={{ p: 3 }}>
              <Typography level="title-md">Equipment Usage</Typography>
              <Typography level="body-sm" color="neutral" sx={{ mb: 3 }}>
                Detailed equipment usage analytics would be shown here:
              </Typography>
              <ul>
                <li>Equipment utilization rates</li>
                <li>Maintenance schedules and history</li>
                <li>Equipment performance metrics</li>
                <li>Lifecycle and replacement planning</li>
              </ul>
            </TabPanel>
          </Card>

          {/* Download Reports */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="outlined"
              color="neutral"
              startDecorator={<Icon icon="mdi:file-download" />}
              onClick={() => {/* Download analytics report */}}
            >
              Download Analytics Report
            </Button>
          </Box>
        </motion.div>
      </AnimatePresence>
    </Box>
  );
}