"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Divider,
  Chip,
  IconButton,
  Input,
  Select,
  Option,
  Sheet,
  Table,
  Avatar,
  Modal,
  ModalDialog,
  ModalClose,
  Alert,
  CircularProgress,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { UserRoleEnum } from '@/lib/schema/facilityStaffSchema';

interface FacilityStaffMember {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  position?: string;
  image?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  assignedAt: string;
}

export default function FacilityStaffPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [staff, setStaff] = useState<FacilityStaffMember[]>([]);
  const [filteredStaff, setFilteredStaff] = useState<FacilityStaffMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterRole, setFilterRole] = useState<string>('all');
  const [filterDepartment, setFilterDepartment] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [facilityName, setFacilityName] = useState('');
  const [openAddStaffModal, setOpenAddStaffModal] = useState(false);
  const [departments, setDepartments] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    const fetchStaff = async () => {
      setIsLoading(true);
      try {
        // Fetch facility details to get the name
        const facilityResponse = await fetch(`/api/admin/facilities/${params.id}`);
        if (!facilityResponse.ok) throw new Error('Failed to fetch facility details');
        const facilityData = await facilityResponse.json();
        setFacilityName(facilityData.name);

        // Fetch staff members
        const staffResponse = await fetch(`/api/admin/facilities/${params.id}/staff`);
        if (!staffResponse.ok) throw new Error('Failed to fetch staff data');
        const staffData = await staffResponse.json();
        setStaff(staffData);
        setFilteredStaff(staffData);

        // Extract unique departments for filtering
        const uniqueDepartments = [...new Set(staffData
          .map((member: FacilityStaffMember) => member.department)
          .filter(Boolean))];
        setDepartments(uniqueDepartments as string[]);
      } catch (err) {
        console.error(err);
        setError('Failed to load facility staff data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStaff();
  }, [params.id]);

  // Apply filters and search
  useEffect(() => {
    let result = [...staff];
    
    // Filter by role
    if (filterRole !== 'all') {
      result = result.filter(member => member.role === filterRole);
    }
    
    // Filter by department
    if (filterDepartment !== 'all') {
      result = result.filter(member => member.department === filterDepartment);
    }
    
    // Apply search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(member => 
        member.name.toLowerCase().includes(term) || 
        member.email.toLowerCase().includes(term) ||
        (member.position && member.position.toLowerCase().includes(term))
      );
    }
    
    setFilteredStaff(result);
  }, [staff, filterRole, filterDepartment, searchTerm]);

  const handleRemoveStaff = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this staff member from the facility?')) {
      return;
    }
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/facilities/${params.id}/staff/${userId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to remove staff member');
      }
      
      // Filter out the removed staff member
      setStaff(prevStaff => prevStaff.filter(member => member.id !== userId));
      setSuccessMessage('Staff member removed successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: any) {
      setError(err.message || 'An error occurred while removing staff member');
      setTimeout(() => setError(null), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddStaff = () => {
    setOpenAddStaffModal(true);
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
        >
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography level="h4">
                Staff Management: {facilityName}
              </Typography>
              <Typography level="body-sm" color="neutral">
                Manage staff members assigned to this facility
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                color="neutral"
                onClick={() => router.push(`/admin/facilities/${params.id}`)}
                startDecorator={<Icon icon="mdi:arrow-left" />}
              >
                Back to Facility
              </Button>
              <Button
                variant="solid"
                color="primary"
                onClick={handleAddStaff}
                startDecorator={<Icon icon="mdi:account-plus" />}
              >
                Add Staff
              </Button>
            </Box>
          </Box>

          {/* Alert Messages */}
          {error && (
            <Alert color="danger" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {successMessage && (
            <Alert color="success" sx={{ mb: 2 }}>
              {successMessage}
            </Alert>
          )}

          {/* Filters */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid xs={12} sm={4}>
                  <Input 
                    placeholder="Search by name, email, or position"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    startDecorator={<Icon icon="mdi:magnify" />}
                    fullWidth
                  />
                </Grid>
                <Grid xs={12} sm={3}>
                  <Select
                    placeholder="Filter by role"
                    value={filterRole}
                    onChange={(_, value) => setFilterRole(value as string)}
                    startDecorator={<Icon icon="mdi:briefcase" />}
                  >
                    <Option value="all">All Roles</Option>
                    {Object.values(UserRoleEnum.Values).map((role) => (
                      <Option key={role} value={role}>{role.replace('_', ' ')}</Option>
                    ))}
                  </Select>
                </Grid>
                <Grid xs={12} sm={3}>
                  <Select
                    placeholder="Filter by department"
                    value={filterDepartment}
                    onChange={(_, value) => setFilterDepartment(value as string)}
                    startDecorator={<Icon icon="mdi:domain" />}
                    disabled={departments.length === 0}
                  >
                    <Option value="all">All Departments</Option>
                    {departments.map((dept) => (
                      <Option key={dept} value={dept}>{dept}</Option>
                    ))}
                  </Select>
                </Grid>
                <Grid xs={12} sm={2} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant="plain"
                    color="neutral"
                    onClick={() => {
                      setFilterRole('all');
                      setFilterDepartment('all');
                      setSearchTerm('');
                    }}
                    startDecorator={<Icon icon="mdi:refresh" />}
                  >
                    Reset
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Staff List */}
          <Card>
            <Sheet sx={{ height: 'calc(100vh - 320px)', overflow: 'auto' }}>
              <Table
                hoverRow
                stickyHeader
                sx={{
                  '--TableCell-headBackground': 'var(--joy-palette-background-level1)',
                  '--Table-headerUnderlineThickness': '1px',
                  '--TableRow-hoverBackground': 'var(--joy-palette-background-level2)',
                }}
              >
                <thead>
                  <tr>
                    <th style={{ width: 240 }}>Staff Member</th>
                    <th style={{ width: 140 }}>Role</th>
                    <th style={{ width: 140 }}>Department</th>
                    <th style={{ width: 140 }}>Position</th>
                    <th style={{ width: 120 }}>Status</th>
                    <th style={{ width: 130 }}>Assigned On</th>
                    <th style={{ width: 120 }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStaff.length > 0 ? (
                    filteredStaff.map((member) => (
                      <tr key={member.id}>
                        <td>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar
                              src={member.image || ''}
                              alt={member.name}
                              variant="outlined"
                            >
                              {member.name.charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography level="title-sm">{member.name}</Typography>
                              <Typography level="body-xs" color="neutral">
                                {member.email}
                              </Typography>
                            </Box>
                          </Box>
                        </td>
                        <td>
                          <Chip
                            size="sm"
                            variant="soft"
                            color={
                              member.role === 'ADMIN' ? 'danger' :
                              member.role === 'PATHOLOGIST' ? 'success' :
                              member.role === 'MORGUE_STAFF' ? 'primary' :
                              member.role === 'SECURITY_STAFF' ? 'warning' : 'neutral'
                            }
                          >
                            {member.role.replace('_', ' ')}
                          </Chip>
                        </td>
                        <td>
                          {member.department || '-'}
                        </td>
                        <td>
                          {member.position || '-'}
                        </td>
                        <td>
                          <Chip
                            size="sm"
                            variant="soft"
                            color={
                              member.status === 'ACTIVE' ? 'success' :
                              member.status === 'INACTIVE' ? 'neutral' : 'danger'
                            }
                          >
                            {member.status}
                          </Chip>
                        </td>
                        <td>
                          {new Date(member.assignedAt).toLocaleDateString()}
                        </td>
                        <td>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <IconButton
                              size="sm"
                              variant="plain"
                              color="neutral"
                              onClick={() => router.push(`/admin/users/${member.id}`)}
                            >
                              <Icon icon="mdi:eye" />
                            </IconButton>
                            <IconButton
                              size="sm"
                              variant="plain"
                              color="danger"
                              onClick={() => handleRemoveStaff(member.id)}
                            >
                              <Icon icon="mdi:account-remove" />
                            </IconButton>
                          </Box>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} style={{ textAlign: 'center' }}>
                        <Box sx={{ py: 3 }}>
                          <Typography level="body-lg" color="neutral">
                            No staff members found
                          </Typography>
                          <Typography level="body-sm" color="neutral">
                            {searchTerm || filterRole !== 'all' || filterDepartment !== 'all'
                              ? 'Try adjusting your filters'
                              : 'Add staff members to this facility'}
                          </Typography>
                        </Box>
                      </td>
                    </tr>
                  )}
                </tbody>
              </Table>
            </Sheet>
            <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography level="body-sm" color="neutral">
                {filteredStaff.length} staff member{filteredStaff.length !== 1 ? 's' : ''} found
              </Typography>
              <Button
                size="sm"
                variant="plain"
                endDecorator={<Icon icon="mdi:download" />}
                onClick={() => {/* Export functionality */}}
              >
                Export List
              </Button>
            </Box>
          </Card>
        </motion.div>
      </AnimatePresence>

      {/* Add Staff Modal - This would normally be a separate component */}
      <Modal open={openAddStaffModal} onClose={() => setOpenAddStaffModal(false)}>
        <ModalDialog size="lg">
          <ModalClose />
          <Typography level="h4">Add Staff to Facility</Typography>
          <Divider sx={{ my: 2 }} />
          <Typography level="body-sm" color="neutral" sx={{ mb: 3 }}>
            Search for users and assign them to this facility
          </Typography>
          
          {/* This would be a component with user search and role assignment */}
          <Typography level="body-sm" color="neutral" sx={{ mt: 2 }}>
            The add staff functionality would include:
          </Typography>
          <ul style={{ margin: '8px 0' }}>
            <li>User search by name or email</li>
            <li>Role assignment</li>
            <li>Department assignment</li>
            <li>Position specification</li>
          </ul>
          
          <Box sx={{ mt: 3, display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              variant="plain"
              color="neutral"
              onClick={() => setOpenAddStaffModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="solid"
              color="primary"
              onClick={() => setOpenAddStaffModal(false)}
            >
              Save
            </Button>
          </Box>
        </ModalDialog>
      </Modal>
    </Box>
  );
} 