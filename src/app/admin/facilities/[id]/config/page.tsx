"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Divider,
  Input,
  Textarea,
  Select,
  Option,
  FormControl,
  FormLabel,
  FormHelperText,
  Chip,
  Alert,
  CircularProgress,
  Modal,
  ModalDialog,
  ModalClose,
  Tab,
  TabList,
  Tabs,
  TabPanel,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { FacilityTypeEnum, FacilityStatusEnum } from '@/lib/schema/facilitySchema';
import { format } from 'date-fns';
import { ReferralTimerBehavior } from '@/lib/utils/admissionUtils';
import ReferralTimerSettings from '@/components/Facilities/ReferralTimerSettings';

// Types
interface FacilityConfig {
  id: string;
  name: string;
  code: string;
  type: string;
  status: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  phone: string;
  email: string;
  totalCapacity: number;
  currentOccupancy: number;
  operatingHours: {
    open: string;
    close: string;
  };
  emergencyContact: string;
  licenseExpiry: string;
  referralTimerBehavior?: ReferralTimerBehavior;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export default function FacilityConfigPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [facility, setFacility] = useState<FacilityConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('general');
  const [formData, setFormData] = useState<Partial<FacilityConfig>>({});
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    const fetchFacility = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/admin/facilities/${params.id}`);
        if (!response.ok) throw new Error('Failed to fetch facility details');
        const data = await response.json();
        setFacility(data);
        setFormData(data);
      } catch (err) {
        console.error(err);
        setError('Failed to load facility configuration data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFacility();
  }, [params.id]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error for this field if it exists
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSelectChange = (name: string, value: string | null) => {
    if (value === null) return;

    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error for this field if it exists
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleTimeChange = (field: 'open' | 'close', value: string) => {
    setFormData({
      ...formData,
      operatingHours: {
        ...formData.operatingHours,
        [field]: value,
      },
    });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name) errors.name = 'Facility name is required';
    if (!formData.code) errors.code = 'Facility code is required';
    if (!formData.type) errors.type = 'Facility type is required';
    if (!formData.totalCapacity) errors.totalCapacity = 'Total capacity is required';
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      setError('Please correct the errors in the form');
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/facilities/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update facility');
      }

      // Get updated facility data
      const updatedFacility = await response.json();
      setFacility(updatedFacility);
      setSuccess('Facility configuration updated successfully');

      // Clear success message after a delay
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message || 'An error occurred while updating the facility');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    setShowDeleteConfirm(false);
    setIsLoading(true);

    try {
      const response = await fetch(`/api/admin/facilities/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete facility');
      }

      // Navigate back to facilities list after successful deletion
      router.push('/admin/facilities');
    } catch (err: any) {
      setError(err.message || 'An error occurred while deleting the facility');
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!facility && !isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert color="danger">
          Failed to load facility configuration. Please try again or contact support.
        </Alert>
        <Button
          sx={{ mt: 2 }}
          onClick={() => router.push('/admin/facilities')}
          startDecorator={<Icon icon="mdi:arrow-left" />}
        >
          Back to Facilities
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
        >
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Box>
              <Typography level="h4">
                Facility Configuration: {facility?.name}
              </Typography>
              <Typography level="body-sm" color="neutral">
                Manage facility settings and details
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                color="neutral"
                onClick={() => router.push(`/admin/facilities/${params.id}`)}
                startDecorator={<Icon icon="mdi:arrow-left" />}
              >
                Back to Facility
              </Button>
              <Button
                variant="solid"
                color="primary"
                onClick={handleSave}
                loading={isSaving}
                startDecorator={<Icon icon="mdi:content-save" />}
              >
                Save Changes
              </Button>
            </Box>
          </Box>

          {/* Messages */}
          {error && (
            <Alert color="danger" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert color="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {/* Configuration Tabs */}
          <Card>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={activeTab}
                onChange={(_, value) => setActiveTab(value as string)}
                sx={{ bgcolor: 'background.body' }}
              >
                <TabList>
                  <Tab value="general">General</Tab>
                  <Tab value="contact">Contact & Location</Tab>
                  <Tab value="capacity">Capacity</Tab>
                  <Tab value="operations">Operations</Tab>
                  <Tab value="advanced">Advanced</Tab>
                </TabList>
              </Tabs>
            </Box>

            {/* General Tab */}
            <TabPanel value="general" sx={{ p: 3 }}>
              <Grid container spacing={3}>
                <Grid xs={12} md={6}>
                  <FormControl error={!!formErrors.name}>
                    <FormLabel>Facility Name</FormLabel>
                    <Input
                      name="name"
                      value={formData.name || ''}
                      onChange={handleInputChange}
                      placeholder="Enter facility name"
                    />
                    {formErrors.name && <FormHelperText>{formErrors.name}</FormHelperText>}
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl error={!!formErrors.code}>
                    <FormLabel>Facility Code</FormLabel>
                    <Input
                      name="code"
                      value={formData.code || ''}
                      onChange={handleInputChange}
                      placeholder="Enter unique facility code"
                    />
                    {formErrors.code && <FormHelperText>{formErrors.code}</FormHelperText>}
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl error={!!formErrors.type}>
                    <FormLabel>Facility Type</FormLabel>
                    <Select
                      name="type"
                      value={formData.type || ''}
                      onChange={(_, value) => handleSelectChange('type', value)}
                    >
                      {Object.values(FacilityTypeEnum.Values).map((type) => (
                        <Option key={type} value={type}>{type.replace('_', ' ')}</Option>
                      ))}
                    </Select>
                    {formErrors.type && <FormHelperText>{formErrors.type}</FormHelperText>}
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Status</FormLabel>
                    <Select
                      name="status"
                      value={formData.status || 'ACTIVE'}
                      onChange={(_, value) => handleSelectChange('status', value)}
                    >
                      {Object.values(FacilityStatusEnum.Values).map((status) => (
                        <Option key={status} value={status}>
                          <Chip
                            size="sm"
                            variant="soft"
                            color={
                              status === 'ACTIVE' ? 'success' :
                              status === 'INACTIVE' ? 'neutral' :
                              status === 'UNDER_MAINTENANCE' ? 'warning' : 'danger'
                            }
                            sx={{ mr: 1 }}
                          >
                            {status}
                          </Chip>
                        </Option>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Contact & Location Tab */}
            <TabPanel value="contact" sx={{ p: 3 }}>
              <Grid container spacing={3}>
                <Grid xs={12}>
                  <FormControl>
                    <FormLabel>Address</FormLabel>
                    <Textarea
                      name="address"
                      value={formData.address || ''}
                      onChange={handleInputChange}
                      placeholder="Enter facility address"
                      minRows={2}
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={4}>
                  <FormControl>
                    <FormLabel>City</FormLabel>
                    <Input
                      name="city"
                      value={formData.city || ''}
                      onChange={handleInputChange}
                      placeholder="Enter city"
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={4}>
                  <FormControl>
                    <FormLabel>Province/State</FormLabel>
                    <Input
                      name="province"
                      value={formData.province || ''}
                      onChange={handleInputChange}
                      placeholder="Enter province or state"
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={4}>
                  <FormControl>
                    <FormLabel>Postal Code</FormLabel>
                    <Input
                      name="postalCode"
                      value={formData.postalCode || ''}
                      onChange={handleInputChange}
                      placeholder="Enter postal code"
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Phone Number</FormLabel>
                    <Input
                      name="phone"
                      value={formData.phone || ''}
                      onChange={handleInputChange}
                      placeholder="Enter phone number"
                      startDecorator={<Icon icon="mdi:phone" />}
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl error={!!formErrors.email}>
                    <FormLabel>Email</FormLabel>
                    <Input
                      name="email"
                      value={formData.email || ''}
                      onChange={handleInputChange}
                      placeholder="Enter facility email"
                      startDecorator={<Icon icon="mdi:email" />}
                    />
                    {formErrors.email && <FormHelperText>{formErrors.email}</FormHelperText>}
                  </FormControl>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Capacity Tab */}
            <TabPanel value="capacity" sx={{ p: 3 }}>
              <Grid container spacing={3}>
                <Grid xs={12} md={6}>
                  <FormControl error={!!formErrors.totalCapacity}>
                    <FormLabel>Total Capacity</FormLabel>
                    <Input
                      type="number"
                      name="totalCapacity"
                      value={formData.totalCapacity || ''}
                      onChange={handleInputChange}
                      placeholder="Enter total capacity"
                      slotProps={{ input: { min: 1 } }}
                    />
                    {formErrors.totalCapacity && <FormHelperText>{formErrors.totalCapacity}</FormHelperText>}
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Current Occupancy</FormLabel>
                    <Input
                      type="number"
                      name="currentOccupancy"
                      value={formData.currentOccupancy || 0}
                      disabled
                      endDecorator={
                        <Chip
                          size="sm"
                          variant="soft"
                          color={
                            ((formData.currentOccupancy || 0) / (formData.totalCapacity || 1) * 100) > 90 ? 'danger' :
                            ((formData.currentOccupancy || 0) / (formData.totalCapacity || 1) * 100) > 75 ? 'warning' : 'success'
                          }
                        >
                          {((formData.currentOccupancy || 0) / (formData.totalCapacity || 1) * 100).toFixed(0)}%
                        </Chip>
                      }
                    />
                    <FormHelperText>This is managed automatically by the system</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid xs={12}>
                  <Card variant="soft">
                    <CardContent>
                      <Typography level="title-sm">Capacity Management</Typography>
                      <Typography level="body-sm" sx={{ mb: 2 }}>
                        The system will automatically track and update the current occupancy based on admissions and releases.
                        Adjusting the total capacity will affect space availability calculations.
                      </Typography>
                      <Typography level="body-sm">
                        <Icon icon="mdi:information" /> Capacity represents the number of storage spaces available in the facility.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Operations Tab */}
            <TabPanel value="operations" sx={{ p: 3 }}>
              <Grid container spacing={3}>
                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Opening Time</FormLabel>
                    <Input
                      type="time"
                      value={formData.operatingHours?.open || '08:00'}
                      onChange={(e) => handleTimeChange('open', e.target.value)}
                      startDecorator={<Icon icon="mdi:clock-time-eight-outline" />}
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Closing Time</FormLabel>
                    <Input
                      type="time"
                      value={formData.operatingHours?.close || '17:00'}
                      onChange={(e) => handleTimeChange('close', e.target.value)}
                      startDecorator={<Icon icon="mdi:clock-time-eight-outline" />}
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Emergency Contact</FormLabel>
                    <Input
                      name="emergencyContact"
                      value={formData.emergencyContact || ''}
                      onChange={handleInputChange}
                      placeholder="Enter emergency contact information"
                      startDecorator={<Icon icon="mdi:phone-alert" />}
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>License Expiry Date</FormLabel>
                    <Input
                      type="date"
                      name="licenseExpiry"
                      value={formData.licenseExpiry ? format(new Date(formData.licenseExpiry), 'yyyy-MM-dd') : ''}
                      onChange={handleInputChange}
                      startDecorator={<Icon icon="mdi:license" />}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Advanced Tab */}
            <TabPanel value="advanced" sx={{ p: 3 }}>
              <Typography level="title-sm" sx={{ mb: 2 }}>Advanced Configuration</Typography>

              {/* Referral Timer Settings */}
              <Grid container spacing={3}>
                <Grid xs={12}>
                  <ReferralTimerSettings
                    facilityId={params.id}
                    initialBehavior={formData.referralTimerBehavior || ReferralTimerBehavior.CONTINUE}
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              <Typography level="title-sm" color="danger" sx={{ mt: 3, mb: 1 }}>Danger Zone</Typography>
              <Card variant="outlined" color="danger" sx={{ mb: 2 }}>
                <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography level="title-sm">Delete Facility</Typography>
                    <Typography level="body-sm">
                      Permanently delete this facility and all its configuration data. This action cannot be undone.
                    </Typography>
                  </Box>
                  <Button
                    color="danger"
                    variant="solid"
                    onClick={() => setShowDeleteConfirm(true)}
                    startDecorator={<Icon icon="mdi:delete" />}
                  >
                    Delete
                  </Button>
                </CardContent>
              </Card>

              <Card variant="outlined" color="warning">
                <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography level="title-sm">Archive Facility</Typography>
                    <Typography level="body-sm">
                      Archive this facility to make it inactive but preserve all data.
                    </Typography>
                  </Box>
                  <Button
                    color="warning"
                    variant="solid"
                    onClick={() => handleSelectChange('status', 'INACTIVE')}
                    startDecorator={<Icon icon="mdi:archive" />}
                  >
                    Archive
                  </Button>
                </CardContent>
              </Card>
            </TabPanel>
          </Card>

          {/* Delete Confirmation Modal */}
          <Modal open={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
            <ModalDialog color="danger" variant="outlined" size="md">
              <ModalClose />
              <Typography level="h4">Delete Facility?</Typography>
              <Divider sx={{ my: 2 }} />
              <Typography level="body-md">
                Are you sure you want to delete the facility <strong>{facility?.name}</strong>? This action cannot be undone.
              </Typography>
              <Typography level="body-sm" color="danger" sx={{ mt: 2 }}>
                Warning: This will permanently delete all facility data including equipment records, staff assignments, and configuration.
              </Typography>

              <Box sx={{ mt: 3, display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Button
                  variant="plain"
                  color="neutral"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="solid"
                  color="danger"
                  startDecorator={<Icon icon="mdi:delete" />}
                  onClick={handleDelete}
                >
                  Delete Permanently
                </Button>
              </Box>
            </ModalDialog>
          </Modal>
        </motion.div>
      </AnimatePresence>
    </Box>
  );
}