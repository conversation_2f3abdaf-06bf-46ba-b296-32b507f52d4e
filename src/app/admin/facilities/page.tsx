'use client';

import { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Stack,
  IconButton,
  CircularProgress,
  Alert
} from '@mui/joy';
import { Icon } from '@iconify/react';
import dynamicImport from 'next/dynamic';

// Force dynamic rendering
export const dynamic = "force-dynamic";
import { Suspense } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

// Dynamically import FacilityList with no SSR
const FacilityList = dynamicImport(
  () => import('@/components/Facilities/FacilityList'),
  {
    ssr: false,
    loading: () => (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    )
  }
);

// Dynamically import FacilityImport component
const FacilityImport = dynamicImport(
  () => import('@/components/Facilities/FacilityImport'),
  {
    ssr: false
  }
);

export default function FacilitiesPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const router = useRouter();

  const handleError = (message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000);
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // Add refresh logic here
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      handleError('Failed to refresh facilities');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Stack spacing={2}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography level="h4">Facilities</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton
              variant="soft"
              color="neutral"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <Icon icon={isLoading ? "mdi:loading" : "mdi:refresh"} />
            </IconButton>
            <Button
              variant="outlined"
              startDecorator={<Icon icon="mdi:file-import" />}
              onClick={() => setIsImportModalOpen(true)}
            >
              Import
            </Button>
            <Link href="/admin/facilities/add">
              <Button
                startDecorator={<Icon icon="mdi:domain-plus" />}
                onClick={() => router.push("/admin/facilities/add")}
              >
                Add Facility
              </Button>
            </Link>
          </Box>
        </Stack>

        {error && (
          <Alert color="danger" variant="soft">
            {error}
          </Alert>
        )}

        <Suspense fallback={
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        }>
          <FacilityList onError={handleError} />
        </Suspense>
      </Stack>

      <FacilityImport
        open={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onSuccess={handleRefresh}
      />
    </Box>
  );
}