'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Option,
  Grid,
  Stack,
  Divider,
  Alert,
  IconButton,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import { FacilityTypeEnum, FacilityStatusEnum } from '@/lib/schema/facilitySchema';
import Link from 'next/link';

export default function AddFacilityPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [facility, setFacility] = useState({
    name: '',
    code: '',
    type: 'STORAGE',
    status: 'ACTIVE',
    address: '',
    city: '',
    province: '',
    postalCode: '',
    phone: '',
    email: '',
    totalCapacity: 100,
    operatingHours: {
      open: '08:00',
      close: '17:00',
    },
    emergencyContact: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFacility(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (name: string, value: string | null) => {
    if (value) {
      setFacility(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleTimeChange = (field: 'open' | 'close', value: string) => {
    setFacility(prev => ({
      ...prev,
      operatingHours: {
        ...prev.operatingHours,
        [field]: value,
      },
    }));
  };

  const handleNumberChange = (name: string, value: string) => {
    const numValue = parseInt(value);
    if (!isNaN(numValue)) {
      setFacility(prev => ({
        ...prev,
        [name]: numValue,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/facilities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(facility),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create facility');
      }

      setSuccess(true);
      // Navigate to the new facility page after a brief delay
      setTimeout(() => {
        router.push(`/admin/facilities/${data.id}`);
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating the facility');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ p: 3, overflow: 'auto', height: '100%' }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Stack spacing={3}>
          {/* Header */}
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Stack direction="row" spacing={2} alignItems="center">
              <Link href="/admin/facilities">
                <IconButton
                  variant="outlined"
                  color="neutral"
                  size="sm"
                  onClick={() => router.push('/admin/facilities')}
              >
                  <Icon icon="mdi:arrow-left" />
                </IconButton>
              </Link>
              <Typography level="h4">Add New Facility</Typography>
            </Stack>
          </Stack>

          {/* Status Messages */}
          {error && (
            <Alert color="danger" variant="soft">
              {error}
            </Alert>
          )}
          {success && (
            <Alert color="success" variant="soft">
              Facility created successfully! Redirecting...
            </Alert>
          )}

          {/* Form */}
          <Card>
            <CardContent>
              <form onSubmit={handleSubmit}>
                <Grid container spacing={2}>
                  <Grid xs={12}>
                    <Typography level="title-md">Basic Information</Typography>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Facility Name</FormLabel>
                      <Input
                        name="name"
                        value={facility.name}
                        onChange={handleInputChange}
                        placeholder="Enter facility name"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Facility Code</FormLabel>
                      <Input
                        name="code"
                        value={facility.code}
                        onChange={handleInputChange}
                        placeholder="Enter facility code"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Type</FormLabel>
                      <Select
                        value={facility.type}
                        onChange={(_, value) => handleSelectChange('type', value)}
                      >
                        {Object.values(FacilityTypeEnum.Values).map((type) => (
                          <Option key={type} value={type}>
                            {type.replace(/_/g, ' ')}
                          </Option>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Status</FormLabel>
                      <Select
                        value={facility.status}
                        onChange={(_, value) => handleSelectChange('status', value)}
                      >
                        {Object.values(FacilityStatusEnum.Values).map((status) => (
                          <Option key={status} value={status}>
                            {status.replace(/_/g, ' ')}
                          </Option>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid xs={12}>
                    <Typography level="title-md" sx={{ mt: 2 }}>
                      Contact and Location
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid xs={12}>
                    <FormControl>
                      <FormLabel>Address</FormLabel>
                      <Input
                        name="address"
                        value={facility.address}
                        onChange={handleInputChange}
                        placeholder="Enter facility address"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={4}>
                    <FormControl>
                      <FormLabel>City</FormLabel>
                      <Input
                        name="city"
                        value={facility.city}
                        onChange={handleInputChange}
                        placeholder="Enter city"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={4}>
                    <FormControl>
                      <FormLabel>Province/State</FormLabel>
                      <Input
                        name="province"
                        value={facility.province}
                        onChange={handleInputChange}
                        placeholder="Enter province/state"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={4}>
                    <FormControl>
                      <FormLabel>Postal Code</FormLabel>
                      <Input
                        name="postalCode"
                        value={facility.postalCode}
                        onChange={handleInputChange}
                        placeholder="Enter postal code"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Phone</FormLabel>
                      <Input
                        name="phone"
                        value={facility.phone}
                        onChange={handleInputChange}
                        placeholder="Enter phone number"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Email</FormLabel>
                      <Input
                        type="email"
                        name="email"
                        value={facility.email}
                        onChange={handleInputChange}
                        placeholder="Enter email address"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12}>
                    <Typography level="title-md" sx={{ mt: 2 }}>
                      Capacity & Operations
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl required>
                      <FormLabel>Total Capacity</FormLabel>
                      <Input
                        type="number"
                        value={facility.totalCapacity}
                        onChange={(e) => handleNumberChange('totalCapacity', e.target.value)}
                        slotProps={{ input: { min: 1 } }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Emergency Contact</FormLabel>
                      <Input
                        name="emergencyContact"
                        value={facility.emergencyContact}
                        onChange={handleInputChange}
                        placeholder="Emergency contact name/number"
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Opening Time</FormLabel>
                      <Input
                        type="time"
                        value={facility.operatingHours.open}
                        onChange={(e) => handleTimeChange('open', e.target.value)}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sm={6}>
                    <FormControl>
                      <FormLabel>Closing Time</FormLabel>
                      <Input
                        type="time"
                        value={facility.operatingHours.close}
                        onChange={(e) => handleTimeChange('close', e.target.value)}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} sx={{ mt: 2 }}>
                    <Stack direction="row" spacing={2} justifyContent="flex-end">
                      <Button
                        variant="outlined"
                        color="neutral"
                        onClick={() => router.push('/admin/facilities')}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        loading={isSubmitting}
                        disabled={success}
                        startDecorator={<Icon icon="mdi:check" />}
                      >
                        Create Facility
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </CardContent>
          </Card>
        </Stack>
      </motion.div>
    </Box>
  );
}