import { auth } from '@/auth';
import { redirect } from 'next/navigation';
import { prismaClient } from '@/lib/prismaClient';
import QueryLogsTable from '@/components/admin/QueryLogsTable';

export default async function QueryLogsPage() {
  const session = await auth();
  
  if (!session?.user || session.user.role !== 'ADMIN') {
    redirect('/');
  }

  const logs = await prismaClient.prismaQueryLog.findMany({
    orderBy: { createdAt: 'desc' },
    take: 100,
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Query Logs</h1>
      <QueryLogsTable logs={logs} />
    </div>
  );
}
