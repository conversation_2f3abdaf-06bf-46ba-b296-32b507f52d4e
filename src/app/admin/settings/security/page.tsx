"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import {
  Box,
  Typo<PERSON>,
  Card,
  FormControl,
  FormLabel,
  Input,
  Switch,
  Button,
  Stack,
  Select,
  Option,
  List,
  ListItem,
  ListItemContent,
  IconButton,
} from "@mui/joy";
import { Icon } from "@iconify/react";

export default function SecuritySettingsPage() {
  return (
    <Box sx={{ p: 4 }}>
      <Typography level="h1" sx={{ mb: 4 }}>
        Security Settings
      </Typography>

      <Stack spacing={3}>
        <Card variant="outlined" sx={{ p: 4 }}>
          <Typography level="title-lg" sx={{ mb: 3 }}>
            Authentication
          </Typography>
          <Stack spacing={3}>
            <FormControl>
              <FormLabel>Minimum Password Length</FormLabel>
              <Input type="number" defaultValue={12} />
            </FormControl>

            <FormControl>
              <FormLabel>Password Expiry (days)</FormLabel>
              <Input type="number" defaultValue={90} />
            </FormControl>

            <FormControl>
              <FormLabel>Authentication Method</FormLabel>
              <Select defaultValue="2fa">
                <Option value="password">Password Only</Option>
                <Option value="2fa">Two-Factor Authentication</Option>
                <Option value="sso">Single Sign-On</Option>
              </Select>
            </FormControl>

            <FormControl orientation="horizontal" sx={{ gap: 2 }}>
              <Box>
                <FormLabel>Force Password Change</FormLabel>
                <Typography level="body-sm">
                  Require users to change password on next login
                </Typography>
              </Box>
              <Switch />
            </FormControl>
          </Stack>
        </Card>

        <Card variant="outlined" sx={{ p: 4 }}>
          <Typography level="title-lg" sx={{ mb: 3 }}>
            Session Management
          </Typography>
          <Stack spacing={3}>
            <FormControl>
              <FormLabel>Session Timeout (minutes)</FormLabel>
              <Input type="number" defaultValue={30} />
            </FormControl>

            <FormControl orientation="horizontal" sx={{ gap: 2 }}>
              <Box>
                <FormLabel>Single Session</FormLabel>
                <Typography level="body-sm">
                  Allow only one active session per user
                </Typography>
              </Box>
              <Switch defaultChecked />
            </FormControl>

            <FormControl orientation="horizontal" sx={{ gap: 2 }}>
              <Box>
                <FormLabel>Remember Me</FormLabel>
                <Typography level="body-sm">
                  Allow users to stay signed in
                </Typography>
              </Box>
              <Switch defaultChecked />
            </FormControl>
          </Stack>
        </Card>

        <Card variant="outlined" sx={{ p: 4 }}>
          <Typography level="title-lg" sx={{ mb: 3 }}>
            IP Whitelist
          </Typography>
          <List>
            <ListItem>
              <ListItemContent>***********</ListItemContent>
              <IconButton variant="plain" color="danger" size="sm">
                <Icon icon="mdi:delete" />
              </IconButton>
            </ListItem>
            <ListItem>
              <ListItemContent>10.0.0.0/24</ListItemContent>
              <IconButton variant="plain" color="danger" size="sm">
                <Icon icon="mdi:delete" />
              </IconButton>
            </ListItem>
          </List>
          <Button
            variant="outlined"
            startDecorator={<Icon icon="mdi:plus" />}
            sx={{ mt: 2 }}
          >
            Add IP Address
          </Button>
        </Card>

        <Card variant="outlined" sx={{ p: 4 }}>
          <Typography level="title-lg" sx={{ mb: 3 }}>
            Security Alerts
          </Typography>
          <Stack spacing={2}>
            <FormControl orientation="horizontal" sx={{ gap: 2 }}>
              <Box>
                <FormLabel>Failed Login Attempts</FormLabel>
                <Typography level="body-sm">
                  Notify admins of suspicious login attempts
                </Typography>
              </Box>
              <Switch defaultChecked />
            </FormControl>

            <FormControl orientation="horizontal" sx={{ gap: 2 }}>
              <Box>
                <FormLabel>New Device Login</FormLabel>
                <Typography level="body-sm">
                  Alert when user logs in from a new device
                </Typography>
              </Box>
              <Switch defaultChecked />
            </FormControl>

            <FormControl orientation="horizontal" sx={{ gap: 2 }}>
              <Box>
                <FormLabel>Password Changes</FormLabel>
                <Typography level="body-sm">
                  Notify user when password is changed
                </Typography>
              </Box>
              <Switch defaultChecked />
            </FormControl>
          </Stack>
        </Card>

        <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
          <Button variant="outlined" color="neutral">
            Cancel
          </Button>
          <Button>Save Changes</Button>
        </Box>
      </Stack>
    </Box>
  );
} 