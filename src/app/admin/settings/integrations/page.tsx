"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  Button,
  Stack,
  Grid,
  IconButton,
  Chip,
  Modal,
  ModalDialog,
  FormControl,
  FormLabel,
  Input,
  Textarea,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import { useState } from "react";

const integrations = [
  {
    id: 1,
    name: "Email Service",
    icon: "mdi:email",
    status: "Connected",
    description: "SMTP email service integration for notifications",
  },
  {
    id: 2,
    name: "Storage Service",
    icon: "mdi:cloud",
    status: "Not Connected",
    description: "Cloud storage for documents and files",
  },
  {
    id: 3,
    name: "Authentication",
    icon: "mdi:shield-account",
    status: "Connected",
    description: "Single sign-on provider integration",
  },
  {
    id: 4,
    name: "Analytics",
    icon: "mdi:chart-bar",
    status: "Not Connected",
    description: "Usage analytics and reporting",
  },
  {
    id: 5,
    name: "Payment Gateway",
    icon: "mdi:credit-card",
    status: "Connected",
    description: "Payment processing integration",
  },
  {
    id: 6,
    name: "Calendar",
    icon: "mdi:calendar",
    status: "Not Connected",
    description: "Calendar sync for appointments",
  },
];

const IntegrationCard = ({ integration }) => (
  <Card variant="outlined" sx={{ p: 3, height: "100%" }}>
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "flex-start",
        mb: 2,
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <Icon icon={integration.icon} width={24} height={24} />
        <Typography level="title-lg">{integration.name}</Typography>
      </Box>
      <Chip
        size="sm"
        variant="soft"
        color={integration.status === "Connected" ? "success" : "neutral"}
      >
        {integration.status}
      </Chip>
    </Box>
    <Typography level="body-sm" sx={{ mb: 2 }}>
      {integration.description}
    </Typography>
    <Box sx={{ display: "flex", gap: 1 }}>
      <Button
        variant="outlined"
        color={integration.status === "Connected" ? "danger" : "primary"}
        size="sm"
      >
        {integration.status === "Connected" ? "Disconnect" : "Connect"}
      </Button>
      <IconButton variant="plain" size="sm">
        <Icon icon="mdi:cog" />
      </IconButton>
    </Box>
  </Card>
);

export default function IntegrationsPage() {
  const [open, setOpen] = useState(false);

  return (
    <Box sx={{ p: 4 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 4,
        }}
      >
        <Typography level="h1">Integrations</Typography>
        <Button
          startDecorator={<Icon icon="mdi:plus" />}
          onClick={() => setOpen(true)}
        >
          Add Integration
        </Button>
      </Box>

      <Grid container spacing={3}>
        {integrations.map((integration) => (
          <Grid key={integration.id} xs={12} md={6} lg={4}>
            <IntegrationCard integration={integration} />
          </Grid>
        ))}
      </Grid>

      <Modal open={open} onClose={() => setOpen(false)}>
        <ModalDialog
          aria-labelledby="new-integration-dialog-title"
          sx={{ maxWidth: 500 }}
        >
          <Typography id="new-integration-dialog-title" level="h2">
            Add New Integration
          </Typography>
          <Stack spacing={3} sx={{ mt: 2 }}>
            <FormControl>
              <FormLabel>Integration Name</FormLabel>
              <Input autoFocus required />
            </FormControl>
            <FormControl>
              <FormLabel>API Key</FormLabel>
              <Input type="password" required />
            </FormControl>
            <FormControl>
              <FormLabel>Webhook URL</FormLabel>
              <Input />
            </FormControl>
            <FormControl>
              <FormLabel>Description</FormLabel>
              <Textarea minRows={3} />
            </FormControl>
            <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                color="neutral"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={() => setOpen(false)}>Add Integration</Button>
            </Box>
          </Stack>
        </ModalDialog>
      </Modal>
    </Box>
  );
} 