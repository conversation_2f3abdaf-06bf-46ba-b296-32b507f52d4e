"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import {
  Box,
  Typography,
  Card,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Switch,
  <PERSON>ton,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
} from "@mui/joy";

export default function GeneralSettingsPage() {
  return (
    <Box sx={{ p: 4 }}>
      <Typography level="h1" sx={{ mb: 4 }}>
        General Settings
      </Typography>

      <Card variant="outlined" sx={{ p: 4 }}>
        <Stack spacing={4}>
          <Box>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              Organization Details
            </Typography>
            <Stack spacing={2}>
              <FormControl>
                <FormLabel>Organization Name</FormLabel>
                <Input defaultValue="GP Pathology" />
              </FormControl>
              <FormControl>
                <FormLabel>Contact Email</FormLabel>
                <Input type="email" defaultValue="<EMAIL>" />
              </FormControl>
              <FormControl>
                <FormLabel>Description</FormLabel>
                <Textarea
                  minRows={3}
                  defaultValue="Leading pathology services provider"
                />
              </FormControl>
            </Stack>
          </Box>

          <Divider />

          <Box>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              System Preferences
            </Typography>
            <Stack spacing={2}>
              <FormControl orientation="horizontal" sx={{ gap: 2 }}>
                <Box>
                  <FormLabel>Enable Notifications</FormLabel>
                  <Typography level="body-sm">
                    Receive system notifications and alerts
                  </Typography>
                </Box>
                <Switch defaultChecked />
              </FormControl>

              <FormControl orientation="horizontal" sx={{ gap: 2 }}>
                <Box>
                  <FormLabel>Maintenance Mode</FormLabel>
                  <Typography level="body-sm">
                    Put system in maintenance mode
                  </Typography>
                </Box>
                <Switch />
              </FormControl>

              <FormControl orientation="horizontal" sx={{ gap: 2 }}>
                <Box>
                  <FormLabel>Debug Mode</FormLabel>
                  <Typography level="body-sm">
                    Enable detailed error logging
                  </Typography>
                </Box>
                <Switch />
              </FormControl>
            </Stack>
          </Box>

          <Divider />

          <Box>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              Default Settings
            </Typography>
            <Stack spacing={2}>
              <FormControl>
                <FormLabel>Default Time Zone</FormLabel>
                <Input defaultValue="UTC+00:00" />
              </FormControl>
              <FormControl>
                <FormLabel>Date Format</FormLabel>
                <Input defaultValue="YYYY-MM-DD" />
              </FormControl>
              <FormControl>
                <FormLabel>Language</FormLabel>
                <Input defaultValue="English" />
              </FormControl>
            </Stack>
          </Box>

          <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
            <Button variant="outlined" color="neutral">
              Cancel
            </Button>
            <Button>Save Changes</Button>
          </Box>
        </Stack>
      </Card>
    </Box>
  );
} 