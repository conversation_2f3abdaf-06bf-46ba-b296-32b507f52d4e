"use client";

import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/joy';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';

const Loading: React.FC = () => {
  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100vh",
        flexDirection: "column",
        gap: "2rem",
        background: (theme) => theme.palette.background.body
      }}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ 
          opacity: 1,
          scale: 1,
          transition: { 
            duration: 0.5,
            ease: "easeOut"
          }
        }}
      >
        <Box sx={{ position: 'relative', width: 120, height: 120 }}>
          <CircularProgress
            size="lg"
            variant="soft"
            thickness={3}
            color="primary"
            sx={{ 
              '--CircularProgress-size': '120px',
              '--CircularProgress-trackThickness': '6px',
              '--CircularProgress-progressThickness': '6px'
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)'
            }}
          >
            <motion.div
              animate={{ 
                rotate: 360,
                transition: { 
                  duration: 2,
                  repeat: Infinity,
                  ease: "linear"
                }
              }}
            >
              <Icon 
                icon="mdi:hospital-building" 
                width={40} 
                height={40}
                style={{ color: '#FAAD01' }}
              />
            </motion.div>
          </Box>
        </Box>
      </motion.div>
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ 
          opacity: 1, 
          y: 0,
          transition: { 
            duration: 0.5,
            delay: 0.3
          }
        }}
      >
        <Typography 
          level="h4" 
          sx={{ 
            color: (theme) => theme.palette.text.primary,
            fontWeight: "bold"
          }}
        >
          Loading Admin Dashboard
        </Typography>
      </motion.div>
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ 
          opacity: [0, 1, 0],
          transition: { 
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }
        }}
      >
        <Typography 
          level="body-md" 
          sx={{ 
            color: (theme) => theme.palette.text.secondary
          }}
        >
          Please wait...
        </Typography>
      </motion.div>
    </Box>
  );
};

export default Loading;
