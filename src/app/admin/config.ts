/**
 * Admin route configuration for Next.js
 * 
 * This file contains exports that ensure all admin routes are dynamically rendered.
 */

// Force dynamic rendering for all admin routes
export const dynamic = 'force-dynamic';

// Set runtime to nodejs for all admin routes
export const runtime = 'nodejs';

// Disable caching for admin routes
export const fetchCache = 'force-no-store';

// Set revalidation to 0 to ensure data is always fresh
export const revalidate = 0;
