import * as React from "react";
import {
  Modal,
  ModalDialog,
  Typography,
  Box,
  Table,
  Sheet,
  CircularProgress,
} from "@mui/joy";
import { Fridge } from "../types";
import { getFridgeHistory } from "../services/fridgeService";

interface HistoryEntry {
  timestamp: string;
  temperature: number;
  status: Fridge["status"];
  occupied: number;
}

interface FridgeHistoryModalProps {
  open: boolean;
  onClose: () => void;
  fridge: Fridge;
}

export default function FridgeHistoryModal({
  open,
  onClose,
  fridge,
}: FridgeHistoryModalProps) {
  const [history, setHistory] = React.useState<HistoryEntry[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (open && fridge.id) {
      setLoading(true);
      setError(null);
      getFridgeHistory(fridge.id)
        .then((data) => {
          setHistory(data);
        })
        .catch((err) => {
          setError(err.message);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [open, fridge.id]);

  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog
        aria-labelledby="history-modal-title"
        aria-describedby="history-modal-description"
        sx={{ width: 600, maxWidth: "100%" }}
      >
        <Typography id="history-modal-title" level="h2" mb={2}>
          {fridge.name} - History
        </Typography>
        <Sheet sx={{ maxHeight: "60vh", overflow: "auto" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Typography color="danger" sx={{ p: 2 }}>
              {error}
            </Typography>
          ) : (
            <Table>
              <thead>
                <tr>
                  <th>Timestamp</th>
                  <th>Temperature</th>
                  <th>Status</th>
                  <th>Occupied</th>
                </tr>
              </thead>
              <tbody>
                {history.map((entry, index) => (
                  <tr key={index}>
                    <td>{new Date(entry.timestamp).toLocaleString()}</td>
                    <td>{entry.temperature.toFixed(1)}°C</td>
                    <td style={{ textTransform: "capitalize" }}>{entry.status}</td>
                    <td>{entry.occupied}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Sheet>
      </ModalDialog>
    </Modal>
  );
} 