import * as React from 'react';
import {
  Table,
  Sheet,
  IconButton,
  Tooltip,
  Box,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { FridgeAssignment } from '../types';

interface FridgeAssignmentsProps {
  assignments: FridgeAssignment[];
  onRemoveAssignment?: (assignmentId: string) => void;
  onViewBody?: (bodyId: string) => void;
}

export default function FridgeAssignments({
  assignments,
  onRemoveAssignment,
  onViewBody,
}: FridgeAssignmentsProps) {
  return (
    <Sheet sx={{ maxHeight: '400px', overflow: 'auto' }}>
      <Table>
        <thead>
          <tr>
            <th>Body ID</th>
            <th>Admission Date</th>
            <th>Temperature</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {assignments.map((assignment) => (
            <tr key={assignment.id}>
              <td>{assignment.bodyAdmission?.bodyId}</td>
              <td>
                {new Date(assignment.bodyAdmission?.admissionDate || '').toLocaleDateString()}
              </td>
              <td>{assignment.temperature}°C</td>
              <td>{assignment.status}</td>
              <td>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title="View Body Details">
                    <IconButton
                      variant="plain"
                      color="neutral"
                      onClick={() => onViewBody?.(assignment.bodyAdmission?.bodyId || '')}
                    >
                      <Icon icon="mdi:eye" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Remove Assignment">
                    <IconButton
                      variant="plain"
                      color="danger"
                      onClick={() => onRemoveAssignment?.(assignment.id)}
                    >
                      <Icon icon="mdi:close" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
    </Sheet>
  );
} 