import * as React from "react";
import {
  <PERSON><PERSON>,
  ModalDialog,
  <PERSON>po<PERSON>,
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Option,
} from "@mui/joy";
import { Fridge, FridgeStatus } from "../types";
import { assignBodyToFridge } from '../services/fridgeAssignmentService';
import FridgeAssignments from './FridgeAssignments';
import { Icon } from '@iconify/react';

interface FridgeModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (fridge: Partial<Fridge>) => void;
  initialData?: Fridge;
  mode: "add" | "edit";
  showAssignments?: boolean;
}

export default function FridgeModal({
  open,
  onClose,
  onSubmit,
  initialData,
  mode,
  showAssignments,
}: FridgeModalProps) {
  const [formData, setFormData] = React.useState<Partial<Fridge>>(
    initialData || {
      fridgeNumber: "",
      barcode: "",
      temperature: -2,
      status: "AVAILABLE" as FridgeStatus,
      capacity: 10,
      currentOccupancy: 0,
    }
  );

  const [showAssignmentForm, setShowAssignmentForm] = React.useState(false);
  const [selectedBodyAdmission, setSelectedBodyAdmission] = React.useState('');
  const [assignmentTemp, setAssignmentTemp] = React.useState('');
  const [assignmentNotes, setAssignmentNotes] = React.useState('');

  React.useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleAssignment = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await assignBodyToFridge(
        formData.id!,
        selectedBodyAdmission,
        parseFloat(assignmentTemp),
        assignmentNotes
      );
      setShowAssignmentForm(false);
      // Refresh fridge data
    } catch (error) {
      console.error('Failed to assign body:', error);
    }
  };

  const handleRemoveAssignment = async (assignmentId: string) => {
    try {
      await fetch(`/api/fridge/assign/${assignmentId}`, {
        method: 'DELETE',
      });
      // Refresh fridge data after removal
      // You might want to call a refresh function passed as prop
    } catch (error) {
      console.error('Failed to remove assignment:', error);
    }
  };

  const handleViewBody = (bodyId: string) => {
    // Implement navigation to body details page
    window.location.href = `/dashboard/bodies/${bodyId}`;
  };

  // Add assignment form JSX
  const assignmentFormJsx = (
    <Box sx={{ mt: 3 }}>
      <Typography level="h3" mb={2}>
        New Assignment
      </Typography>
      <form onSubmit={handleAssignment}>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <FormControl required>
            <FormLabel>Body Admission</FormLabel>
            <Select
              value={selectedBodyAdmission}
              onChange={(_, value) => setSelectedBodyAdmission(value || '')}
            >
              {/* You'll need to fetch and map available body admissions */}
              <Option value="example">Example Body</Option>
            </Select>
          </FormControl>

          <FormControl required>
            <FormLabel>Temperature (°C)</FormLabel>
            <Input
              type="number"
              value={assignmentTemp}
              onChange={(e) => setAssignmentTemp(e.target.value)}
              slotProps={{
                input: {
                  step: "0.1",
                },
              }}
            />
          </FormControl>

          <FormControl>
            <FormLabel>Notes</FormLabel>
            <Input
              multiline
              minRows={2}
              value={assignmentNotes}
              onChange={(e) => setAssignmentNotes(e.target.value)}
            />
          </FormControl>

          <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
            <Button
              variant="plain"
              color="neutral"
              onClick={() => setShowAssignmentForm(false)}
            >
              Cancel
            </Button>
            <Button type="submit">Assign Body</Button>
          </Box>
        </Box>
      </form>
    </Box>
  );

  // Update the main form to match new types
  return (
    <Modal open={open} onClose={onClose}>
      <ModalDialog
        aria-labelledby="fridge-modal-title"
        sx={{ width: showAssignments ? 600 : 400, maxWidth: "100%" }}
      >
        <Typography id="fridge-modal-title" level="h2" mb={2}>
          {mode === "add" ? "Add New Fridge" : "Edit Fridge"}
        </Typography>
        <form onSubmit={handleSubmit}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <FormControl required>
              <FormLabel>Fridge Number</FormLabel>
              <Input
                value={formData.fridgeNumber}
                onChange={(e) =>
                  setFormData({ ...formData, fridgeNumber: e.target.value })
                }
              />
            </FormControl>

            <FormControl required>
              <FormLabel>Barcode</FormLabel>
              <Input
                value={formData.barcode}
                onChange={(e) =>
                  setFormData({ ...formData, barcode: e.target.value })
                }
              />
            </FormControl>

            <FormControl required>
              <FormLabel>Temperature (°C)</FormLabel>
              <Input
                type="number"
                value={formData.temperature}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    temperature: parseFloat(e.target.value),
                  })
                }
                slotProps={{
                  input: {
                    step: "0.1",
                  },
                }}
              />
            </FormControl>

            <FormControl required>
              <FormLabel>Status</FormLabel>
              <Select
                value={formData.status}
                onChange={(_, value) =>
                  setFormData({ ...formData, status: value as FridgeStatus })
                }
              >
                <Option value="AVAILABLE">Available</Option>
                <Option value="OCCUPIED">Occupied</Option>
                <Option value="MAINTENANCE">Maintenance</Option>
                <Option value="OFFLINE">Offline</Option>
              </Select>
            </FormControl>

            <FormControl required>
              <FormLabel>Capacity</FormLabel>
              <Input
                type="number"
                value={formData.capacity}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    capacity: parseInt(e.target.value),
                  })
                }
              />
            </FormControl>

            <FormControl required>
              <FormLabel>Current Occupancy</FormLabel>
              <Input
                type="number"
                value={formData.currentOccupancy}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    currentOccupancy: parseInt(e.target.value),
                  })
                }
              />
            </FormControl>

            <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end", mt: 2 }}>
              <Button variant="plain" color="neutral" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">
                {mode === "add" ? "Add Fridge" : "Save Changes"}
              </Button>
            </Box>
          </Box>
        </form>

        {showAssignments && (
          <>
            <Typography level="h3" mt={3} mb={2}>
              Current Assignments
            </Typography>
            <FridgeAssignments
              assignments={formData.assignments || []}
              onRemoveAssignment={handleRemoveAssignment}
              onViewBody={handleViewBody}
            />
            {!showAssignmentForm && (
              <Button
                startDecorator={<Icon icon="mdi:plus" />}
                onClick={() => setShowAssignmentForm(true)}
                sx={{ mt: 2 }}
              >
                New Assignment
              </Button>
            )}
          </>
        )}

        {showAssignmentForm && assignmentFormJsx}
      </ModalDialog>
    </Modal>
  );
} 