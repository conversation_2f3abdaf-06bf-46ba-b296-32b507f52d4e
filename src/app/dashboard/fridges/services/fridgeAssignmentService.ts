import { FridgeAssignment } from '../types';

export async function assignBodyToFridge(
  fridgeId: string,
  bodyAdmissionId: string,
  temperature?: number,
  notes?: string
): Promise<FridgeAssignment> {
  const response = await fetch('/api/fridge/assign', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      fridgeId,
      bodyAdmissionId,
      temperature,
      notes,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to assign body to fridge');
  }

  return response.json();
}

export async function updateFridgeAssignment(
  assignmentId: string,
  data: Partial<FridgeAssignment>
): Promise<FridgeAssignment> {
  const response = await fetch(`/api/fridge/assign/${assignmentId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error('Failed to update fridge assignment');
  }

  return response.json();
} 