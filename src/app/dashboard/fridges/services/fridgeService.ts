import { Fridge } from "../types";

export async function getFridges(): Promise<Fridge[]> {
  const response = await fetch('/api/fridge');
  if (!response.ok) {
    throw new Error('Failed to fetch fridges');
  }
  return response.json();
}

export async function getFridge(id: string): Promise<Fridge> {
  const response = await fetch(`/api/fridge/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch fridge');
  }
  return response.json();
}

export async function updateFridge(id: string, data: Partial<Fridge>): Promise<Fridge> {
  const response = await fetch(`/api/fridge/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to update fridge');
  }
  return response.json();
}

export async function createFridge(data: Partial<Fridge>): Promise<Fridge> {
  const response = await fetch('/api/fridge', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to create fridge');
  }
  return response.json();
}

export async function getFridgeHistory(id: string): Promise<any[]> {
  const response = await fetch(`/api/fridge/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch fridge history');
  }
  const data = await response.json();
  return data.temperatureLogs || [];
} 