"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import * as React from "react";
import {
  Box,
  Typo<PERSON>,
  Card,
  Grid,
  IconButton,
  Tooltip,
  Button,
  CircularProgress,
  Alert,
  Divider,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import { Fridge } from "./types";
import FridgeModal from "./components/FridgeModal";
import FridgeHistoryModal from "./components/FridgeHistoryModal";
import { getFridges, createFridge, updateFridge } from "./services/fridgeService";

export default function FridgesPage() {
  const [fridges, setFridges] = React.useState<Fridge[]>([]);
  const [selectedFridge, setSelectedFridge] = React.useState<Fridge | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = React.useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = React.useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  // Fetch fridges on component mount
  React.useEffect(() => {
    loadFridges();
  }, []);

  const loadFridges = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getFridges();
      setFridges(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load fridges');
    } finally {
      setLoading(false);
    }
  };

  const handleAddFridge = async (newFridge: Partial<Fridge>) => {
    try {
      setError(null);
      const createdFridge = await createFridge(newFridge);
      setFridges([...fridges, createdFridge]);
      setIsAddModalOpen(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create fridge');
    }
  };

  const handleEditFridge = async (updatedFridge: Partial<Fridge>) => {
    if (!selectedFridge) return;
    
    try {
      setError(null);
      const updated = await updateFridge(selectedFridge.id, updatedFridge);
      setFridges(fridges.map((f) => (f.id === updated.id ? updated : f)));
      setIsEditModalOpen(false);
      setSelectedFridge(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update fridge');
    }
  };

  const handleEditClick = (fridge: Fridge) => {
    setSelectedFridge(fridge);
    setIsEditModalOpen(true);
  };

  const handleHistoryClick = (fridge: Fridge) => {
    setSelectedFridge(fridge);
    setIsHistoryModalOpen(true);
  };

  const getStatusColor = (status: Fridge['status']) => {
    switch (status) {
      case 'AVAILABLE':
        return 'success';
      case 'OCCUPIED':
        return 'primary';
      case 'MAINTENANCE':
        return 'warning';
      case 'OFFLINE':
        return 'danger';
      default:
        return 'neutral';
    }
  };

  const getStatusIcon = (status: Fridge['status']) => {
    switch (status) {
      case 'AVAILABLE':
        return 'mdi:check-circle';
      case 'OCCUPIED':
        return 'mdi:account-multiple';
      case 'MAINTENANCE':
        return 'mdi:tools';
      case 'OFFLINE':
        return 'mdi:power-off';
      default:
        return 'mdi:help-circle';
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
          p: 3,
        }}
      >
        <CircularProgress size="lg" />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {error && (
        <Alert
          color="danger"
          sx={{ mb: 2 }}
          endDecorator={
            <Button variant="soft" color="danger" onClick={() => setError(null)}>
              Dismiss
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography level="h1">Fridges Management</Typography>
        <Button
          startDecorator={<Icon icon="mdi:plus" />}
          variant="solid"
          color="primary"
          onClick={() => setIsAddModalOpen(true)}
        >
          Add New Fridge
        </Button>
      </Box>

      <Grid container spacing={2}>
        {fridges.map((fridge) => (
          <Grid key={fridge.id} xs={12} sm={6} md={4}>
            <Card variant="outlined">
              <Box sx={{ p: 2 }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography level="h2" fontSize="xl">
                    {fridge.fridgeNumber}
                  </Typography>
                  <Tooltip title={fridge.status}>
                    <IconButton
                      variant="plain"
                      color={getStatusColor(fridge.status)}
                    >
                      <Icon icon={getStatusIcon(fridge.status)} />
                    </IconButton>
                  </Tooltip>
                </Box>

                <Typography level="body-sm" sx={{ mt: 1, mb: 2 }}>
                  Barcode: {fridge.barcode}
                </Typography>

                <Divider />

                <Box sx={{ mt: 2 }}>
                  <Typography
                    level="body-sm"
                    fontWeight="lg"
                    sx={{ mb: 1 }}
                  >
                    Temperature
                  </Typography>
                  <Typography
                    level="h3"
                    fontSize="xl"
                    color={fridge.temperature > -2 ? "warning" : "neutral"}
                  >
                    {fridge.temperature}°C
                  </Typography>
                </Box>

                <Box sx={{ mt: 2 }}>
                  <Typography
                    level="body-sm"
                    fontWeight="lg"
                    sx={{ mb: 1 }}
                  >
                    Capacity
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Typography>
                      {fridge.currentOccupancy} / {fridge.capacity}
                    </Typography>
                    <Typography level="body-sm" color="neutral">
                      spaces used
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ mt: 2, display: "flex", gap: 1 }}>
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:pencil" />}
                    onClick={() => handleEditClick(fridge)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:history" />}
                    onClick={() => handleHistoryClick(fridge)}
                  >
                    History
                  </Button>
                </Box>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Add Fridge Modal */}
      <FridgeModal
        open={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddFridge}
        mode="add"
      />

      {/* Edit Fridge Modal */}
      {selectedFridge && (
        <FridgeModal
          open={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedFridge(null);
          }}
          onSubmit={handleEditFridge}
          initialData={selectedFridge}
          mode="edit"
          showAssignments
        />
      )}

      {/* History Modal */}
      {selectedFridge && (
        <FridgeHistoryModal
          open={isHistoryModalOpen}
          onClose={() => {
            setIsHistoryModalOpen(false);
            setSelectedFridge(null);
          }}
          fridge={selectedFridge}
        />
      )}
    </Box>
  );
} 