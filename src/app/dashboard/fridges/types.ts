import { FridgeStatus } from '@prisma/client';

export interface Fridge {
  id: string;
  fridgeNumber: string;
  barcode: string;
  temperature: number;
  status: FridgeStatus;
  capacity: number;
  currentOccupancy: number;
  assignments?: FridgeAssignment[];
  temperatureLogs?: TemperatureLog[];
  maintenanceLogs?: MaintenanceLog[];
}

export interface FridgeAssignment {
  id: string;
  fridgeId: string;
  bodyAdmissionId: string;
  assignedAt: Date;
  temperature?: number;
  status: string;
  notes?: string;
  bodyAdmission?: BodyAdmission;
}

export interface TemperatureLog {
  id: string;
  fridgeId: string;
  temperature: number;
  timestamp: Date;
  recordedBy: string;
}

export interface MaintenanceLog {
  id: string;
  fridgeId: string;
  type: string;
  description: string;
  performedBy: string;
  performedAt: Date;
  nextScheduledDate?: Date;
  notes?: string;
}

export interface BodyAdmission {
  id: string;
  bodyId: string;
  admissionDate: string;
  deathRegisterNumber: string;
  bodyCondition: string;
  notes?: string;
}