import { BentoLayout } from "@/components/ui/bento/schemas/bentoComponentSchemas";

// First, let's create types for our dashboard data
export interface Admission {
  id: string;
  bodyTag: string;
  admissionDate: string;
  temperature: number;
  assignedFridge: string;
  deathRegisterNumber?: string;
  createdAt: string;
}

export interface Collection {
  id: string;
  createdAt: string;
}

export interface Release {
  id: string;
  bodyTag: string;
  releaseDate: string;
}

export interface DashboardMetrics {
  totalBodies: number;
  activeCases: number;
  pendingIdentification: number;
  recentAdmissions: any[];
  processingTime: number;
  storageUtilization: number;
}

export interface TemperatureReading {
  time: string;
  temperature: number;
  isWithinRange: boolean;
}

export interface TrendDataPoint {
  date: string;
  count: number;
  x: number;
  y: number;
}

export interface ChartData {
  name: string;
  data: number[];
}

export interface DashboardData {
  collections: any[];
  admissions: any[];
  releases: any[];
  trendData: TrendDataPoint[];
  temperatureData: TemperatureReading[];
  metrics: DashboardMetrics;
  layouts: BentoLayout[];
  lastUpdated?: string;
  refreshInterval?: number;
}

export interface DashboardStats {
  totalCases: number;
  pendingReports: number;
  completedToday: number;
  averageProcessingTime: number;
} 