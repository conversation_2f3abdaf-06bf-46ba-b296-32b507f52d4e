'use client';

import * as React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  Typography,
  IconButton,
  Stack,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Tab,
  Input,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';

interface HistoryPanelProps {
  images: GeneratedImage[];
  onSelect?: (image: GeneratedImage) => void;
  onFavorite?: (image: GeneratedImage) => void;
  onDelete?: (image: GeneratedImage) => void;
}

interface GeneratedImage {
  id: string;
  url: string;
  prompt: string;
  timestamp: Date;
  favorite: boolean;
  metadata?: {
    style: string;
    aspectRatio: string;
    settings: {
      quality: number;
      enhanceDetails: boolean;
      removeBackground: boolean;
    };
  };
}

export default function HistoryPanel({
  images,
  onSelect,
  onFavorite,
  onDelete,
}: HistoryPanelProps) {
  const [activeTab, setActiveTab] = React.useState<'all' | 'favorites'>('all');
  const [searchQuery, setSearchQuery] = React.useState('');

  const filteredImages = React.useMemo(() => {
    let filtered = images;
    
    if (activeTab === 'favorites') {
      filtered = filtered.filter((img) => img.favorite);
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((img) =>
        img.prompt.toLowerCase().includes(query) ||
        img.metadata?.style.toLowerCase().includes(query)
      );
    }
    
    return filtered;
  }, [images, activeTab, searchQuery]);

  return (
    <Stack spacing={2}>
      <Input
        size="sm"
        placeholder="Search history..."
        startDecorator={<Icon icon="mdi:magnify" />}
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />

      <Tabs
        value={activeTab}
        onChange={(_, value) => setActiveTab(value as 'all' | 'favorites')}
        sx={{ borderRadius: 'lg' }}
      >
        <TabList>
          <Tab value="all">All</Tab>
          <Tab value="favorites">Favorites</Tab>
        </TabList>
      </Tabs>

      <List
        sx={{
          '--ListItem-minHeight': '80px',
          '--ListItem-paddingY': '1rem',
          '--ListItem-gap': '1rem',
        }}
      >
        <AnimatePresence>
          {filteredImages.map((image) => (
            <motion.div
              key={image.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <ListItem
                endAction={
                  <Stack direction="row" spacing={1}>
                    <IconButton
                      variant="plain"
                      color={image.favorite ? 'warning' : 'neutral'}
                      onClick={() => onFavorite?.(image)}
                    >
                      <Icon
                        icon={image.favorite ? 'mdi:star' : 'mdi:star-outline'}
                      />
                    </IconButton>
                    <IconButton
                      variant="plain"
                      color="danger"
                      onClick={() => onDelete?.(image)}
                    >
                      <Icon icon="mdi:delete" />
                    </IconButton>
                  </Stack>
                }
              >
                <ListItemButton onClick={() => onSelect?.(image)}>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Box
                      component="img"
                      src={image.url}
                      alt={image.prompt}
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: 'md',
                        objectFit: 'cover',
                      }}
                    />
                    <Stack spacing={0.5}>
                      <Typography
                        level="body-sm"
                        sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                        }}
                      >
                        {image.prompt}
                      </Typography>
                      <Stack direction="row" spacing={1}>
                        <Chip
                          size="sm"
                          variant="soft"
                          startDecorator={<Icon icon="mdi:palette" />}
                        >
                          {image.metadata?.style}
                        </Chip>
                        <Typography level="body-xs">
                          {new Date(image.timestamp).toLocaleDateString()}
                        </Typography>
                      </Stack>
                    </Stack>
                  </Stack>
                </ListItemButton>
              </ListItem>
            </motion.div>
          ))}
        </AnimatePresence>

        {filteredImages.length === 0 && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 4,
              gap: 2,
            }}
          >
            <Icon
              icon="mdi:image-off"
              width={48}
              height={48}
              style={{ color: 'var(--joy-palette-neutral-400)' }}
            />
            <Typography level="body-sm" textAlign="center">
              {activeTab === 'favorites'
                ? 'No favorite images yet'
                : searchQuery
                ? 'No images match your search'
                : 'No images in history'}
            </Typography>
          </Box>
        )}
      </List>
    </Stack>
  );
}
