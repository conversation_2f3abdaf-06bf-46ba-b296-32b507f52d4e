'use client';

import * as React from 'react';
import { Box, IconButton, Stack, Typography, Tooltip } from '@mui/joy';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';

interface ImageCanvasProps {
  images: GeneratedImage[];
  onFavorite?: (image: GeneratedImage) => void;
  onDelete?: (image: GeneratedImage) => void;
  onDownload?: (image: GeneratedImage) => void;
  isGenerating?: boolean;
}

interface GeneratedImage {
  id: string;
  url: string;
  prompt: string;
  timestamp: Date;
  favorite: boolean;
  metadata?: {
    style: string;
    aspectRatio: string;
    settings: {
      quality: number;
      enhanceDetails: boolean;
      removeBackground: boolean;
    };
  };
}

export default function ImageCanvas({
  images,
  onFavorite,
  onDelete,
  onDownload,
  isGenerating = false,
}: ImageCanvasProps) {
  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: 2,
        p: 2,
      }}
    >
      <AnimatePresence>
        {images.map((image) => (
          <motion.div
            key={image.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.2 }}
          >
            <Box
              sx={{
                position: 'relative',
                borderRadius: 'md',
                overflow: 'hidden',
                '&:hover': {
                  '& .image-actions': {
                    opacity: 1,
                  },
                },
              }}
            >
              <Box
                component="img"
                src={image.url}
                alt={image.prompt}
                sx={{
                  width: '100%',
                  aspectRatio: image.metadata?.aspectRatio || '1/1',
                  objectFit: 'cover',
                  display: 'block',
                }}
              />
              <Box
                className="image-actions"
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  bgcolor: 'rgba(0, 0, 0, 0.5)',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  opacity: 0,
                  transition: 'opacity 0.2s',
                }}
              >
                <Stack direction="row" spacing={1}>
                  <Tooltip title={image.favorite ? 'Remove from favorites' : 'Add to favorites'}>
                    <IconButton
                      variant="soft"
                      color={image.favorite ? 'warning' : 'neutral'}
                      onClick={() => onFavorite?.(image)}
                    >
                      <Icon
                        icon={image.favorite ? 'mdi:star' : 'mdi:star-outline'}
                      />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Download image">
                    <IconButton
                      variant="soft"
                      color="primary"
                      onClick={() => onDownload?.(image)}
                    >
                      <Icon icon="mdi:download" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete image">
                    <IconButton
                      variant="soft"
                      color="danger"
                      onClick={() => onDelete?.(image)}
                    >
                      <Icon icon="mdi:delete" />
                    </IconButton>
                  </Tooltip>
                </Stack>
                <Typography
                  level="body-sm"
                  sx={{
                    color: 'white',
                    mt: 2,
                    px: 2,
                    textAlign: 'center',
                    maxWidth: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                  }}
                >
                  {image.prompt}
                </Typography>
              </Box>
            </Box>
          </motion.div>
        ))}
      </AnimatePresence>

      {isGenerating && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 300,
          }}
        >
          <motion.div
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'linear',
            }}
          >
            <Icon
              icon="mdi:loading"
              width={48}
              height={48}
              style={{ color: 'var(--joy-palette-primary-500)' }}
            />
          </motion.div>
        </Box>
      )}
    </Box>
  );
}
