'use client';

import * as React from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Slider,
  Stack,
  IconButton,
  Typography,
  Chip,
  Select,
  Option,
  Textarea,
  Tooltip,
} from '@mui/joy';
import { Icon } from '@iconify/react';

const STYLE_PRESETS = [
  { label: 'Realistic', icon: 'mdi:camera' },
  { label: 'Artistic', icon: 'mdi:palette' },
  { label: 'Anime', icon: 'mdi:emoticon-excited' },
  { label: '3D', icon: 'mdi:cube-outline' },
  { label: 'Sketch', icon: 'mdi:pencil' },
];

const ASPECT_RATIOS = [
  { label: 'Square', value: '1:1', width: 1024, height: 1024 },
  { label: 'Portrait', value: '2:3', width: 1024, height: 1536 },
  { label: 'Landscape', value: '3:2', width: 1536, height: 1024 },
  { label: 'Wide', value: '16:9', width: 1792, height: 1024 },
];

interface ToolsPanelProps {
  onGenerate: (params: GenerateImageParams) => Promise<void>;
  isGenerating: boolean;
}

interface GenerateImageParams {
  prompt: string;
  negativePrompt?: string;
  style: string;
  aspectRatio: string;
  quality: number;
  imageCount: number;
  enhanceDetails: boolean;
  removeBackground: boolean;
}

export default function ToolsPanel({ onGenerate, isGenerating }: ToolsPanelProps) {
  const [prompt, setPrompt] = React.useState('');
  const [negativePrompt, setNegativePrompt] = React.useState('');
  const [selectedStyle, setSelectedStyle] = React.useState('Realistic');
  const [aspectRatio, setAspectRatio] = React.useState(ASPECT_RATIOS[0]);
  const [quality, setQuality] = React.useState(75);
  const [imageCount, setImageCount] = React.useState(1);
  const [enhanceDetails, setEnhanceDetails] = React.useState(false);
  const [removeBackground, setRemoveBackground] = React.useState(false);

  const handleGenerate = () => {
    onGenerate({
      prompt,
      negativePrompt,
      style: selectedStyle,
      aspectRatio: aspectRatio.value,
      quality,
      imageCount,
      enhanceDetails,
      removeBackground,
    });
  };

  return (
    <Stack spacing={2}>
      <FormControl>
        <FormLabel>Prompt</FormLabel>
        <Textarea
          placeholder="Describe what you want to generate..."
          minRows={3}
          maxRows={6}
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
        />
      </FormControl>

      <FormControl>
        <FormLabel>Negative Prompt</FormLabel>
        <Textarea
          placeholder="Describe what you don't want in the image..."
          minRows={2}
          maxRows={4}
          value={negativePrompt}
          onChange={(e) => setNegativePrompt(e.target.value)}
        />
      </FormControl>

      <FormControl>
        <FormLabel>Style</FormLabel>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {STYLE_PRESETS.map((style) => (
            <Tooltip key={style.label} title={style.label}>
              <IconButton
                variant={selectedStyle === style.label ? 'solid' : 'soft'}
                color={selectedStyle === style.label ? 'primary' : 'neutral'}
                onClick={() => setSelectedStyle(style.label)}
              >
                <Icon icon={style.icon} />
              </IconButton>
            </Tooltip>
          ))}
        </Box>
      </FormControl>

      <FormControl>
        <FormLabel>Aspect Ratio</FormLabel>
        <Select
          value={aspectRatio.value}
          onChange={(_, value) => 
            setAspectRatio(ASPECT_RATIOS.find(ratio => ratio.value === value) || ASPECT_RATIOS[0])
          }
        >
          {ASPECT_RATIOS.map((ratio) => (
            <Option key={ratio.value} value={ratio.value}>
              {ratio.label} ({ratio.value})
            </Option>
          ))}
        </Select>
      </FormControl>

      <FormControl>
        <FormLabel>Quality</FormLabel>
        <Slider
          value={quality}
          onChange={(_, value) => setQuality(value as number)}
          valueLabelDisplay="auto"
          min={25}
          max={100}
          marks={[
            { value: 25, label: '25%' },
            { value: 50, label: '50%' },
            { value: 75, label: '75%' },
            { value: 100, label: '100%' },
          ]}
        />
      </FormControl>

      <FormControl>
        <FormLabel>Number of Images</FormLabel>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <IconButton
            size="sm"
            variant="outlined"
            disabled={imageCount <= 1}
            onClick={() => setImageCount((prev) => Math.max(1, prev - 1))}
          >
            <Icon icon="mdi:minus" />
          </IconButton>
          <Typography level="body-lg">{imageCount}</Typography>
          <IconButton
            size="sm"
            variant="outlined"
            disabled={imageCount >= 8}
            onClick={() => setImageCount((prev) => Math.min(8, prev + 1))}
          >
            <Icon icon="mdi:plus" />
          </IconButton>
        </Box>
      </FormControl>

      <Stack direction="row" spacing={1}>
        <Chip
          variant={enhanceDetails ? 'solid' : 'soft'}
          color={enhanceDetails ? 'primary' : 'neutral'}
          onClick={() => setEnhanceDetails(!enhanceDetails)}
          startDecorator={<Icon icon="mdi:image-filter-hdr" />}
        >
          Enhance Details
        </Chip>
        <Chip
          variant={removeBackground ? 'solid' : 'soft'}
          color={removeBackground ? 'primary' : 'neutral'}
          onClick={() => setRemoveBackground(!removeBackground)}
          startDecorator={<Icon icon="mdi:image-filter-center-focus" />}
        >
          Remove Background
        </Chip>
      </Stack>

      <Button
        fullWidth
        loading={isGenerating}
        disabled={!prompt.trim()}
        onClick={handleGenerate}
        startDecorator={<Icon icon="mdi:image-plus" />}
      >
        Generate
      </Button>
    </Stack>
  );
}
