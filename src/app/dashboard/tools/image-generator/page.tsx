'use client';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

import * as React from 'react';
import { Box, Button, Stack, Typography } from '@mui/joy';
import { Icon } from '@iconify/react';
import EditorLayout from '@/components/Dashboard/EditorLayout';
import ToolsPanel from './components/ToolsPanel';
import ImageCanvas from './components/ImageCanvas';
import HistoryPanel from './components/HistoryPanel';
import { useImageGeneration } from '@/hooks/useImageGeneration';

interface GeneratedImage {
  id: string;
  url: string;
  prompt: string;
  timestamp: Date;
  favorite: boolean;
  metadata?: {
    style: string;
    aspectRatio: string;
    settings: {
      quality: number;
      enhanceDetails: boolean;
      removeBackground: boolean;
    };
  };
}

export default function ImageGeneratorPage() {
  const [images, setImages] = React.useState<GeneratedImage[]>([]);
  const { generateImage, isGenerating, error } = useImageGeneration();

  const handleGenerate = async (params: any) => {
    const generatedImage = await generateImage(params);
    if (generatedImage) {
      setImages((prev) => [generatedImage, ...prev]);
    }
  };

  const handleFavorite = (image: GeneratedImage) => {
    setImages((prev) =>
      prev.map((img) =>
        img.id === image.id ? { ...img, favorite: !img.favorite } : img
      )
    );
  };

  const handleDelete = (image: GeneratedImage) => {
    setImages((prev) => prev.filter((img) => img.id !== image.id));
  };

  const handleDownload = async (image: GeneratedImage) => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `generated-image-${image.id}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading image:', error);
    }
  };

  const toolbar = (
    <Stack direction="row" spacing={2} alignItems="center">
      <Typography level="h4">Image Generator</Typography>
      <Box sx={{ flex: 1 }} />
      <Button
        variant="outlined"
        color="neutral"
        startDecorator={<Icon icon="mdi:help" />}
      >
        Help
      </Button>
    </Stack>
  );

  const statusbar = (
    <Stack direction="row" spacing={2} alignItems="center">
      <Typography level="body-sm">
        {images.length} image{images.length !== 1 ? 's' : ''} generated
      </Typography>
      <Box sx={{ flex: 1 }} />
      {error && (
        <Typography level="body-sm" color="danger">
          Error: {error}
        </Typography>
      )}
    </Stack>
  );

  return (
    <EditorLayout
      leftPanel={
        <ToolsPanel
          onGenerate={handleGenerate}
          isGenerating={isGenerating}
        />
      }
      rightPanel={
        <HistoryPanel
          images={images}
          onFavorite={handleFavorite}
          onDelete={handleDelete}
        />
      }
      centerPanel={
        <ImageCanvas
          images={images}
          onFavorite={handleFavorite}
          onDelete={handleDelete}
          onDownload={handleDownload}
          isGenerating={isGenerating}
        />
      }
      toolbar={toolbar}
      statusbar={statusbar}
      leftPanelTitle="Generation Tools"
      rightPanelTitle="History"
    />
  );
}
