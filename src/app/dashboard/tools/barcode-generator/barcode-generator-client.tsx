'use client';

import * as React from 'react';
import { Box, Button, Stack, Typography, Modal, ModalDialog } from '@mui/joy';
import { Icon } from '@iconify/react';
import { Session } from 'next-auth';
import EditorLayout from '@/components/Dashboard/EditorLayout';
import { BarcodeGenerator } from '@/components/Barcode/BarcodeGenerator';
import { BarcodeHistory } from '@/components/Barcode/BarcodeHistory';
import { BarcodeTools } from '@/components/Barcode/BarcodeTools';
import { BodyTagScanner } from '@/components/Scanner/BodyTagScanner';
import { BodyTagScanType } from '@/types/body';

interface BarcodeGeneratorClientProps {
  session: Session | null;
}

interface Facility {
  id: string;
  name: string;
  code: string;
}

// Rest of the interfaces remain the same...

export default function BarcodeGeneratorClient({ session }: BarcodeGeneratorClientProps) {
  const [barcodes, setBarcodes] = React.useState<any[]>([]);
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [facilities, setFacilities] = React.useState<Facility[]>([]);
  const [showScanner, setShowScanner] = React.useState(false);

  // Fetch facilities on component mount
  React.useEffect(() => {
    const fetchFacilities = async () => {
      try {
        const response = await fetch('/api/facilities', {
          headers: {
            'Content-Type': 'application/json',
          }
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch facilities');
        }
        const data = await response.json();
        setFacilities(data);
      } catch (err) {
        console.error('Error fetching facilities:', err);
        setError(err instanceof Error ? err.message : 'Failed to load facilities');
      }
    };

    fetchFacilities();
  }, []);

  const handleGenerate = async (params: any) => {
    try {
      if (!params.qrOptions?.metadata.facilityId) {
        throw new Error('Facility ID is required');
      }

      if (!session?.user) {
        throw new Error('User session is required to generate body tags');
      }

      // Determine who the tag is being generated for
      const isAdmin = session.user.role === 'ADMIN';
      const handledByUser = params.qrOptions.metadata.handledBy;
      const generatedByUser = session.user.name || session.user.email || 'Unknown User';

      // For admins, if handledBy is specified and different from current user,
      // they are generating on behalf of another user
      const isGeneratingOnBehalf = isAdmin && handledByUser && handledByUser !== generatedByUser;

      setIsGenerating(true);
      setError(null);

      // Use the proper body tag generation API endpoint
      const response = await fetch('/api/body-tags/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          facilityId: params.qrOptions.metadata.facilityId,
          count: 1,
          qrCodeFormat: 'QR_CODE',
          metadata: {
            ...params.qrOptions.metadata,
            generatedAt: new Date().toISOString(),
            generatedBy: generatedByUser,
            generatedByUserId: session.user.id,
            generatedByEmail: session.user.email,
            handledBy: handledByUser || generatedByUser,
            isGeneratedOnBehalf: isGeneratingOnBehalf,
            ...(isGeneratingOnBehalf && {
              onBehalfOf: handledByUser,
              adminGeneratedBy: generatedByUser,
            }),
            barcodeParams: params
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate body tag');
      }

      const result = await response.json();

      if (!result.success || !result.tags || result.tags.length === 0) {
        throw new Error('No body tags were generated');
      }

      const newTag = result.tags[0]; // Get the first (and only) generated tag

      // Update local state with new tag
      setBarcodes(prev => [{
        id: newTag.id,
        value: newTag.qrCodeValue,
        tagNumber: newTag.tagNumber,
        format: params.format || 'QR',
        timestamp: new Date(newTag.generatedAt),
        status: newTag.status,
        favorite: false,
        metadata: {
          ...params.qrOptions.metadata,
          facilityId: newTag.facilityId,
          type: 'body_tag',
          facilityName: newTag.facility?.name,
          facilityCode: newTag.facility?.code,
          generatedBy: generatedByUser,
          generatedByUserId: session.user.id,
          generatedByEmail: session.user.email,
          handledBy: handledByUser || generatedByUser,
          isGeneratedOnBehalf: isGeneratingOnBehalf,
          ...(isGeneratingOnBehalf && {
            onBehalfOf: handledByUser,
            adminGeneratedBy: generatedByUser,
          })
        }
      }, ...prev]);

    } catch (err) {
      console.error('Error generating body tag:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate body tag');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleFavorite = (barcode: any) => {
    setBarcodes(prev =>
      prev.map(b =>
        b.id === barcode.id ? { ...b, favorite: !b.favorite } : b
      )
    );
  };

  const handleDelete = async (barcode: any) => {
    try {
      const response = await fetch(`/api/body-tags/${barcode.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete body tag');
      }

      setBarcodes(prev => prev.filter(b => b.id !== barcode.id));
    } catch (err) {
      console.error('Error deleting body tag:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete body tag');
    }
  };

  const toolbar = (
    <Stack direction="row" spacing={2} alignItems="center">
      <Typography level="h4">Pathology Barcode Generator</Typography>
      <Box sx={{ flex: 1 }} />
      <Button
        variant="soft"
        color="primary"
        startDecorator={<Icon icon="mdi:qrcode-scan" />}
        onClick={() => setShowScanner(true)}
      >
        Scan Code
      </Button>
      <Button
        variant="soft"
        color="success"
        startDecorator={<Icon icon="mdi:qrcode-plus" />}
        disabled={facilities.length === 0 || isGenerating || !session?.user}
        onClick={() => handleGenerate({
          value: '', // Will be generated by the API
          format: 'QR', // Default to QR code format
          size: 200,
          color: '#000000',
          includeText: true,
          textPosition: 'bottom',
          backgroundColor: '#FFFFFF',
          border: true,
          qrOptions: {
            errorCorrection: 'H',
            contentType: 'body_tag',
            quietZone: 4,
            metadata: {
              facilityId: facilities[0]?.id || '',
              type: 'body_tag',
              status: 'ACTIVE',
              handledBy: session?.user?.name || session?.user?.email || 'Unknown User',
              notes: `Generated from quick generate button${session?.user?.role === 'ADMIN' ? ' (Admin)' : ''}`,
              deathRegisterNo: '',
            }
          }
        })}
      >
        {isGenerating ? 'Generating...' : 'Quick Generate'}
      </Button>
      <Button
        variant="outlined"
        color="neutral"
        startDecorator={<Icon icon="mdi:help" />}
      >
        Help
      </Button>
    </Stack>
  );

  const statusbar = (
    <Stack direction="row" spacing={2} alignItems="center">
      <Typography level="body-sm">
        {barcodes.length} body tag{barcodes.length !== 1 ? 's' : ''} generated
      </Typography>
      {session?.user && (
        <Typography level="body-sm" color="neutral">
          <Icon icon="mdi:account" style={{ marginRight: 4 }} />
          {session.user.name || session.user.email}
          {session.user.role === 'ADMIN' && (
            <Icon icon="mdi:shield-crown" style={{ marginLeft: 4, color: 'var(--joy-palette-warning-500)' }} />
          )}
        </Typography>
      )}
      <Box sx={{ flex: 1 }} />
      {isGenerating && (
        <Typography level="body-sm" color="primary">
          <Icon icon="mdi:loading" style={{ marginRight: 4, animation: 'spin 1s linear infinite' }} />
          Generating body tag...
        </Typography>
      )}
      {error && (
        <Typography level="body-sm" color="danger">
          <Icon icon="mdi:alert-circle" style={{ marginRight: 4 }} />
          {error}
        </Typography>
      )}
      {facilities.length === 0 && !error && (
        <Typography level="body-sm" color="warning">
          <Icon icon="mdi:alert" style={{ marginRight: 4 }} />
          No facilities available
        </Typography>
      )}
      {!session?.user && (
        <Typography level="body-sm" color="warning">
          <Icon icon="mdi:account-alert" style={{ marginRight: 4 }} />
          Not authenticated
        </Typography>
      )}
    </Stack>
  );

  return (
    <>
      <EditorLayout
        leftPanel={
          <BarcodeTools
            onGenerate={handleGenerate}
            isGenerating={isGenerating}
            facilities={facilities.map(f => ({ ...f, code: f.code || f.name }))}
            session={session}
          />
        }
        rightPanel={
          <BarcodeHistory/>
        }
        centerPanel={
          <BarcodeGenerator
            barcodes={barcodes}
            onFavorite={handleFavorite}
            onDelete={handleDelete}
            isGenerating={isGenerating}
          />
        }
        toolbar={toolbar}
        statusbar={statusbar}
        leftPanelTitle="Barcode Tools"
        rightPanelTitle="History"
      />

      {/* QR Code Scanner Modal */}
      <Modal open={showScanner} onClose={() => setShowScanner(false)}>
        <ModalDialog
          size="lg"
          sx={{
            width:"100%",
            maxWidth: '90vw',
            maxHeight: '90vh',
            overflow: 'auto',
          }}
        >
          <BodyTagScanner
            onScan={async (bodyTag) => {
              // Handle scanned body tag
              if (bodyTag) {
                // Add scanned tag to the list if it's not already there
                const existingTag = barcodes.find(b => b.tagNumber === bodyTag.tagNumber);
                if (!existingTag) {
                  setBarcodes(prev => [{
                    id: bodyTag.id,
                    value: bodyTag.qrCodeValue || bodyTag.tagNumber,
                    tagNumber: bodyTag.tagNumber,
                    format: 'QR',
                    timestamp: new Date(bodyTag.generatedAt),
                    status: bodyTag.status,
                    favorite: false,
                    metadata: {
                      facilityId: bodyTag.facilityId,
                      type: 'body_tag',
                      facilityName: bodyTag.facility?.name,
                      scanned: true,
                    }
                  }, ...prev]);
                }
              }
              setShowScanner(false);
            }}
            scanType={BodyTagScanType.COLLECTION}
          />
        </ModalDialog>
      </Modal>
    </>
  );
}
