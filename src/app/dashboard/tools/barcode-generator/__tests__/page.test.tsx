import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CssVarsProvider } from '@mui/joy/styles';
import { SessionProvider } from 'next-auth/react';
import { theme } from '@/components/ThemeRegistry/theme';
import BarcodeGeneratorPage from '../page';

// Mock fetch
global.fetch = jest.fn();

// Mock next-auth
const mockSession = {
  user: {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'ADMIN',
  },
  expires: '2024-12-31',
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <SessionProvider session={mockSession}>
    <CssVarsProvider theme={theme}>
      {children}
    </CssVarsProvider>
  </SessionProvider>
);

// Mock facilities data
const mockFacilities = [
  { id: '1', name: 'Test Facility 1', code: 'PTA-FPS' },
  { id: '2', name: 'Test Facility 2', code: 'JHB-FPS' },
];

// Mock body tag generation response
const mockGenerationResponse = {
  success: true,
  message: 'Successfully generated 1 body tag(s)',
  tags: [
    {
      id: 'tag-1',
      tagNumber: 'GP/PTA/1222/2025',
      status: 'GENERATED',
      qrCodeValue: 'GP/PTA/1222/2025',
      generatedAt: '2025-01-01T00:00:00.000Z',
      facilityId: '1',
      facility: {
        id: '1',
        name: 'Test Facility 1',
        code: 'PTA-FPS',
      }
    }
  ],
  count: 1
};

describe('BarcodeGeneratorPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock facilities fetch
    (global.fetch as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('/api/facilities')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockFacilities),
        });
      }
      
      if (url.includes('/api/body-tags/generate')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockGenerationResponse),
        });
      }
      
      return Promise.reject(new Error('Unknown URL'));
    });
  });

  it('renders the barcode generator page', async () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    expect(screen.getByText('Pathology Barcode Generator')).toBeInTheDocument();
    expect(screen.getByText('Scan Code')).toBeInTheDocument();
    expect(screen.getByText('Help')).toBeInTheDocument();

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });
  });

  it('loads facilities on mount', async () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/facilities');
    });
  });

  it('shows loading state when no facilities are available', () => {
    // Mock empty facilities response
    (global.fetch as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('/api/facilities')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([]),
        });
      }
      return Promise.reject(new Error('Unknown URL'));
    });

    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    expect(screen.getByText('Quick Generate')).toBeDisabled();
  });

  it('generates body tag when quick generate is clicked', async () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });

    const quickGenerateButton = screen.getByText('Quick Generate');
    fireEvent.click(quickGenerateButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/body-tags/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: expect.stringContaining('"facilityId":"1"'),
      });
    });

    // Check that the generated tag appears in the status
    await waitFor(() => {
      expect(screen.getByText('1 body tag generated')).toBeInTheDocument();
    });
  });

  it('shows error message when generation fails', async () => {
    // Mock failed generation
    (global.fetch as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('/api/facilities')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockFacilities),
        });
      }
      
      if (url.includes('/api/body-tags/generate')) {
        return Promise.resolve({
          ok: false,
          json: () => Promise.resolve({ error: 'Generation failed' }),
        });
      }
      
      return Promise.reject(new Error('Unknown URL'));
    });

    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });

    const quickGenerateButton = screen.getByText('Quick Generate');
    fireEvent.click(quickGenerateButton);

    await waitFor(() => {
      expect(screen.getByText('Generation failed')).toBeInTheDocument();
    });
  });

  it('shows loading state during generation', async () => {
    // Mock slow generation
    (global.fetch as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('/api/facilities')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockFacilities),
        });
      }
      
      if (url.includes('/api/body-tags/generate')) {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              ok: true,
              json: () => Promise.resolve(mockGenerationResponse),
            });
          }, 100);
        });
      }
      
      return Promise.reject(new Error('Unknown URL'));
    });

    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });

    const quickGenerateButton = screen.getByText('Quick Generate');
    fireEvent.click(quickGenerateButton);

    // Check loading state
    expect(screen.getByText('Generating...')).toBeInTheDocument();
    expect(screen.getByText('Generating body tag...')).toBeInTheDocument();

    // Wait for completion
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });
  });

  it('opens scanner modal when scan code is clicked', () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    const scanButton = screen.getByText('Scan Code');
    fireEvent.click(scanButton);

    // Check that scanner modal is opened (this would depend on the BodyTagScanner component)
    // For now, we just verify the button click doesn't cause errors
    expect(scanButton).toBeInTheDocument();
  });

  it('validates required facility ID before generation', async () => {
    // Mock empty facilities
    (global.fetch as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('/api/facilities')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([]),
        });
      }
      return Promise.reject(new Error('Unknown URL'));
    });

    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Quick generate should be disabled when no facilities
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeDisabled();
    });
  });

  it('handles network errors gracefully', async () => {
    // Mock network error
    (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Should show error state
    await waitFor(() => {
      expect(screen.getByText('No facilities available')).toBeInTheDocument();
    });
  });

  it('uses correct API endpoint for body tag generation', async () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });

    const quickGenerateButton = screen.getByText('Quick Generate');
    fireEvent.click(quickGenerateButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/body-tags/generate', expect.any(Object));
    });
  });

  it('requires user authentication for generation', async () => {
    // Test with no session
    const TestWrapperNoSession: React.FC<{ children: React.ReactNode }> = ({ children }) => (
      <SessionProvider session={null}>
        <CssVarsProvider theme={theme}>
          {children}
        </CssVarsProvider>
      </SessionProvider>
    );

    render(
      <TestWrapperNoSession>
        <BarcodeGeneratorPage />
      </TestWrapperNoSession>
    );

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeDisabled();
    });

    // Should show authentication warning
    expect(screen.getByText('Not authenticated')).toBeInTheDocument();
  });

  it('shows user information in status bar', async () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Should show user name in status bar
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument();
    });
  });

  it('shows admin crown icon for admin users', async () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Should show admin crown icon since mockSession has ADMIN role
    await waitFor(() => {
      const adminIcon = document.querySelector('[data-icon="mdi:shield-crown"]');
      expect(adminIcon).toBeInTheDocument();
    });
  });

  it('handles admin generating on behalf of another user', async () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });

    // Mock a scenario where admin selects a different user in BarcodeTools
    const quickGenerateButton = screen.getByText('Quick Generate');
    fireEvent.click(quickGenerateButton);

    await waitFor(() => {
      const lastCall = (global.fetch as jest.Mock).mock.calls.find(call =>
        call[0].includes('/api/body-tags/generate')
      );

      expect(lastCall).toBeDefined();
      const requestBody = JSON.parse(lastCall[1].body);

      // Should include admin metadata
      expect(requestBody.metadata).toMatchObject({
        generatedBy: 'Test User',
        generatedByUserId: '1',
        generatedByEmail: '<EMAIL>',
        handledBy: 'Test User',
        isGeneratedOnBehalf: false, // False for quick generate since it uses current user
      });
    });
  });

  it('handles admin generating on behalf of different user', async () => {
    // Create a mock handleGenerate call with different handledBy user
    const mockHandleGenerate = jest.fn();

    // Mock the BarcodeTools component to simulate admin selecting different user
    const mockParams = {
      value: 'GP/PTA/1222/2025',
      format: 'QR',
      size: 200,
      color: '#000000',
      includeText: true,
      textPosition: 'bottom',
      backgroundColor: '#FFFFFF',
      border: true,
      qrOptions: {
        errorCorrection: 'H',
        contentType: 'body_tag',
        quietZone: 4,
        metadata: {
          facilityId: '1',
          type: 'body_tag',
          status: 'ACTIVE',
          handledBy: 'Different User', // Admin selected different user
          notes: 'Generated on behalf of another user',
          deathRegisterNo: '',
        }
      }
    };

    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });

    // Simulate the BarcodeTools component calling handleGenerate with different user
    const page = screen.getByText('Quick Generate').closest('div');

    // We can't directly test the BarcodeTools interaction here, but we can verify
    // that the page handles the admin scenario correctly in the API call structure
    expect(page).toBeInTheDocument();
  });

  it('includes proper metadata in generation request', async () => {
    render(
      <TestWrapper>
        <BarcodeGeneratorPage />
      </TestWrapper>
    );

    // Wait for facilities to load
    await waitFor(() => {
      expect(screen.getByText('Quick Generate')).toBeInTheDocument();
    });

    const quickGenerateButton = screen.getByText('Quick Generate');
    fireEvent.click(quickGenerateButton);

    await waitFor(() => {
      const lastCall = (global.fetch as jest.Mock).mock.calls.find(call =>
        call[0].includes('/api/body-tags/generate')
      );

      expect(lastCall).toBeDefined();
      const requestBody = JSON.parse(lastCall[1].body);

      expect(requestBody).toMatchObject({
        facilityId: '1',
        count: 1,
        qrCodeFormat: 'QR_CODE',
        metadata: expect.objectContaining({
          type: 'body_tag',
          status: 'ACTIVE',
          handledBy: 'Test User',
          generatedBy: 'Test User',
          generatedByUserId: '1',
          generatedByEmail: '<EMAIL>',
          isGeneratedOnBehalf: false,
        }),
      });
    });
  });
});
