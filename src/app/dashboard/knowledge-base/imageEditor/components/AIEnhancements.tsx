import React, { useState } from 'react';
import { <PERSON><PERSON>, Circular<PERSON><PERSON>ress, She<PERSON>, <PERSON>ack, Typography } from '@mui/joy';
import { Icon } from '@iconify/react';
import { useChat } from 'ai/react';

interface AIEnhancementsProps {
  onEnhance: (type: string, adjustments: ImageAdjustments) => Promise<void>;
  adjustments: ImageAdjustments;
  loading: boolean;
  imageFile: File | null;
}

interface ImageAdjustments {
  brightness: number;
  contrast: number;
  saturation: number;
  blur: number;
  sharpen: number;
}

const enhancementOptions = [
  {
    id: 'quality',
    label: 'Enhance Quality',
    description: 'Improve overall image quality and reduce noise',
    icon: 'material-symbols:auto-fix-high',
  },
  {
    id: 'lighting',
    label: 'Fix Lighting',
    description: 'Adjust exposure and balance lighting',
    icon: 'material-symbols:wb-sunny',
  },
  {
    id: 'color',
    label: 'Color Correction',
    description: 'Optimize color balance and vibrancy',
    icon: 'material-symbols:palette',
  },
  {
    id: 'detail',
    label: 'Enhance Details',
    description: 'Sharpen and improve image details',
    icon: 'material-symbols:details',
  },
  {
    id: 'remove-bg',
    label: 'Remove Background',
    description: 'Automatically remove image background',
    icon: 'material-symbols:layers',
  },
];

export const AIEnhancements: React.FC<AIEnhancementsProps> = ({
  onEnhance,
  adjustments,
  loading: parentLoading,
  imageFile,
}) => {
  const [activeEnhancement, setActiveEnhancement] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const {
    messages,
    append,
    isLoading: chatLoading,
    error: chatError,
    setInput,
  } = useChat({
    api: '/api/image-enhance',
    onResponse: (response) => {
      if (response.status === 200) {
        setActiveEnhancement(null);
        setError(null);
      } else {
        setError('Enhancement failed. Please try again.');
      }
    },
    onError: (err) => {
      console.error('Enhancement error:', err);
      setError('Enhancement failed. Please try again.');
      setActiveEnhancement(null);
    },
    onFinish: () => {
      setActiveEnhancement(null);
    },
  });

  const handleEnhance = async (type: string) => {
    try {
      if (!imageFile) {
        setError('No image selected');
        return;
      }

      setActiveEnhancement(type);
      setError(null);
      
      // First, apply the enhancement to the image
      await onEnhance(type, adjustments);

      // Create a message with the enhancement parameters
      const message = {
        enhancementType: type,
        adjustments,
        imageBase64: await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64 = reader.result as string;
            resolve(base64.split(',')[1]); // Remove data URL prefix
          };
          reader.readAsDataURL(imageFile);
        }),
      };

      // Send the message
      await append({
        role: 'user',
        content: JSON.stringify(message),
      });

    } catch (err) {
      console.error('Enhancement failed:', err);
      setError('Enhancement failed. Please try again.');
      setActiveEnhancement(null);
    }
  };

  const loading = parentLoading || chatLoading;

  return (
    <Stack spacing={2}>
      <Typography level="title-lg" sx={{ mb: 1 }}>
        AI Enhancements
      </Typography>
      
      {(error || chatError) && (
        <Sheet
          variant="soft"
          color="danger"
          sx={{ p: 2, borderRadius: 'sm', mb: 2 }}
        >
          <Typography level="body-sm">{error || chatError?.message}</Typography>
        </Sheet>
      )}
      
      {!imageFile && (
        <Sheet
          variant="soft"
          color="warning"
          sx={{ p: 2, borderRadius: 'sm', mb: 2 }}
        >
          <Typography level="body-sm">Please select an image to enhance</Typography>
        </Sheet>
      )}
      
      {enhancementOptions.map((option) => (
        <Button
          key={option.id}
          variant="soft"
          color="primary"
          fullWidth
          startDecorator={<Icon icon={option.icon} />}
          endDecorator={loading && activeEnhancement === option.id && (
            <CircularProgress size="sm" />
          )}
          disabled={loading || !imageFile}
          onClick={() => handleEnhance(option.id)}
          sx={{
            justifyContent: 'flex-start',
            gap: 2,
            p: 2,
            '&:hover': {
              bgcolor: 'background.level2',
            },
            ...(activeEnhancement === option.id && {
              bgcolor: 'primary.softBg',
            }),
          }}
        >
          <Sheet sx={{ textAlign: 'left' }}>
            <Typography level="body-md">{option.label}</Typography>
            <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
              {option.description}
            </Typography>
          </Sheet>
        </Button>
      ))}

      {messages.length > 0 && (
        <Sheet
          variant="soft"
          color="success"
          sx={{ p: 2, borderRadius: 'sm', mt: 2 }}
        >
          {messages.map((message, index) => (
            <Typography
              key={index}
              level="body-sm"
              sx={{ 
                whiteSpace: 'pre-wrap',
                mb: index < messages.length - 1 ? 2 : 0
              }}
            >
              {message.content}
            </Typography>
          ))}
        </Sheet>
      )}
    </Stack>
  );
}; 