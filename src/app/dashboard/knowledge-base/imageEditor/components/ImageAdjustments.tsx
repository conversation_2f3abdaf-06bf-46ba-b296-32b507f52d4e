import { Sheet, Typography, Slider } from '@mui/joy';

export interface ImageAdjustmentsType {
  brightness: number;
  contrast: number;
  saturation: number;
  blur: number;
  sharpen: number;
}

interface ImageAdjustmentsProps {
  adjustments: ImageAdjustmentsType;
  onAdjustmentChange: (type: keyof ImageAdjustmentsType, value: number) => void;
}

export function ImageAdjustments({ adjustments, onAdjustmentChange }: ImageAdjustmentsProps) {
  const adjustmentControls = [
    { key: 'brightness', label: 'Brightness', max: 200 },
    { key: 'contrast', label: 'Contrast', max: 200 },
    { key: 'saturation', label: 'Saturation', max: 200 },
    { key: 'blur', label: 'Blur', max: 10 },
    { key: 'sharpen', label: 'Sharpen', max: 10 },
  ] as const;

  return (
    <Sheet sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      {adjustmentControls.map(({ key, label, max }) => (
        <Sheet key={key}>
          <Typography level="body-sm">
            {label} ({adjustments[key]}
            {key === 'blur' || key === 'sharpen' ? 'px' : '%'})
          </Typography>
          <Slider
            value={adjustments[key]}
            onChange={(e, val) => onAdjustmentChange(key, val as number)}
            min={0}
            max={max}
          />
        </Sheet>
      ))}
    </Sheet>
  );
} 