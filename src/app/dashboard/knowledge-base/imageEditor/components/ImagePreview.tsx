import { Sheet, Typo<PERSON>, But<PERSON> } from '@mui/joy';
import { Icon } from '@iconify/react';
import { useCallback, useState } from 'react';
import { ImageActions } from './ImageActions';

interface ImagePreviewProps {
  image: string | null;
  style: React.CSSProperties;
  onImageUpload: (file: File) => void;
  onDownload: () => void;
  onCopy: () => void;
}

export function ImagePreview({ image, style, onImageUpload, onDownload, onCopy }: ImagePreviewProps) {
  const [isDragging, setIsDragging] = useState(false);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      onImageUpload(file);
    }
  }, [onImageUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleClick = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        onImageUpload(file);
      }
    };
    input.click();
  };

  return (
    <Sheet 
      sx={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: 2, 
        height: '100%',
        position: 'relative'
      }}
    >
      <Sheet 
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        sx={{ 
          flex: 1,
          borderRadius: 'lg',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: isDragging ? '#2A2A2A' : '#333',
          position: 'relative',
          border: '2px dashed',
          borderColor: isDragging ? 'primary.500' : 'divider',
          transition: 'all 0.2s ease-in-out',
          cursor: 'pointer',
          '&:hover': {
            borderColor: 'primary.400',
            bgcolor: '#2A2A2A',
            '& .upload-overlay': {
              opacity: 1
            }
          }
        }}
      >
        {image ? (
          <>
            <img
              src={image}
              alt="Edited image"
              style={{
                ...style,
                transition: 'all 0.3s ease-in-out'
              }}
            />
            <Sheet 
              className="upload-overlay"
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: 'rgba(0,0,0,0.5)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                opacity: 0,
                transition: 'opacity 0.2s ease-in-out',
                backdropFilter: 'blur(4px)'
              }}
            >
              <Button
                variant="soft"
                color="primary"
                startDecorator={<Icon icon="material-symbols:cloud-upload" />}
                sx={{ mb: 1 }}
              >
                Upload New Image
              </Button>
              <Typography level="body-sm" sx={{ color: 'text.tertiary' }}>
                or drop an image here
              </Typography>
            </Sheet>
          </>
        ) : (
          <Sheet 
            variant="soft" 
            sx={{ 
              textAlign: 'center',
              color: 'text.secondary',
              p: 4,
              borderRadius: 'lg',
              bgcolor: 'transparent',
              transition: 'transform 0.2s ease',
              transform: isDragging ? 'scale(1.02)' : 'scale(1)'
            }}
          >
            <Icon 
              icon="material-symbols:cloud-upload" 
              style={{ 
                fontSize: '64px', 
                marginBottom: '16px',
                color: isDragging ? '#7C3AED' : undefined
              }} 
            />
            <Typography 
              level="h4" 
              sx={{ 
                mb: 1,
                color: isDragging ? 'primary.400' : 'text.primary'
              }}
            >
              {isDragging ? 'Drop to Upload' : 'Drop your image here'}
            </Typography>
            <Typography level="body-sm" sx={{ color: 'text.tertiary', mb: 2 }}>
              Supports: JPG, PNG, WebP
            </Typography>
            <Button
              variant="soft"
              color="neutral"
              startDecorator={<Icon icon="material-symbols:upload-file" />}
            >
              Browse Files
            </Button>
          </Sheet>
        )}
      </Sheet>

      <ImageActions 
        onDownload={onDownload}
        onCopy={onCopy}
        disabled={!image}
      />
    </Sheet>
  );
} 