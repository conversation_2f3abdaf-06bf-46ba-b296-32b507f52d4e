import { Sheet, IconButton, Tooltip } from '@mui/joy';
import { Icon } from '@iconify/react';

interface ImageActionsProps {
  onDownload: () => void;
  onCopy: () => void;
  disabled: boolean;
}

export function ImageActions({ onDownload, onCopy, disabled }: ImageActionsProps) {
  return (
    <Sheet 
      sx={{ 
        display: 'flex', 
        gap: 1, 
        justifyContent: 'space-between',
        p: 1,
        borderRadius: 'lg',
        bgcolor: 'background.level2',
        opacity: disabled ? 0.7 : 1,
        transition: 'opacity 0.2s ease'
      }}
    >
      <Sheet sx={{ display: 'flex', gap: 1 }}>
        <Tooltip title="Download Image" variant="soft">
          <IconButton
            variant="plain"
            color="primary"
            onClick={onDownload}
            disabled={disabled}
            sx={{
              transition: 'transform 0.2s ease',
              '&:hover': {
                transform: 'scale(1.05)',
                bgcolor: 'primary.800'
              }
            }}
          >
            <Icon icon="material-symbols:download" />
          </IconButton>
        </Tooltip>
        <Tooltip title="Copy to Clipboard" variant="soft">
          <IconButton
            variant="plain"
            color="neutral"
            onClick={onCopy}
            disabled={disabled}
            sx={{
              transition: 'transform 0.2s ease',
              '&:hover': {
                transform: 'scale(1.05)',
                bgcolor: 'neutral.800'
              }
            }}
          >
            <Icon icon="material-symbols:content-copy" />
          </IconButton>
        </Tooltip>
      </Sheet>
      <Sheet sx={{ display: 'flex', gap: 1 }}>
        <Tooltip title="Share Image" variant="soft">
          <IconButton
            variant="plain"
            color="success"
            disabled={disabled}
            sx={{
              transition: 'transform 0.2s ease',
              '&:hover': {
                transform: 'scale(1.05)',
                bgcolor: 'success.800'
              }
            }}
          >
            <Icon icon="material-symbols:share" />
          </IconButton>
        </Tooltip>
        <Tooltip title="More Options" variant="soft">
          <IconButton
            variant="plain"
            color="neutral"
            disabled={disabled}
            sx={{
              transition: 'transform 0.2s ease',
              '&:hover': {
                transform: 'scale(1.05)',
                bgcolor: 'neutral.800'
              }
            }}
          >
            <Icon icon="material-symbols:more-vert" />
          </IconButton>
        </Tooltip>
      </Sheet>
    </Sheet>
  );
} 