import { Sheet, But<PERSON> } from '@mui/joy';
import { Icon } from '@iconify/react';

interface SidebarProps {
  activePage: string;
  onPageChange: (page: string) => void;
}

export function Sidebar({ activePage, onPageChange }: SidebarProps) {
  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'material-symbols:dashboard'
    },
    {
      id: 'image-editor',
      label: 'AI Image Editor',
      icon: 'material-symbols:image'
    },
    {
      id: 'design-studio',
      label: 'Design Studio',
      icon: 'material-symbols:brush'
    }
  ] as const;

  return (
    <Sheet 
      sx={{ 
        width: '280px', 
        borderRight: '1px solid',
        borderColor: 'divider',
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        gap: 1
      }}
    >
      {menuItems.map(({ id, label, icon }) => (
        <Button
          key={id}
          variant={activePage === id ? 'solid' : 'soft'}
          color={activePage === id ? 'primary' : 'neutral'}
          startDecorator={<Icon icon={icon} />}
          onClick={() => onPageChange(id)}
          sx={{ justifyContent: 'flex-start' }}
        >
          {label}
        </Button>
      ))}
    </Sheet>
  );
} 