import { Sheet, Typography, IconButton } from '@mui/joy';
import { Icon } from '@iconify/react';

interface RelatedImage {
  id: string;
  url: string;
  alt: string;
}

interface RelatedImagesProps {
  images: RelatedImage[];
  onSelect: (image: RelatedImage) => void;
}

export function RelatedImages({ images, onSelect }: RelatedImagesProps) {
  return (
    <Sheet 
      sx={{ 
        mt: 3,
        position: 'relative'
      }}
    >
      <Sheet 
        sx={{ 
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 2
        }}
      >
        <Typography level="title-sm" sx={{ color: 'text.secondary' }}>
          Related Images
        </Typography>
        <Sheet sx={{ display: 'flex', gap: 1 }}>
          <IconButton
            size="sm"
            variant="soft"
            color="neutral"
            sx={{ 
              borderRadius: '50%',
              '&:hover': { transform: 'scale(1.05)' },
              transition: 'transform 0.2s ease'
            }}
          >
            <Icon icon="material-symbols:refresh" />
          </IconButton>
          <IconButton
            size="sm"
            variant="soft"
            color="neutral"
            sx={{ 
              borderRadius: '50%',
              '&:hover': { transform: 'scale(1.05)' },
              transition: 'transform 0.2s ease'
            }}
          >
            <Icon icon="material-symbols:grid-view" />
          </IconButton>
        </Sheet>
      </Sheet>

      <Sheet 
        sx={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: 1,
          borderRadius: 'lg',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        {images.map((image) => (
          <Sheet
            key={image.id}
            onClick={() => onSelect(image)}
            sx={{
              aspectRatio: '1',
              cursor: 'pointer',
              overflow: 'hidden',
              borderRadius: 'md',
              position: 'relative',
              '&:hover': {
                '& .image-overlay': {
                  opacity: 1
                },
                '& img': {
                  transform: 'scale(1.05)'
                }
              }
            }}
          >
            <img
              src={image.url}
              alt={image.alt}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                transition: 'transform 0.3s ease'
              }}
            />
            <Sheet
              className="image-overlay"
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: 'rgba(0,0,0,0.4)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                opacity: 0,
                transition: 'opacity 0.2s ease',
                backdropFilter: 'blur(2px)'
              }}
            >
              <IconButton
                variant="soft"
                color="primary"
                size="sm"
                sx={{ 
                  borderRadius: '50%',
                  '&:hover': { transform: 'scale(1.1)' },
                  transition: 'transform 0.2s ease'
                }}
              >
                <Icon icon="material-symbols:add" />
              </IconButton>
            </Sheet>
          </Sheet>
        ))}
      </Sheet>
    </Sheet>
  );
} 