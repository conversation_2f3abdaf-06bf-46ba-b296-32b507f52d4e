import { Sheet, Typography, <PERSON>, <PERSON><PERSON> } from '@mui/joy';
import { Icon } from '@iconify/react';

interface GenerateOptionsProps {
  imageRatio: string;
  imageType: string;
  onRatioChange: (ratio: string) => void;
  onTypeChange: (type: string) => void;
  onGenerate: () => Promise<void>;
  loading: boolean;
  disabled: boolean;
}

export function GenerateOptions({
  imageRatio,
  imageType,
  onRatioChange,
  onTypeChange,
  onGenerate,
  loading,
  disabled
}: GenerateOptionsProps) {
  const ratios = ['4:5', '2:3', '1:1', '4:5'];
  const types = ['3D', 'Anime', 'Realistic'];

  return (
    <Sheet sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography level="body-sm" sx={{ mb: 1 }}>Image Description</Typography>
      <textarea
        className="w-full p-2 rounded-lg bg-[#333] text-white resize-none"
        rows={4}
        placeholder="Describe your image..."
        style={{ border: '1px solid #444' }}
      />

      <Typography level="body-sm" sx={{ mt: 2, mb: 1 }}>Image Ratio</Typography>
      <Sheet sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        {ratios.map((ratio) => (
          <Chip
            key={ratio}
            variant={imageRatio === ratio ? 'solid' : 'soft'}
            color={imageRatio === ratio ? 'primary' : 'neutral'}
            onClick={() => onRatioChange(ratio)}
          >
            {ratio}
          </Chip>
        ))}
      </Sheet>

      <Typography level="body-sm" sx={{ mt: 2, mb: 1 }}>Image Type</Typography>
      <Sheet sx={{ display: 'flex', gap: 2 }}>
        {types.map((type) => (
          <Sheet
            key={type}
            onClick={() => onTypeChange(type)}
            sx={{
              flex: 1,
              p: 1,
              borderRadius: 'lg',
              cursor: 'pointer',
              bgcolor: imageType === type ? 'primary.500' : '#333',
              textAlign: 'center'
            }}
          >
            <Icon 
              icon={
                type === '3D' ? 'material-symbols:3d-rotation' :
                type === 'Anime' ? 'material-symbols:animation' :
                'material-symbols:photo-camera'
              }
              style={{ fontSize: '24px', marginBottom: '4px' }}
            />
            <Typography level="body-sm">{type}</Typography>
          </Sheet>
        ))}
      </Sheet>

      <Button
        fullWidth
        variant="solid"
        color="primary"
        loading={loading}
        onClick={onGenerate}
        disabled={disabled}
        startDecorator={!loading && <Icon icon="material-symbols:auto-awesome" />}
        sx={{ mt: 2 }}
      >
        {loading ? 'Generating...' : 'Generate Image'}
      </Button>
    </Sheet>
  );
} 