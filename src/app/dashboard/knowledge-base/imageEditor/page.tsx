'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";





import { useState, useRef, useCallback } from 'react';
import { Icon } from '@iconify/react';
import { Sheet, Typography, IconButton, Ta<PERSON>, <PERSON>b<PERSON>ist, Tab, TabPanel } from '@mui/joy';
import { ImagePreview } from './components/ImagePreview';
import { ImageAdjustments, ImageAdjustmentsType } from './components/ImageAdjustments';
import { AIEnhancements } from './components/AIEnhancements';
import { GenerateOptions } from './components/GenerateOptions';
import { Sidebar } from './components/Sidebar';
import { RelatedImages } from './components/RelatedImages';

interface RelatedImage {
  id: string;
  url: string;
  alt: string;
}

export default function ImageEditor() {
  const [image, setImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [imageRatio, setImageRatio] = useState('1:1');
  const [imageType, setImageType] = useState('3D');
  const [activePage, setActivePage] = useState('image-editor');
  const [activeTab, setActiveTab] = useState<number>(0);
  const [relatedImages, setRelatedImages] = useState<RelatedImage[]>([]);
  const [adjustments, setAdjustments] = useState<ImageAdjustmentsType>({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
    sharpen: 0
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      setImage(e.target?.result as string);
      setError(null);
      setAdjustments({
        brightness: 100,
        contrast: 100,
        saturation: 100,
        blur: 0,
        sharpen: 0
      });
      // Generate related images
      generateRelatedImages();
    };
    reader.readAsDataURL(file);
  }, []);

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  const handleDownload = useCallback(() => {
    if (image) {
      const link = document.createElement('a');
      link.href = image;
      link.download = 'edited-image.png';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [image]);

  const handleCopy = useCallback(async () => {
    if (image) {
      try {
        const response = await fetch(image);
        const blob = await response.blob();
        await navigator.clipboard.write([
          new ClipboardItem({
            [blob.type]: blob
          })
        ]);
      } catch (error) {
        console.error('Failed to copy image:', error);
      }
    }
  }, [image]);

  const handleAIEnhance = async (enhancementType?: string) => {
    if (!image) return;
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(image);
      const blob = await response.blob();
      const formData = new FormData();
      formData.append('image', blob);
      formData.append('enhancementType', enhancementType || 'general');
      formData.append('adjustments', JSON.stringify(adjustments));

      const result = await fetch('/api/image-enhance', {
        method: 'POST',
        body: formData,
      });

      if (!result.ok) {
        throw new Error('Failed to enhance image');
      }

      const data = await result.json();

      if (data.success && data.image) {
        setImage(`data:image/png;base64,${data.image}`);
        // Generate new related images after enhancement
        generateRelatedImages();
      } else {
        throw new Error(data.error || 'Failed to enhance image');
      }
    } catch (error) {
      console.error('AI Enhancement failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to enhance image');
    } finally {
      setLoading(false);
    }
  };

  const generateRelatedImages = useCallback(async () => {
    // Simulated related images - replace with actual API call
    setRelatedImages([
      { id: '1', url: '/path/to/related1.jpg', alt: 'Related 1' },
      { id: '2', url: '/path/to/related2.jpg', alt: 'Related 2' },
      { id: '3', url: '/path/to/related3.jpg', alt: 'Related 3' },
    ]);
  }, []);

  const handleAdjustmentChange = (type: keyof ImageAdjustmentsType, value: number) => {
    setAdjustments(prev => ({
      ...prev,
      [type]: value
    }));
  };

  const getImageStyle = () => {
    return {
      maxWidth: '100%',
      maxHeight: '100%',
      objectFit: 'contain' as const,
      transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
      transition: 'transform 0.3s ease',
      filter: `
        brightness(${adjustments.brightness}%) 
        contrast(${adjustments.contrast}%) 
        saturate(${adjustments.saturation}%)
        blur(${adjustments.blur}px)
        ${adjustments.sharpen > 0 ? `sharpen(${adjustments.sharpen}px)` : ''}
      `
    };
  };

  return (
    <Sheet 
        variant="soft" 
        sx={{ 
          height: '100%',
          mx: 'auto', 
          overflow: 'hidden',
        }}
      >
        {/* Header */}
        <Sheet 
          sx={{ 
            p: 2, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Typography level="h3" sx={{ color: 'white' }}>
            AI Image Editor
          </Typography>
          <Sheet sx={{ display: 'flex', gap: 1 }}>
            <IconButton variant="soft" color="neutral">
              <Icon icon="material-symbols:settings" />
            </IconButton>
            <IconButton variant="soft" color="neutral">
              <Icon icon="material-symbols:help-outline" />
            </IconButton>
          </Sheet>
        </Sheet>

        <Sheet sx={{ display: 'flex', height: 'calc(100vh - 200px)' }}>
          {/* Sidebar */}
          <Sidebar activePage={activePage} onPageChange={setActivePage} />

          {/* Main Content */}
          <Sheet sx={{ flex: 1, p: 0, display: 'flex', gap: 3 }}>
            {/* Image Preview Area */}
            <Sheet 
              sx={{ 
                flex: 2,
                overflow: 'hidden',
                bgcolor: '#2A2A2A',
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                p: 2
              }}
            >
              <ImagePreview 
                image={image} 
                style={getImageStyle()} 
                onImageUpload={handleImageUpload}
                onDownload={handleDownload}
                onCopy={handleCopy}
              />
              {image && <RelatedImages images={relatedImages} onSelect={(img) => setImage(img.url)} />}
            </Sheet>

            {/* Right Settings Panel */}
            <Sheet 
              sx={{ 
                width: '320px',
                borderRadius: 'lg',
                bgcolor: '#2A2A2A',
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                overflow: 'auto'
              }}
            >
              <Tabs 
                value={activeTab} 
                onChange={(_, value) => setActiveTab(value as number)}
              >
                <TabList>
                  <Tab value={0}>Enhance</Tab>
                  <Tab value={1}>Adjust</Tab>
                  <Tab value={2}>Generate</Tab>
                </TabList>

                <TabPanel value={0}>
                  <AIEnhancements onEnhance={handleAIEnhance} loading={loading} />
                </TabPanel>

                <TabPanel value={1}>
                  <ImageAdjustments 
                    adjustments={adjustments}
                    onAdjustmentChange={handleAdjustmentChange}
                  />
                </TabPanel>

                <TabPanel value={2}>
                  <GenerateOptions
                    imageRatio={imageRatio}
                    imageType={imageType}
                    onRatioChange={setImageRatio}
                    onTypeChange={setImageType}
                    onGenerate={() => handleAIEnhance()}
                    loading={loading}
                    disabled={!image || loading}
                  />
                </TabPanel>
              </Tabs>
            </Sheet>
          </Sheet>
        </Sheet>
      </Sheet>
  );
}
