import { DocsLayout } from "@/components/knowledge/api-docs/components/docs-layout"
import { TableOfContents } from "@/components/knowledge/api-docs/components/table-of-contents"
import { AutoGeneratedDocs } from "@/components/knowledge/api-docs/components/auto-generated-docs"
import { DocNode } from "@/components/knowledge/api-docs/lib/docs-scanner"
import Box from "@mui/joy/Box"
import Grid from "@mui/joy/Grid"
import Card from "@mui/joy/Card"
import Typography from "@mui/joy/Typography"
import Tabs from "@mui/joy/Tabs"
import TabList from "@mui/joy/TabList"
import Tab from "@mui/joy/Tab"
import TabPanel from "@mui/joy/TabPanel"
import Chip from "@mui/joy/Chip"
import Stack from "@mui/joy/Stack"
import { CodeBlock } from "@/components/knowledge/api-docs/components/code-block"
import { ApiEndpoint } from "@/components/knowledge/api-docs/components/api-endpoint"
import { Callout } from "@/components/knowledge/api-docs/components/callout"
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import LinkIcon from '@mui/icons-material/Link'
import AutoGraphIcon from '@mui/icons-material/AutoGraph'
import fs from 'fs'
import path from 'path'

// API documentation structure in proper format for DocsLayout
const apiDocNavigation: DocNode[] = [
  {
    title: "Getting Started",
    isDirectory: true,
    path: "getting-started",
    children: [
      { title: "Introduction", path: "#introduction", isDirectory: false, children: [] },
      { title: "Authentication", path: "#authentication", isDirectory: false, children: [] },
      { title: "Rate Limits", path: "#rate-limits", isDirectory: false, children: [] },
      { title: "Versioning", path: "#versioning", isDirectory: false, children: [] },
    ],
  },
  {
    title: "Core Resources",
    isDirectory: true,
    path: "core-resources",
    children: [
      { title: "Collections", path: "#collections", isDirectory: false, children: [] },
      { title: "Admissions", path: "#admissions", isDirectory: false, children: [] },
      { title: "Referrals", path: "#referrals", isDirectory: false, children: [] },
      { title: "Releases", path: "#releases", isDirectory: false, children: [] },
      { title: "Body Management", path: "#body-management", isDirectory: false, children: [] },
    ],
  },
  {
    title: "Advanced Features",
    isDirectory: true,
    path: "advanced-features",
    children: [
      { title: "Dashboards", path: "#dashboards", isDirectory: false, children: [] },
      { title: "Notifications", path: "#notifications", isDirectory: false, children: [] },
      { title: "Reporting", path: "#reporting", isDirectory: false, children: [] },
    ],
  },
  {
    title: "Developer Tools",
    isDirectory: true,
    path: "developer-tools",
    children: [
      { title: "API Routes", path: "#api-routes", isDirectory: false, children: [] },
      { title: "Webhooks", path: "#webhooks", isDirectory: false, children: [] },
      { title: "SDK Libraries", path: "#sdk-libraries", isDirectory: false, children: [] },
    ],
  },
]

// API Categories for tabs with enhanced metadata
const apiCategories = [
  { 
    id: "overview", 
    label: "Overview", 
    file: "api",
    icon: <InfoOutlinedIcon />,
    description: "General API information and authentication"
  },
  { 
    id: "collections", 
    label: "Collections", 
    file: "api/collections",
    icon: <AutoGraphIcon />,
    description: "Endpoints for managing body collections"
  },
  { 
    id: "admissions", 
    label: "Admissions", 
    file: "api/admissions",
    icon: <LinkIcon />,
    description: "Managing facility admissions"
  },
  { 
    id: "referrals", 
    label: "Referrals", 
    file: "api/referrals",
    icon: <AutoGraphIcon />,
    description: "Handle case referrals between facilities"
  },
  { 
    id: "releases", 
    label: "Releases", 
    file: "api/releases",
    icon: <LinkIcon />,
    description: "Manage body releases and transfers"
  },
  { 
    id: "dashboards", 
    label: "Dashboards", 
    file: "api/dashboards",
    icon: <AutoGraphIcon />,
    description: "Dashboard data and configuration"
  },
]

// Function to get main API documentation
async function getApiDocumentation() {
  const apiDocPath = path.join(process.cwd(), 'docs', 'api.md')
  try {
    const content = await fs.promises.readFile(apiDocPath, 'utf8')
    return content
  } catch (error) {
    console.error("Error reading API documentation:", error)
    return "# API Documentation\n\nDocumentation not available at this time."
  }
}

// Generate table of contents from the API navigation
function generateTableOfContents() {
  let tocContent = "# API Documentation\n\n"
  
  apiDocNavigation.forEach(section => {
    tocContent += `## ${section.title}\n\n`
    
    section.children.forEach(item => {
      tocContent += `- [${item.title}](${item.path})\n`
    })
    
    tocContent += "\n"
  })
  
  return tocContent
}

// Authentication example code
const authenticationCode = `
// API Key Authentication
const response = await fetch('https://api.pathology.example/v1/collections', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

// Get the data
const data = await response.json();
`;

// Example request
const exampleRequest = `
// Create a new collection
const response = await fetch('https://api.pathology.example/v1/collections', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: "Collection #12345",
    collectionDate: "2023-06-15T10:30:00Z",
    locationId: "loc_15f8c94d",
    notes: "Routine collection"
  })
});

const result = await response.json();
console.log(result.id); // "col_8f7d3a2e1c5b9"
`;

export default async function APIDocumentationPage() {
  const apiDocContent = await getApiDocumentation()
  const tocContent = generateTableOfContents()
  
  return (
    <DocsLayout 
      title="API Documentation" 
      navigation={apiDocNavigation}
      basePath="/dashboard/knowledge-base/api-documentation"
    >
      <Box sx={{ maxWidth: "1100px", mx: 'auto' }}>
        <Stack spacing={2} sx={{ mb: 5 }}>
          <Typography
            level="h1" 
            component="h1" 
            sx={{ 
              fontSize: { xs: '2rem', md: '2.5rem' },
              fontWeight: 'bold',
              background: 'linear-gradient(90deg, var(--joy-palette-primary-600) 0%, var(--joy-palette-primary-400) 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1
            }}
          >
            API Documentation
          </Typography>
          
          <Typography level="body-lg" sx={{ color: 'text.secondary', maxWidth: '800px' }}>
            Access and integrate with the GP Pathology system through our RESTful API. 
            Our comprehensive API allows you to manage collections, admissions, referrals, and more.
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
            <Chip color="primary" size="lg" variant="soft">v1.0</Chip>
            <Chip color="success" size="lg" variant="soft">REST</Chip>
            <Chip color="neutral" size="lg" variant="soft">JSON</Chip>
          </Box>
        </Stack>

        <Tabs 
          defaultValue="manual" 
          sx={{ 
            mb: 4,
            '--Tabs-gap': '0px',
          }}
        >
          <TabList 
            variant="outlined" 
            sx={{ 
              p: 0.5,
              borderRadius: 'xl',
              bgcolor: 'background.level1',
              mb: 4,
              gap: 0.5,
              '& .MuiTab-root': {
                borderRadius: 'lg',
                py: 1,
                px: 2,
                fontWeight: 'md',
                transition: 'all 0.2s ease',
                '&:hover': {
                  bgcolor: 'background.level2',
                },
                '&.Mui-selected': {
                  bgcolor: 'background.surface',
                  color: 'primary.600',
                  fontWeight: 'lg',
                  boxShadow: 'sm',
                }
              }
            }}
          >
            <Tab 
              value="manual" 
            >
              Documentation
            </Tab>
            <Tab 
              value="auto" 
            >
              API Routes
            </Tab>
          </TabList>

          <TabPanel value="manual">
            <Grid container spacing={4}>
              <Grid xs={12} md={8}>
                {/* Main Content Section */}
                <Box id="introduction">
                  <Typography level="h2" component="h2" sx={{ mb: 3, scrollMarginTop: '100px' }}>
                    Introduction
                  </Typography>
                  
                  <Typography level="body-md" sx={{ mb: 2 }}>
                    The GP Pathology API provides programmatic access to the pathology system. 
                    You can manage collections, admissions, referrals, releases, and more.
                    The API follows RESTful design principles and uses standard HTTP methods.
                  </Typography>
                  
                  <Card 
                    variant="outlined" 
                    sx={{ 
                      mb: 4, 
                      borderRadius: 'lg',
                      p: 3,
                      bgcolor: 'background.level1'
                    }}
                  >
                    <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
                      <Box sx={{ flex: 1 }}>
                        <Typography level="title-md" sx={{ mb: 1 }}>Base URL</Typography>
                        <CodeBlock language="bash">https://api.pathology.example/v1</CodeBlock>
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography level="title-md" sx={{ mb: 1 }}>Content Type</Typography>
                        <CodeBlock language="bash">Content-Type: application/json</CodeBlock>
                      </Box>
                    </Stack>
                  </Card>
                </Box>
                
                <Box id="authentication" sx={{ mt: 6, scrollMarginTop: '100px' }}>
                  <Typography level="h2" component="h2" sx={{ mb: 3 }}>
                    Authentication
                  </Typography>
                  
                  <Typography level="body-md" sx={{ mb: 2 }}>
                    All API requests require authentication using API keys. To authenticate, provide
                    your API key in the Authorization header as a Bearer token.
                  </Typography>
                  
                  <Callout type="info" title="API Key Security">
                    Keep your API keys secure and never share them in public repositories or client-side code.
                    If you believe your API key has been compromised, contact support immediately.
                  </Callout>
                  
                  <Box sx={{ mt: 3 }}>
                    <Typography level="title-md" sx={{ mb: 2 }}>Authentication Example</Typography>
                    <CodeBlock language="javascript">{authenticationCode}</CodeBlock>
                  </Box>
                </Box>
                
                <Box id="rate-limits" sx={{ mt: 6, scrollMarginTop: '100px' }}>
                  <Typography level="h2" component="h2" sx={{ mb: 3 }}>
                    Rate Limits
                  </Typography>
                  
                  <Typography level="body-md" sx={{ mb: 2 }}>
                    To ensure a reliable service for all users, the API implements rate limiting.
                    By default, each API key is limited to 100 requests per minute.
                  </Typography>
                  
                  <Box sx={{ mb: 3 }}>
                    <ApiEndpoint
                      method="GET"
                      path="/rate-limits"
                      description="Get your current rate limit status"
                      parameters={[
                        { name: "detailed", type: "boolean", description: "Whether to include detailed statistics" }
                      ]}
                      responses={[
                        { status: 200, description: "Rate limit details" },
                        { status: 401, description: "Unauthorized - Invalid API key" }
                      ]}
                    />
                  </Box>
                  
                  <Callout type="warning" title="Rate Limit Headers">
                    Each response includes headers with rate limit information:
                    <ul>
                      <li><code>X-RateLimit-Limit</code>: Maximum requests per minute</li>
                      <li><code>X-RateLimit-Remaining</code>: Remaining requests in the current window</li>
                      <li><code>X-RateLimit-Reset</code>: Time in seconds until the limit resets</li>
                    </ul>
                  </Callout>
                </Box>
                
                <Box id="collections" sx={{ mt: 6, scrollMarginTop: '100px' }}>
                  <Typography level="h2" component="h2" sx={{ mb: 3 }}>
                    Collections API
                  </Typography>
                  
                  <Typography level="body-md" sx={{ mb: 3 }}>
                    The Collections API allows you to manage body collections, including creating
                    new collections, updating collection details, and retrieving collection information.
                  </Typography>
                  
                  <Box sx={{ mb: 3 }}>
                    <ApiEndpoint
                      method="GET"
                      path="/collections"
                      description="List all collections"
                      parameters={[
                        { name: "limit", type: "integer", description: "Maximum number of results to return" },
                        { name: "offset", type: "integer", description: "Number of results to skip" },
                        { name: "status", type: "string", description: "Filter by status" }
                      ]}
                      responses={[
                        { status: 200, description: "List of collections" },
                        { status: 401, description: "Unauthorized" }
                      ]}
                    />
                  </Box>
                  
                  <Box sx={{ mb: 3 }}>
                    <ApiEndpoint
                      method="POST"
                      path="/collections"
                      description="Create a new collection"
                      parameters={[
                        { name: "name", type: "string", required: true, description: "Collection name" },
                        { name: "collectionDate", type: "string (ISO 8601)", required: true, description: "Date of collection" },
                        { name: "locationId", type: "string", required: true, description: "Location ID" },
                        { name: "notes", type: "string", description: "Additional notes" }
                      ]}
                      responses={[
                        { status: 201, description: "Collection created" },
                        { status: 400, description: "Invalid request data" },
                        { status: 401, description: "Unauthorized" }
                      ]}
                    />
                  </Box>
                  
                  <Typography level="title-md" sx={{ mt: 4, mb: 2 }}>Example Request</Typography>
                  <CodeBlock language="javascript">{exampleRequest}</CodeBlock>
                </Box>
                
                {/* Remaining sections would go here */}
              </Grid>
              
              <Grid xs={12} md={4}>
                {/* Right sidebar with TOC and quick links */}
                <Box sx={{ 
                  position: 'sticky', 
                  top: '100px', 
                  width: '100%'
                }}>
                  <Card 
                    variant="outlined" 
                    sx={{ 
                      p: 2, 
                      borderRadius: 'lg', 
                      mb: 3,
                      boxShadow: 'sm'
                    }}
                  >
                    <Typography level="title-lg" sx={{ mb: 2 }}>On This Page</Typography>
                    <TableOfContents content={tocContent} />
                  </Card>
                  
                  <Card 
                    variant="outlined" 
                    sx={{ 
                      p: 2, 
                      borderRadius: 'lg',
                      bgcolor: 'background.level1',
                      boxShadow: 'sm'
                    }}
                  >
                    <Typography level="title-lg" sx={{ mb: 2 }}>API Resources</Typography>
                    <Stack spacing={1}>
                      {apiCategories.map((category) => (
                        <Box
                          key={category.id}
                          component="a"
                          href={`#${category.id}`}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1.5,
                            p: 1.5,
                            borderRadius: 'md',
                            color: 'text.primary',
                            textDecoration: 'none',
                            transition: 'all 0.2s',
                            '&:hover': {
                              bgcolor: 'background.level2',
                              transform: 'translateX(4px)'
                            }
                          }}
                        >
                          <Box sx={{ 
                            color: 'primary.500',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            {category.icon}
                          </Box>
                          <Box>
                            <Typography level="title-sm">{category.label}</Typography>
                            <Typography level="body-xs" sx={{ color: 'text.secondary' }}>
                              {category.description}
                            </Typography>
                          </Box>
                        </Box>
                      ))}
                    </Stack>
                  </Card>
                </Box>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value="auto">
            <Box id="api-routes">
              <AutoGeneratedDocs />
            </Box>
          </TabPanel>
        </Tabs>
      </Box>
    </DocsLayout>
  )
}