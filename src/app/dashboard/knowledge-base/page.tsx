import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { KnowledgeSearch } from "@/components/knowledge/knowledge-search";
import { KnowledgeList } from "@/components/knowledge/knowledge-list";
import { Box, Typography, Container } from "@mui/joy";

export default async function KnowledgeBasePage() {
  const session = await auth();

  if (!session) {
    redirect("/auth/signin");
  }

  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <Box>
          <Typography level="h1" sx={{ fontWeight: 'bold', letterSpacing: 'tight' }}>
            Knowledge Base
          </Typography>
          <Typography level="body-md" sx={{ mt: 1, color: 'text.secondary' }}>
            Access system documentation, guides, and best practices
          </Typography>
        </Box>
        
        <KnowledgeSearch />
        <KnowledgeList />
      </Box>
    </Container>
  );
}
