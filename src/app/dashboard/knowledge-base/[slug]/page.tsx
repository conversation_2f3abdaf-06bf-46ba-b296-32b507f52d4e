"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState, useEffect, useRef } from "react";
import { Box, Typography, IconButton, Tooltip, Stack, CircularProgress, Alert, LinearProgress, Grid, Input } from "@mui/joy";
import { VolumeUp as VolumeUpIcon, MenuBook as MenuBookIcon, ZoomIn as ZoomInIcon, ZoomOut as ZoomOutIcon, Fullscreen as FullscreenIcon, FullscreenExit as FullscreenExitIcon, Print as PrintIcon, ContentCopy as ContentCopyIcon, Search as SearchIcon, DarkMode as DarkModeIcon, LightMode as LightModeIcon } from "@mui/icons-material";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { KnowledgeAIChatbot } from "@/components/AI/KnowledgeAIChatbot";
import { useParams } from "next/navigation";
import MermaidProcessor from "@/components/MermaidProcessor";

interface TableOfContentsItem {
  title: string;
  level: number;
  id: string;
}

interface Document {
  id: string;
  title: string;
  content: string;
  author?: string;
  category?: string;
  readingTime?: number;
  tableOfContents?: TableOfContentsItem[];
  audioUrl?: string;
  metadata?: {
    title?: string;
  };
  slug?: string;
}

// Fix for TypeScript errors with Document API
interface FullscreenDocument extends Document {
  querySelector(arg0: string): unknown;
  fullscreenElement: Element | null;
  exitFullscreen: () => Promise<void>;
}

export default function KnowledgeBasePage() {
  const params = useParams();
  const [document, setDocument] = useState<Document | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [scale, setScale] = useState(1.0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const contentRef = useRef<HTMLDivElement | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showTOC, setShowTOC] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [selectedText, setSelectedText] = useState("");

  useEffect(() => {
    const fetchDocument = async () => {
      if (!params.slug) return;
      try {
        setLoading(true);
        const response = await fetch(`/api/knowledge-base/${params.slug}`);
        if (!response.ok) throw new Error("Failed to fetch document");
        const doc = await response.json();
        setDocument(doc);
      } catch (error) {
        console.error("Error fetching document:", error);
        setError("Failed to load document");
      } finally {
        setLoading(false);
      }
    };
    fetchDocument();
  }, [params.slug]);

  useEffect(() => {
    if (document?.content && contentRef.current) {
      const headings = contentRef.current.querySelectorAll("h1, h2, h3, h4, h5, h6");
      const toc = Array.from(headings).map((heading) => ({
        title: heading.textContent || "",
        level: parseInt(heading.tagName[1]),
        id: heading.id,
      }));
      setDocument((prev) => (prev ? { ...prev, tableOfContents: toc } : null));
    }
  }, [document?.content]);

  useEffect(() => {
    const handleScroll = () => {
      if (contentRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
        const progress = (scrollTop / (scrollHeight - clientHeight)) * 100;
        setReadingProgress(progress);
      }
    };
    const currentRef = contentRef.current;
    if (currentRef) {
      currentRef.addEventListener("scroll", handleScroll);
      return () => currentRef.removeEventListener("scroll", handleScroll);
    }
  }, []);

  const handleTTS = async (text: string = document?.content || "") => {
    if (!text || !audioRef.current) return;
    try {
      setIsPlaying(true);
      setLoading(true);
      setError(null);

      const response = await fetch('/api/tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate audio');
      }

      const audioBlob = await response.blob();
      const url = URL.createObjectURL(audioBlob);

      if (audioRef.current) {
        audioRef.current.src = url;
        await audioRef.current.play();
        audioRef.current.ontimeupdate = () => {
          if (audioRef.current) {
            setProgress((audioRef.current.currentTime / audioRef.current.duration) * 100);
          }
        };
        audioRef.current.onended = () => {
          setIsPlaying(false);
          setProgress(0);
          URL.revokeObjectURL(url);
        };
      }
    } catch (error) {
      setError("Error generating audio. Please try again.");
      console.error("Error generating TTS:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleZoomIn = () => setScale((prev) => Math.min(prev + 0.1, 2.0));
  const handleZoomOut = () => setScale((prev) => Math.max(prev - 0.1, 0.5));
  const toggleFullscreen = () => {
    const doc = getDocument() as FullscreenDocument | undefined;
    if (!doc) return;
    
    if (!doc.fullscreenElement && contentRef.current) {
      contentRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else if (doc.exitFullscreen) {
      doc.exitFullscreen();
      setIsFullscreen(false);
    }
  };
  const handlePrint = () => window.print();
  const handleCopyContent = async () => {
    if (!document?.content) return;
    try {
      await navigator.clipboard.writeText(document.content);
      // You could add a toast notification here
    } catch (err) {
      console.error("Failed to copy content:", err);
    }
  };
  const handleSearch = () => {
    if (!searchTerm || !contentRef.current) return;
    const content = contentRef.current.innerHTML;
    const regex = new RegExp(searchTerm, 'gi');
    const highlightedContent = content.replace(regex, (match) => `<mark>${match}</mark>`);
    contentRef.current.innerHTML = highlightedContent;
  };
  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection) {
      const text = selection.toString().trim();
      if (text) setSelectedText(text);
    }
  };
  const toggleDarkMode = () => setIsDarkMode(!isDarkMode);
  const toggleTOC = () => setShowTOC(!showTOC);

  // Fix for getElementById
  const getDocument = (): FullscreenDocument | undefined => {
    return typeof document !== 'undefined' ? document as FullscreenDocument : undefined;
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100vh" }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert color="danger">{error}</Alert>
      </Box>
    );
  }

  if (!document) return null;

  return (
    <Box sx={{ height: "100vh", display: "flex", flexDirection: "column", overflow: "hidden" }}>
      <Box sx={{ p: 2, borderBottom: "1px solid", borderColor: "divider" }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Typography level="h4">{document.metadata?.title || document.slug}</Typography>
          <Tooltip title="Text to Speech">
            <IconButton onClick={() => handleTTS()} disabled={isPlaying}>
              <VolumeUpIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom In"><IconButton onClick={handleZoomIn}><ZoomInIcon /></IconButton></Tooltip>
          <Tooltip title="Zoom Out"><IconButton onClick={handleZoomOut}><ZoomOutIcon /></IconButton></Tooltip>
          <Tooltip title="Toggle Fullscreen">
            <IconButton onClick={toggleFullscreen}>
              {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Print"><IconButton onClick={handlePrint}><PrintIcon /></IconButton></Tooltip>
          <Tooltip title="Copy Content"><IconButton onClick={handleCopyContent}><ContentCopyIcon /></IconButton></Tooltip>
          <Tooltip title="Toggle Dark Mode">
            <IconButton onClick={toggleDarkMode}>
              {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Toggle Table of Contents">
            <IconButton onClick={toggleTOC}><MenuBookIcon /></IconButton>
          </Tooltip>
          <Input
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            endDecorator={<IconButton onClick={handleSearch}><SearchIcon /></IconButton>}
          />
        </Stack>
      </Box>

      <Grid container spacing={2} sx={{ flexGrow: 1, overflow: "hidden" }}>
        <Grid xs={12} md={showTOC ? 9 : 12} sx={{ height: "100%", overflow: "hidden" }}>
          <Box
            ref={contentRef}
            sx={{
              height: "100%",
              overflow: "auto",
              p: 3,
              transform: `scale(${scale})`,
              transformOrigin: "top left",
              bgcolor: isDarkMode ? "background.level1" : "background.surface",
              color: isDarkMode ? "text.primary" : "text.primary",
            }}
            className={isDarkMode ? "dark-mode" : ""}
          >
            <Box onMouseUp={handleTextSelection}>
              <MermaidProcessor isDarkMode={isDarkMode}>
                <ReactMarkdown 
                  remarkPlugins={[remarkGfm]}
                >
                  {document.content}
                </ReactMarkdown>
              </MermaidProcessor>
            </Box>
          </Box>
        </Grid>

        {showTOC && (
          <Grid xs={12} md={3} sx={{ borderLeft: "1px solid", borderColor: "divider", overflowY: "auto" }}>
            <Box sx={{ p: 2 }}>
              <Typography level="title-sm" sx={{ mb: 1 }}>Table of Contents</Typography>
              {document.tableOfContents?.map((item, index) => (
                <Typography
                  key={index}
                  level="body-sm"
                  sx={{
                    pl: `${(item.level - 1) * 16}px`,
                    cursor: "pointer",
                    "&:hover": { color: "primary.main" },
                  }}
                  onClick={() => {
                    const doc = getDocument();
                    if (!doc) return;
                    const element = doc.querySelector(`#${item.id}`) as HTMLElement;
                    element?.scrollIntoView({ behavior: "smooth" });
                  }}
                >
                  {item.title}
                </Typography>
              ))}
            </Box>
          </Grid>
        )}
      </Grid>

      <Box sx={{ p: 2, borderTop: "1px solid", borderColor: "divider" }}>
        <LinearProgress variant="outlined" value={readingProgress} />
      </Box>

      <audio ref={audioRef} style={{ display: "none" }} />

      <Box sx={{ position: 'fixed', bottom: 20, right: 20, zIndex: 1000, maxWidth: '400px', width: '100%' }}>
        <KnowledgeAIChatbot
          context={{
            documentContent: document?.content || "",
            documentTitle: document?.metadata?.title || document?.slug || "",
            selectedText,
            onPlayTTS: handleTTS
          }}
        />
      </Box>
    </Box>
  );
}
