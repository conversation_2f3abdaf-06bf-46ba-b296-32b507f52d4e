"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  FormLabel,
  Grid,
  Input,
  Stack,
  Typography,
  Textarea,
} from '@mui/joy';
import { Icon } from '@iconify/react';

export default function XRayReferralPage() {
  const [formData, setFormData] = useState({
    persalNumber: '',
    employeeName: '',
    institution: '',
    bodyTagNumber: '',
    specimenKitNumber: '',
    referralReason: '',
    vehicleRegistration: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log(formData);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Card>
        <CardContent>
          <Typography level="title-lg" mb={2}>
            X-Ray / Lodox Referral Form
          </Typography>
          
          <form onSubmit={handleSubmit}>
            <Grid container spacing={2}>
              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>PERSAL Number</FormLabel>
                  <Input
                    value={formData.persalNumber}
                    onChange={(e) => setFormData({...formData, persalNumber: e.target.value})}
                    required
                  />
                </FormControl>
              </Grid>
              
              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Employee Name</FormLabel>
                  <Input
                    value={formData.employeeName}
                    onChange={(e) => setFormData({...formData, employeeName: e.target.value})}
                    required
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Referral Institution</FormLabel>
                  <Input
                    value={formData.institution}
                    onChange={(e) => setFormData({...formData, institution: e.target.value})}
                    required
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Body Tag Number</FormLabel>
                  <Input
                    value={formData.bodyTagNumber}
                    onChange={(e) => setFormData({...formData, bodyTagNumber: e.target.value})}
                    required
                  />
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <FormControl>
                  <FormLabel>Referral Reason</FormLabel>
                  <Textarea
                    minRows={3}
                    value={formData.referralReason}
                    onChange={(e) => setFormData({...formData, referralReason: e.target.value})}
                    required
                  />
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <Stack direction="row" spacing={2} justifyContent="flex-end">
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:refresh" />}
                    onClick={() => setFormData({
                      persalNumber: '',
                      employeeName: '',
                      institution: '',
                      bodyTagNumber: '',
                      specimenKitNumber: '',
                      referralReason: '',
                      vehicleRegistration: '',
                    })}
                  >
                    Reset
                  </Button>
                  <Button
                    type="submit"
                    startDecorator={<Icon icon="mdi:content-save" />}
                  >
                    Submit Referral
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
} 