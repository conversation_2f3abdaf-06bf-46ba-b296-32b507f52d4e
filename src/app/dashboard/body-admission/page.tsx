"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { Box } from '@mui/joy';
import BodyAdmissionForm from '@/components/Forms/BodyAdmissionForm';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { redirect } from 'next/navigation';
import { UserRole } from '@prisma/client';

export default async function BodyAdmissionPage() {
  const session = await auth();
  if (!session) {
    redirect('/login');
  }

  const bodies = await prisma.body.findMany({
    select: { 
      id: true, 
      bodyTag: {
        select: {
          tagNumber: true
        }
      }
    }
  }).then(bodies => bodies.map(body => ({
    id: body.id,
    bodyTag: body.bodyTag.tagNumber
  })));

  const users = await prisma.user.findMany({
    where: {
      OR: [
        { role: UserRole.MORGUE_STAFF },
        { role: UserRole.ADMIN }
      ]
    },
    select: { 
      id: true, 
      name: true 
    }
  });

  const userRole = session.user.role === UserRole.MORGUE_STAFF || session.user.role === UserRole.ADMIN 
    ? session.user.role 
    : 'MORGUE_STAFF';

  return (
    <Box sx={{ 
      p: 3,
      height: '100%',
      overflow: 'auto',
      backgroundColor: 'background.level1'
    }}>
      <BodyAdmissionForm 
        bodies={bodies} 
        users={users} 
        userRole={userRole} 
      />
    </Box>
  );
} 