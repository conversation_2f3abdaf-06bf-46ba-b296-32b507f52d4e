"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { Box, Card, Grid, Typography, Button, Stack } from '@mui/joy';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { prisma } from '@/lib/prisma';

export default function RecordsPage() {
  const router = useRouter();
  const [stats, setStats] = useState({
    collections: 0,
    admissions: 0,
    releases: 0
  });

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const [collections, admissions, releases] = await Promise.all([
        prisma.bodyCollection.findMany(),
        prisma.bodyAdmission.findMany(),
        prisma.bodyRelease.findMany()
      ]);

      setStats({
        collections: collections.length,
        admissions: admissions.length,
        releases: releases.length
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const records = [
    {
      title: 'Collections',
      count: stats.collections,
      icon: 'mdi:clipboard-text',
      color: 'primary',
      path: '/dashboard/records/collections'
    },
    {
      title: 'Admissions',
      count: stats.admissions,
      icon: 'mdi:hospital-box',
      color: 'success',
      path: '/dashboard/records/admissions'
    },
    {
      title: 'Releases',
      count: stats.releases,
      icon: 'mdi:exit-run',
      color: 'warning',
      path: '/dashboard/records/releases'
    }
  ];

  return (
    <Box sx={{ p: 3, height: '100%' }}>
      <Typography level="h4" component="h1" sx={{ mb: 3 }}>
        Records Overview
      </Typography>

      <Grid container spacing={3}>
        {records.map((record) => (
          <Grid key={record.title} xs={12} md={4}>
            <Card>
              <Stack direction="row" spacing={2} alignItems="center">
                <Box
                  sx={{
                    p: 2,
                    borderRadius: 'sm',
                    bgcolor: `${record.color}.softBg`
                  }}
                >
                  <Icon 
                    icon={record.icon} 
                    width={24} 
                    height={24}
                    style={{ color: `var(--joy-palette-${record.color}-500)` }}
                  />
                </Box>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography level="body-sm">{record.title}</Typography>
                  <Typography level="h4">{record.count}</Typography>
                </Box>
                <Button
                  variant="outlined"
                  color={record.color as any}
                  onClick={() => router.push(record.path)}
                >
                  View
                </Button>
              </Stack>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
} 