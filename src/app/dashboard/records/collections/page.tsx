"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React from "react";
import {
  <PERSON>,
  But<PERSON>,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal,
  ModalDialog,
  Card,
  CardContent,
  Grid,
  Chip,
  Sheet,
  Table,
  CircularProgress,
  IconButton,
  Divider,
  Input,
  Select,
  Option,
  Checkbox,
  Link as MUILink
} from "@mui/joy";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { CollectionRecord } from "@/types/records";
import { format, formatDistanceToNow } from "date-fns";
import { AnimatePresence, motion } from "framer-motion";
import { BodyStatus } from "@prisma/client";
import MiniBarcode from "@/components/Barcode/MiniBarcode";

export default function CollectionsPage() {
  const router = useRouter();
  const [collections, setCollections] = React.useState<CollectionRecord[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [deleteModal, setDeleteModal] = React.useState(false);
  const [selectedCollection, setSelectedCollection] = React.useState<string | null>(null);
  const [activeTab, setActiveTab] = React.useState(0);
  const [view, setView] = React.useState<"grid" | "table">("grid");
  const [selectedCollections, setSelectedCollections] = React.useState<string[]>([]);
  const [filterStatus, setFilterStatus] = React.useState<BodyStatus | null>(null);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [showSearch, setShowSearch] = React.useState(false);
  const [sortConfig, setSortConfig] = React.useState<{
    key: keyof CollectionRecord;
    direction: "asc" | "desc";
  }>({
    key: "createdAt",
    direction: "desc",
  });

  // Load collections
  const loadCollections = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/collections');
      if (!response.ok) throw new Error('Failed to fetch collections');
      const data = await response.json();
      setCollections(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    loadCollections();
  }, []);

  // Filter and sort collections
  const filteredAndSortedCollections = React.useMemo(() => {
    return collections
      .filter((collection) => {
        // Status filter
        if (filterStatus && collection.status !== filterStatus) {
          return false;
        }

        // Search query
        if (searchQuery) {
          const searchLower = searchQuery.toLowerCase();
          return (
            collection.bodyTag?.toLowerCase().includes(searchLower) ||
            collection.name?.toLowerCase().includes(searchLower) ||
            collection.institution?.toLowerCase().includes(searchLower) ||
            collection.vehicleReg?.toLowerCase().includes(searchLower)
          );
        }

        return true;
      })
      .sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (aValue < bValue) return sortConfig.direction === "asc" ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === "asc" ? 1 : -1;
        return 0;
      });
  }, [collections, filterStatus, searchQuery, sortConfig]);

  // Handle delete
  const handleDelete = async () => {
    if (!selectedCollection) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/collections/${selectedCollection}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete collection");
      }

      await loadCollections();
      setDeleteModal(false);
      setSelectedCollection(null);
    } catch (err) {
      console.error("Error deleting collection:", err);
      setError(err instanceof Error ? err.message : "Failed to delete collection");
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action: "verify" | "export" | "delete") => {
    if (selectedCollections.length === 0) return;

    try {
      setLoading(true);

      switch (action) {
        case "verify":
          await Promise.all(
            selectedCollections.map((id) =>
              fetch(`/api/collections/${id}/verify`, { method: "POST" })
            )
          );
          break;

        case "export":
          // Implement export functionality
          break;

        case "delete":
          await Promise.all(
            selectedCollections.map((id) =>
              fetch(`/api/collections/${id}`, { method: "DELETE" })
            )
          );
          break;
      }

      await loadCollections();
      setSelectedCollections([]);
    } catch (err) {
      console.error("Error performing bulk action:", err);
      setError(err instanceof Error ? err.message : "Failed to perform bulk action");
    } finally {
      setLoading(false);
    }
  };

  // Handle sort
  const handleSort = (key: keyof CollectionRecord) => {
    setSortConfig((current) => ({
      key,
      direction: current.key === key && current.direction === "asc" ? "desc" : "asc",
    }));
  };

  // Render functions
  const renderGridView = () => (
    <Grid container spacing={2}>
      {filteredAndSortedCollections.map((collection) => (
        <Grid key={collection.id} xs={12} sm={6} md={4} lg={3}>
          <Card
            variant="outlined"
            sx={{
              cursor: "pointer",
              '&:hover': {
                boxShadow: 'md',
                transition: 'all 0.2s ease-in-out',
                //Subtly highlight the border on hover
                border: '1px solid var(--joy-palette-divider)',
              },
            }}
            onClick={() => router.push(`/dashboard/records/collections/${collection.id}`)}
          >
            <CardContent>
              <Stack spacing={2}>
                <Grid container spacing={1} alignItems="center">
                  <Grid xs={10}>
                    <Link href={`/dashboard/records/collections/${collection.id}`} passHref>
                      <MUILink
                        overlay
                        href="#introduction"
                        underline="none"
                        sx={{ display: "flex", flexDirection: "column", alignItems: "start" }}
                      >
                        <MiniBarcode
                          value={collection.id}
                          size={100}
                        />
                      </MUILink>
                    </Link>
                  </Grid>
                  <Grid xs={2}>
                    <Chip
                      size="sm"
                      variant="soft"
                      color={collection.isVerified ? "success" : "warning"}
                    >
                      {collection.isVerified ? "Verified" : "Pending"}
                    </Chip>
                  </Grid>
                </Grid>

                <Divider />

                <Stack spacing={1}>
                  <Typography level="body-sm">
                    Collected: {formatDistanceToNow(new Date(collection.arrivalTime))} ago
                  </Typography>
                  <Typography level="body-sm">From: {collection.institution}</Typography>
                  <Typography level="body-sm">By: {collection.name}</Typography>
                  {collection.caseNumber && (
                    <Typography level="body-sm">Case: {collection.caseNumber}</Typography>
                  )}
                  {collection.hospitalRef && (
                    <Typography level="body-sm">Hospital Ref: {collection.hospitalRef}</Typography>
                  )}
                </Stack>

                <Stack direction="row" spacing={1} justifyContent="flex-end">
                  <IconButton
                    size="sm"
                    variant="plain"
                    color="neutral"
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(`/dashboard/records/collections/${collection.id}/edit`);
                    }}
                  >
                    <Icon icon="mdi:pencil" />
                  </IconButton>
                  <IconButton
                    size="sm"
                    variant="plain"
                    color="danger"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedCollection(collection.id);
                      setDeleteModal(true);
                    }}
                  >
                    <Icon icon="mdi:delete" />
                  </IconButton>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderTableView = () => (
    <Table
      borderAxis="both"
      stickyHeader
      stripe="odd"
      hoverRow
      sx={{
        '& tr:hover td': {
          bgcolor: 'background.level1',
        },
      }}
    >
      <thead>
        <tr>
          <th style={{ width: 48 }}>
            <Checkbox
              checked={
                selectedCollections.length === filteredAndSortedCollections.length
              }
              indeterminate={
                selectedCollections.length > 0 &&
                selectedCollections.length < filteredAndSortedCollections.length
              }
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedCollections(
                    filteredAndSortedCollections.map((c) => c.id)
                  );
                } else {
                  setSelectedCollections([]);
                }
              }}
            />
          </th>
          <th>Body Tag</th>
          <th>Collection Type</th>
          <th>Status</th>
          <th>Institution</th>
          <th>Collected By</th>
          <th>Arrival Time</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {filteredAndSortedCollections.map((collection) => (
          <tr
            key={collection.id}
            onClick={() =>
              router.push(`/dashboard/records/collections/${collection.id}`)
            }
            style={{ cursor: "pointer" }}
          >
            <td onClick={(e) => e.stopPropagation()}>
              <Checkbox
                checked={selectedCollections.includes(collection.id)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedCollections([...selectedCollections, collection.id]);
                  } else {
                    setSelectedCollections(
                      selectedCollections.filter((id) => id !== collection.id)
                    );
                  }
                }}
              />
            </td>
            <td>
              <Link href={`/dashboard/records/collections/${collection.id}`} passHref>
                <MUILink
                  overlay
                  href="#introduction"
                  underline="none"
                  sx={{ display: "flex", flexDirection: "column", alignItems: "start" }}
                >
                  <MiniBarcode
                    value={collection.id}
                  />
                </MUILink>
              </Link>
            </td>
            <td>
              <Chip
                size="sm"
                variant="soft"
                color={collection.collectionType === 'CRIME_SCENE' ? 'danger' : 'primary'}
              >
                {collection.collectionType.replace('_', ' ')}
              </Chip>
            </td>
            <td>
              <Chip
                size="sm"
                variant="soft"
                color={collection.status === 'COLLECTED' ? 'success' : 'warning'}
              >
                {collection.status}
              </Chip>
            </td>
            <td>{collection.institution}</td>
            <td>{collection.name}</td>
            <td>{format(new Date(collection.arrivalTime), 'PPpp')}</td>
            <td>
              <Stack direction="row" spacing={1}>
                <IconButton
                  size="sm"
                  variant="plain"
                  color="neutral"
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push(
                      `/dashboard/records/collections/${collection.id}/edit`
                    );
                  }}
                >
                  <Icon icon="mdi:pencil" />
                </IconButton>
                <IconButton
                  size="sm"
                  variant="plain"
                  color="danger"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedCollection(collection.id);
                    setDeleteModal(true);
                  }}
                >
                  <Icon icon="mdi:delete" />
                </IconButton>
              </Stack>
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
  );

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: 400,
        }}
      >
        <CircularProgress size="lg" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="danger"
          variant="soft"
          startDecorator={<Icon icon="mdi:alert" />}
        >
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Stack spacing={3}>
        {/* Header */}
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          flexWrap="wrap"
          gap={2}
        >
          <Typography level="h2">Body Collections</Typography>
          <Stack direction="row" spacing={1}>
            <Button
              variant="outlined"
              color="neutral"
              startDecorator={<Icon icon={showSearch ? "mdi:close" : "mdi:magnify"} />}
              onClick={() => setShowSearch(!showSearch)}
            >
              {showSearch ? "Close Search" : "Search"}
            </Button>
            <Button
              variant="outlined"
              color="neutral"
              startDecorator={<Icon icon={view === "grid" ? "mdi:table" : "mdi:grid"} />}
              onClick={() => setView(view === "grid" ? "table" : "grid")}
            >
              {view === "grid" ? "Table View" : "Grid View"}
            </Button>
            <Button
              component={Link}
              href="/dashboard/records/collections/new"
              startDecorator={<Icon icon="mdi:plus" />}
            >
              New Collection
            </Button>
          </Stack>
        </Stack>

        {/* Search and Filters */}
        <AnimatePresence>
          {showSearch && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <Card variant="outlined">
                <CardContent>
                  <Stack spacing={2}>
                    <Stack direction="row" spacing={2}>
                      <Input
                        placeholder="Search collections..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        startDecorator={<Icon icon="mdi:magnify" />}
                        fullWidth
                      />
                      <Select
                        placeholder="Filter by status"
                        value={filterStatus}
                        onChange={(_, value) => setFilterStatus(value)}
                        startDecorator={<Icon icon="mdi:filter" />}
                        sx={{ minWidth: 200 }}
                      >
                        <Option value={null}>All Status</Option>
                        {Object.values(BodyStatus).map((status) => (
                          <Option key={status} value={status}>
                            {status.replace('_', ' ')}
                          </Option>
                        ))}
                      </Select>
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bulk Actions */}
        {selectedCollections.length > 0 && (
          <Sheet
            variant="soft"
            sx={{
              p: 2,
              borderRadius: "sm",
              display: "flex",
              gap: 2,
              alignItems: "center",
            }}
          >
            <Typography level="body-sm">
              {selectedCollections.length} collections selected
            </Typography>
            <Button
              size="sm"
              variant="plain"
              color="neutral"
              onClick={() => setSelectedCollections([])}
            >
              Clear Selection
            </Button>
            <Divider orientation="vertical" />
            <Button
              size="sm"
              startDecorator={<Icon icon="mdi:check-circle" />}
              onClick={() => handleBulkAction("verify")}
            >
              Verify Selected
            </Button>
            <Button
              size="sm"
              startDecorator={<Icon icon="mdi:file-export" />}
              onClick={() => handleBulkAction("export")}
            >
              Export Selected
            </Button>
            <Button
              size="sm"
              color="danger"
              startDecorator={<Icon icon="mdi:delete" />}
              onClick={() => handleBulkAction("delete")}
            >
              Delete Selected
            </Button>
          </Sheet>
        )}

        {/* Collections Grid/Table */}
        {filteredAndSortedCollections.length === 0 ? (
          <Card variant="outlined">
            <CardContent>
              <Stack spacing={2} alignItems="center" sx={{ py: 4 }}>
                <Icon icon="mdi:folder-open" width={48} height={48} />
                <Typography level="h4" textAlign="center">
                  No Collections Found
                </Typography>
                <Typography level="body-sm" textAlign="center">
                  {searchQuery || filterStatus
                    ? "Try adjusting your search or filters"
                    : "Start by creating a new collection"}
                </Typography>
                {!searchQuery && !filterStatus && (
                  <Button
                    component={Link}
                    href="/dashboard/records/collections/new"
                    startDecorator={<Icon icon="mdi:plus" />}
                  >
                    New Collection
                  </Button>
                )}
              </Stack>
            </CardContent>
          </Card>
        ) : (
          view === "grid" ? renderGridView() : renderTableView()
        )}
      </Stack>

      {/* Delete Confirmation Modal */}
      <Modal open={deleteModal} onClose={() => setDeleteModal(false)}>
        <ModalDialog
          variant="outlined"
          role="alertdialog"
          aria-labelledby="delete-modal-title"
          aria-describedby="delete-modal-desc"
        >
          <Typography id="delete-modal-title" level="h4">
            Confirm Deletion
          </Typography>
          <Typography id="delete-modal-desc" level="body-md">
            Are you sure you want to delete this collection? This action cannot be
            undone.
          </Typography>
          <Stack direction="row" spacing={1} justifyContent="flex-end" sx={{ mt: 2 }}>
            <Button
              variant="plain"
              color="neutral"
              onClick={() => setDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button color="danger" onClick={handleDelete}>
              Delete
            </Button>
          </Stack>
        </ModalDialog>
      </Modal>
    </Box>
  );
}
