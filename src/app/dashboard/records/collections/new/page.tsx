"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { Box } from '@mui/joy';
import CollectionForm from '@/components/Forms/BodyCollection/CollectionForm';

/* // Dynamically import the form with no SSR
const CollectionForm = dynamic(
  () => import('@/components/Forms/BodyCollection/CollectionForm'),
  { 
    ssr: false,
    loading: () => (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    )
  }
); */

export default function AddCollectionPage() {
  return (
    <Box sx={{
      display: 'flex',
      p: 0,
      width: `calc(100vw - 54px)`,
      height: `calc(100vh - 60px)`,
    }}>
      <CollectionForm />
    </Box>
  );
}