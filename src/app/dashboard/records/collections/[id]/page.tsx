"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, <PERSON>b, TabPanel, CircularProgress, Card, CardContent, Grid, Chip, IconButton, Divider, AspectRatio, Modal, ModalDialog, ModalClose } from '@mui/joy';
import { useRouter } from 'next/navigation';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import MiniBarcode from '@/components/Barcode/MiniBarcode';
import { format, differenceInDays } from 'date-fns';
import Image from 'next/image';
import type { Body, BodyCollection, BodyStatus, User, Facility, BodyTag } from '@prisma/client';
import dynamicImport from 'next/dynamic';
import { BodyTagSelector } from '@/components/Forms/BodyTag/BodyTagSelector';
import { BodyTagScanType } from '@/types/body';
import {
  AdmissionToReferralInForm,
  ReferralInToReferralOutForm,
  ReferralOutToPendingReleaseForm,
  AutoOverdueDetectionForm
} from '@/components/Forms/StatusTransition';
import { AdmissionForm } from '@/components/Forms/BodyAdmission/AdmissionForm';
import { validateStatusTransition } from '@/lib/schema/statusTransitionSchemas';
import { BodyTagStatus } from '../../admissions/[id]/types/statusTransition';

interface ActivityLog {
  id: string;
  action: string;
  timestamp: Date;
  user: string;
  details: string;
}

interface ExtendedCollection extends BodyCollection {
  user: Pick<User, 'id' | 'name' | 'role' | 'department'> & {
    facility: Pick<Facility, 'name' | 'code' | 'type'>;
  };
  body?: Body & {
    bodyTag?: BodyTag & {
      facility: Pick<Facility, 'name' | 'code'>;
    };
    admissions: Array<{
      id: string;
      facility: Facility;
      assignedTo: Pick<User, 'id' | 'name' | 'role' | 'department'>;
    }>;
  };
  _metadata: {
    currentStatus: BodyStatus;
    isAdmitted: boolean;
    lastUpdated: Date;
    activityCount: number;
    hasPhotos: boolean;
    createdAt: string;
    updatedAt: string;
    daysSinceLastUpdate?: number;
    isOverdue?: boolean;
    isApproachingOverdue?: boolean;
  };
  activityLog: ActivityLog[];
}

// Import MiniMap dynamically to avoid SSR issues with Leaflet
const MiniMap = dynamicImport(() => import('@/components/Maps/MiniMap'), {
  ssr: false,
  loading: () => (
    <Card variant="outlined" sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <CircularProgress size="sm" />
    </Card>
  )
});

const DynamicMiniMap = dynamicImport(() => import('@/components/Maps/MiniMap'), {
  loading: () => <CircularProgress />,
  ssr: false
});

export default function CollectionDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [collection, setCollection] = React.useState<ExtendedCollection | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [activeTab, setActiveTab] = React.useState<string>('overview');
  const [photoModal, setPhotoModal] = React.useState<string | null>(null);
  const [deleteModal, setDeleteModal] = React.useState(false);

  // Status transition modal states
  const [statusTransitionModal, setStatusTransitionModal] = React.useState<{
    type: 'admission-to-referral-in' | 'referral-in-to-referral-out' | 'referral-out-to-pending-release' | 'auto-overdue' | null;
    bodyTagId?: string;
    currentStatus?: string;
  }>({ type: null });

  // Admission modal state
  const [admissionModal, setAdmissionModal] = React.useState(false);

  const refreshCollection = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/collections/${params.id}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Collection not found');
        }
        throw new Error('Failed to fetch collection details');
      }
      const data = await response.json();
      setCollection(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching collection details');
    } finally {
      setLoading(false);
    }
  }, [params.id]);

  React.useEffect(() => {
    refreshCollection();
  }, [refreshCollection]);

  // Helper functions for status management
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'GENERATED': return 'neutral';
      case 'COLLECTED': return 'primary';
      case 'ADMITTED': return 'success';
      case 'REFERRAL_IN': return 'warning';
      case 'REFERRAL_OUT': return 'warning';
      case 'PENDING_RELEASE': return 'danger';
      case 'OVERDUE': return 'danger';
      case 'RELEASED': return 'success';
      default: return 'neutral';
    }
  };

  const getDaysSinceLastUpdate = (lastUpdate: Date) => {
    return differenceInDays(new Date(), new Date(lastUpdate));
  };

  const isOverdue = (status: string, lastUpdate: Date) => {
    const daysSince = getDaysSinceLastUpdate(lastUpdate);
    return ['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(status) && daysSince > 7;
  };

  const isApproachingOverdue = (status: string, lastUpdate: Date) => {
    const daysSince = getDaysSinceLastUpdate(lastUpdate);
    return ['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(status) && daysSince >= 5 && daysSince <= 7;
  };

  const getValidTransitions = (currentStatus: string) => {
    const transitions = [];
    
    // Note: Admission option is now handled by dedicated button in header
    
    if (validateStatusTransition(currentStatus, 'REFERRAL_IN')) {
      transitions.push({ type: 'admission-to-referral-in', label: 'Process Referral In', icon: 'mdi:transfer-right' });
    }
    if (validateStatusTransition(currentStatus, 'REFERRAL_OUT')) {
      transitions.push({ type: 'referral-in-to-referral-out', label: 'Transfer to Facility', icon: 'mdi:send' });
    }
    if (validateStatusTransition(currentStatus, 'PENDING_RELEASE')) {
      transitions.push({ type: 'referral-out-to-pending-release', label: 'Mark Pending Release', icon: 'mdi:clipboard-check' });
    }
    if (validateStatusTransition(currentStatus, 'OVERDUE')) {
      transitions.push({ type: 'auto-overdue', label: 'Mark as Overdue', icon: 'mdi:clock-alert' });
    }
    return transitions;
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/collections/${params.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete collection');
      }
      
      router.push('/dashboard/records/collections');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete collection');
    } finally {
      setLoading(false);
      setDeleteModal(false);
    }
  };

  const handleVerify = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/collections/${params.id}/verify`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to verify collection');
      }

      refreshCollection();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to verify collection');
    } finally {
      setLoading(false);
    }
  };

  // Status transition handlers
  const handleStatusTransition = (type: string, bodyTagId?: string, currentStatus?: string) => {
    if (type === 'admit-body') {
      setAdmissionModal(true);
    } else {
      setStatusTransitionModal({
        type: type as any,
        bodyTagId,
        currentStatus
      });
    }
  };

  const handleTransitionSuccess = () => {
    setStatusTransitionModal({ type: null });
    refreshCollection();
  };

  const handleTransitionCancel = () => {
    setStatusTransitionModal({ type: null });
  };

  // Admission modal handlers
  const handleAdmissionSuccess = () => {
    setAdmissionModal(false);
    refreshCollection();
  };

  const handleAdmissionCancel = () => {
    setAdmissionModal(false);
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress size="lg" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="danger"
          variant="soft"
          endDecorator={
            <Stack direction="row" spacing={1}>
              <Button
                onClick={refreshCollection}
                size="sm"
                variant="soft"
                startDecorator={<Icon icon="mdi:refresh" />}
              >
                Retry
              </Button>
              <Button
                component={Link}
                href="/dashboard/records/collections"
                size="sm"
                variant="soft"
              >
                Return to List
              </Button>
            </Stack>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  if (!collection) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="danger"
          variant="soft"
          endDecorator={
            <Button
              component={Link}
              href="/dashboard/records/collections"
              size="sm"
              variant="soft"
            >
              Return to List
            </Button>
          }
        >
          Collection not found
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 4 } }}>
      <Stack spacing={3}>
        {/* Header */}
        <Card variant="outlined">
          <CardContent>
            <Grid container spacing={2}>
              <Grid xs={12} md={8}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Button
                    component={Link}
                    href="/dashboard/records/collections"
                    variant="plain"
                    size="sm"
                    startDecorator={<Icon icon="mdi:arrow-left" />}
                  >
                    Back
                  </Button>
                  <Divider orientation="vertical" />
                  <Stack spacing={1}>
                    <Typography level="h3">
                      Collection Details
                    </Typography>
                    <Stack direction="row" spacing={1} alignItems="center" flexWrap="wrap">
                      <Chip
                        size="sm"
                        variant="soft"
                        color={getStatusColor(collection._metadata.currentStatus)}
                        startDecorator={<Icon icon="mdi:clock-outline" />}
                      >
                        {collection._metadata.currentStatus.replace('_', ' ')}
                      </Chip>

                      {/* Overdue Warning */}
                      {collection.body?.bodyTag && isOverdue(collection.body.bodyTag.status, collection.body.bodyTag.updatedAt) && (
                        <Chip
                          size="sm"
                          variant="soft"
                          color="danger"
                          startDecorator={<Icon icon="mdi:alert" />}
                        >
                          OVERDUE ({getDaysSinceLastUpdate(collection.body.bodyTag.updatedAt)} days)
                        </Chip>
                      )}

                      {/* Approaching Overdue Warning */}
                      {collection.body?.bodyTag && isApproachingOverdue(collection.body.bodyTag.status, collection.body.bodyTag.updatedAt) && (
                        <Chip
                          size="sm"
                          variant="soft"
                          color="warning"
                          startDecorator={<Icon icon="mdi:clock-alert" />}
                        >
                          APPROACHING OVERDUE ({getDaysSinceLastUpdate(collection.body.bodyTag.updatedAt)} days)
                        </Chip>
                      )}

                      <Typography level="body-sm" color="neutral">
                        Last updated: {format(new Date(collection._metadata.lastUpdated), 'PPp')}
                      </Typography>
                    </Stack>
                  </Stack>
                </Stack>
              </Grid>
              <Grid xs={12} md={4}>
                <Stack direction="row" spacing={1} justifyContent="flex-end" flexWrap="wrap">
                  {/* Status Transition Buttons */}
                  {collection.body?.bodyTag && getValidTransitions(collection.body.bodyTag.status).map((transition) => (
                    <Button
                      key={transition.type}
                      variant="soft"
                      color={transition.type === 'auto-overdue' ? 'danger' : 'primary'}
                      size="sm"
                      startDecorator={<Icon icon={transition.icon} />}
                      onClick={() => handleStatusTransition(transition.type, collection.body?.bodyTag?.id, collection.body?.bodyTag?.status)}
                    >
                      {transition.label}
                    </Button>
                  ))}

                  {/* Admit Body button - always show but enable only when body tag status is COLLECTED */}
                  <Button
                    variant="soft"
                    color={collection.body?.bodyTag?.status === 'COLLECTED' ? "success" : "neutral"}
                    size="sm"
                    startDecorator={<Icon icon="mdi:hospital-box" />}
                    onClick={() => handleStatusTransition('admit-body')}
                    disabled={collection.body?.bodyTag?.status !== 'COLLECTED'}
                  >
                    Admit Body
                  </Button>

                  <Button
                    onClick={refreshCollection}
                    variant="plain"
                    startDecorator={<Icon icon="mdi:refresh" />}
                  >
                    Refresh
                  </Button>
                  <Button
                    component={Link}
                    href={`/dashboard/records/collections/${params.id}/edit`}
                    startDecorator={<Icon icon="mdi:pencil" />}
                  >
                    Edit
                  </Button>
                  <Button
                    color="danger"
                    variant="outlined"
                    startDecorator={<Icon icon="mdi:delete" />}
                    onClick={() => setDeleteModal(true)}
                  >
                    Delete
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Quick Info Cards */}
        <Grid container spacing={2}>
          <Grid xs={12} md={6} lg={3}>
            <Card variant="outlined">
              <CardContent>
                <Stack spacing={1}>
                  <Typography level="body-xs">Collection Details</Typography>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Icon icon="mdi:account" width={24} />
                    <Stack>
                      <Typography level="body-sm">{collection.name}</Typography>
                      <Typography level="body-xs">{collection.institution}</Typography>
                    </Stack>
                  </Stack>
                  <Divider />
                  <Typography level="body-xs">Collection Date</Typography>
                  <Typography level="body-sm">
                    {format(new Date(collection.arrivalTime), 'PPp')}
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid xs={12} md={6} lg={3}>
            <Card variant="outlined">
              <CardContent>
                <Stack spacing={1}>
                  <Typography level="body-xs">Body Status</Typography>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Icon icon="mdi:clipboard-check" width={24} />
                    <Stack>
                      <Typography level="body-sm">
                        {collection._metadata.currentStatus.replace('_', ' ')}
                      </Typography>
                      <Typography level="body-xs">
                        {collection._metadata.isAdmitted ? 'Admitted' : 'Pending Admission'}
                      </Typography>
                    </Stack>
                  </Stack>
                  <Divider />
                  <Typography level="body-xs">Collection Type</Typography>
                  <Typography level="body-sm">
                    {collection.collectionType.replace('_', ' ')}
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid xs={12} md={6} lg={3}>
            <Card variant="outlined">
              <CardContent>
                <Stack spacing={1}>
                  <Typography level="body-xs">Activity</Typography>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Icon icon="mdi:history" width={24} />
                    <Stack>
                      <Typography level="body-sm">
                        {collection._metadata.activityCount} Actions
                      </Typography>
                      <Typography level="body-xs">
                        {collection._metadata.hasPhotos ? 'Has Photos' : 'No Photos'}
                      </Typography>
                    </Stack>
                  </Stack>
                  <Divider />
                  <Typography level="body-xs">Vehicle Registration</Typography>
                  <Typography level="body-sm">
                    {collection.vehicleReg}
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid xs={12} md={6} lg={3}>
            <Card variant="outlined">
              <CardContent>
                <Stack spacing={1}>
                  <Typography level="body-xs">Barcode</Typography>
                  <Box sx={{ p: 1, bgcolor: 'background.level1', borderRadius: 'sm' }}>
                    <MiniBarcode
                      value={collection.barcodeValue}
                      size={120}
                    />
                  </Box>
                  <Typography level="body-xs" textAlign="center">
                    {collection.barcodeValue}
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs Content */}
        <Card variant="outlined">
          <Tabs
            value={activeTab}
            onChange={(event, value) => setActiveTab(value as string)}
            sx={{ backgroundColor: 'background.surface' }}
          >
            <TabList>
              <Tab value="overview">Overview</Tab>
              <Tab value="body-tag">
                Body Tag & Status
                {collection.body?.bodyTag && (
                  <Chip
                    size="sm"
                    variant="soft"
                    color={getStatusColor(collection.body.bodyTag.status)}
                  >
                    {collection.body.bodyTag.status.replace('_', ' ')}
                  </Chip>
                )}
              </Tab>
              <Tab value="photos">
                Photos
                {collection._metadata.hasPhotos && (
                  <Chip size="sm" variant="soft" color="primary">
                    {collection.photos?.length || 0}
                  </Chip>
                )}
              </Tab>
              <Tab value="activity">
                Activity Log
                <Chip size="sm" variant="soft" color="primary">
                  {collection._metadata.activityCount}
                </Chip>
              </Tab>
            </TabList>

            <Box sx={{ p: 2 }}>
              <TabPanel value="overview">
                <Grid container spacing={3}>
                  {/* Collection Information */}
                  <Grid xs={12} lg={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography level="title-md" startDecorator={<Icon icon="mdi:information" />}>
                          Collection Information
                        </Typography>
                        <Divider sx={{ my: 2 }} />
                        <Stack spacing={2}>
                          <Grid container spacing={2}>
                            <Grid xs={6}>
                              <Typography level="body-xs">Collected By</Typography>
                              <Typography level="body-sm">{collection.name}</Typography>
                            </Grid>
                            <Grid xs={6}>
                              <Typography level="body-xs">Institution</Typography>
                              <Typography level="body-sm">{collection.institution}</Typography>
                            </Grid>
                            <Grid xs={6}>
                              <Typography level="body-xs">Vehicle Registration</Typography>
                              <Typography level="body-sm">{collection.vehicleReg}</Typography>
                            </Grid>
                            <Grid xs={6}>
                              <Typography level="body-xs">Collection Type</Typography>
                              <Typography level="body-sm">{collection.collectionType.replace('_', ' ')}</Typography>
                            </Grid>
                            <Grid xs={12}>
                              <Typography level="body-xs">Description</Typography>
                              <Typography level="body-sm">{collection.bodyDescription || 'No description provided'}</Typography>
                            </Grid>
                          </Grid>
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Location Information */}
                  <Grid xs={12} lg={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography level="title-md" startDecorator={<Icon icon="mdi:map-marker" />}>
                          Location Information
                        </Typography>
                        <Divider sx={{ my: 2 }} />
                        <Stack spacing={2}>
                          <Grid container spacing={2}>
                            <Grid xs={6}>
                              <Typography level="body-xs">Facility</Typography>
                              <Typography level="body-sm">{collection.user.facility.name}</Typography>
                            </Grid>
                            <Grid xs={6}>
                              <Typography level="body-xs">Department</Typography>
                              <Typography level="body-sm">{collection.user.department}</Typography>
                            </Grid>
                            <Grid xs={12}>
                              <Typography level="body-xs">GPS Coordinates</Typography>
                              <Typography level="body-sm">
                                {collection.gpsCoords && typeof collection.gpsCoords === 'object' && 'latitude' in collection.gpsCoords ? 
                                  `${collection.gpsCoords.latitude}, ${collection.gpsCoords.longitude}` : 
                                  'N/A'
                                }
                              </Typography>
                            </Grid>
                            {collection.gpsCoords && typeof collection.gpsCoords === 'object' && 'latitude' in collection.gpsCoords && (
                              <Grid xs={12}>
                                <Box sx={{ height: 300, mt: 2 }}>
                                  <DynamicMiniMap 
                                    coordinates={{
                                      lat: Number(collection.gpsCoords.latitude),
                                      lng: Number(collection.gpsCoords.longitude)
                                    }}
                                    title="Collection Location"
                                    description={`Collection point for ${collection.name}`}
                                    height={300}
                                    zoom={15}
                                    showPopup
                                  />
                                </Box>
                              </Grid>
                            )}
                          </Grid>
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </TabPanel>

              <TabPanel value="body-tag">
                <Stack spacing={3}>
                  {collection.body?.bodyTag ? (
                    <>
                      {/* Body Tag Information */}
                      <Card variant="outlined">
                        <CardContent>
                          <Typography level="title-md" startDecorator={<Icon icon="mdi:qrcode" />} sx={{ mb: 2 }}>
                            Body Tag Information
                          </Typography>
                          <Divider sx={{ mb: 2 }} />

                          {/* Enhanced BodyTagSelector in edit mode (read-only) */}
                          <BodyTagSelector
                            name="bodyTag"
                            mode="edit"
                            bodyTagId={collection.body.bodyTag.id}
                            scanType={BodyTagScanType.COLLECTION}
                            showResult={true}
                            label="Current Body Tag"
                          />
                        </CardContent>
                      </Card>

                      {/* Status Transition Actions */}
                      <Card variant="outlined">
                        <CardContent>
                          <Typography level="title-md" startDecorator={<Icon icon="mdi:swap-horizontal" />} sx={{ mb: 2 }}>
                            Status Transition Actions
                          </Typography>
                          <Divider sx={{ mb: 2 }} />

                          {getValidTransitions(collection.body.bodyTag.status).length > 0 ? (
                            <Grid container spacing={2}>
                              {getValidTransitions(collection.body.bodyTag.status).map((transition) => (
                                <Grid key={transition.type} xs={12} sm={6} md={4}>
                                  <Card variant="soft" sx={{ cursor: 'pointer' }} onClick={() => handleStatusTransition(transition.type, collection.body?.bodyTag?.id, collection.body?.bodyTag?.status)}>
                                    <CardContent>
                                      <Stack direction="row" spacing={2} alignItems="center">
                                        <Icon icon={transition.icon} width={24} height={24} />
                                        <Stack>
                                          <Typography level="title-sm">{transition.label}</Typography>
                                          <Typography level="body-xs">
                                            {transition.type === 'admission-to-referral-in' && 'Process body for referral intake'}
                                            {transition.type === 'referral-in-to-referral-out' && 'Transfer body to another facility'}
                                            {transition.type === 'referral-out-to-pending-release' && 'Mark body as ready for release'}
                                            {transition.type === 'auto-overdue' && 'Mark body as overdue for processing'}
                                          </Typography>
                                        </Stack>
                                      </Stack>
                                    </CardContent>
                                  </Card>
                                </Grid>
                              ))}
                            </Grid>
                          ) : (
                            <Alert color="neutral">
                              No status transitions available for current status: {collection.body.bodyTag.status.replace('_', ' ')}
                            </Alert>
                          )}
                        </CardContent>
                      </Card>

                      {/* Overdue Information */}
                      {(isOverdue(collection.body.bodyTag.status, collection.body.bodyTag.updatedAt) ||
                        isApproachingOverdue(collection.body.bodyTag.status, collection.body.bodyTag.updatedAt)) && (
                        <Alert
                          color={isOverdue(collection.body.bodyTag.status, collection.body.bodyTag.updatedAt) ? 'danger' : 'warning'}
                          startDecorator={<Icon icon="mdi:clock-alert" />}
                        >
                          <Stack spacing={1}>
                            <Typography level="title-sm">
                              {isOverdue(collection.body.bodyTag.status, collection.body.bodyTag.updatedAt) ? 'Overdue Alert' : 'Approaching Overdue'}
                            </Typography>
                            <Typography level="body-sm">
                              This body has been in {collection.body.bodyTag.status.replace('_', ' ')} status for {getDaysSinceLastUpdate(collection.body.bodyTag.updatedAt)} days.
                              {isOverdue(collection.body.bodyTag.status, collection.body.bodyTag.updatedAt)
                                ? ' Immediate action required.'
                                : ' Consider taking action soon to avoid overdue status.'}
                            </Typography>
                          </Stack>
                        </Alert>
                      )}
                    </>
                  ) : (
                    <Card variant="outlined">
                      <CardContent>
                        <Stack spacing={2} alignItems="center" sx={{ py: 4 }}>
                          <Icon icon="mdi:qrcode-off" width={48} height={48} />
                          <Typography level="h4">No Body Tag Associated</Typography>
                          <Typography level="body-sm" textAlign="center">
                            This collection does not have an associated body tag
                          </Typography>
                        </Stack>
                      </CardContent>
                    </Card>
                  )}
                </Stack>
              </TabPanel>

              <TabPanel value="photos">
                <Stack spacing={3}>
                  {collection.photos && collection.photos.length > 0 ? (
                    <Grid container spacing={2}>
                      {collection.photos.map((photo, index) => (
                        <Grid key={index} xs={12} sm={6} md={4}>
                          <Card variant="outlined">
                            <AspectRatio ratio="4/3">
                              <img
                                src={photo}
                                alt={`Collection photo ${index + 1}`}
                                style={{ objectFit: 'cover' }}
                                onClick={() => setPhotoModal(photo)}
                              />
                            </AspectRatio>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Card variant="outlined">
                      <CardContent>
                        <Stack spacing={2} alignItems="center" sx={{ py: 4 }}>
                          <Icon icon="mdi:image-off" width={48} height={48} />
                          <Typography level="h4">No Photos Available</Typography>
                          <Typography level="body-sm" textAlign="center">
                            No photos have been uploaded for this collection
                          </Typography>
                        </Stack>
                      </CardContent>
                    </Card>
                  )}
                </Stack>
              </TabPanel>

              <TabPanel value="activity">
                <Stack spacing={3}>
                  {collection.activityLog.length > 0 ? (
                    collection.activityLog.map((log) => (
                      <Card key={log.id} variant="outlined">
                        <CardContent>
                          <Stack direction="row" spacing={2} alignItems="center">
                            <Box>
                              <Typography level="body-sm">{log.action}</Typography>
                              <Stack direction="row" spacing={1} alignItems="center">
                                <Typography level="body-xs">
                                  {format(new Date(log.timestamp), 'PPp')}
                                </Typography>
                                <Typography level="body-xs">by {log.user}</Typography>
                              </Stack>
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                              <Typography level="body-xs" sx={{ whiteSpace: 'pre-wrap' }}>
                                {log.details}
                              </Typography>
                            </Box>
                          </Stack>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <Card variant="outlined">
                      <CardContent>
                        <Stack spacing={2} alignItems="center" sx={{ py: 4 }}>
                          <Icon icon="mdi:history" width={48} height={48} />
                          <Typography level="h4">No Activity Log</Typography>
                          <Typography level="body-sm" textAlign="center">
                            No activity has been recorded for this collection
                          </Typography>
                        </Stack>
                      </CardContent>
                    </Card>
                  )}
                </Stack>
              </TabPanel>
            </Box>
          </Tabs>
        </Card>
      </Stack>

      {/* Photo Modal */}
      <Modal open={!!photoModal} onClose={() => setPhotoModal(null)}>
        <ModalDialog
          variant="outlined"
          layout="fullscreen"
          sx={{
            p: 3,
            bgcolor: 'background.level1',
          }}
        >
          <Stack spacing={2}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography level="h4">Photo View</Typography>
              <IconButton
                variant="plain"
                color="neutral"
                onClick={() => setPhotoModal(null)}
              >
                <Icon icon="mdi:close" />
              </IconButton>
            </Stack>
            {photoModal && (
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: 'calc(100vh - 200px)',
                }}
              >
                <Image
                  src={photoModal}
                  alt="Collection photo"
                  fill
                  style={{ objectFit: 'contain' }}
                />
              </Box>
            )}
          </Stack>
        </ModalDialog>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal open={deleteModal} onClose={() => setDeleteModal(false)}>
        <ModalDialog
          variant="outlined"
          role="alertdialog"
          aria-labelledby="delete-modal-title"
          aria-describedby="delete-modal-desc"
        >
          <Typography id="delete-modal-title" level="h4">
            Confirm Deletion
          </Typography>
          <Typography id="delete-modal-desc" level="body-md">
            Are you sure you want to delete this collection? This action cannot be
            undone.
          </Typography>
          <Stack direction="row" spacing={1} justifyContent="flex-end" sx={{ mt: 2 }}>
            <Button
              variant="plain"
              color="neutral"
              onClick={() => setDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button color="danger" onClick={handleDelete}>
              Delete
            </Button>
          </Stack>
        </ModalDialog>
      </Modal>

      {/* Status Transition Modals */}
      <Modal
        open={statusTransitionModal.type !== null}
        onClose={handleTransitionCancel}
      >
        <ModalDialog
          variant="outlined"
          layout="center"
          sx={{
            maxWidth: "90vw",
            width: "800px",
            maxHeight: "90vh",
            overflow: 'auto'
          }}
        >
          <ModalClose />
          <Stack spacing={2}>
            <Typography level="h4">
              {statusTransitionModal.type === 'admission-to-referral-in' && 'Process Referral In'}
              {statusTransitionModal.type === 'referral-in-to-referral-out' && 'Transfer to Facility'}
              {statusTransitionModal.type === 'referral-out-to-pending-release' && 'Mark Pending Release'}
              {statusTransitionModal.type === 'auto-overdue' && 'Mark as Overdue'}
            </Typography>

            {statusTransitionModal.type === 'admission-to-referral-in' && (
              <AdmissionToReferralInForm
                bodyTagId={statusTransitionModal.bodyTagId}
                onSuccess={handleTransitionSuccess}
                onCancel={handleTransitionCancel}
              />
            )}

            {statusTransitionModal.type === 'referral-in-to-referral-out' && (
              <ReferralInToReferralOutForm
                bodyTagId={statusTransitionModal.bodyTagId}
                onSuccess={handleTransitionSuccess}
                onCancel={handleTransitionCancel}
              />
            )}

            {statusTransitionModal.type === 'referral-out-to-pending-release' && (
              <ReferralOutToPendingReleaseForm
                bodyTagId={statusTransitionModal.bodyTagId}
                onSuccess={handleTransitionSuccess}
                onCancel={handleTransitionCancel}
              />
            )}

            {statusTransitionModal.type === 'auto-overdue' && (
              <AutoOverdueDetectionForm
                bodyTagId={statusTransitionModal.bodyTagId}
                currentStatus={statusTransitionModal.currentStatus as keyof BodyTagStatus}
                daysSinceLastUpdate={collection?.body?.bodyTag ? getDaysSinceLastUpdate(collection.body.bodyTag.updatedAt) : 0}
                onSuccess={handleTransitionSuccess}
                onCancel={handleTransitionCancel}
              />
            )}
          </Stack>
        </ModalDialog>
      </Modal>

      {/* Admission Modal */}
      <Modal
        open={admissionModal}
        onClose={handleAdmissionCancel}
      >
        <ModalDialog
          variant="outlined"
          layout="center"
          sx={{
            maxWidth: "95vw",
            width: "1000px",
            maxHeight: "95vh",
            overflow: 'auto'
          }}
        >
          <ModalClose />
          <Stack spacing={2}>
            <Typography level="h4">
              Admit Body to Facility
            </Typography>
            <Typography level="body-sm" color="neutral">
              Complete the admission process for this collected body. The form will be pre-populated with available collection details including:
            </Typography>
            {collection && (
              <Box sx={{ pl: 2 }}>
                <Typography level="body-xs" color="neutral">
                  • Body ID: {collection.name}
                  {collection.body?.bodyTag?.id && (
                    <>
                      <br />• Body Tag: {collection.body.bodyTag.id}
                    </>
                  )}
                  {collection.barcodeValue && (
                    <>
                      <br />• Barcode: {collection.barcodeValue}
                    </>
                  )}
                  {collection.institution && (
                    <>
                      <br />• Institution: {collection.institution}
                    </>
                  )}
                </Typography>
              </Box>
            )}
            
            <AdmissionForm
              collectionData={{
                id: collection?.id || '',
                bodyId: collection?.bodyId || '',
                name: collection?.name,
                institution: collection?.institution,
                vehicleReg: collection?.vehicleReg,
                arrivalTime: collection?.arrivalTime,
                collectionType: collection?.collectionType,
                bodyDescription: collection?.bodyDescription,
                sceneDescription: collection?.sceneDescription,
                weatherConditions: collection?.weatherConditions,
                temperature: collection?.temperature,
                collectionNotes: collection?.collectionNotes,
                photos: collection?.photos,
                handedOverBy: collection?.handedOverBy,
                handedOverRole: collection?.handedOverRole,
                handedOverContact: collection?.handedOverContact,
                handoverNotes: collection?.handoverNotes,
                barcodeValue: collection?.barcodeValue,
                body: collection?.body ? {
                  id: collection.body.id || collection.bodyId,
                  trackingNumber: collection.body.trackingNumber || '',
                  bodyTagId: collection.body.bodyTag?.id || '',
                  firstName: collection.body.firstName,
                  lastName: collection.body.lastName,
                  gender: collection.body.gender,
                  approximateAge: collection.body.approximateAge,
                  height: collection.body.height,
                  weight: collection.body.weight,
                  distinguishingFeatures: collection.body.distinguishingFeatures,
                  deathRegistration: collection.body.deathRegistration,
                  causeOfDeath: collection.body.causeOfDeath,
                  placeOfDeath: collection.body.placeOfDeath,
                  dateOfDeath: collection.body.dateOfDeath,
                  bodyTag: collection.body.bodyTag ? {
                    id: collection.body.bodyTag.id,
                    tagNumber: collection.body.bodyTag.tagNumber || '',
                    status: collection.body.bodyTag.status || '',
                    facilityId: collection.body.bodyTag.facility?.code
                  } : undefined
                } : undefined,
                user: collection?.user ? {
                  id: collection.user.id,
                  name: collection.user.name,
                  facilityId: collection.user.facility?.code
                } : undefined
              }}
              onSuccess={handleAdmissionSuccess}
              onCancel={handleAdmissionCancel}
            />
          </Stack>
        </ModalDialog>
      </Modal>
    </Box>
  );
}