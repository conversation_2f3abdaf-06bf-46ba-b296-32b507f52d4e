import React from 'react';
import { Metadata } from 'next';
import { Box, Typography } from '@mui/joy';
import EditCollectionForm from '@/components/Forms/BodyCollection/EditCollectionForm';
import { auth } from '@/auth';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Edit Collection | GP Pathology',
  description: 'Edit body collection details',
};

export default async function EditCollectionPage({ params }: { params: { id: string } }) {
  const session = await auth();
  
  // Check if user is authenticated and has necessary permissions
  if (!session || !['ADMIN', 'FIELD_EMPLOYEE'].includes(session.user.role)) {
    redirect('/dashboard');
  }
  
  return (
    <Box sx={{ p: { xs: 2, md: 4 } }}>
      <Typography level="h3" sx={{ mb: 3 }}>
        Edit Body Collection
      </Typography>
      
      <EditCollectionForm collectionId={params.id} />
    </Box>
  );
}
