'use client'

import { useEffect } from 'react';
import { Box, Typography, Button, Alert, CircularProgress, Stack } from '@mui/joy';
import { Icon } from '@iconify/react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        p: 3,
        backgroundColor: 'background.level1',
      }}
    >
      <Alert
        variant="soft"
        color="danger"
        sx={{ mb: 3, maxWidth: '450px', width: '100%' }}
      >
        <Stack direction="row" spacing={2} alignItems="center">
          <Icon icon="mdi:alert-circle" width={32} height={32} />
          <Typography level="h4" fontWeight="lg" mb={1}>
            Oops! Something went wrong
          </Typography>
          <Typography level="body-md" mb={2}>
            {error.message || 'An unexpected error occurred'}
          </Typography>
        </Stack>

        {error.digest && (
          <Typography level="body-xs" sx={{ opacity: 0.8 }}>
            Error ID: {error.digest}
          </Typography>
        )}
      </Alert>
      <Button
        variant="soft"
        color="primary"
        onClick={() => {
          reset();
          return <CircularProgress size="sm" />;
        }}
        startDecorator={<Icon icon="mdi:refresh" />}
        sx={{
          transition: 'all 0.2s',
          '&:hover': { transform: 'scale(1.05)' }
        }}
      >
        Try again
      </Button>
      <Typography level="body-sm" mt={4} textAlign="center" sx={{ maxWidth: '300px' }}>
        If the problem persists, please contact our support team.
      </Typography>
    </Box>
  );
}