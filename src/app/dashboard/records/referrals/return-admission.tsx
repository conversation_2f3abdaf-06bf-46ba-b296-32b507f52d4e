'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  Typography,
  Button,
  Box,
  Alert,
  Grid,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Textarea,
  Stack,
  Card,
  CardContent,
  Chip,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const returnSchema = z.object({
  receivedBy: z.string().min(1, 'Receiver name is required'),
  condition: z.string().min(1, 'Body condition is required'),
  fridgeNumber: z.string().min(1, 'Fridge number is required'),
  temperature: z.string().min(1, 'Temperature is required'),
  locationNotes: z.string().optional(),
  notes: z.string().optional(),
});

type ReturnFormData = z.infer<typeof returnSchema>;

interface ReturnAdmissionProps {
  referralId: string;
  onComplete: (data: ReturnFormData) => Promise<void>;
  onCancel: () => void;
}

export default function ReturnAdmission({ referralId, onComplete, onCancel }: ReturnAdmissionProps) {
  const [error, setError] = React.useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ReturnFormData>({
    resolver: zodResolver(returnSchema),
  });

  const handleFormSubmit = async (data: ReturnFormData) => {
    try {
      await onComplete(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process return');
    }
  };

  return (
    <Sheet
      variant="outlined"
      sx={{
        width: '100%',
        p: 3,
        borderRadius: 'sm',
      }}
    >
      <Stack spacing={3}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography level="h4">Process Return</Typography>
          <Chip
            variant="soft"
            color="primary"
            startDecorator={<Icon icon="mdi:arrow-left-circle" />}
          >
            Return Processing
          </Chip>
        </Stack>

        {error && (
          <Alert 
            color="danger" 
            variant="soft"
            endDecorator={
              <Button size="sm" variant="soft" color="danger" onClick={() => setError(null)}>
                Dismiss
              </Button>
            }
          >
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit(handleFormSubmit)}>
          <Stack spacing={3}>
            {/* Return Details */}
            <Box>
              <Typography level="title-md" sx={{ mb: 2 }}>Return Details</Typography>
              <Grid container spacing={2}>
                <Grid xs={12} md={6}>
                  <Controller
                    name="receivedBy"
                    control={control}
                    render={({ field }) => (
                      <FormControl error={!!errors.receivedBy}>
                        <FormLabel>Received By</FormLabel>
                        <Input
                          {...field}
                          placeholder="Enter receiver's name"
                        />
                        {errors.receivedBy && (
                          <FormHelperText>{errors.receivedBy.message}</FormHelperText>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid xs={12} md={6}>
                  <Controller
                    name="condition"
                    control={control}
                    render={({ field }) => (
                      <FormControl error={!!errors.condition}>
                        <FormLabel>Body Condition</FormLabel>
                        <Input
                          {...field}
                          placeholder="Enter body condition"
                        />
                        {errors.condition && (
                          <FormHelperText>{errors.condition.message}</FormHelperText>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid xs={12}>
                  <Controller
                    name="notes"
                    control={control}
                    render={({ field }) => (
                      <FormControl error={!!errors.notes}>
                        <FormLabel>Additional Notes</FormLabel>
                        <Textarea
                          {...field}
                          minRows={3}
                          placeholder="Enter any additional notes..."
                        />
                        {errors.notes && (
                          <FormHelperText>{errors.notes.message}</FormHelperText>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Storage Details */}
            <Card variant="outlined">
              <CardContent>
                <Stack spacing={2}>
                  <Typography level="title-md">Storage Information</Typography>
                  
                  <Grid container spacing={2}>
                    <Grid xs={12} md={6}>
                      <Controller
                        name="fridgeNumber"
                        control={control}
                        render={({ field }) => (
                          <FormControl error={!!errors.fridgeNumber}>
                            <FormLabel>Fridge Number</FormLabel>
                            <Input
                              {...field}
                              placeholder="Enter fridge number"
                            />
                            {errors.fridgeNumber && (
                              <FormHelperText>{errors.fridgeNumber.message}</FormHelperText>
                            )}
                          </FormControl>
                        )}
                      />
                    </Grid>

                    <Grid xs={12} md={6}>
                      <Controller
                        name="temperature"
                        control={control}
                        render={({ field }) => (
                          <FormControl error={!!errors.temperature}>
                            <FormLabel>Temperature</FormLabel>
                            <Input
                              {...field}
                              placeholder="Enter temperature"
                              endDecorator="°C"
                            />
                            {errors.temperature && (
                              <FormHelperText>{errors.temperature.message}</FormHelperText>
                            )}
                          </FormControl>
                        )}
                      />
                    </Grid>

                    <Grid xs={12}>
                      <Controller
                        name="locationNotes"
                        control={control}
                        render={({ field }) => (
                          <FormControl error={!!errors.locationNotes}>
                            <FormLabel>Location Notes</FormLabel>
                            <Textarea
                              {...field}
                              minRows={2}
                              placeholder="Enter any location or storage notes..."
                            />
                            {errors.locationNotes && (
                              <FormHelperText>{errors.locationNotes.message}</FormHelperText>
                            )}
                          </FormControl>
                        )}
                      />
                    </Grid>
                  </Grid>
                </Stack>
              </CardContent>
            </Card>

            <Stack direction="row" spacing={2} justifyContent="flex-end">
              <Button
                variant="outlined"
                color="neutral"
                onClick={onCancel}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={isSubmitting}
                startDecorator={<Icon icon="mdi:check" />}
              >
                Complete Return Process
              </Button>
            </Stack>
          </Stack>
        </form>
      </Stack>
    </Sheet>
  );
} 