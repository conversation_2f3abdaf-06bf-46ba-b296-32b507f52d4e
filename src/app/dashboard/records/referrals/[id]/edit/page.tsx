"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState, useEffect, useCallback } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Grid,
  Stack,
  Typography,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Select,
  Option,
  Textarea,
  Alert,
  IconButton,
  Divider,
  Chip,
  Sheet,
  AspectRatio,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserSelectField, User } from "@/components/UI/FormFields/UserSelect";
import CurrentLocationMap from "@/components/Forms/BodyCollection/CurrentLocationMap";

// Import our schema
import {
  updateBodyReferralSchema,
  ReferralType,
  ReferralStatus
} from "@/lib/schema/referralSchema";

// Toast component for success messages
interface ToastProps {
  message: string;
  open: boolean;
  onClose: () => void;
  type?: "success" | "danger" | "warning" | "primary" | "neutral";
}

const Toast = ({ message, open, onClose, type = "success" }: ToastProps) => {
  if (!open) return null;

  return (
    <Sheet
      sx={{
        position: "fixed",
        top: 20,
        right: 20,
        zIndex: 9999,
        maxWidth: "80%",
        width: "350px",
        borderRadius: "md",
        boxShadow: "lg",
        animation: "slideIn 0.3s ease-out forwards",
        "@keyframes slideIn": {
          from: { transform: "translateX(100%)", opacity: 0 },
          to: { transform: "translateX(0)", opacity: 1 },
        },
      }}
    >
      <Alert
        color={type}
        variant="soft"
        startDecorator={<Icon icon={type === "success" ? "mdi:check-circle" : "mdi:information"} />}
        endDecorator={
          <IconButton
            variant="plain"
            color="neutral"
            size="sm"
            onClick={onClose}
          >
            <Icon icon="mdi:close" />
          </IconButton>
        }
        sx={{ width: "100%" }}
      >
        {message}
      </Alert>
    </Sheet>
  );
};

// Define a form-specific schema for editing based on our updateBodyReferralSchema
const referralEditFormSchema = updateBodyReferralSchema.extend({
  // Convert nullable string fields to optional strings for better form handling
  specimenKitNumber: updateBodyReferralSchema.shape.specimenKitNumber.unwrap().optional(),
  evidenceBagSerial: updateBodyReferralSchema.shape.evidenceBagSerial.unwrap().optional(),
  notes: updateBodyReferralSchema.shape.notes.unwrap().optional(),
  // Handle GPS coordinates as regular optionals for form state
  gpsLatitude: updateBodyReferralSchema.shape.gpsLatitude.unwrap().optional(),
  gpsLongitude: updateBodyReferralSchema.shape.gpsLongitude.unwrap().optional(),
});

type ReferralEditFormValues = typeof referralEditFormSchema._type;

interface Referral {
  id: string;
  bodyId: string;
  referralType: ReferralType;
  status: ReferralStatus;
  referralDate: string;
  returnDate: string | null;
  employeePersal: string;
  employeeName: string;
  institutionName: string;
  vehicleReg: string;
  specimenKitNumber: string | null;
  evidenceBagSerial: string | null;
  notes: string | null;
  bodyTagPhoto: string | null;
  vehiclePhoto: string | null;
  gpsLatitude: number | null;
  gpsLongitude: number | null;
  referredById: string;
  assignedToId: string;
  createdAt: string;
  updatedAt: string;
  body: {
    id: string;
    trackingNumber: string;
    status: string;
    bodyTag?: {
      tagNumber: string;
      status: string;
    } | null;
  };
  referredBy: {
    id: string;
    name: string | null;
    role: string;
    department: string | null;
  };
  assignedTo: {
    id: string;
    name: string | null;
    role: string;
    department: string | null;
  };
}

export default function EditReferralPage() {
  const params = useParams();
  const router = useRouter();
  const referralId = params.id as string;

  // State variables
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [referral, setReferral] = useState<Referral | null>(null);
  // Removed unused state variables
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [assignedStaff, setAssignedStaff] = useState<User | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [toast, setToast] = useState<{
    show: boolean;
    message: string;
    type: "success" | "danger" | "warning" | "primary" | "neutral"
  }>({
    show: false,
    message: "",
    type: "success"
  });

  // Initialize form with react-hook-form
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    control,
    formState: { errors },
  } = useForm<ReferralEditFormValues>({
    resolver: zodResolver(referralEditFormSchema),
    defaultValues: {
      id: referralId,
      referralType: "LODOX",
      status: "PENDING",
      employeePersal: "",
      employeeName: "",
      institutionName: "",
      vehicleReg: "",
      specimenKitNumber: "",
      evidenceBagSerial: "",
      notes: "",
      assignedToId: "",
    },
  });

  // Watch form values
  const referralType = watch("referralType");
  // Removed unused statusValue watch

  // Close toast after timeout
  useEffect(() => {
    if (toast.show) {
      const timer = setTimeout(() => {
        setToast(prev => ({ ...prev, show: false }));
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [toast.show]);

  // Fetch referral data
  const fetchReferral = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/referrals/${referralId}`);

      if (response.status === 404) {
        setError("Referral not found. It may have been deleted.");
        setLoading(false);
        return;
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch referral: ${response.statusText}`);
      }

      const data = await response.json();
      setReferral(data);

      // Populate form with existing data
      reset({
        id: data.id,
        referralType: data.referralType,
        status: data.status,
        employeePersal: data.employeePersal,
        employeeName: data.employeeName,
        institutionName: data.institutionName,
        vehicleReg: data.vehicleReg,
        specimenKitNumber: data.specimenKitNumber || "",
        evidenceBagSerial: data.evidenceBagSerial || "",
        notes: data.notes || "",
        gpsLatitude: data.gpsLatitude || undefined,
        gpsLongitude: data.gpsLongitude || undefined,
        assignedToId: data.assignedToId,
      });

      // Set location if available
      if (data.gpsLatitude && data.gpsLongitude) {
        setUserLocation({
          lat: data.gpsLatitude,
          lng: data.gpsLongitude,
        });
      }

      if (data.assignedTo) {
        setAssignedStaff({
          id: data.assignedTo.id,
          name: data.assignedTo.name || "Unknown",
          email: "",
          role: data.assignedTo.role,
        });
      }

    } catch (err) {
      console.error("Error fetching referral:", err);
      setError(err instanceof Error ? err.message : "An error occurred while loading the referral");
    } finally {
      setLoading(false);
    }
  }, [referralId, reset]);

  // Load referral data on mount
  useEffect(() => {
    fetchReferral();
  }, [fetchReferral]);

  // Location capture handler
  const handleLocationCaptured = useCallback((coords: { lat: number; lng: number }) => {
    setUserLocation(coords);
    setValue("gpsLatitude", coords.lat);
    setValue("gpsLongitude", coords.lng);
  }, [setValue]);

  // Form submission handler
  const onSubmit = async (data: ReferralEditFormValues) => {
    try {
      setSubmitting(true);
      setError(null);

      // Prepare data for submission
      const preparedData = {
        ...data,
        // Keep existing photos, don't allow changes
        bodyTagPhoto: referral?.bodyTagPhoto || null,
        vehiclePhoto: referral?.vehiclePhoto || null,
        assignedToId: assignedStaff?.id || referral?.assignedToId,
      };

      // Submit to API
      const response = await fetch(`/api/referrals/${referralId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(preparedData),
      });

      if (!response.ok) {
        const errorData = await response.json();

        if (errorData.errors && Array.isArray(errorData.errors)) {
          throw new Error(`Validation errors: ${errorData.errors.join(", ")}`);
        }

        throw new Error(errorData.error || `Error: ${response.statusText}`);
      }

      // Update was successful
      const result = await response.json();

      // Show toast notification
      setToast({
        show: true,
        message: "Referral updated successfully",
        type: "success"
      });

      // Refresh data
      setReferral(result);

      // After a delay, redirect back to the referrals list
      setTimeout(() => {
        router.push("/dashboard/records/referrals");
      }, 2000);

    } catch (err) {
      console.error("Error updating referral:", err);
      setError(err instanceof Error ? err.message : "An error occurred while updating the referral");

      // Show error toast
      setToast({
        show: true,
        message: err instanceof Error ? err.message : "An error occurred while updating the referral",
        type: "danger"
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Delete handler
  const handleDelete = async () => {
    try {
      setSubmitting(true);
      setError(null);

      const response = await fetch(`/api/referrals/${referralId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete: ${response.statusText}`);
      }

      // Show success toast
      setToast({
        show: true,
        message: "Referral deleted successfully",
        type: "success"
      });

      // After a delay, redirect back to the referrals list
      setTimeout(() => {
        router.push("/dashboard/records/referrals");
      }, 1500);

    } catch (err) {
      console.error("Error deleting referral:", err);
      setError(err instanceof Error ? err.message : "An error occurred while deleting the referral");

      // Show error toast
      setToast({
        show: true,
        message: err instanceof Error ? err.message : "An error occurred while deleting the referral",
        type: "danger"
      });
    } finally {
      setSubmitting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Helper function to get status color
  const getStatusColor = (status: ReferralStatus) => {
    switch (status) {
      case "PENDING":
        return "warning";
      case "IN_PROGRESS":
        return "primary";
      case "COMPLETED":
        return "success";
      case "RETURNED":
        return "warning";
      case "CANCELLED":
        return "danger";
      default:
        return "neutral";
    }
  };

  // Error component
  const ErrorDisplay = () => (
    <Card variant="outlined" sx={{ mb: 3 }}>
      <CardContent>
        <Stack spacing={2}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Icon icon="mdi:alert-circle" style={{ fontSize: "2rem", color: "var(--joy-palette-danger-500)" }} />
            <Typography level="title-lg">Error</Typography>
          </Stack>
          <Alert color="danger" variant="soft">
            {error}
          </Alert>
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              color="neutral"
              onClick={() => router.push("/dashboard/records/referrals")}
              startDecorator={<Icon icon="mdi:arrow-left" />}
            >
              Back to Referrals
            </Button>
            <Button
              variant="solid"
              color="primary"
              onClick={fetchReferral}
              startDecorator={<Icon icon="mdi:refresh" />}
            >
              Try Again
            </Button>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );

  // Loading component
  const LoadingDisplay = () => (
    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "60vh" }}>
      <Stack spacing={2} alignItems="center">
        <CircularProgress size="lg" />
        <Typography level="body-md">Loading referral...</Typography>
      </Stack>
    </Box>
  );

  // Empty state component
  const EmptyState = () => (
    <Card variant="outlined" sx={{ mb: 3 }}>
      <CardContent>
        <Stack spacing={2} alignItems="center" sx={{ py: 4 }}>
          <Icon icon="mdi:file-document-outline" style={{ fontSize: "4rem", opacity: 0.4 }} />
          <Typography level="h4">Referral Not Found</Typography>
          <Typography level="body-md" textAlign="center">
            The referral you're looking for doesn't exist or has been deleted.
          </Typography>
          <Button
            component={Link}
            href="/dashboard/records/referrals"
            startDecorator={<Icon icon="mdi:arrow-left" />}
          >
            Back to Referrals
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LoadingDisplay />
      </Box>
    );
  }

  // Render error state
  if (error && !referral) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography level="h2" sx={{ mb: 3 }}>Edit Referral</Typography>
        <ErrorDisplay />
      </Box>
    );
  }

  // Render empty state
  if (!referral) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography level="h2" sx={{ mb: 3 }}>Edit Referral</Typography>
        <EmptyState />
      </Box>
    );
  }

  // Render form
  return (
    <Box sx={{ p: 3 }}>
      {/* Toast notification */}
      <Toast
        message={toast.message}
        open={toast.show}
        type={toast.type}
        onClose={() => setToast(prev => ({ ...prev, show: false }))}
      />

      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={3}>
        <Stack direction="row" spacing={1} alignItems="center">
          <IconButton
            variant="plain"
            color="neutral"
            component={Link}
            href="/dashboard/records/referrals"
          >
            <Icon icon="mdi:arrow-left" />
          </IconButton>
          <Typography level="h2">Edit Referral</Typography>
          <Chip
            color={getStatusColor(referral.status)}
            variant="soft"
          >
            {referral.status}
          </Chip>
        </Stack>
        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            color="danger"
            onClick={() => setShowDeleteConfirm(true)}
            startDecorator={<Icon icon="mdi:delete" />}
          >
            Delete
          </Button>
          <Button
            component={Link}
            href={`/dashboard/records/referrals/${referralId}`}
            variant="outlined"
            color="neutral"
            startDecorator={<Icon icon="mdi:eye" />}
          >
            View Details
          </Button>
        </Stack>
      </Stack>

      {/* Show error if present */}
      {error && (
        <Alert
          color="danger"
          variant="soft"
          startDecorator={<Icon icon="mdi:alert-circle" />}
          sx={{ mb: 3 }}
        >
          {error}
        </Alert>
      )}

      {/* Body Information Card */}
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Typography level="title-md" sx={{ mb: 2 }}>Body Information</Typography>
          <Grid container spacing={2}>
            <Grid xs={12} sm={6} md={4}>
              <Stack spacing={0.5}>
                <Typography level="body-xs" color="neutral">Body ID</Typography>
                <Typography level="body-md" fontWeight="md">{referral.body.trackingNumber}</Typography>
              </Stack>
            </Grid>

            {referral.body.bodyTag && (
              <Grid xs={12} sm={6} md={4}>
                <Stack spacing={0.5}>
                  <Typography level="body-xs" color="neutral">Tag Number</Typography>
                  <Typography level="body-md" fontWeight="md">{referral.body.bodyTag.tagNumber}</Typography>
                </Stack>
              </Grid>
            )}

            <Grid xs={12} sm={6} md={4}>
              <Stack spacing={0.5}>
                <Typography level="body-xs" color="neutral">Body Status</Typography>
                <Chip size="sm" variant="soft" color="neutral">{referral.body.status}</Chip>
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Main Form */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            <Typography level="title-md" sx={{ mb: 2 }}>Referral Information</Typography>
            <Grid container spacing={3}>
              {/* Referral Type */}
              <Grid xs={12} sm={6}>
                <FormControl error={!!errors.referralType}>
                  <FormLabel>Referral Type</FormLabel>
                  <Select
                    {...register("referralType")}
                    defaultValue={referral.referralType}
                  >
                    <Option value="LODOX">LODOX</Option>
                    <Option value="XRAY">X-Ray</Option>
                    <Option value="SPECIMEN">Specimen</Option>
                    <Option value="OTHER">Other</Option>
                  </Select>
                  {errors.referralType && (
                    <FormHelperText>{errors.referralType.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* Status */}
              <Grid xs={12} sm={6}>
                <FormControl error={!!errors.status}>
                  <FormLabel>Status</FormLabel>
                  <Select
                    {...register("status")}
                    defaultValue={referral.status}
                  >
                    <Option value="PENDING">Pending</Option>
                    <Option value="IN_PROGRESS">In Progress</Option>
                    <Option value="COMPLETED">Completed</Option>
                    <Option value="RETURNED">Returned</Option>
                    <Option value="CANCELLED">Cancelled</Option>
                  </Select>
                  {errors.status && (
                    <FormHelperText>{errors.status.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* Institution Name */}
              <Grid xs={12}>
                <FormControl error={!!errors.institutionName}>
                  <FormLabel>Institution Name</FormLabel>
                  <Input {...register("institutionName")} />
                  {errors.institutionName && (
                    <FormHelperText>{errors.institutionName.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* Assigned To */}
              <Grid xs={12}>
                <FormControl>
                  <FormLabel>Assigned Staff</FormLabel>
                  <UserSelectField
                    name="assignedStaff"
                    control={control}
                    label="Assigned staff member"
                    onChange={(user) => {
                      if (user && !Array.isArray(user)) {
                        setAssignedStaff(user);
                        setValue("assignedToId", user.id);
                      }
                    }}
                    initialUsers={assignedStaff ? [assignedStaff] : []}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <Divider>Employee Information</Divider>
              </Grid>

              {/* Employee Persal */}
              <Grid xs={12} sm={6}>
                <FormControl error={!!errors.employeePersal}>
                  <FormLabel>Employee PERSAL Number</FormLabel>
                  <Input {...register("employeePersal")} />
                  {errors.employeePersal && (
                    <FormHelperText>{errors.employeePersal.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* Employee Name */}
              <Grid xs={12} sm={6}>
                <FormControl error={!!errors.employeeName}>
                  <FormLabel>Employee Name</FormLabel>
                  <Input {...register("employeeName")} />
                  {errors.employeeName && (
                    <FormHelperText>{errors.employeeName.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* Vehicle Registration */}
              <Grid xs={12} sm={6}>
                <FormControl error={!!errors.vehicleReg}>
                  <FormLabel>Vehicle Registration</FormLabel>
                  <Input {...register("vehicleReg")} />
                  {errors.vehicleReg && (
                    <FormHelperText>{errors.vehicleReg.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* Location */}
              <Grid xs={12}>
                <FormLabel>GPS Location</FormLabel>
                <Box sx={{
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 'sm',
                  p: 2,
                  mt: 1,
                  mb: 2
                }}>
                  <CurrentLocationMap
                    onLocationCaptured={handleLocationCaptured}
                    initialLocation={userLocation}
                    useMockLocation={process.env.NODE_ENV === 'development'}
                  />

                  {userLocation && (
                    <Box sx={{ mt: 2 }}>
                      <Typography level="body-sm" fontWeight="md">
                        Coordinates: {userLocation.lat.toFixed(6)}, {userLocation.lng.toFixed(6)}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Grid>

              <Grid xs={12}>
                <Divider>Documentation</Divider>
              </Grid>

              {/* Specimen Kit Number (for SPECIMEN type) */}
              {(referralType === "SPECIMEN" || referralType === "OTHER") && (
                <Grid xs={12} sm={6}>
                  <FormControl>
                    <FormLabel>Specimen Kit Number</FormLabel>
                    <Input {...register("specimenKitNumber")} />
                  </FormControl>
                </Grid>
              )}

              {/* Evidence Bag Serial (for SPECIMEN type) */}
              {(referralType === "SPECIMEN" || referralType === "OTHER") && (
                <Grid xs={12} sm={6}>
                  <FormControl>
                    <FormLabel>Evidence Bag Serial</FormLabel>
                    <Input {...register("evidenceBagSerial")} />
                  </FormControl>
                </Grid>
              )}

              {/* Body Tag Photo - Read Only */}
              <Grid xs={12} sm={6}>
                <FormLabel>Body Tag Photo</FormLabel>
                {referral.bodyTagPhoto ? (
                  <Box sx={{ position: 'relative', mt: 1 }}>
                    <AspectRatio ratio="4/3" sx={{ maxWidth: 300 }}>
                      <img
                        src={referral.bodyTagPhoto}
                        alt="Body tag photo"
                        style={{ objectFit: 'cover', borderRadius: '8px' }}
                      />
                    </AspectRatio>
                  </Box>
                ) : (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 2,
                      p: 3,
                      border: '1px dashed',
                      borderColor: 'divider',
                      borderRadius: 'sm',
                    }}
                  >
                    <Icon icon="mdi:tag" style={{ fontSize: '2rem', opacity: 0.5 }} />
                    <Typography level="body-sm" textAlign="center">
                      No body tag photo available
                    </Typography>
                    <Typography level="body-sm" color="neutral" textAlign="center">
                      Body tag photos cannot be modified after referral creation
                    </Typography>
                  </Box>
                )}
              </Grid>

              {/* Vehicle Photo - Read Only */}
              <Grid xs={12} sm={6}>
                <FormLabel>Vehicle Photo</FormLabel>
                {referral.vehiclePhoto ? (
                  <Box sx={{ position: 'relative', mt: 1 }}>
                    <AspectRatio ratio="4/3" sx={{ maxWidth: 300 }}>
                      <img
                        src={referral.vehiclePhoto}
                        alt="Vehicle photo"
                        style={{ objectFit: 'cover', borderRadius: '8px' }}
                      />
                    </AspectRatio>
                  </Box>
                ) : (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 2,
                      p: 3,
                      border: '1px dashed',
                      borderColor: 'divider',
                      borderRadius: 'sm',
                    }}
                  >
                    <Icon icon="mdi:car" style={{ fontSize: '2rem', opacity: 0.5 }} />
                    <Typography level="body-sm" textAlign="center">
                      No vehicle photo available
                    </Typography>
                    <Typography level="body-sm" color="neutral" textAlign="center">
                      Vehicle photos cannot be modified after referral creation
                    </Typography>
                  </Box>
                )}
              </Grid>

              {/* Notes */}
              <Grid xs={12}>
                <FormControl error={!!errors.notes}>
                  <FormLabel>Notes</FormLabel>
                  <Textarea minRows={3} maxRows={5} {...register("notes")} />
                  {errors.notes && (
                    <FormHelperText>{errors.notes.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>

          <Divider />

          <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              color="neutral"
              component={Link}
              href="/dashboard/records/referrals"
              startDecorator={<Icon icon="mdi:arrow-left" />}
            >
              Cancel
            </Button>

            <Button
              type="submit"
              loading={submitting}
              disabled={submitting}
              startDecorator={!submitting && <Icon icon="mdi:content-save" />}
            >
              {submitting ? "Saving..." : "Save Changes"}
            </Button>
          </Box>
        </Card>
      </form>

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <Card
          variant="outlined"
          sx={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            maxWidth: '450px',
            width: '100%',
            zIndex: 9999,
            boxShadow: 'lg',
          }}
        >
          <CardContent>
            <Typography level="title-md" sx={{ mb: 2 }}>Confirm Deletion</Typography>
            <Typography level="body-md" sx={{ mb: 2 }}>
              Are you sure you want to delete this referral? This action cannot be undone.
            </Typography>
            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Button
                variant="plain"
                color="neutral"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={submitting}
              >
                Cancel
              </Button>
              <Button
                variant="solid"
                color="danger"
                onClick={handleDelete}
                loading={submitting}
                disabled={submitting}
              >
                Delete
              </Button>
            </Stack>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}
