// Remove auth check
const loadReferral = async () => {
  try {
    setLoading(true);
    const response = await fetch(`/api/body-referrals/${params.id}`);
    if (!response.ok) throw new Error('Failed to fetch referral');
    const data = await response.json();
    setReferral(data);
  } catch (err) {
    setError(err instanceof Error ? err.message : 'An error occurred');
  } finally {
    setLoading(false);
  }
}; 