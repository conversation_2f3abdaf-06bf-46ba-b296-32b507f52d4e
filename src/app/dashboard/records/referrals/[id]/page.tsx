"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>ack, <PERSON>po<PERSON>, <PERSON>ton, Card, Chip, Grid, <PERSON>bs, TabList, Tab, TabPanel, Divider, Input, IconButton, CircularProgress, Alert, AspectRatio, Modal, ModalDialog } from '@mui/joy';
import { Icon } from '@iconify/react';
import { format } from 'date-fns';
import Link from 'next/link';
import { Body, BodyReferral, ReferralStatus, User } from '@prisma/client';
import { useRouter } from 'next/navigation';
// Helper function to get status color
const getReferralStatusColor = (status: ReferralStatus) => {
  switch (status) {
    case 'PENDING':
      return 'neutral';
    case 'IN_PROGRESS':
      return 'primary';
    case 'COMPLETED':
      return 'success';
    case 'RETURNED':
      return 'warning';
    case 'CANCELLED':
      return 'danger';
    default:
      return 'neutral';
  }
};

interface ReferralActivity {
  id: string;
  activityType: string;
  description: string;
  timestamp: Date;
  userId: string;
  bodyReferralId: string;
  user: Pick<User, 'id' | 'name' | 'role'>;
}

interface ExtendedReferral extends BodyReferral {
  body: Body & {
    bodyTag: {
      id: string;
      tagNumber: string;
      status: string;
    } | null;
  };
  referredBy: Pick<User, 'id' | 'name' | 'role' | 'department'>;
  assignedTo: Pick<User, 'id' | 'name' | 'role' | 'department'>;
  activityLogs: ReferralActivity[];
}

export default function ReferralDetailsPage({ params }: { params: { id: string } }) {
  const [referral, setReferral] = useState<ExtendedReferral | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [noteText, setNoteText] = useState('');
  const [activities, setActivities] = useState<ReferralActivity[]>([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  //navigation
  const router = useRouter();

  useEffect(() => {
    if (referral?.activityLogs) {
      setActivities(referral.activityLogs);
    }
  }, [referral]);

  const loadReferral = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/referrals/${params.id}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Referral not found');
        }
        throw new Error('Failed to fetch referral details');
      }
      const data = await response.json();
      setReferral(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching referral details');
    } finally {
      setLoading(false);
    }
  };

  const handleAddNote = async () => {
    if (!noteText.trim()) return;

    try {
      const response = await fetch(`/api/referrals/${params.id}/activity`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          activityType: 'NOTE',
          description: noteText,
        }),
      });

      if (!response.ok) throw new Error('Failed to add note');
      
      setNoteText('');
      await loadReferral(); // Reload entire referral to get updated activities
    } catch (err) {
      console.error('Error adding note:', err);
    }
  };

  const handleStatusChange = async (newStatus: ReferralStatus) => {
    try {
      const response = await fetch(`/api/referrals/${params.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) throw new Error('Failed to update status');
      
      // Create activity for status change
      await fetch(`/api/referrals/${params.id}/activity`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          activityType: 'STATUS_CHANGE',
          description: `Status changed to ${newStatus}`,
        }),
      });

      await loadReferral(); // Reload the entire referral
    } catch (err) {
      console.error('Error updating status:', err);
    }
  };

  const handleDelete = async () => {
    try {
      await fetch(`/api/referrals/${params.id}`, { method: 'DELETE' });
      router.push('/dashboard/records/referrals');
    } catch (err) {
      console.error('Error deleting referral:', err);
    }
  };

  useEffect(() => {
    loadReferral();
  }, [params.id]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !referral) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert color="danger">{error || 'Referral not found'}</Alert>
      </Box>
    );
  }

  const DeleteConfirmationModal = () => (
    <Modal open={deleteModalOpen} onClose={() => setDeleteModalOpen(false)}>
      <ModalDialog
        variant="outlined"
        role="alertdialog"
        aria-labelledby="delete-confirmation-title"
        aria-describedby="delete-confirmation-description"
      >
        <Typography id="delete-confirmation-title" level="h2">
          Confirm Deletion
        </Typography>
        <Typography id="delete-confirmation-description" level="body-md">
          Are you sure you want to delete this admission? This action cannot be undone.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', mt: 2 }}>
          <Button
            variant="plain"
            color="neutral"
            onClick={() => setDeleteModalOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="solid"
            color="danger"
            onClick={() => {
              handleDelete();
              setDeleteModalOpen(false);
            }}
          >
            Delete Admission
          </Button>
        </Box>
      </ModalDialog>
    </Modal>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 3 }}>
        <Link href="/dashboard/records/referrals">
          <IconButton>
            <Icon icon="mdi:arrow-left" />
          </IconButton>
        </Link>
        <Typography level="h4">Referral Details</Typography>
        <Box sx={{ flexGrow: 1 }} />
        <Chip
          color={
            referral.status === 'COMPLETED' ? 'success' :
            referral.status === 'IN_PROGRESS' ? 'warning' :
            referral.status === 'CANCELLED' ? 'danger' :
            'neutral'
          }
        >
          {referral.status}
          </Chip>
        </Stack>

        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            color="primary"
            startDecorator={<Icon icon="mdi:pencil" />}
            component={Link}
            href={`/dashboard/records/referrals/${referral.id}/edit`}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color="danger"
            startDecorator={<Icon icon="mdi:delete" />}
            onClick={() => setDeleteModalOpen(true)}
          >
            Delete
          </Button>
        </Stack>
      </Stack>

      {/* Main Content */}
      <Grid container spacing={2}>
        {/* Left Column */}
        <Grid xs={12} md={8}>
          <Card>
            <Tabs value={activeTab} onChange={(e, v) => setActiveTab(v as string)}>
              <TabList>
                <Tab value="overview">Overview</Tab>
                <Tab value="activity">Activity</Tab>
                <Tab value="documents">Documents</Tab>
              </TabList>

              <TabPanel value="overview">
                <Grid container spacing={3}>
                  {/* Basic Information */}
                  <Grid xs={12}>
                    <Card variant="outlined">
                      <Typography level="title-md" sx={{ mb: 2 }}>Basic Information</Typography>
                      <Grid container spacing={2}>
                        <Grid xs={12} sm={6}>
                          <Stack spacing={2}>
                            <Box>
                              <Typography level="body-sm" color="neutral">Type</Typography>
                              <Chip
                                variant="soft"
                                color="primary"
                                size="sm"
                              >
                                {referral.referralType}
                              </Chip>
                            </Box>
                            <Box>
                              <Typography level="body-sm" color="neutral">Status</Typography>
                              <Chip
                                variant="soft"
                                color={getReferralStatusColor(referral.status)}
                                size="sm"
                              >
                                {referral.status}
                              </Chip>
                            </Box>
                            <Box>
                              <Typography level="body-sm" color="neutral">Dates</Typography>
                              <Typography level="body-md">
                                Created: {format(new Date(referral.referralDate), 'PPp')}
                              </Typography>
                              {referral.returnDate && (
                                <Typography level="body-md">
                                  Return: {format(new Date(referral.returnDate), 'PPp')}
                                </Typography>
                              )}
                            </Box>
                          </Stack>
                        </Grid>
                        <Grid xs={12} sm={6}>
                          <Stack spacing={2}>
                            <Box>
                              <Typography level="body-sm" color="neutral">Referred By</Typography>
                              <Typography level="body-md">
                                {referral.referredBy.name}
                              </Typography>
                              <Typography level="body-sm" color="neutral">
                                {referral.referredBy.role}
                              </Typography>
                            </Box>
                            <Box>
                              <Typography level="body-sm" color="neutral">Assigned To</Typography>
                              <Typography level="body-md">
                                {referral.assignedTo.name}
                              </Typography>
                              <Typography level="body-sm" color="neutral">
                                {referral.assignedTo.role}
                              </Typography>
                            </Box>
                          </Stack>
                        </Grid>
                      </Grid>
                    </Card>
                  </Grid>

                  {/* Employee Details */}
                  <Grid xs={12}>
                    <Card variant="outlined">
                      <Typography level="title-md" sx={{ mb: 2 }}>Employee Details</Typography>
                      <Grid container spacing={2}>
                        <Grid xs={12} sm={6}>
                          <Stack spacing={2}>
                            <Box>
                              <Typography level="body-sm" color="neutral">Name & PERSAL</Typography>
                              <Typography level="body-md">
                                {referral.employeeName}
                              </Typography>
                              <Typography level="body-sm" color="neutral">
                                PERSAL: {referral.employeePersal}
                              </Typography>
                            </Box>
                            <Box>
                              <Typography level="body-sm" color="neutral">Institution</Typography>
                              <Typography level="body-md">
                                {referral.institutionName}
                              </Typography>
                            </Box>
                          </Stack>
                        </Grid>
                        <Grid xs={12} sm={6}>
                          <Stack spacing={2}>
                            {referral.vehicleReg && (
                              <Box>
                                <Typography level="body-sm" color="neutral">Vehicle Registration</Typography>
                                <Typography level="body-md">{referral.vehicleReg}</Typography>
                              </Box>
                            )}
                            {(referral.gpsLatitude && referral.gpsLongitude) && (
                              <Box>
                                <Typography level="body-sm" color="neutral">GPS Location</Typography>
                                <Typography level="body-md">
                                  {referral.gpsLatitude}, {referral.gpsLongitude}
                                </Typography>
                              </Box>
                            )}
                          </Stack>
                        </Grid>
                      </Grid>
                    </Card>
                  </Grid>

                  {/* Specimen Details */}
                  {(referral.specimenKitNumber || referral.evidenceBagSerial) && (
                    <Grid xs={12}>
                      <Card variant="outlined">
                        <Typography level="title-md" sx={{ mb: 2 }}>Specimen Details</Typography>
                        <Grid container spacing={2}>
                          <Grid xs={12} sm={6}>
                            {referral.specimenKitNumber && (
                              <Box>
                                <Typography level="body-sm" color="neutral">Kit Number</Typography>
                                <Typography level="body-md">{referral.specimenKitNumber}</Typography>
                              </Box>
                            )}
                          </Grid>
                          <Grid xs={12} sm={6}>
                            {referral.evidenceBagSerial && (
                              <Box>
                                <Typography level="body-sm" color="neutral">Evidence Bag Serial</Typography>
                                <Typography level="body-md">{referral.evidenceBagSerial}</Typography>
                              </Box>
                            )}
                          </Grid>
                        </Grid>
                      </Card>
                    </Grid>
                  )}


                  {/* Photos */}
                  {(referral.bodyTagPhoto || referral.vehiclePhoto) && (
                    <Grid xs={12}>
                      <Card variant="outlined">
                        <Typography level="title-md" sx={{ mb: 2 }}>Photos</Typography>
                        <Grid container spacing={2}>
                          {referral.bodyTagPhoto && (
                            <Grid xs={12} sm={6}>
                              <Card variant="soft">
                                <Typography level="body-sm" color="neutral">Body Tag Photo</Typography>
                                <AspectRatio ratio={16/9} sx={{ mt: 1 }}>
                                  <img
                                    src={referral.bodyTagPhoto}
                                    alt="Body Tag"
                                    style={{ objectFit: 'cover', borderRadius: 8 }}
                                  />
                                </AspectRatio>
                              </Card>
                            </Grid>
                          )}
                          {referral.vehiclePhoto && (
                            <Grid xs={12} sm={6}>
                              <Card variant="soft">
                                <Typography level="body-sm" color="neutral">Vehicle Photo</Typography>
                                <AspectRatio ratio={16/9} sx={{ mt: 1 }}>
                                  <img
                                    src={referral.vehiclePhoto}
                                    alt="Vehicle"
                                    style={{ objectFit: 'cover', borderRadius: 8 }}
                                  />
                                </AspectRatio>
                              </Card>
                            </Grid>
                          )}
                        </Grid>
                      </Card>
                    </Grid>
                  )}

                </Grid>
              </TabPanel>

              <TabPanel value="activity">
                <Stack spacing={2}>
                  <Box>
                    <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                      <Input
                        placeholder="Add a note..."
                        value={noteText}
                        onChange={(e) => setNoteText(e.target.value)}
                        sx={{ flexGrow: 1 }}
                      />
                      <Button onClick={handleAddNote}>Add Note</Button>
                    </Stack>
                  </Box>
                  <Divider />
                  {activities.map((activity) => (
                    <Card key={activity.id} size="sm">
                      <Stack spacing={1}>
                        <Stack direction="row" spacing={1} alignItems="center">
                          <Typography level="body-sm" color="primary">
                            {activity.user.name}
                          </Typography>
                          <Typography level="body-xs">
                            {format(new Date(activity.timestamp), 'PPp')}
                          </Typography>
                        </Stack>
                        <Typography>{activity.description}</Typography>
                        <Typography level="body-xs" color="neutral">
                          Type: {activity.activityType}
                        </Typography>
                      </Stack>
                    </Card>
                  ))}
                  {activities.length === 0 && (
                    <Typography level="body-sm" textAlign="center" sx={{ py: 2 }}>
                      No activity logs found
                    </Typography>
                  )}
                </Stack>
              </TabPanel>

              <TabPanel value="documents">
                <Typography>Document management coming soon...</Typography>
              </TabPanel>
            </Tabs>
          </Card>
        </Grid>

        {/* Right Column */}
        <Grid xs={12} md={4}>
          <Stack spacing={2}>
            <Card>
              <Typography level="title-md" sx={{ mb: 2 }}>Status Actions</Typography>
              <Stack spacing={1}>
                {Object.values(ReferralStatus).map((status) => (
                  <Button
                    key={status}
                    variant={referral.status === status ? 'solid' : 'soft'}
                    color={
                      status === 'COMPLETED' ? 'success' :
                      status === 'IN_PROGRESS' ? 'warning' :
                      status === 'CANCELLED' ? 'danger' :
                      'neutral'
                    }
                    onClick={() => handleStatusChange(status)}
                    disabled={referral.status === status}
                  >
                    Mark as {status.toLowerCase()}
                  </Button>
                ))}
              </Stack>
            </Card>

            <Card>
              <Typography level="title-md" sx={{ mb: 2 }}>Body Information</Typography>
              <Stack spacing={2}>
                <Box>
                  <Typography level="body-sm">Tracking Number</Typography>
                  <Typography>{referral.body.trackingNumber}</Typography>
                </Box>
                <Box>
                  <Typography level="body-sm">Status</Typography>
                  <Typography>{referral.body.status}</Typography>
                </Box>
              </Stack>
            </Card>

            <Card>
              <Typography level="title-md" sx={{ mb: 2 }}>Assignment</Typography>
              <Stack spacing={2}>
                <Box>
                  <Typography level="body-sm">Referred By</Typography>
                  <Typography>{referral.referredBy.name}</Typography>
                  <Typography level="body-xs">{referral.referredBy.department}</Typography>
                </Box>
                <Box>
                  <Typography level="body-sm">Assigned To</Typography>
                  <Typography>{referral.assignedTo.name}</Typography>
                  <Typography level="body-xs">{referral.assignedTo.department}</Typography>
                </Box>
              </Stack>
            </Card>

            {referral.gpsLatitude && referral.gpsLongitude && (
              <Card>
                <Typography level="title-md" sx={{ mb: 2 }}>Location</Typography>
                <Typography>
                  {referral.gpsLatitude}, {referral.gpsLongitude}
                </Typography>
              </Card>
            )}
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
}