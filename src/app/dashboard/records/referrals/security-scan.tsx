'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  Typography,
  Button,
  Box,
  Alert,
  Grid,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Textarea,
  Stack,
  Card,
  CardContent,
  IconButton,
  AspectRatio,
  Chip,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import BarcodeReader from '@/components/Barcode/BarcodeReader';

const scanSchema = z.object({
  performedBy: z.string().min(1, 'Security officer name is required'),
  scanResults: z.string().min(1, 'Scan results are required'),
  notes: z.string().optional(),
  passed: z.boolean(),
});

type ScanFormData = z.infer<typeof scanSchema>;

interface SecurityScanProps {
  referralId: string;
  onComplete: (scanData: ScanFormData) => Promise<void>;
  onCancel: () => void;
}

export default function SecurityScan({ referralId, onComplete, onCancel }: SecurityScanProps) {
  const [scanning, setScanning] = React.useState(false);
  const [photo, setPhoto] = React.useState<string | null>(null);
  const [error, setError] = React.useState<string | null>(null);

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<ScanFormData>({
    resolver: zodResolver(scanSchema),
    defaultValues: {
      passed: false,
    },
  });

  const handleScan = async (result: string) => {
    setScanning(false);
    setValue('scanResults', result);
  };

  const handlePhotoCapture = () => {
    // Implement photo capture logic
    // This could use the device camera or file upload
    console.log('Capturing photo...');
  };

  const onSubmit = async (data: ScanFormData) => {
    try {
      await onComplete({
        ...data,
        scanResults: data.scanResults,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to complete security scan');
    }
  };

  return (
    <Sheet
      variant="outlined"
      sx={{
        width: '100%',
        p: 3,
        borderRadius: 'sm',
      }}
    >
      <Stack spacing={3}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography level="h4">Security Scan</Typography>
          <Chip
            variant="soft"
            color="primary"
            startDecorator={<Icon icon="mdi:shield-check" />}
          >
            Security Check Required
          </Chip>
        </Stack>

        {error && (
          <Alert 
            color="danger" 
            variant="soft"
            endDecorator={
              <Button size="sm" variant="soft" color="danger" onClick={() => setError(null)}>
                Dismiss
              </Button>
            }
          >
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <Grid container spacing={2}>
              <Grid xs={12} md={6}>
                <Controller
                  name="performedBy"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.performedBy}>
                      <FormLabel>Security Officer Name</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter name"
                      />
                      {errors.performedBy && (
                        <FormHelperText>{errors.performedBy.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Stack spacing={2}>
                      <Typography level="title-md">Body Scanning</Typography>
                      
                      {scanning ? (
                        <Box sx={{ position: 'relative', height: 300 }}>
                          <BarcodeReader onScan={handleScan} />
                          <Button
                            variant="outlined"
                            color="neutral"
                            onClick={() => setScanning(false)}
                            sx={{ position: 'absolute', top: 8, right: 8 }}
                          >
                            Cancel Scan
                          </Button>
                        </Box>
                      ) : (
                        <Button
                          startDecorator={<Icon icon="mdi:barcode-scan" />}
                          onClick={() => setScanning(true)}
                        >
                          Start Scanning
                        </Button>
                      )}

                      <Controller
                        name="scanResults"
                        control={control}
                        render={({ field }) => (
                          <FormControl error={!!errors.scanResults}>
                            <FormLabel>Scan Results</FormLabel>
                            <Textarea
                              {...field}
                              minRows={3}
                              placeholder="Scan results will appear here..."
                            />
                            {errors.scanResults && (
                              <FormHelperText>{errors.scanResults.message}</FormHelperText>
                            )}
                          </FormControl>
                        )}
                      />
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>

              <Grid xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Stack spacing={2}>
                      <Typography level="title-md">Body Bag Photo</Typography>
                      
                      {photo ? (
                        <AspectRatio ratio="16/9" objectFit="cover">
                          <img
                            src={photo}
                            alt="Body bag"
                            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                          />
                          <IconButton
                            size="sm"
                            variant="solid"
                            color="danger"
                            onClick={() => setPhoto(null)}
                            sx={{ position: 'absolute', top: 8, right: 8 }}
                          >
                            <Icon icon="mdi:delete" />
                          </IconButton>
                        </AspectRatio>
                      ) : (
                        <Button
                          startDecorator={<Icon icon="mdi:camera" />}
                          onClick={handlePhotoCapture}
                        >
                          Capture Photo
                        </Button>
                      )}
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>

              <Grid xs={12}>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.notes}>
                      <FormLabel>Additional Notes</FormLabel>
                      <Textarea
                        {...field}
                        minRows={3}
                        placeholder="Enter any additional notes or observations..."
                      />
                      {errors.notes && (
                        <FormHelperText>{errors.notes.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12}>
                <Controller
                  name="passed"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.passed}>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Button
                          variant={field.value ? 'solid' : 'outlined'}
                          color="success"
                          onClick={() => field.onChange(true)}
                          startDecorator={<Icon icon="mdi:check-circle" />}
                        >
                          Pass Security Check
                        </Button>
                        <Button
                          variant={!field.value ? 'solid' : 'outlined'}
                          color="danger"
                          onClick={() => field.onChange(false)}
                          startDecorator={<Icon icon="mdi:close-circle" />}
                        >
                          Fail Security Check
                        </Button>
                      </Stack>
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>

            <Stack direction="row" spacing={2} justifyContent="flex-end">
              <Button
                variant="outlined"
                color="neutral"
                onClick={onCancel}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={isSubmitting}
                startDecorator={<Icon icon="mdi:check" />}
              >
                Complete Security Check
              </Button>
            </Stack>
          </Stack>
        </form>
      </Stack>
    </Sheet>
  );
}