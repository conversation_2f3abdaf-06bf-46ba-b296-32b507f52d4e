"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>ton, <PERSON>ack, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, CardContent, Grid, Chip, Input, IconButton, Tooltip, Select, Option, Divider, CircularProgress } from '@mui/joy';
import Link from 'next/link';
import { Icon } from '@iconify/react';
import { BodyReferral, ReferralStatus, ReferralType } from '@prisma/client';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';

interface ExtendedReferral extends Omit<BodyReferral, 'body' | 'referredBy' | 'assignedTo'> {
  body: {
    id: string;
    trackingNumber: string;
    status: string;
    bodyTag?: {
      tagNumber: string;
      status: string;
    } | null;
  };
  referredBy: {
    id: string;
    name: string | null;
    role: string;
    department: string | null;
  };
  assignedTo: {
    id: string;
    name: string | null;
    role: string;
    department: string | null;
  };
}

type ViewMode = 'grid' | 'list';

export default function ReferralsDirectoryPage() {
  const [referrals, setReferrals] = useState<ExtendedReferral[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<ReferralStatus | null>(null);
  const [filterType, setFilterType] = useState<ReferralType | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState('status');
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 1
  });

  const loadReferrals = async (page = pagination.page, pageSize = pagination.pageSize) => {
    setLoading(true);
    setError(null);
    try {
      const searchParams = new URLSearchParams();
      if (searchTerm) searchParams.append('search', searchTerm);
      if (filterStatus) searchParams.append('status', filterStatus);
      if (filterType) searchParams.append('type', filterType);
      searchParams.append('page', page.toString());
      searchParams.append('pageSize', pageSize.toString());

      const response = await fetch(`/api/referrals?${searchParams.toString()}`, {
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('You are not authorized to view referrals');
        } else if (response.status === 404) {
          throw new Error('The referrals endpoint could not be found');
        } else {
          throw new Error(`Failed to fetch referrals: ${response.statusText}`);
        }
      }
      
      let result;
      try {
        result = await response.json();
      } catch (err) {
        throw new Error('Invalid JSON response from server');
      }

      // Handle paginated response (format: { data: [...], pagination: {...} })
      if (result && typeof result === 'object' && 'data' in result && Array.isArray(result.data)) {
        // It's a paginated response, extract the data array
        console.log('Received paginated data:', result);
        setReferrals(result.data);
        
        // If we have pagination info, store it
        if (result.pagination) {
          setPagination({
            page: result.pagination.page || page,
            pageSize: result.pagination.pageSize || pageSize,
            total: result.pagination.total || result.data.length,
            totalPages: result.pagination.totalPages || 1
          });
        }
      } else if (Array.isArray(result)) {
        // Handle case where API returns direct array
        console.log('Received array data:', result);
        setReferrals(result);
        // Reset pagination for unpaginated response
        setPagination({
          page: 1,
          pageSize: result.length,
          total: result.length,
          totalPages: 1
        });
      } else {
        console.error('Received invalid data format:', result);
        throw new Error('Invalid data format received from server');
      }
    } catch (err) {
      console.error('Error loading referrals:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadReferrals();
  }, [searchTerm, filterStatus, filterType]);

  const getStatusColor = (status: ReferralStatus) => {
    const colors: Record<ReferralStatus, 'primary' | 'success' | 'warning' | 'danger' | 'neutral'> = {
      PENDING: 'warning',
      IN_PROGRESS: 'primary',
      COMPLETED: 'success',
      CANCELLED: 'danger',
      RETURNED: 'neutral'
    };
    return colors[status] || 'neutral';
  };

  const getReferralTypeIcon = (type: ReferralType) => {
    const icons: Record<ReferralType, string> = {
      LODOX: 'mdi:radiography',
      XRAY: 'mdi:radiography-box',
      SPECIMEN: 'mdi:test-tube',
      OTHER: 'mdi:file-document-outline'
    };
    return icons[type] || 'mdi:help-circle-outline';
  };

  const sortedReferrals = [...referrals].sort((a, b) => {
    switch (sortBy) {
      case 'status':
        return (a.status || '').localeCompare(b.status || '');
      case 'date':
        return new Date(b.referralDate).getTime() - new Date(a.referralDate).getTime();
      case 'type':
        return (a.referralType || '').localeCompare(b.referralType || '');
      case 'deadline':
        return new Date(a.returnDate || '').getTime() - new Date(b.returnDate || '').getTime();
      default:
        return 0;
    }
  });

  const renderGridView = () => (
    <Grid container spacing={2}>
      <AnimatePresence>
        {sortedReferrals.map((referral) => (
          <Grid key={referral.id} xs={12} sm={6} md={4} lg={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <Card 
                component={Link} 
                href={`/dashboard/records/referrals/${referral.id}`}
                variant="outlined"
                sx={{ 
                  textDecoration: 'none',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 'md',
                    borderColor: 'primary.500',
                  }
                }}
              >
                <CardContent>
                  <Stack spacing={2}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Chip
                        size="sm"
                        variant="soft"
                        color={getStatusColor(referral.status)}
                        startDecorator={<Icon icon={getReferralTypeIcon(referral.referralType)} />}
                      >
                        {referral.referralType}
                      </Chip>
                      <Chip
                        size="sm"
                        variant="soft"
                        color={getStatusColor(referral.status)}
                      >
                        {referral.status.replace('_', ' ')}
                      </Chip>
                    </Stack>
                    
                    <Divider />
                    
                    <Stack spacing={1}>
                      <Box>
                        <Typography level="body-xs">Employee Details</Typography>
                        <Typography level="body-sm" startDecorator={<Icon icon="mdi:account" />}>
                          {referral.employeeName}
                        </Typography>
                        <Typography level="body-sm" startDecorator={<Icon icon="mdi:card-account-details" />}>
                          {referral.employeePersal}
                        </Typography>
                      </Box>

                      <Box>
                        <Typography level="body-xs">Institution</Typography>
                        <Typography level="body-sm" startDecorator={<Icon icon="mdi:hospital-building" />}>
                          {referral.institutionName}
                        </Typography>
                      </Box>

                      <Box>
                        <Typography level="body-xs">Dates</Typography>
                        <Typography level="body-sm" startDecorator={<Icon icon="mdi:calendar-start" />}>
                          {format(new Date(referral.referralDate), 'PPP')}
                        </Typography>
                        {referral.returnDate && (
                          <Typography level="body-sm" startDecorator={<Icon icon="mdi:calendar-end" />}>
                            Due: {format(new Date(referral.returnDate), 'PPP')}
                          </Typography>
                        )}
                      </Box>

                      <Box>
                        <Typography level="body-xs">Assigned To</Typography>
                        <Typography level="body-sm" startDecorator={<Icon icon="mdi:account-cog" />}>
                          {referral.assignedTo.name}
                        </Typography>
                      </Box>
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </AnimatePresence>
    </Grid>
  );

  const renderListView = () => (
    <Stack spacing={2}>
      <AnimatePresence>
        {sortedReferrals.map((referral) => (
          <motion.div
            key={referral.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
          >
            <Card
              component={Link}
              href={`/dashboard/records/referrals/${referral.id}`}
              variant="outlined"
              sx={{
                textDecoration: 'none',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateX(4px)',
                  boxShadow: 'sm',
                  borderColor: 'primary.500',
                }
              }}
            >
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid xs={12} sm={3}>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Icon icon={getReferralTypeIcon(referral.referralType)} width={24} />
                      <Stack>
                        <Typography level="body-sm" fontWeight="bold">
                          {referral.referralType}
                        </Typography>
                        <Typography level="body-xs">
                          {referral.body.trackingNumber}
                        </Typography>
                      </Stack>
                    </Stack>
                  </Grid>

                  <Grid xs={12} sm={3}>
                    <Stack>
                      <Typography level="body-xs">Employee</Typography>
                      <Typography level="body-sm">
                        {referral.employeeName}
                      </Typography>
                    </Stack>
                  </Grid>

                  <Grid xs={12} sm={3}>
                    <Stack>
                      <Typography level="body-xs">Institution</Typography>
                      <Typography level="body-sm">
                        {referral.institutionName}
                      </Typography>
                    </Stack>
                  </Grid>

                  <Grid xs={12} sm={2}>
                    <Stack>
                      <Typography level="body-xs">Due Date</Typography>
                      <Typography level="body-sm">
                        {referral.returnDate ? format(new Date(referral.returnDate), 'PP') : 'N/A'}
                      </Typography>
                    </Stack>
                  </Grid>

                  <Grid xs={12} sm={1}>
                    <Chip
                      size="sm"
                      variant="soft"
                      color={getStatusColor(referral.status)}
                    >
                      {referral.status}
                    </Chip>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </AnimatePresence>
    </Stack>
  );

  // Add a function to handle page changes
  const handlePageChange = (event: React.MouseEvent, newPage: number) => {
    event.preventDefault();
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      loadReferrals(newPage, pagination.pageSize);
    }
  };

  // Add this function after the renderListView function
  const renderPagination = () => {
    if (pagination.totalPages <= 1) return null;
    
    return (
      <Card variant="outlined" sx={{ mt: 2 }}>
        <CardContent>
          <Stack 
            direction="row" 
            spacing={1} 
            justifyContent="center" 
            alignItems="center"
          >
            <Button
              variant="plain"
              color="neutral"
              disabled={pagination.page <= 1}
              onClick={(e) => handlePageChange(e, 1)}
            >
              <Icon icon="mdi:page-first" />
            </Button>
            <Button
              variant="plain"
              color="neutral"
              disabled={pagination.page <= 1}
              onClick={(e) => handlePageChange(e, pagination.page - 1)}
            >
              <Icon icon="mdi:chevron-left" />
            </Button>
            
            <Typography level="body-sm">
              Page {pagination.page} of {pagination.totalPages}
            </Typography>
            
            <Button
              variant="plain"
              color="neutral"
              disabled={pagination.page >= pagination.totalPages}
              onClick={(e) => handlePageChange(e, pagination.page + 1)}
            >
              <Icon icon="mdi:chevron-right" />
            </Button>
            <Button
              variant="plain"
              color="neutral"
              disabled={pagination.page >= pagination.totalPages}
              onClick={(e) => handlePageChange(e, pagination.totalPages)}
            >
              <Icon icon="mdi:page-last" />
            </Button>
          </Stack>
        </CardContent>
      </Card>
    );
  };

  return (
    <Stack spacing={3} sx={{ p: 3 }}>
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Stack direction="row" spacing={2} justifyContent="space-between" alignItems="center">
            <Typography level="h2">
              Referrals
            </Typography>
            <Stack direction="row" alignItems="center" spacing={1}>
              {pagination.total > 0 && (
                <Typography level="body-sm" color="neutral">
                  Showing {Math.min(referrals.length, pagination.pageSize)} of {pagination.total} records
                </Typography>
              )}
              <Link href="/dashboard/records/referrals/new" passHref>
                <Button startDecorator={<Icon icon="mdi:plus" />}>New Referral</Button>
              </Link>
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {error && (
        <Alert 
          color="danger" 
          variant="soft"
          endDecorator={
            <Button onClick={loadReferrals} size="sm" variant="soft">
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      <Card variant="outlined">
        <CardContent>
          <Stack spacing={2}>
            <Grid container spacing={2} alignItems="center">
              <Grid xs={12} sm={4}>
                <Input
                  size="sm"
                  placeholder="Search referrals..."
                  startDecorator={<Icon icon="mdi:magnify" />}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </Grid>

              <Grid xs={12} sm={3}>
                <Select
                  size="sm"
                  placeholder="Filter by status"
                  value={filterStatus || ''}
                  onChange={(_, value) => setFilterStatus(value as ReferralStatus)}
                  startDecorator={<Icon icon="mdi:filter-variant" />}
                >
                  <Option value="">All Statuses</Option>
                  {Object.values(ReferralStatus).map((status) => (
                    <Option key={status} value={status}>
                      {status.replace('_', ' ')}
                    </Option>
                  ))}
                </Select>
              </Grid>

              <Grid xs={12} sm={3}>
                <Select
                  size="sm"
                  placeholder="Filter by type"
                  value={filterType || ''}
                  onChange={(_, value) => setFilterType(value as ReferralType)}
                  startDecorator={<Icon icon="mdi:shape-outline" />}
                >
                  <Option value="">All Types</Option>
                  {Object.values(ReferralType).map((type) => (
                    <Option key={type} value={type}>
                      {type}
                    </Option>
                  ))}
                </Select>
              </Grid>

              <Grid xs={12} sm={2}>
                <Stack direction="row" spacing={1} justifyContent="flex-end">
                  <Tooltip title="Grid View">
                    <IconButton
                      variant={viewMode === 'grid' ? 'soft' : 'plain'}
                      color={viewMode === 'grid' ? 'primary' : 'neutral'}
                      onClick={() => setViewMode('grid')}
                    >
                      <Icon icon="mdi:grid" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="List View">
                    <IconButton
                      variant={viewMode === 'list' ? 'soft' : 'plain'}
                      color={viewMode === 'list' ? 'primary' : 'neutral'}
                      onClick={() => setViewMode('list')}
                    >
                      <Icon icon="mdi:format-list-bulleted" />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Grid>
            </Grid>

            <Divider />

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress size="lg" />
              </Box>
            ) : referrals.length === 0 ? (
              <Box sx={{ textAlign: 'center', p: 3 }}>
                <Typography level="body-lg" sx={{ mb: 1 }}>
                  No referrals found
                </Typography>
                <Typography level="body-sm" sx={{ mb: 2 }}>
                  {searchTerm || filterStatus || filterType
                    ? 'Try adjusting your filters'
                    : 'Create a new referral to get started'}
                </Typography>
                <Button
                  component={Link}
                  href="/dashboard/records/referrals/new"
                  startDecorator={<Icon icon="mdi:plus" />}
                  size="sm"
                >
                  New Referral
                </Button>
              </Box>
            ) : viewMode === 'grid' ? (
              <>
                {renderGridView()}
                {renderPagination()}
              </>
            ) : (
              <>
                {renderListView()}
                {renderPagination()}
              </>
            )}
          </Stack>
        </CardContent>
      </Card>
    </Stack>
  );
}
