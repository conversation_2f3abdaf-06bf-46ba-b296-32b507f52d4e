'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";

import { Box, CircularProgress } from '@mui/joy';
import { Suspense } from 'react';
import { BodyReferralForm } from '@/components/Forms/BodyReferral/ReferralForm';

export default function AddReferralPage() {
  return (
    <Box sx={{ p: 3 }}>
      {/* Form */}
      <Suspense fallback={
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        }>
          <BodyReferralForm />
        </Suspense>
    </Box>
  );
}
