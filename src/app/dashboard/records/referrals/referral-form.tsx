'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Alert,
  Grid,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Textarea,
  Stack,
  Select,
  Option,
  Stepper,
  Step,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ReferralRecord } from '@/types/records';
import DatePicker from '@/components/UI/FormFields/DatePicker';

const referralSchema = z.object({
  bodyTag: z.string().min(1, 'Body tag is required'),
  deathRegisterNumber: z.string().min(1, 'Death register number is required'),
  referralType: z.enum(['LODOX', 'XRAY', 'SPECIMEN', 'OTHER']),
  referralInstitution: z.string().min(1, 'Referral institution is required'),
  referralReason: z.string().min(1, 'Referral reason is required'),
  
  // Employee Details
  employeeId: z.string().min(1, 'Employee ID is required'),
  persalNumber: z.string().min(1, 'PERSAL number is required'),
  employeeName: z.string().min(1, 'Employee name is required'),
  
  // Reference Numbers
  specimenKitNumber: z.string().optional(),
  evidenceBagNumber: z.string().optional(),
  
  // Transport Details
  vehicleReg: z.string().min(1, 'Vehicle registration is required'),
  driverName: z.string().min(1, 'Driver name is required'),
  driverContact: z.string().min(1, 'Driver contact is required'),
  
  // Dates
  referralDate: z.date(),
  expectedReturnDate: z.date().optional(),
  
  notes: z.string().optional(),
});

type ReferralFormData = z.infer<typeof referralSchema>;

interface ReferralFormProps {
  isEdit?: boolean;
  initialData?: Partial<ReferralRecord>;
  onComplete: () => void;
}

export default function ReferralForm({ isEdit = false, initialData, onComplete }: ReferralFormProps) {
  const [error, setError] = React.useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    trigger,
    formState: { errors },
  } = useForm<ReferralFormData>({
    resolver: zodResolver(referralSchema),
    defaultValues: {
      ...initialData,
      referralDate: initialData?.referralDate || new Date(),
    },
  });

  const handleFormSubmit = async (data: ReferralFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const url = isEdit 
        ? `/api/body-referrals/${initialData?.id}` 
        : '/api/body-referrals';
      
      const response = await fetch(url, {
        method: isEdit ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${isEdit ? 'update' : 'create'} referral`);
      }

      onComplete();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = async () => {
    let isValid = false;
    switch (activeStep) {
      case 0:
        isValid = await trigger(['bodyTag', 'deathRegisterNumber']);
        break;
      case 1:
        isValid = await trigger(['referralType', 'referralInstitution', 'referralReason']);
        break;
      case 2:
        isValid = await trigger(['employeeId', 'persalNumber', 'employeeName']);
        break;
      case 3:
        isValid = await trigger(['vehicleReg', 'driverName', 'driverContact']);
        break;
      case 4:
        isValid = await trigger(['referralDate']);
        break;
    }
    
    if (isValid) {
      setActiveStep((prev) => Math.min(prev + 1, 5));
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => Math.max(prev - 1, 0));
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box>
            <Typography level="title-md" sx={{ mb: 2 }}>Body Information</Typography>
            <Grid container spacing={2}>
              <Grid xs={12} md={6}>
                <Controller
                  name="bodyTag"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.bodyTag}>
                      <FormLabel>Body Tag</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter body tag"
                      />
                      {errors.bodyTag && (
                        <FormHelperText>{errors.bodyTag.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12} md={6}>
                <Controller
                  name="deathRegisterNumber"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.deathRegisterNumber}>
                      <FormLabel>Death Register Number</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter death register number"
                      />
                      {errors.deathRegisterNumber && (
                        <FormHelperText>{errors.deathRegisterNumber.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography level="title-md" sx={{ mb: 2 }}>Referral Details</Typography>
            <Grid container spacing={2}>
              <Grid xs={12} md={6}>
                <Controller
                  name="referralType"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.referralType}>
                      <FormLabel>Referral Type</FormLabel>
                      <Select
                        {...field}
                        placeholder="Select type"
                      >
                        <Option value="LODOX">Lodox</Option>
                        <Option value="XRAY">X-Ray</Option>
                        <Option value="SPECIMEN">Specimen Taking</Option>
                        <Option value="OTHER">Other</Option>
                      </Select>
                      {errors.referralType && (
                        <FormHelperText>{errors.referralType.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12} md={6}>
                <Controller
                  name="referralInstitution"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.referralInstitution}>
                      <FormLabel>Referral Institution</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter institution name"
                      />
                      {errors.referralInstitution && (
                        <FormHelperText>{errors.referralInstitution.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12}>
                <Controller
                  name="referralReason"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.referralReason}>
                      <FormLabel>Reason for Referral</FormLabel>
                      <Textarea
                        {...field}
                        minRows={3}
                        placeholder="Enter reason for referral"
                      />
                      {errors.referralReason && (
                        <FormHelperText>{errors.referralReason.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography level="title-md" sx={{ mb: 2 }}>Employee Details</Typography>
            <Grid container spacing={2}>
              <Grid xs={12} md={4}>
                <Controller
                  name="employeeId"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.employeeId}>
                      <FormLabel>Employee ID</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter employee ID"
                      />
                      {errors.employeeId && (
                        <FormHelperText>{errors.employeeId.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12} md={4}>
                <Controller
                  name="persalNumber"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.persalNumber}>
                      <FormLabel>PERSAL Number</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter PERSAL number"
                      />
                      {errors.persalNumber && (
                        <FormHelperText>{errors.persalNumber.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12} md={4}>
                <Controller
                  name="employeeName"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.employeeName}>
                      <FormLabel>Employee Name</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter full name"
                      />
                      {errors.employeeName && (
                        <FormHelperText>{errors.employeeName.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography level="title-md" sx={{ mb: 2 }}>Transport Details</Typography>
            <Grid container spacing={2}>
              <Grid xs={12} md={4}>
                <Controller
                  name="vehicleReg"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.vehicleReg}>
                      <FormLabel>Vehicle Registration</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter vehicle registration"
                      />
                      {errors.vehicleReg && (
                        <FormHelperText>{errors.vehicleReg.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12} md={4}>
                <Controller
                  name="driverName"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.driverName}>
                      <FormLabel>Driver Name</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter driver's name"
                      />
                      {errors.driverName && (
                        <FormHelperText>{errors.driverName.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12} md={4}>
                <Controller
                  name="driverContact"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.driverContact}>
                      <FormLabel>Driver Contact</FormLabel>
                      <Input
                        {...field}
                        placeholder="Enter driver's contact"
                      />
                      {errors.driverContact && (
                        <FormHelperText>{errors.driverContact.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        );
      case 4:
        return (
          <Box>
            <Typography level="title-md" sx={{ mb: 2 }}>Dates</Typography>
            <Grid container spacing={2}>
              <Grid xs={12} md={6}>
                <Controller
                  name="referralDate"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.referralDate}>
                      <DatePicker
                        {...field}
                        control={control}
                        label="Referral Date"
                        required
                      />
                      {errors.referralDate && (
                        <FormHelperText>{errors.referralDate.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid xs={12} md={6}>
                <Controller
                  name="expectedReturnDate"
                  control={control}
                  render={({ field }) => (
                    <FormControl error={!!errors.expectedReturnDate}>
                      <DatePicker
                        {...field}
                        control={control}
                        label="Expected Return Date"
                        required
                      />
                      {errors.expectedReturnDate && (
                        <FormHelperText>{errors.expectedReturnDate.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        );
      case 5:
        return (
          <Box>
            <Typography level="title-md" sx={{ mb: 2 }}>Review Referral Details</Typography>
            {/* Add a summary of all entered details */}
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Sheet
      variant="outlined"
      sx={{
        width: '100%',
        p: 3,
        borderRadius: 'sm',
      }}
    >
      <Stack spacing={3}>
        <Typography level="h4">
          {isEdit ? 'Edit Referral' : 'New Referral'}
        </Typography>

        {error && (
          <Alert 
            color="danger" 
            variant="soft"
            endDecorator={
              <Button size="sm" variant="soft" color="danger" onClick={() => setError(null)}>
                Dismiss
              </Button>
            }
          >
            {error}
          </Alert>
        )}

        <Stepper 
          orientation="horizontal" 
          sx={{ width: '100%', mb: 2 }}
        >
          <Step
            completed={activeStep > 0} 
            active={activeStep === 0}
          >
            Body Info
          </Step>
          <Step 
            completed={activeStep > 1} 
            active={activeStep === 1}
          >
            Referral Details
          </Step>
          <Step 
            completed={activeStep > 2} 
            active={activeStep === 2}
          >
            Employee Details
          </Step>
          <Step 
            completed={activeStep > 3} 
            active={activeStep === 3}
          >
            Transport Details
          </Step>
          <Step 
            completed={activeStep > 4} 
            active={activeStep === 4}
          >
            Dates
          </Step>
          <Step 
            completed={activeStep === 5} 
            active={activeStep === 5}
          >
            Review
          </Step>
        </Stepper>

        <form onSubmit={handleSubmit(handleFormSubmit)}>
          <Stack spacing={3}>
            {renderStepContent()}

            <Stack direction="row" spacing={2} justifyContent="space-between">
              <Button
                variant="outlined"
                color="neutral"
                onClick={activeStep === 0 ? onComplete : handleBack}
              >
                {activeStep === 0 ? 'Cancel' : 'Back'}
              </Button>
              
              {activeStep < 5 ? (
                <Button
                  onClick={handleNext}
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="submit"
                  loading={isSubmitting}
                  startDecorator={<Icon icon="mdi:check" />}
                >
                  {isEdit ? 'Update Referral' : 'Create Referral'}
                </Button>
              )}
            </Stack>
          </Stack>
        </form>
      </Stack>
    </Sheet>
  );
}