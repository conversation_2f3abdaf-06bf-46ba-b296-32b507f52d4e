"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Typo<PERSON>,
  <PERSON>,
  Stack,
  Divider,
  IconButton,
  CircularProgress,
  Input,
  Select,
  Option,
  Table,
  Sheet,
  Modal,
  ModalDialog,
  ToggleButtonGroup,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { BodyRelease, ReleaseStatus } from '@prisma/client';

interface ExtendedBodyRelease extends BodyRelease {
  body?: {
    id: string;
    trackingNumber: string;
    status: string;
    bodyTag?: {
      tagNumber: string;
      status: string;
    };
  };
  verifiedBy?: {
    name: string;
    role: string;
  };
  releasedBy?: {
    name: string;
    role: string;
  };
  undertakerName: string;
  undertakerCompany: string;
  releasedTo: string;
  relationship: string;
}

// Add paginated response interface
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page?: number;
    pageSize?: number;
    total?: number;
    totalPages?: number;
  };
}

export default function ReleasesPage() {
  const router = useRouter();
  const [releases, setReleases] = React.useState<ExtendedBodyRelease[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<{
    message: string;
    details?: string;
    code?: string;
  } | null>(null);
  const [successMessage, setSuccessMessage] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState<ReleaseStatus | 'all'>('all');
  const [deleteModal, setDeleteModal] = React.useState<string | null>(null);
  const [viewMode, setViewMode] = React.useState<'grid' | 'table'>('grid');
  const [pagination, setPagination] = React.useState<{
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  }>({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });

  // Load releases
  const loadReleases = React.useCallback(async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      setError(null); // Clear previous errors
      setSuccessMessage(null); // Clear previous success messages
      
      const response = await fetch(`/api/releases?page=${page}&pageSize=${pageSize}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch releases: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      
      // Check if response has the expected pagination structure
      if (result && typeof result === 'object' && 'data' in result && Array.isArray(result.data)) {
        // It's a paginated response, extract the data array
        setReleases(result.data);
        // Update pagination state if available
        if (result.pagination) {
          setPagination({
            page: result.pagination.page || page,
            pageSize: result.pagination.pageSize || pageSize,
            total: result.pagination.total || 0,
            totalPages: result.pagination.totalPages || 0
          });
        } else {
          // If no pagination info provided, calculate basic info
          setPagination({
            page: page,
            pageSize: pageSize,
            total: result.data.length,
            totalPages: 1
          });
        }
      } else if (Array.isArray(result)) {
        // It's already an array
        setReleases(result);
        // Reset pagination for unpaginated response
        setPagination({
          page: 1,
          pageSize: result.length,
          total: result.length,
          totalPages: 1
        });
      } else {
        // Try to extract some information about the result for debugging
        const resultType = typeof result;
        let resultKeys = '';
        try {
          if (result && typeof result === 'object') {
            resultKeys = Object.keys(result).join(', ');
          }
        } catch (e) {
          resultKeys = 'Unable to extract keys';
        }
        
        console.error('API returned invalid format:', result);
        console.error('Response type:', resultType);
        console.error('Available keys:', resultKeys);
        
        setReleases([]);
        setError({
          message: 'Invalid data received from server',
          details: `Expected an array or paginated response object but received: ${resultType}${resultKeys ? ` with keys: ${resultKeys}` : ''}. Check the browser console for more details.`,
          code: 'FORMAT_ERROR'
        });
    }
    } catch (err) {
      console.error('Error loading releases:', err);
      setReleases([]);
      
      if (err instanceof Error) {
        // Check for network connectivity issues
        if (err.message.includes('fetch') || err.message.includes('network') || err.name === 'TypeError') {
          setError({
            message: 'Network connection issue',
            details: 'Unable to connect to the server. Please check your internet connection and try again.',
            code: 'NETWORK_ERROR'
          });
        } else {
          setError({
            message: 'Failed to load releases',
            details: err.message,
            code: err.name
          });
        }
      } else {
        setError({
          message: 'An unexpected error occurred',
          details: 'Please try again or contact support if the problem persists',
          code: 'UNKNOWN_ERROR'
        });
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.totalPages) return;
    loadReleases(newPage, pagination.pageSize);
  };

  React.useEffect(() => {
    // Initial load of the first page
    loadReleases(1, 10);
  }, [loadReleases]); // Only depend on loadReleases

  // Handle delete
  const handleDelete = async () => {
    if (!deleteModal) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/releases/${deleteModal}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete release: ${response.status} ${response.statusText}`);
      }

      await loadReleases();
      // Show success notification
      setSuccessMessage('Release has been successfully deleted');
    } catch (err) {
      console.error('Error deleting release:', err);
      
      if (err instanceof Error) {
        // Check for network connectivity issues
        if (err.message.includes('fetch') || err.message.includes('network') || err.name === 'TypeError') {
          setError({
            message: 'Network connection issue',
            details: 'Unable to connect to the server. Please check your internet connection and try again.',
            code: 'NETWORK_ERROR'
          });
        } else {
          setError({
            message: 'Failed to delete release',
            details: err.message,
            code: err.name
          });
        }
      } else {
        setError({
          message: 'An error occurred',
          details: 'Failed to delete release',
          code: 'UNKNOWN_ERROR'
        });
      }
    } finally {
      setLoading(false);
      setDeleteModal(null);
    }
  };

  // Filter releases
  const filteredReleases = React.useMemo(() => {
    if (!Array.isArray(releases)) return [];

    return releases.filter(release => {
      const matchesSearch =
        (release.releasedTo?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
        (release.bodyId?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
        (release.undertakerName?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
        (release.body?.trackingNumber?.toLowerCase() || '').includes(searchQuery.toLowerCase());

      const matchesStatus =
        statusFilter === 'all' ||
        release.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [releases, searchQuery, statusFilter]);

  const getStatusColor = (status: ReleaseStatus): 'primary' | 'neutral' | 'danger' | 'success' | 'warning' => {
    switch (status) {
      case 'PENDING':
        return 'warning';
      case 'VERIFIED':
        return 'primary';
      case 'COMPLETED':
        return 'success';
      case 'CANCELLED':
        return 'danger';
      default:
        return 'neutral';
    }
  };

  const getStatusLabel = (status: ReleaseStatus) => {
    return status.charAt(0) + status.slice(1).toLowerCase();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Stack spacing={3}>
        {/* Header */}
        <Card variant="outlined">
          <CardContent>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Stack spacing={1}>
                <Typography level="h3">
                  Body Releases
                </Typography>
                <Typography level="body-sm" color="neutral">
                  Manage and track body release records
                </Typography>
              </Stack>
              <Stack direction="row" spacing={2} alignItems="center">
                {pagination && pagination.total > 0 && (
                  <Typography level="body-sm" color="neutral">
                    Showing {Math.min(pagination.pageSize, pagination.total)} of {pagination.total} records
                  </Typography>
                )}
              <Button
                component={Link}
                href="/dashboard/records/releases/new"
                startDecorator={<Icon icon="mdi:plus" />}
                size="md"
              >
                New Release
              </Button>
              </Stack>
            </Stack>
          </CardContent>
        </Card>

        {/* Filters */}
        <Card>
          <CardContent>
            <Grid container spacing={2} alignItems="flex-end">
              <Grid xs={12} sm={6} md={4}>
                <Stack spacing={1}>
                  <Typography level="body-sm">Search</Typography>
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search by name, body ID, or undertaker"
                    startDecorator={<Icon icon="mdi:magnify" />}
                  />
                </Stack>
              </Grid>
              <Grid xs={12} sm={6} md={4}>
                <Stack spacing={1}>
                  <Typography level="body-sm">Status</Typography>
                  <Select
                    value={statusFilter}
                    onChange={(e, value) => setStatusFilter(value || 'all')}
                  >
                    <Option value="all">All Statuses</Option>
                    <Option value="PENDING">Pending</Option>
                    <Option value="VERIFIED">Verified</Option>
                    <Option value="COMPLETED">Completed</Option>
                    <Option value="CANCELLED">Cancelled</Option>
                  </Select>
                </Stack>
              </Grid>
              <Grid xs={12} md={4}>
                <Stack direction="row" spacing={1} justifyContent="flex-end">
                  <ToggleButtonGroup
                    value={viewMode}
                    onChange={(e, value) => value && setViewMode(value)}
                    sx={{ mr: 1 }}
                  >
                    <Button value="grid">
                      <Icon icon="mdi:grid" />
                    </Button>
                    <Button value="table">
                      <Icon icon="mdi:table" />
                    </Button>
                  </ToggleButtonGroup>
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:refresh" />}
                    onClick={() => loadReleases()}
                  >
                    Refresh
                  </Button>
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:filter-off" />}
                    onClick={() => {
                      setSearchQuery('');
                      setStatusFilter('all');
                    }}
                  >
                    Clear Filters
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Results */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Card variant="soft" color="danger">
            <CardContent>
              <Stack spacing={2} sx={{ p: 1 }}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Icon icon="mdi:alert-circle" width={24} height={24} />
                  <Typography level="title-md">{error.message}</Typography>
                </Stack>
                
                {error.details && (
                  <Typography level="body-sm" sx={{ ml: 4 }}>
                    {error.details}
                  </Typography>
                )}
                
                <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
                  <Button 
                    startDecorator={<Icon icon="mdi:refresh" />}
                    onClick={() => loadReleases()}
                    variant="soft"
                  >
                    Try Again
                  </Button>
                  <Button 
                    startDecorator={<Icon icon="mdi:help-circle" />}
                    variant="outlined"
                    color="neutral"
                    component={Link}
                    href="/support"
                  >
                    Get Help
                  </Button>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        ) : successMessage ? (
          <Card variant="soft" color="success" sx={{ mb: 2 }}>
            <CardContent>
              <Stack direction="row" spacing={2} alignItems="center">
                <Icon icon="mdi:check-circle" width={24} height={24} />
                <Typography>{successMessage}</Typography>
                <IconButton 
                  size="sm" 
                  variant="plain" 
                  color="neutral" 
                  sx={{ ml: 'auto' }}
                  onClick={() => setSuccessMessage(null)}
                >
                  <Icon icon="mdi:close" />
                </IconButton>
              </Stack>
            </CardContent>
          </Card>
        ) : filteredReleases.length === 0 ? (
          <Card variant="soft">
            <CardContent>
              <Stack spacing={2} alignItems="center" py={3}>
                <Icon icon="mdi:folder-open" width={48} height={48} />
                <Typography level="body-lg">No releases found</Typography>
                <Typography level="body-sm">
                  {searchQuery || statusFilter !== 'all'
                    ? 'Try adjusting your filters'
                    : 'Create a new release to get started'}
                </Typography>
                {!searchQuery && statusFilter === 'all' && (
                  <Button
                    component={Link}
                    href="/dashboard/records/releases/new"
                    startDecorator={<Icon icon="mdi:plus" />}
                  >
                    New Release
                  </Button>
                )}
              </Stack>
            </CardContent>
          </Card>
        ) : viewMode === 'table' ? (
          <Sheet variant="outlined">
            <Table>
              <thead>
                <tr>
                  <th>Body ID</th>
                  <th>Status</th>
                  <th>Released To</th>
                  <th>Undertaker</th>
                  <th>Release Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredReleases.map((release) => (
                  <tr key={release.id} onClick={() => router.push(`/dashboard/records/releases/${release.id}`)} style={{cursor: 'pointer'}}>
                    <td>
                      <Stack>
                        <Typography level="body-sm">{release.body?.trackingNumber}</Typography>
                        <Typography level="body-xs" color="neutral">{release.body?.bodyTag?.tagNumber}</Typography>
                      </Stack>
                    </td>
                    <td>
                      <Chip
                        size="sm"
                        variant="soft"
                        color={getStatusColor(release.status)}
                      >
                        {getStatusLabel(release.status)}
                      </Chip>
                    </td>
                    <td>
                      <Stack>
                        <Typography level="body-sm">{release.releasedTo}</Typography>
                        <Typography level="body-xs" color="neutral">{release.relationship}</Typography>
                      </Stack>
                    </td>
                    <td>
                      <Stack>
                        <Typography level="body-sm">{release.undertakerName || 'N/A'}</Typography>
                        <Typography level="body-xs" color="neutral">{release.undertakerCompany}</Typography>
                      </Stack>
                    </td>
                    <td>
                      <Stack>
                        <Typography level="body-sm">{format(new Date(release.releaseDate), 'PPP')}</Typography>
                        <Typography level="body-xs" color="neutral">{format(new Date(release.releaseDate), 'p')}</Typography>
                      </Stack>
                    </td>
                    <td>
                      <Stack direction="row" spacing={1}>
                        <IconButton
                          size="sm"
                          variant="plain"
                          color="neutral"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/dashboard/records/releases/${release.id}/edit`);
                          }}
                        >
                          <Icon icon="mdi:pencil" />
                        </IconButton>
                        <IconButton
                          size="sm"
                          variant="plain"
                          color="danger"
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteModal(release.id);
                          }}
                        >
                          <Icon icon="mdi:delete" />
                        </IconButton>
                      </Stack>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Sheet>
        ) : (
          <Grid container spacing={2}>
            {filteredReleases.map((release) => (
              <Grid key={release.id} xs={12} md={6} lg={4}>
                <Card
                  variant="outlined"
                  sx={{
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: 'primary.outlinedHoverBorder',
                      transform: 'scale(1.02)',
                      boxShadow: 'sm',
                    },
                    transition: 'all 0.2s',
                    transform: 'scale(1)',
                  }}
                  onClick={() => router.push(`/dashboard/records/releases/${release.id}`)}
                >
                  <CardContent>
                    <Stack spacing={2}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Stack>
                          <Typography level="title-sm">{release.body?.trackingNumber}</Typography>
                          <Typography level="body-xs">{release.body?.bodyTag?.tagNumber}</Typography>
                        </Stack>
                        <Chip
                          size="sm"
                          variant="soft"
                          color={getStatusColor(release.status)}
                        >
                          {getStatusLabel(release.status)}
                        </Chip>
                      </Stack>

                      <Divider />

                      <Stack spacing={1}>
                        <Stack direction="row" justifyContent="space-between">
                          <Typography level="body-sm">Released To</Typography>
                          <Stack alignItems="flex-end">
                            <Typography level="body-sm">{release.releasedTo}</Typography>
                            <Typography level="body-xs" color="neutral">{release.relationship}</Typography>
                          </Stack>
                        </Stack>
                        <Stack direction="row" justifyContent="space-between">
                          <Typography level="body-sm">Undertaker</Typography>
                          <Stack alignItems="flex-end">
                            <Typography level="body-sm">{release.undertakerName || 'N/A'}</Typography>
                            <Typography level="body-xs" color="neutral">{release.undertakerCompany}</Typography>
                          </Stack>
                        </Stack>
                        <Stack direction="row" justifyContent="space-between">
                          <Typography level="body-sm">Release Date</Typography>
                          <Stack alignItems="flex-end">
                            <Typography level="body-sm">{format(new Date(release.releaseDate), 'PPP')}</Typography>
                            <Typography level="body-xs" color="neutral">{format(new Date(release.releaseDate), 'p')}</Typography>
                          </Stack>
                        </Stack>
                      </Stack>

                      <Stack direction="row" spacing={1} justifyContent="flex-end">
                        <IconButton
                          size="sm"
                          variant="plain"
                          color="neutral"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/dashboard/records/releases/${release.id}/edit`);
                          }}
                        >
                          <Icon icon="mdi:pencil" />
                        </IconButton>
                        <IconButton
                          size="sm"
                          variant="plain"
                          color="danger"
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteModal(release.id);
                          }}
                        >
                          <Icon icon="mdi:delete" />
                        </IconButton>
                      </Stack>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Pagination Controls - only show if we have more than one page */}
        {!loading && !error && pagination.totalPages > 1 && (
          <Card variant="outlined" sx={{ mt: 2 }}>
            <CardContent>
              <Stack 
                direction="row" 
                spacing={1} 
                justifyContent="center" 
                alignItems="center"
              >
                <Button
                  variant="plain"
                  color="neutral"
                  disabled={pagination.page <= 1}
                  onClick={() => {
                    // Logic to handle page change would go here
                    // For now just log the intent
                    console.log('Navigate to first page');
                    handlePageChange(1);
                  }}
                >
                  <Icon icon="mdi:page-first" />
                </Button>
                <Button
                  variant="plain"
                  color="neutral"
                  disabled={pagination.page <= 1}
                  onClick={() => {
                    console.log('Navigate to previous page');
                    handlePageChange(pagination.page - 1);
                  }}
                >
                  <Icon icon="mdi:chevron-left" />
                </Button>
                
                <Typography level="body-sm">
                  Page {pagination.page} of {pagination.totalPages}
                </Typography>
                
                <Button
                  variant="plain"
                  color="neutral"
                  disabled={pagination.page >= pagination.totalPages}
                  onClick={() => {
                    console.log('Navigate to next page');
                    handlePageChange(pagination.page + 1);
                  }}
                >
                  <Icon icon="mdi:chevron-right" />
                </Button>
                <Button
                  variant="plain"
                  color="neutral"
                  disabled={pagination.page >= pagination.totalPages}
                  onClick={() => {
                    console.log('Navigate to last page');
                    handlePageChange(pagination.totalPages);
                  }}
                >
                  <Icon icon="mdi:page-last" />
                </Button>
              </Stack>
            </CardContent>
          </Card>
        )}

        {/* Delete Modal */}
        <Modal open={!!deleteModal} onClose={() => setDeleteModal(null)}>
          <ModalDialog variant="outlined" role="alertdialog">
            <Typography level="title-lg" color="danger">
              Delete Release Record
            </Typography>
            <Divider />
            <Typography level="body-md">
              Are you sure you want to delete this release record? This action cannot be undone.
            </Typography>
            <Stack direction="row" spacing={1} justifyContent="flex-end" sx={{ mt: 2 }}>
              <Button
                variant="plain"
                color="neutral"
                onClick={() => setDeleteModal(null)}
              >
                Cancel
              </Button>
              <Button
                variant="solid"
                color="danger"
                onClick={handleDelete}
                loading={loading}
              >
                Delete
              </Button>
            </Stack>
          </ModalDialog>
        </Modal>
      </Stack>
    </Box>
  );
}