"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Typo<PERSON>,
  <PERSON>,
  <PERSON>ack,
  AspectRatio,
  Divider,
  IconButton,
  CircularProgress,
  <PERSON>ert,
  <PERSON>b,
  Tab<PERSON>ist,
  TabPanel,
  Tabs,
  Modal,
  ModalDialog,
  List,
  ListItem,
  ListItemDecorator,
  Table
} from '@mui/joy';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';

// Define a proper ReleaseStatus enum to handle both cases
enum ReleaseStatus {
  PENDING = 'PENDING',
  VERIFIED = 'VERIFIED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// Map string values to enum for compatibility
const mapStatusToEnum = (status: string): ReleaseStatus => {
  switch (status?.toUpperCase()) {
    case 'PENDING':
      return ReleaseStatus.PENDING;
    case 'VERIFIED':
      return ReleaseStatus.VERIFIED;
    case 'COMPLETED':
      return ReleaseStatus.COMPLETED;
    case 'CANCELLED':
      return ReleaseStatus.CANCELLED;
    default:
      return ReleaseStatus.PENDING;
  }
};

interface ExtendedBodyRelease {
  id: string;
  status: string;
  bodyId: string;
  releaseDate: Date;
  releasedTo: string;
  relationship: string;
  identificationDocument: string;
  contactDetails: string;
  notes?: string;
  photoEvidence?: string;
  signatureImage?: string;
  undertakerName?: string;
  undertakerCompany?: string;
  persalNumber?: string;
  body?: {
    trackingNumber: string;
    status: string;
    bodyTag?: {
      tagNumber: string;
      status: string;
    };
  };
  facility?: {
    name: string;
    code: string;
  };
  verifiedBy?: {
    name: string;
    role: string;
  };
  releasedBy?: {
    name: string;
    role: string;
  };
  _metadata?: {
    currentStatus: string;
    isVerified: boolean;
    lastUpdated: Date;
    activityCount: number;
    hasPhotos: boolean;
  };
  activityLog?: Array<{
    id: string;
    action: string;
    timestamp: Date;
    user: string;
    details: string;
  }>;
}

export default function ReleaseDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [release, setRelease] = React.useState<ExtendedBodyRelease | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [activeTab, setActiveTab] = React.useState(0);
  const [photoModal, setPhotoModal] = React.useState<string | null>(null);
  const [deleteModal, setDeleteModal] = React.useState(false);

  // Load release details
  const loadRelease = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/releases/${params.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch release details');
      }
      const data = await response.json();
      setRelease(data);
    } catch (err) {
      console.error('Error loading release:', err);
      setError(err instanceof Error ? err.message : 'Failed to load release details');
    } finally {
      setLoading(false);
    }
  }, [params.id]);

  React.useEffect(() => {
    loadRelease();
  }, [loadRelease]);

  // Handle delete
  const handleDelete = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/releases/${params.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete release');
      }
      
      router.push('/dashboard/records/releases');
    } catch (err) {
      console.error('Error deleting release:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete release');
    } finally {
      setLoading(false);
      setDeleteModal(false);
    }
  };

  const getStatusColor = (status: string): 'primary' | 'neutral' | 'danger' | 'success' | 'warning' => {
    const enumStatus = mapStatusToEnum(status);
    switch (enumStatus) {
      case ReleaseStatus.PENDING:
        return 'warning';
      case ReleaseStatus.VERIFIED:
        return 'primary';
      case ReleaseStatus.COMPLETED:
        return 'success';
      case ReleaseStatus.CANCELLED:
        return 'danger';
      default:
        return 'neutral';
    }
  };

  const getStatusIcon = (status: string): string => {
    const enumStatus = mapStatusToEnum(status);
    switch (enumStatus) {
      case ReleaseStatus.COMPLETED:
        return 'mdi:check-circle';
      case ReleaseStatus.PENDING:
        return 'mdi:clock-outline';
      case ReleaseStatus.CANCELLED:
        return 'mdi:cancel';
      case ReleaseStatus.VERIFIED:
        return 'mdi:shield-check';
      default:
        return 'mdi:information';
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <Stack spacing={2} alignItems="center">
          <CircularProgress />
          <Typography level="body-sm">Loading release details...</Typography>
        </Stack>
      </Box>
    );
  }

  if (error || !release) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="danger"
          variant="soft"
          startDecorator={<Icon icon="mdi:alert" />}
          endDecorator={
            <Button
              variant="soft"
              color="danger"
              onClick={() => router.push('/dashboard/records/releases')}
            >
              Return to List
            </Button>
          }
        >
          {error || 'Release not found'}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Stack spacing={3}>
        {/* Enhanced Header */}
        <Card variant="outlined">
          <CardContent>
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              justifyContent="space-between"
              alignItems={{ xs: 'flex-start', sm: 'center' }}
              spacing={2}
              mb={2}
            >
              <Stack direction="row" spacing={2} alignItems="center">
                <Button
                  component={Link}
                  href="/dashboard/records/releases"
                  variant="plain"
                  startDecorator={<Icon icon="mdi:arrow-left" />}
                >
                  Back
                </Button>
                <Typography level="h2">
                  Release Details
                </Typography>
              </Stack>
              <Stack direction="row" spacing={1}>
                <Button
                  component={Link}
                  href={`/dashboard/records/releases/${params.id}/edit`}
                  startDecorator={<Icon icon="mdi:pencil" />}
                  variant="outlined"
                >
                  Edit
                </Button>
                <Button
                  color="danger"
                  variant="outlined"
                  startDecorator={<Icon icon="mdi:delete" />}
                  onClick={() => setDeleteModal(true)}
                >
                  Delete
                </Button>
              </Stack>
            </Stack>
            
            <Divider />
            
            <Box sx={{ py: 2 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid xs={12} md={3}>
                  <Stack>
                    <Typography level="body-xs">Body ID</Typography>
                    <Typography level="body-lg" fontWeight="bold">{release.body?.trackingNumber}</Typography>
                  </Stack>
                </Grid>
                
                <Grid xs={12} md={3}>
                  <Stack>
                    <Typography level="body-xs">Release Date</Typography>
                    <Typography level="body-md">
                      {format(new Date(release.releaseDate), 'PPP')}
                    </Typography>
                  </Stack>
                </Grid>
                
                <Grid xs={12} md={3}>
                  <Stack>
                    <Typography level="body-xs">Facility</Typography>
                    <Typography level="body-md">{release.facility?.name || 'N/A'}</Typography>
                  </Stack>
                </Grid>
                
                <Grid xs={12} md={3}>
                  <Stack alignItems="flex-start">
                    <Typography level="body-xs">Status</Typography>
                    <Chip
                      variant="soft"
                      color={getStatusColor(release.status)}
                      size="lg"
                      startDecorator={
                        <Icon icon={getStatusIcon(release.status)} />
                      }
                    >
                      {release.status}
                    </Chip>
                  </Stack>
                </Grid>
              </Grid>
            </Box>
          </CardContent>
        </Card>

        {/* Content Tabs */}
        <Tabs
          value={activeTab}
          onChange={(event, value) => setActiveTab(value as number)}
          sx={{ bgcolor: 'background.surface' }}
        >
          <TabList>
            <Tab>Details</Tab>
            <Tab>Documents</Tab>
            <Tab>Activity Log</Tab>
          </TabList>

          {/* Details Tab */}
          <TabPanel value={0}>
            <Grid container spacing={3}>
              {/* Recipient Information Card */}
              <Grid xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Stack spacing={3}>
                      <Typography level="title-lg">
                        <Icon icon="mdi:account" style={{ verticalAlign: 'middle', marginRight: '8px' }} />
                        Recipient Information
                      </Typography>
                      
                      <Stack spacing={2}>
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Released To</Typography>
                          <Typography level="body-lg">{release.releasedTo}</Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Relationship</Typography>
                          <Typography level="body-lg">{release.relationship}</Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Identification Document</Typography>
                          <Typography level="body-lg">{release.identificationDocument}</Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Contact Details</Typography>
                          <Typography level="body-lg">{release.contactDetails}</Typography>
                        </Stack>
                      </Stack>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              
              {/* Undertaker Information Card */}
              <Grid xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Stack spacing={3}>
                      <Typography level="title-lg">
                        <Icon icon="mdi:office-building" style={{ verticalAlign: 'middle', marginRight: '8px' }} />
                        Funeral Home Details
                      </Typography>
                      
                      <Stack spacing={2}>
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Undertaker Name</Typography>
                          <Typography level="body-lg">{release.undertakerName || 'Not specified'}</Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Undertaker Company</Typography>
                          <Typography level="body-lg">{release.undertakerCompany || 'Not specified'}</Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Persal Number</Typography>
                          <Typography level="body-lg">{release.persalNumber || 'Not specified'}</Typography>
                        </Stack>
                      </Stack>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              
              {/* Body Information Card */}
              <Grid xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Stack spacing={3}>
                      <Typography level="title-lg">
                        <Icon icon="mdi:information" style={{ verticalAlign: 'middle', marginRight: '8px' }} />
                        Body Information
                      </Typography>
                      
                      <Stack spacing={2}>
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Body ID</Typography>
                          <Typography level="body-lg">{release.body?.trackingNumber}</Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Body Tag</Typography>
                          <Typography level="body-lg">{release.body?.bodyTag?.tagNumber || 'No tag'}</Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Body Status</Typography>
                          <Chip
                            size="sm"
                            color={release.body?.status === 'RELEASED' ? 'success' : 'primary'}
                          >
                            {release.body?.status || 'Unknown'}
                          </Chip>
                        </Stack>
                      </Stack>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              
              {/* Processing Information Card */}
              <Grid xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Stack spacing={3}>
                      <Typography level="title-lg">
                        <Icon icon="mdi:clipboard-text" style={{ verticalAlign: 'middle', marginRight: '8px' }} />
                        Processing Details
                      </Typography>
                      
                      <Stack spacing={2}>
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Facility</Typography>
                          <Typography level="body-lg">
                            {release.facility?.name}
                            {release.facility?.code && (
                              <Typography level="body-sm" color="neutral">
                                Code: {release.facility?.code}
                              </Typography>
                            )}
                          </Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Released By</Typography>
                          <Typography level="body-lg">
                            {release.releasedBy?.name || 'Not assigned'}
                            {release.releasedBy?.role && (
                              <Typography level="body-sm" color="neutral">
                                Role: {release.releasedBy.role}
                              </Typography>
                            )}
                          </Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Verified By</Typography>
                          <Typography level="body-lg">
                            {release.verifiedBy?.name || 'Not verified'}
                            {release.verifiedBy?.role && (
                              <Typography level="body-sm" color="neutral">
                                Role: {release.verifiedBy.role}
                              </Typography>
                            )}
                          </Typography>
                        </Stack>
                        
                        <Stack spacing={1}>
                          <Typography level="body-xs" color="neutral">Notes</Typography>
                          <Typography level="body-md">
                            {release.notes || 'No additional notes'}
                          </Typography>
                        </Stack>
                      </Stack>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>

              {/* Photos Section with enhanced display */}
              {(release.photoEvidence || release.signatureImage) && (
                <Grid xs={12}>
                  <Card>
                    <CardContent>
                      <Typography level="title-lg" mb={2}>
                        <Icon icon="mdi:image" style={{ verticalAlign: 'middle', marginRight: '8px' }} />
                        Photos & Evidence
                      </Typography>
                      <Grid container spacing={2}>
                        {release.photoEvidence && (
                          <Grid xs={12} sm={6}>
                            <Card variant="outlined">
                              <CardContent>
                                <Typography level="title-sm" mb={1}>Body Bag Photo</Typography>
                                <AspectRatio ratio="16/9" sx={{ maxWidth: '100%' }}>
                                  <img
                                    src={release.photoEvidence}
                                    alt="Body Bag Photo"
                                    style={{ objectFit: 'cover', cursor: 'pointer', width: '100%', height: '100%' }}
                                    onClick={() => setPhotoModal(release.photoEvidence)}
                                  />
                                </AspectRatio>
                                <Button 
                                  fullWidth 
                                  variant="soft" 
                                  color="neutral" 
                                  sx={{ mt: 1 }}
                                  onClick={() => setPhotoModal(release.photoEvidence)}
                                  startDecorator={<Icon icon="mdi:magnify" />}
                                >
                                  View Full Size
                                </Button>
                              </CardContent>
                            </Card>
                          </Grid>
                        )}
                        {release.signatureImage && (
                          <Grid xs={12} sm={6}>
                            <Card variant="outlined">
                              <CardContent>
                                <Typography level="title-sm" mb={1}>Signature</Typography>
                                <AspectRatio ratio="16/9" sx={{ maxWidth: '100%', background: '#f8f8f8' }}>
                                  <img
                                    src={release.signatureImage}
                                    alt="Signature"
                                    style={{ objectFit: 'contain', cursor: 'pointer', width: '100%', height: '100%' }}
                                    onClick={() => setPhotoModal(release.signatureImage)}
                                  />
                                </AspectRatio>
                                <Button 
                                  fullWidth 
                                  variant="soft" 
                                  color="neutral" 
                                  sx={{ mt: 1 }}
                                  onClick={() => setPhotoModal(release.signatureImage)}
                                  startDecorator={<Icon icon="mdi:magnify" />}
                                >
                                  View Full Size
                                </Button>
                              </CardContent>
                            </Card>
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          </TabPanel>

          {/* Documents Tab - Enhanced */}
          <TabPanel value={1}>
            <Card>
              <CardContent>
                <Stack spacing={3}>
                  <Typography level="title-lg">
                    <Icon icon="mdi:file-document" style={{ verticalAlign: 'middle', marginRight: '8px' }} />
                    Release Documentation
                  </Typography>
                  
                  <Table>
                    <thead>
                      <tr>
                        <th style={{ width: '20%' }}>Document Type</th>
                        <th style={{ width: '50%' }}>Details</th>
                        <th style={{ width: '15%' }}>Status</th>
                        <th style={{ width: '15%' }}>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>Identification Document</td>
                        <td>{release.identificationDocument}</td>
                        <td>
                          <Chip
                            variant="soft"
                            color="success"
                            size="sm"
                          >
                            Verified
                          </Chip>
                        </td>
                        <td>
                          <IconButton 
                            size="sm" 
                            variant="plain" 
                            color="neutral"
                            onClick={() => {/* Logic to view document */}}
                          >
                            <Icon icon="mdi:eye" />
                          </IconButton>
                        </td>
                      </tr>
                      <tr>
                        <td>Contact Information</td>
                        <td>{release.contactDetails}</td>
                        <td>
                          <Chip
                            variant="soft"
                            color="success"
                            size="sm"
                          >
                            Provided
                          </Chip>
                        </td>
                        <td>
                          <IconButton 
                            size="sm" 
                            variant="plain" 
                            color="neutral"
                            onClick={() => {/* Logic to view contact */}}
                          >
                            <Icon icon="mdi:eye" />
                          </IconButton>
                        </td>
                      </tr>
                      {release.signatureImage && (
                        <tr>
                          <td>Signature</td>
                          <td>Digital signature captured</td>
                          <td>
                            <Chip
                              variant="soft"
                              color="success"
                              size="sm"
                            >
                              Available
                            </Chip>
                          </td>
                          <td>
                            <IconButton 
                              size="sm" 
                              variant="plain" 
                              color="neutral"
                              onClick={() => setPhotoModal(release.signatureImage)}
                            >
                              <Icon icon="mdi:eye" />
                            </IconButton>
                          </td>
                        </tr>
                      )}
                      {release.persalNumber && (
                        <tr>
                          <td>Persal Number</td>
                          <td>{release.persalNumber}</td>
                          <td>
                            <Chip
                              variant="soft"
                              color="success"
                              size="sm"
                            >
                              Verified
                            </Chip>
                          </td>
                          <td>
                            <IconButton 
                              size="sm" 
                              variant="plain" 
                              color="neutral"
                              onClick={() => {/* Logic to verify */}}
                            >
                              <Icon icon="mdi:check-circle" />
                            </IconButton>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                  
                  <Divider />
                  
                  <Box>
                    <Typography level="title-md" mb={2}>Release Terms</Typography>
                    <Card variant="soft" color="neutral" size="sm">
                      <CardContent>
                        <Typography level="body-sm">
                          By accepting receipt of the human remains, the recipient acknowledges that all identification procedures have been followed correctly, and that they take full responsibility for the proper handling and disposition of the remains in accordance with applicable laws and regulations.
                        </Typography>
                      </CardContent>
                    </Card>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </TabPanel>

          {/* Activity Log Tab - Enhanced */}
          <TabPanel value={2}>
            <Card>
              <CardContent>
                <Stack spacing={3}>
                  <Typography level="title-lg">
                    <Icon icon="mdi:history" style={{ verticalAlign: 'middle', marginRight: '8px' }} />
                    Activity History
                  </Typography>
                  
                  {release.activityLog && release.activityLog.length > 0 ? (
                    <List>
                      {release.activityLog.map((activity, index) => (
                        <ListItem 
                          key={activity.id || index}
                          sx={{ 
                            mb: 1, 
                            p: 2, 
                            borderRadius: 'sm', 
                            border: '1px solid',
                            borderColor: 'divider'
                          }}
                        >
                          <ListItemDecorator sx={{ alignSelf: 'flex-start', mt: 0.5 }}>
                            <Icon 
                              icon={
                                activity.action.includes('create') ? 'mdi:plus-circle' :
                                activity.action.includes('update') ? 'mdi:pencil' :
                                activity.action.includes('delete') ? 'mdi:delete' :
                                activity.action.includes('verify') ? 'mdi:shield-check' :
                                'mdi:information-outline'
                              } 
                              style={{ 
                                color: 
                                  activity.action.includes('create') ? 'var(--joy-palette-success-500)' :
                                  activity.action.includes('update') ? 'var(--joy-palette-primary-500)' :
                                  activity.action.includes('delete') ? 'var(--joy-palette-danger-500)' :
                                  activity.action.includes('verify') ? 'var(--joy-palette-success-600)' :
                                  'var(--joy-palette-neutral-500)'
                              }}
                              width={24} 
                              height={24} 
                            />
                          </ListItemDecorator>
                          <Stack spacing={0.5} sx={{ flex: 1 }}>
                            <Typography fontWeight="lg">{activity.action}</Typography>
                            <Typography level="body-xs">
                              {format(new Date(activity.timestamp), 'PPpp')} by {activity.user}
                            </Typography>
                            {activity.details && (
                              <Card 
                                variant="soft" 
                                color="neutral" 
                                size="sm" 
                                sx={{ mt: 1 }}
                              >
                                <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                                  <Typography level="body-xs" sx={{ whiteSpace: 'pre-wrap' }}>
                                    {activity.details}
                                  </Typography>
                                </CardContent>
                              </Card>
                            )}
                          </Stack>
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Box 
                      sx={{ 
                        p: 3, 
                        display: 'flex', 
                        flexDirection: 'column', 
                        alignItems: 'center', 
                        gap: 1, 
                        borderRadius: 'sm',
                        border: '1px dashed',
                        borderColor: 'divider'
                      }}
                    >
                      <Icon icon="mdi:information-outline" style={{ fontSize: '2rem', opacity: 0.5 }} />
                      <Typography level="body-sm" textAlign="center">
                        No activity has been recorded for this release.
                      </Typography>
                    </Box>
                  )}
                </Stack>
              </CardContent>
            </Card>
          </TabPanel>
        </Tabs>

        {/* Photo Modal - Enhanced */}
        <Modal
          open={!!photoModal}
          onClose={() => setPhotoModal(null)}
        >
          <ModalDialog
            variant="outlined"
            sx={{
              maxWidth: '90vw',
              maxHeight: '90vh',
              width: 'auto',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ position: 'relative' }}>
              {photoModal && (
                <AspectRatio ratio="16/9" sx={{ minWidth: 400, maxWidth: '80vw' }}>
                  <img
                    src={photoModal}
                    alt="Photo preview"
                    style={{ objectFit: 'contain' }}
                  />
                </AspectRatio>
              )}
              <IconButton
                variant="solid"
                color="neutral"
                size="sm"
                onClick={() => setPhotoModal(null)}
                sx={{ position: 'absolute', top: 8, right: 8 }}
              >
                <Icon icon="mdi:close" />
              </IconButton>
            </Box>
          </ModalDialog>
        </Modal>

        {/* Delete Confirmation Modal - Enhanced */}
        <Modal open={deleteModal} onClose={() => setDeleteModal(false)}>
          <ModalDialog variant="outlined" role="alertdialog">
            <Stack spacing={2}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Icon icon="mdi:alert-circle" color="var(--joy-palette-danger-500)" width={24} height={24} />
                <Typography level="title-lg" color="danger">
                  Delete Release Record
                </Typography>
              </Stack>
              
              <Divider />
              
              <Typography level="body-md">
                Are you sure you want to delete this release record for <strong>{release?.body?.trackingNumber}</strong>? This action cannot be undone.
              </Typography>
              
              <Card variant="soft" color="warning" size="sm">
                <CardContent>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Icon icon="mdi:information" />
                    <Typography level="body-sm">
                      Deleting this record will remove all associated data, including activity logs, photos, and signature information.
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
              
              <Stack direction="row" spacing={1} justifyContent="flex-end">
                <Button
                  variant="plain"
                  color="neutral"
                  onClick={() => setDeleteModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="solid"
                  color="danger"
                  onClick={handleDelete}
                  loading={loading}
                  startDecorator={<Icon icon="mdi:delete" />}
                >
                  Delete
                </Button>
              </Stack>
            </Stack>
          </ModalDialog>
        </Modal>
      </Stack>
      
      {/* Actions FAB */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 20,
          right: 20,
          zIndex: 1000,
        }}
      >
        <Button
          component={Link}
          href={`/dashboard/records/releases/${params.id}/edit`}
          startDecorator={<Icon icon="mdi:pencil" />}
          variant="solid"
          color="primary"
          size="lg"
          sx={{ borderRadius: '50%', p: 2, minWidth: 0, width: 56, height: 56 }}
        >
          <Typography sx={{ display: { xs: 'none', md: 'block' } }}>Edit</Typography>
        </Button>
      </Box>
    </Box>
  );
} 