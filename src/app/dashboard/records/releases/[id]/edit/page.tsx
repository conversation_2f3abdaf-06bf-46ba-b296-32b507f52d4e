'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";



import {
  Box,
  Typography,
  CircularProgress,
  Snackbar,
  Button,
  Card,
  CardContent,
  Grid,
  Stack,
  Tabs,
  TabList,
  Tab,
  TabPanel,
  FormControl,
  FormLabel,
  Input,
  Select,
  Option,
  Textarea,
  FormHelperText,
  IconButton,
  AspectRatio,
  Modal,
  ModalDialog,
  ModalClose,
  Checkbox,
  Chip
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { SignatureCapture } from '@/components/Forms/signature-capture';
import { CameraCapture } from '@/components/Forms/camera-capture';
import moment from 'moment';

// Define the ReleaseStatus enum to match Prisma
enum ReleaseStatus {
  PENDING = 'PENDING',
  VERIFIED = 'VERIFIED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// Define the form schema using Zod for validation
const releaseEditFormSchema = z.object({
  bodyId: z.string().cuid(),
  status: z.nativeEnum(ReleaseStatus, {
    required_error: 'Please select a status',
  }),
  releaseDate: z.date().default(() => new Date()),
  facilityId: z.string().min(1, { message: 'Please select a facility' }),
  releasedTo: z.string().min(1, { message: 'Recipient name is required' }),
  relationship: z.string().min(1, { message: 'Relationship to deceased is required' }),
  identificationDocument: z.string().min(1, { message: 'Identification document is required' }),
  contactDetails: z.string().min(1, { message: 'Contact details are required' }),
  notes: z.string().optional(),
  releasedById: z.string().optional(),
  verifiedById: z.string().optional(),
  persalNumber: z.string().min(1, { message: 'Persal number is required' })
    .regex(/^\d+$/, { message: 'Persal number must contain only digits' }),
  undertakerName: z.string().optional(),
  undertakerCompany: z.string().optional(),
  termsAccepted: z.boolean().optional(),
});

type ReleaseEditFormValues = z.infer<typeof releaseEditFormSchema>;

// Interface for comprehensive release type
interface ExtendedBodyRelease {
  id: string;
  status: ReleaseStatus;
  bodyId: string;
  releaseDate: Date;
  releasedTo: string;
  relationship: string;
  identificationDocument: string;
  contactDetails: string;
  notes?: string | null;
  photoEvidence?: string | null;
  signatureImage?: string | null;
  facilityId: string;
  releasedById?: string | null;
  verifiedById?: string | null;
  persalNumber?: string | null;
  undertakerName?: string | null;
  undertakerCompany?: string | null;
  createdAt: string;
  updatedAt: string;
  body?: {
    id: string;
    trackingNumber: string;
    status: string;
    bodyTag?: {
      tagNumber: string;
      status: string;
    } | null;
  };
  facility?: {
    id: string;
    name: string;
    code: string;
  };
  verifiedBy?: {
    id: string;
    name: string | null;
    role: string;
  };
  releasedBy?: {
    id: string;
    name: string | null;
    role: string;
  };
  _metadata?: {
    currentStatus: ReleaseStatus;
    isVerified: boolean;
    lastUpdated: string;
    activityCount: number;
    hasPhotos: boolean;
  };
  activityLog?: Array<{
    id: string;
    action: string;
    timestamp: string;
    user: string;
    details: string;
  }>;
}

export default function EditReleasePage() {
  const { data: session } = useSession();
  const params = useParams();
  const router = useRouter();
  const releaseId = params.id as string;

  // State management
  const [facilities, setFacilities] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [release, setRelease] = useState<ExtendedBodyRelease | null>(null);
  const [activeTab, setActiveTab] = useState('details');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarColor, setSnackbarColor] = useState<'success' | 'danger'>('success');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [captureSignatureOpen, setCaptureSignatureOpen] = useState(false);
  const [capturePhotoOpen, setCapturePhotoOpen] = useState(false);
  const [scanBarcodeOpen, setScanBarcodeOpen] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<ReleaseEditFormValues>({
    resolver: zodResolver(releaseEditFormSchema),
    defaultValues: {
      bodyId: '',
      status: ReleaseStatus.PENDING,
      releaseDate: new Date(),
      facilityId: '',
      releasedTo: '',
      relationship: '',
      identificationDocument: '',
      contactDetails: '',
      notes: '',
      releasedById: '',
      verifiedById: '',
      persalNumber: '',
      undertakerName: '',
      undertakerCompany: '',
      termsAccepted: false
    }
  });

  // Fetch facilities and users
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [facilitiesRes, usersRes] = await Promise.all([
          fetch('/api/facilities'),
          fetch('/api/users')
        ]);

        if (!facilitiesRes.ok || !usersRes.ok) {
          throw new Error('Failed to fetch reference data');
        }

        const [facilitiesData, usersData] = await Promise.all([
          facilitiesRes.json(),
          usersRes.json()
        ]);

        setFacilities(facilitiesData);
        setUsers(usersData);
      } catch (err) {
        console.error('Error fetching reference data:', err);
        setError('Failed to load required reference data. Please try refreshing the page.');
      }
    };

    fetchData();
  }, []);

  // Fetch release details
  useEffect(() => {
    const fetchRelease = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/releases/${releaseId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Release not found');
          }
          throw new Error('Failed to fetch release details');
        }

        const data = await response.json();
        setRelease(data);

        // Populate form with release data
        reset({
          bodyId: data.bodyId,
          status: data.status,
          releaseDate: moment(data.releaseDate).toDate(),
          facilityId: data.facilityId,
          releasedTo: data.releasedTo,
          relationship: data.relationship,
          identificationDocument: data.identificationDocument,
          contactDetails: data.contactDetails,
          notes: data.notes || '',
          releasedById: data.releasedById || '',
          verifiedById: data.verifiedById || '',
          persalNumber: data.persalNumber || '',
          undertakerName: data.undertakerName || '',
          undertakerCompany: data.undertakerCompany || '',
          termsAccepted: true
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (releaseId) {
      fetchRelease();
    }
  }, [releaseId, reset]);

  // Handle form submission
  const onSubmit = async (data: ReleaseEditFormValues) => {
    setSubmitting(true);
    setError(null);

    try {
      const response = await fetch(`/api/releases/${releaseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update release');
      }

      setSnackbarColor('success');
      setSnackbarMessage('Release updated successfully');
      setSnackbarOpen(true);

      // Redirect after a short delay
      setTimeout(() => {
        router.push(`/dashboard/records/releases/${releaseId}`);
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setSnackbarColor('danger');
      setSnackbarMessage(err instanceof Error ? err.message : 'Failed to update release');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    setDeleteLoading(true);

    try {
      const response = await fetch(`/api/releases/${releaseId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete release');
      }

      setSnackbarColor('success');
      setSnackbarMessage('Release deleted successfully');
      setSnackbarOpen(true);

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/dashboard/records/releases');
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setSnackbarColor('danger');
      setSnackbarMessage(err instanceof Error ? err.message : 'Failed to delete release');
      setSnackbarOpen(true);
    } finally {
      setDeleteLoading(false);
      setShowDeleteModal(false);
    }
  };

  // Handle photo capture
  const handlePhotoCapture = (photoDataUrl: string) => {
    setCapturePhotoOpen(false);

    // Logic to update photo evidence
    // This would typically involve an API call to update the photo
    console.log('Captured photo evidence:', photoDataUrl);
  };

  // Handle signature capture
  const handleSignatureCapture = (signatureDataUrl: string) => {
    setCaptureSignatureOpen(false);

    // Logic to update signature image
    console.log('Captured signature:', signatureDataUrl);
  };

  // Handle barcode scan
  const handleBarcodeScanned = (barcode: string) => {
    setScanBarcodeOpen(false);
    console.log('Scanned barcode:', barcode);
    // Logic to handle the scanned barcode
  };

  // Get status color
  const getStatusColor = (status: ReleaseStatus): 'primary' | 'neutral' | 'danger' | 'success' | 'warning' => {
    switch (status) {
      case ReleaseStatus.PENDING:
        return 'warning';
      case ReleaseStatus.VERIFIED:
        return 'primary';
      case ReleaseStatus.COMPLETED:
        return 'success';
      case ReleaseStatus.CANCELLED:
        return 'danger';
      default:
        return 'neutral';
    }
  };

  // Display loading state
  const LoadingDisplay = () => (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2, p: 4 }}>
      <CircularProgress size="lg" />
      <Typography level="body-md">Loading release details...</Typography>
    </Box>
  );

  // Display error state
  const ErrorDisplay = () => (
    <Card variant="outlined" sx={{ p: 3, maxWidth: 'md', mx: 'auto' }}>
      <CardContent>
        <Stack spacing={2} alignItems="center">
          <Icon icon="mdi:alert-circle" style={{ fontSize: '3rem', color: 'var(--joy-palette-danger-500)' }} />
          <Typography level="h4" color="danger">
            Error Loading Release
          </Typography>
          <Typography level="body-md" textAlign="center">
            {error}
          </Typography>
          <Button
            variant="solid"
            color="primary"
            onClick={() => router.push('/dashboard/records/releases')}
            startDecorator={<Icon icon="mdi:arrow-left" />}
          >
            Return to Releases
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );

  // Display empty state
  const EmptyState = () => (
    <Card variant="outlined" sx={{ p: 3, maxWidth: 'md', mx: 'auto' }}>
      <CardContent>
        <Stack spacing={2} alignItems="center">
          <Icon icon="mdi:folder-open" style={{ fontSize: '3rem', color: 'var(--joy-palette-warning-500)' }} />
          <Typography level="h4" color="warning">
            Release Not Found
          </Typography>
          <Typography level="body-md" textAlign="center">
            The release you're looking for doesn't exist or has been removed.
          </Typography>
          <Button
            variant="solid"
            color="primary"
            onClick={() => router.push('/dashboard/records/releases')}
            startDecorator={<Icon icon="mdi:arrow-left" />}
          >
            Return to Releases
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );

  if (loading) {
    return <LoadingDisplay />;
  }

  if (error && !release) {
    return <ErrorDisplay />;
  }

  if (!release) {
    return <EmptyState />;
  }

  return (
    <Box sx={{ p: 2, maxWidth: 'xl', mx: 'auto' }}>
      {/* Page Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          p: 2,
          borderRadius: 'sm',
          background: 'var(--joy-palette-background-level1)',
        }}
      >
        <Stack direction="column">
          <Typography level="h4" component="h1">
            Edit Release
          </Typography>
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography level="body-sm" color="primary">
              Body ID: {release?.body?.trackingNumber || 'N/A'}
            </Typography>
            <Chip
              size="sm"
              variant="soft"
              color={getStatusColor(release.status)}
            >
              {release.status}
            </Chip>
          </Stack>
        </Stack>

        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            color="neutral"
            onClick={() => router.push(`/dashboard/records/releases/${releaseId}`)}
            startDecorator={<Icon icon="mdi:arrow-left" />}
          >
            Back
          </Button>
          <Button
            variant="outlined"
            color="danger"
            onClick={() => setShowDeleteModal(true)}
            startDecorator={<Icon icon="mdi:delete" />}
          >
            Delete
          </Button>
          <Button
            variant="solid"
            color="primary"
            onClick={handleSubmit(onSubmit)}
            loading={submitting}
            startDecorator={<Icon icon="mdi:content-save" />}
          >
            Save Changes
          </Button>
        </Stack>
      </Box>

      {/* Form Content */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Tabs
          value={activeTab}
          onChange={(e, value) => setActiveTab(value as string)}
          sx={{ mb: 3 }}
        >
          <TabList>
            <Tab value="details">Basic Details</Tab>
            <Tab value="recipient">Recipient Info</Tab>
            <Tab value="processing">Processing</Tab>
            <Tab value="documentation">Documentation</Tab>
          </TabList>

          {/* Basic Details Tab */}
          <TabPanel value="details">
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!errors.status}>
                  <FormLabel required>Release Status</FormLabel>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        onChange={(e, value) => field.onChange(value)}
                      >
                        {Object.values(ReleaseStatus).map((status) => (
                          <Option key={status} value={status}>
                            {status}
                          </Option>
                        ))}
                      </Select>
                    )}
                  />
                  {errors.status && (
                    <FormHelperText>{errors.status.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.releaseDate}>
                  <FormLabel required>Release Date</FormLabel>
                  <Controller
                    name="releaseDate"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        onChange={(e) => {
                          // On field change, set the value on the form
                          // to the new date: type Date
                          field.onChange(new Date(e.target.value));
                        }}
                        value={field.value ? field.value.toISOString().slice(0, 16) : ''}
                        slotProps={{
                          input: {
                            type: 'datetime-local',
                          },
                        }}
                      />
                    )}
                  />
                  {errors.releaseDate && (
                    <FormHelperText>{errors.releaseDate.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.facilityId}>
                  <FormLabel required>Facility</FormLabel>
                  <Controller
                    name="facilityId"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        onChange={(e, value) => field.onChange(value)}
                      >
                        {facilities?.map((facility: any) => (
                          <Option key={facility.id} value={facility.id}>
                            {facility.name} ({facility.type})
                          </Option>
                        ))}
                      </Select>
                    )}
                  />
                  {errors.facilityId && (
                    <FormHelperText>{errors.facilityId.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <FormControl error={!!errors.notes}>
                  <FormLabel>Notes</FormLabel>
                  <Controller
                    name="notes"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        minRows={3}
                        error={!!errors.notes}
                      />
                    )}
                  />
                  {errors.notes && (
                    <FormHelperText>{errors.notes.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Recipient Info Tab */}
          <TabPanel value="recipient">
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!errors.releasedTo}>
                  <FormLabel required>Released To</FormLabel>
                  <Controller
                    name="releasedTo"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        error={!!errors.releasedTo}
                      />
                    )}
                  />
                  {errors.releasedTo && (
                    <FormHelperText>{errors.releasedTo.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.relationship}>
                  <FormLabel required>Relationship to Deceased</FormLabel>
                  <Controller
                    name="relationship"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        error={!!errors.relationship}
                      />
                    )}
                  />
                  {errors.relationship && (
                    <FormHelperText>{errors.relationship.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.identificationDocument}>
                  <FormLabel required>Identification Document</FormLabel>
                  <Controller
                    name="identificationDocument"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        error={!!errors.identificationDocument}
                      />
                    )}
                  />
                  {errors.identificationDocument && (
                    <FormHelperText>{errors.identificationDocument.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.contactDetails}>
                  <FormLabel required>Contact Details</FormLabel>
                  <Controller
                    name="contactDetails"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        error={!!errors.contactDetails}
                      />
                    )}
                  />
                  {errors.contactDetails && (
                    <FormHelperText>{errors.contactDetails.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.undertakerName}>
                  <FormLabel>Undertaker Name</FormLabel>
                  <Controller
                    name="undertakerName"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        error={!!errors.undertakerName}
                      />
                    )}
                  />
                  {errors.undertakerName && (
                    <FormHelperText>{errors.undertakerName.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.undertakerCompany}>
                  <FormLabel>Undertaker Company</FormLabel>
                  <Controller
                    name="undertakerCompany"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        error={!!errors.undertakerCompany}
                      />
                    )}
                  />
                  {errors.undertakerCompany && (
                    <FormHelperText>{errors.undertakerCompany.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Processing Tab */}
          <TabPanel value="processing">
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!errors.releasedById}>
                  <FormLabel>Released By</FormLabel>
                  <Controller
                    name="releasedById"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        onChange={(e, value) => field.onChange(value)}
                      >
                        <Option value="">None</Option>
                        {users
                          ?.filter((user: any) => ['MORGUE_STAFF', 'ADMIN'].includes(user.role))
                          .map((user: any) => (
                            <Option key={user.id} value={user.id}>
                              {user.name} ({user.role})
                            </Option>
                        ))}
                      </Select>
                    )}
                  />
                  {errors.releasedById && (
                    <FormHelperText>{errors.releasedById.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.verifiedById}>
                  <FormLabel>Verified By</FormLabel>
                  <Controller
                    name="verifiedById"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        onChange={(e, value) => field.onChange(value)}
                      >
                        <Option value="">None</Option>
                        {users
                          ?.filter((user: any) => ['PATHOLOGIST', 'ADMIN'].includes(user.role))
                          .map((user: any) => (
                            <Option key={user.id} value={user.id}>
                              {user.name} ({user.role})
                            </Option>
                        ))}
                      </Select>
                    )}
                  />
                  {errors.verifiedById && (
                    <FormHelperText>{errors.verifiedById.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.persalNumber}>
                  <FormLabel required>Persal Number</FormLabel>
                  <Controller
                    name="persalNumber"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        error={!!errors.persalNumber}
                      />
                    )}
                  />
                  {errors.persalNumber && (
                    <FormHelperText>{errors.persalNumber.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <FormControl error={!!errors.termsAccepted}>
                  <Controller
                    name="termsAccepted"
                    control={control}
                    render={({ field }) => (
                      <Checkbox
                        label="I confirm that all information provided is accurate and that the proper identification has been verified."
                        checked={field.value}
                        onChange={(event) => field.onChange(event.target.checked)}
                      />
                    )}
                  />
                  {errors.termsAccepted && (
                    <FormHelperText>{errors.termsAccepted.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Documentation Tab */}
          <TabPanel value="documentation">
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography level="title-md" mb={2}>
                      Body Bag Photo
                    </Typography>
                    {release?.photoEvidence ? (
                      <Box sx={{ position: 'relative' }}>
                        <AspectRatio ratio="4/3">
                          <img
                            src={release.photoEvidence}
                            alt="Body bag photo"
                            style={{ objectFit: 'cover', borderRadius: '8px' }}
                          />
                        </AspectRatio>
                        <IconButton
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                          color="danger"
                          variant="solid"
                          size="sm"
                          onClick={() => {/* Logic to remove photo */}}
                        >
                          <Icon icon="mdi:delete" />
                        </IconButton>
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: 2,
                          p: 3,
                          border: '1px dashed',
                          borderColor: 'divider',
                          borderRadius: 'sm',
                        }}
                      >
                        <Icon icon="mdi:image" style={{ fontSize: '3rem', opacity: 0.5 }} />
                        <Typography level="body-sm" textAlign="center">
                          No body bag photo available
                        </Typography>
                        <Button
                          variant="soft"
                          color="primary"
                          startDecorator={<Icon icon="mdi:camera" />}
                          onClick={() => setCapturePhotoOpen(true)}
                        >
                          Capture Photo
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography level="title-md" mb={2}>
                      Signature
                    </Typography>
                    {release?.signatureImage ? (
                      <Box sx={{ position: 'relative' }}>
                        <AspectRatio ratio="4/3">
                          <img
                            src={release.signatureImage}
                            alt="Signature"
                            style={{ objectFit: 'contain', borderRadius: '8px', background: '#f8f8f8' }}
                          />
                        </AspectRatio>
                        <IconButton
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                          color="danger"
                          variant="solid"
                          size="sm"
                          onClick={() => {/* Logic to remove signature */}}
                        >
                          <Icon icon="mdi:delete" />
                        </IconButton>
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: 2,
                          p: 3,
                          border: '1px dashed',
                          borderColor: 'divider',
                          borderRadius: 'sm',
                        }}
                      >
                        <Icon icon="mdi:draw-pen" style={{ fontSize: '3rem', opacity: 0.5 }} />
                        <Typography level="body-sm" textAlign="center">
                          No signature available
                        </Typography>
                        <Button
                          variant="soft"
                          color="primary"
                          startDecorator={<Icon icon="mdi:draw" />}
                          onClick={() => setCaptureSignatureOpen(true)}
                        >
                          Capture Signature
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography level="title-md" mb={2}>
                      Identification Documents
                    </Typography>
                    <Stack spacing={2}>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          p: 2,
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 'sm',
                        }}
                      >
                        <Stack>
                          <Typography level="body-md">Identity Document</Typography>
                          <Typography level="body-sm" color="neutral">
                            {release.identificationDocument}
                          </Typography>
                        </Stack>
                        <Chip color="success" variant="soft">Verified</Chip>
                      </Box>

                      <Button
                        variant="outlined"
                        color="neutral"
                        startDecorator={<Icon icon="mdi:scan" />}
                        onClick={() => setScanBarcodeOpen(true)}
                      >
                        Scan Barcode/QR Code
                      </Button>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </Tabs>
      </form>

      {/* Photo Capture Modal */}
      {capturePhotoOpen && (
        <Modal open={capturePhotoOpen} onClose={() => setCapturePhotoOpen(false)}>
          <ModalDialog size="lg">
            <ModalClose />
            <Typography level="h4" mb={2}>
              Capture Photo Evidence
            </Typography>
            <CameraCapture
             onCapture={handlePhotoCapture}
             onCancel={() => setCapturePhotoOpen(false)}
            />
          </ModalDialog>
        </Modal>
      )}

      {/* Signature Capture Modal */}
      {captureSignatureOpen && (
        <Modal open={captureSignatureOpen} onClose={() => setCaptureSignatureOpen(false)}>
          <ModalDialog size="lg">
            <ModalClose />
            <Typography level="h4" mb={2}>
              Capture Signature
            </Typography>
            <SignatureCapture
             onCapture={handleSignatureCapture}
             onCancel={() => setCaptureSignatureOpen(false)}
            />
          </ModalDialog>
        </Modal>
      )}

      {/* Delete Confirmation Modal */}
      <Modal open={showDeleteModal} onClose={() => setShowDeleteModal(false)}>
        <ModalDialog variant="outlined" role="alertdialog">
          <Typography level="h4" color="danger">
            Delete Release
          </Typography>
          <Typography level="body-md" mb={2}>
            Are you sure you want to delete this release record? This action cannot be undone.
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              variant="plain"
              color="neutral"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="solid"
              color="danger"
              onClick={handleDelete}
              loading={deleteLoading}
            >
              Delete
            </Button>
          </Box>
        </ModalDialog>
      </Modal>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={5000}
        onClose={() => setSnackbarOpen(false)}
        color={snackbarColor}
        variant="soft"
        startDecorator={
          <Icon icon={snackbarColor === 'danger' ? 'mdi:alert-circle' : 'mdi:check-circle'} />
        }
      >
        {snackbarMessage}
      </Snackbar>
    </Box>
  );
}
