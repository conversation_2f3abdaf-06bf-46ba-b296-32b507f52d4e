"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React from "react";
import { Box } from "@mui/joy";
import { AdmissionForm } from "@/components/Forms/BodyAdmission/AdmissionForm";
import { useRouter } from "next/navigation";

export default function NewAdmissionPage() {
  const router = useRouter();
  const [isBusy, setIsBusy] = React.useState(false);

  const handleAdmissionCreated = () => {
    router.push("/dashboard/records/admissions");
  };

  return (
    <Box sx={{ p: 2 }}>
      <AdmissionForm/>
    </Box>
  );
}
