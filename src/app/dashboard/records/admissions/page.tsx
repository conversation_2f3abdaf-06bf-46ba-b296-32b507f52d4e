"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { Suspense } from "react";
import {
  Box,
  Button,
  Stack,
  <PERSON><PERSON><PERSON>,
  <PERSON>ert,
  <PERSON>dal,
  ModalDialog,
  Card,
  CardContent,
  Grid,
  Chip,
  Sheet,
  CircularProgress,
  IconButton,
  Tooltip,
  Divider,
  Input,
  Select,
  Option,
  Checkbox,
  Table,
  Drawer,
  FormControl,
  FormLabel,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { AnimatePresence, motion } from "framer-motion";
import type { ExtendedAdmission, Fridge } from "@/types";
import { useSession } from "next-auth/react";
import MiniBarcode from "@/components/Barcode/MiniBarcode";

interface ApiErrorResponse {
  error: string;
  message?: string;
}

interface Facility {
  id: string;
  name: string;
  code: string;
}

export default function AdmissionsPage() {
  const router = useRouter();
  const [fridges, setFridges] = React.useState<Fridge[]>([]);
  const [admissions, setAdmissions] = React.useState<ExtendedAdmission[]>([]);
  const [facilities, setFacilities] = React.useState<Facility[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [selectedFridge, setSelectedFridge] = React.useState<string | null>(
    null
  );
  const [selectedFacility, setSelectedFacility] = React.useState<string | null>(
    null
  );
  const [isAdmin, setIsAdmin] = React.useState(false);
  const [showSearch, setShowSearch] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [filterStatus, setFilterStatus] = React.useState<string | null>(null);
  const [moveModal, setMoveModal] = React.useState(false);
  const [selectedAdmission, setSelectedAdmission] = React.useState<
    string | null
  >(null);
  const [targetFridge, setTargetFridge] = React.useState<string | null>(null);
  const [view, setView] = React.useState<"grid" | "table">("grid");
  const [selectedAdmissions, setSelectedAdmissions] = React.useState<string[]>(
    []
  );
  const [sortConfig, setSortConfig] = React.useState<{
    key: keyof ExtendedAdmission;
    direction: "asc" | "desc";
  }>({
    key: "admissionDate",
    direction: "desc",
  });
  // Pagination state
  const [pagination, setPagination] = React.useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });
  //New Admission Form States
  const [openNewAdmission, setOpenNewAdmission] = React.useState(false);
  const [isAddingAdmission, setIsAddingAdmission] = React.useState(false);

  const { data: session } = useSession();

  // Check if user is admin
  React.useEffect(() => {
    const checkAdmin = async () => {
      console.log("SESSION", session);
      setIsAdmin(session?.user?.role === "ADMIN");
    };
    checkAdmin();
  }, []);

  // Load facilities if admin
  React.useEffect(() => {
    const loadFacilities = async () => {
      if (!isAdmin) return;
      try {
        const res = await fetch("/api/facilities");
        if (!res.ok) throw new Error("Failed to fetch facilities");
        const data = await res.json();
        setFacilities(data);
      } catch (err) {
        console.error("Error loading facilities:", err);
      }
    };
    loadFacilities();
  }, [isAdmin]);

  // Load admissions with pagination
  const loadData = React.useCallback(async (page = pagination.page, limit = pagination.limit) => {
    try {
      setLoading(true);

      // Build query params including pagination
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());

      // Add filters if present
      if (searchQuery) params.append('search', searchQuery);
      if (filterStatus) params.append('status', filterStatus);
      if (selectedFacility) params.append('facilityId', selectedFacility);

      const response = await fetch(`/api/admissions?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch admissions");

      const result = await response.json();

      // Handle paginated data structure
      if (result && typeof result === 'object' && 'data' in result && Array.isArray(result.data)) {
        setAdmissions(result.data);
        setPagination({
          page: result.pagination.page || page,
          limit: result.pagination.limit || limit,
          total: result.pagination.total || 0,
          pages: result.pagination.pages || 1
        });
      } else if (Array.isArray(result)) {
        // Handle case where API directly returns an array (fallback)
        setAdmissions(result);
        setPagination({
          ...pagination,
          total: result.length,
          pages: 1
        });
      } else {
        throw new Error("Invalid data format received from server");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, searchQuery, filterStatus, selectedFacility]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      loadData(newPage, pagination.limit);
    }
  };

  // Effect to reload data when filters change
  React.useEffect(() => {
    // Don't trigger on first render, only on filter changes
    if (loading) return;

    // Add a slight delay to avoid too many requests while typing
    const timer = setTimeout(() => {
      if (searchQuery || filterStatus || selectedFacility) {
        loadData(1, pagination.limit);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery, filterStatus, selectedFacility]);

  // Initial data load
  React.useEffect(() => {
    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Add status indicator in the title
  const headerTitle = React.useMemo(() => {
    let title = 'Admissions';

    if (loading) {
      title += ' (Loading...)';
    } else if (admissions.length > 0) {
      if (searchQuery || filterStatus || selectedFacility) {
        title += ` (Filtered: ${admissions.length} results)`;
      } else {
        title += ` (${pagination.total})`;
      }
    }

    return title;
  }, [loading, admissions.length, searchQuery, filterStatus, selectedFacility, pagination.total]);

  // Improved filtering and sorting logic
  const filteredAndSortedAdmissions = React.useMemo(() => {
    let result = [...admissions];

    // Filter by search query
    if (searchQuery) {
      const lowerQuery = searchQuery.toLowerCase();
      result = result.filter(
        (admission) =>
          admission.body.id?.toLowerCase().includes(lowerQuery) ||
          admission.bodyTag?.tagNumber?.toLowerCase().includes(lowerQuery) ||
          admission.facility?.name?.toLowerCase().includes(lowerQuery)
      );
    }

    // Filter by status
    if (filterStatus) {
      result = result.filter((admission) => admission.status === filterStatus);
    }

    // Filter by fridge
    if (selectedFridge) {
      result = result.filter(
        (admission) => admission.facilityId === selectedFridge
      );
    }

    // Sorting
    result.sort((a, b) => {
      const modifier = sortConfig.direction === "asc" ? 1 : -1;
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue === undefined || bValue === undefined) return 0;

      if (typeof aValue === "string" && typeof bValue === "string") {
        return modifier * aValue.localeCompare(bValue);
      }

      if (aValue instanceof Date && bValue instanceof Date) {
        return modifier * (aValue.getTime() - bValue.getTime());
      }

      return modifier * (aValue > bValue ? 1 : -1);
    });

    return result;
  }, [admissions, searchQuery, filterStatus, selectedFridge, sortConfig]);

  // Improved error handling component
  const ErrorDisplay = React.useCallback(() => {
    if (!error) return null;

    return (
      <Alert color="danger" startDecorator={<Icon icon="mdi:alert-circle" />}>
        <Stack>
          <Typography level="title-md">Error Loading Data</Typography>
          <Typography level="body-sm">{error}</Typography>
          <Button
            variant="soft"
            color="primary"
            onClick={(e) => {
              e.preventDefault();
              loadData(1, pagination.limit);
            }}
            startDecorator={<Icon icon="mdi:refresh" />}
          >
            Refresh
          </Button>
        </Stack>
      </Alert>
    );
  }, [error, loadData]);

  // Improved bulk export functionality
  const handleExport = async () => {
    if (selectedAdmissions.length === 0) return;

    try {
      setLoading(true);
      const response = await fetch("/api/admissions/export", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ admissionIds: selectedAdmissions }),
      });

      if (!response.ok) {
        throw new Error("Failed to export admissions");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `admissions_export_${new Date().toISOString()}.csv`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error("Export error:", err);
      setError(
        err instanceof Error ? err.message : "Failed to export admissions"
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action: "verify" | "export" | "release") => {
    if (selectedAdmissions.length === 0) return;

    try {
      setLoading(true);

      switch (action) {
        case "verify":
          await Promise.all(
            selectedAdmissions.map((id) =>
              fetch(`/api/admissions/${id}/verify`, { method: "POST" })
            )
          );
          break;

        case "export":
          await handleExport();
          break;

        case "release":
          await Promise.all(
            selectedAdmissions.map((id) =>
              fetch(`/api/admissions/${id}/release`, { method: "POST" })
            )
          );
          break;
      }

      await loadData();
      setSelectedAdmissions([]);
    } catch (err) {
      console.error("Error performing bulk action:", err);
      setError(
        err instanceof Error ? err.message : "Failed to perform bulk action"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleMoveBody = async () => {
    if (!selectedAdmission || !targetFridge) return;

    try {
      setLoading(true);
      const response = await fetch(
        `/api/admissions/${selectedAdmission}/move`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ fridgeId: targetFridge }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to move body");
      }

      await loadData();
      setMoveModal(false);
      setSelectedAdmission(null);
      setTargetFridge(null);
    } catch (err) {
      console.error("Error moving body:", err);
      setError(err instanceof Error ? err.message : "Failed to move body");
    } finally {
      setLoading(false);
    }
  };

  const handleAdmissionCreated = async () => {
    await loadData();
    setOpenNewAdmission(false);
  };

  const getFridgeColor = (status: Fridge["status"], temperature: number) => {
    if (status === "MAINTENANCE" || status === "OFFLINE") return "neutral";
    if (temperature > 8) return "danger";
    if (temperature > 4) return "warning";
    return "success";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "primary";
      case "IN_STORAGE":
        return "success";
      case "PENDING_RELEASE":
        return "warning";
      case "RELEASED":
        return "neutral";
      case "SECURITY_VERIFIED":
        return "success";
      case "REFERRED":
        return "warning";
      default:
        return "neutral";
    }
  };

  const getBodyStatusColor = (status: string) => {
    switch (status) {
      case "COLLECTED":
        return "neutral";
      case "ADMITTED":
        return "primary";
      case "IN_STORAGE":
        return "success";
      case "PENDING_RELEASE":
        return "warning";
      case "SECURITY_VERIFIED":
        return "success";
      case "RELEASED":
        return "neutral";
      case "REFERRED":
        return "warning";
      default:
        return "neutral";
    }
  };

  const renderGridView = () => (
    <Grid container spacing={2}>
      {filteredAndSortedAdmissions.map((admission) => (
        <Grid key={admission.id} xs={12} sm={6} lg={4}>
          <Card
            variant="outlined"
            sx={{
              cursor: "pointer",
              "&:hover": {
                boxShadow: "md",
                borderColor: "primary.500",
                transition: "all 0.2s ease-in-out",
              },
            }}
            onClick={() =>
              router.push(`/dashboard/records/admissions/${admission.id}`)
            }
          >
            <CardContent>
              <Stack spacing={2}>
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Chip
                    size="sm"
                    variant="soft"
                    color={getStatusColor(admission.status)}
                  >
                    {admission.status.replace("_", " ")}
                  </Chip>
                  <Typography level="body-xs">
                    {format(new Date(admission.admissionDate), "PPp")}
                  </Typography>
                </Stack>

                <Stack spacing={1}>
                  <Typography level="title-md">
                    {admission.deathRegisterNumber || "No Register Number"}
                  </Typography>

                  {/* Body Information */}
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Typography level="body-sm" fontWeight="md">
                      Body:
                    </Typography>
                    <Typography level="body-sm">
                      {admission.body?.firstName && admission.body?.lastName
                        ? `${admission.body.firstName} ${admission.body.lastName}`
                        : admission.body?.firstName || admission.body?.lastName || "Unknown"}
                    </Typography>
                    {admission.body?.status && (
                      <Chip
                        size="sm"
                        variant="soft"
                        color={getBodyStatusColor(admission.body.status)}
                      >
                        {admission.body.status.replace('_', ' ')}
                      </Chip>
                    )}
                  </Stack>

                  <Typography level="body-sm">
                    Tag: {admission.body?.bodyTag?.tagNumber || "N/A"}
                  </Typography>

                  {/* Physical Information */}
                  {(admission.body?.gender || admission.body?.approximateAge) && (
                    <Stack direction="row" spacing={2}>
                      {admission.body?.gender && (
                        <Typography level="body-sm">
                          Gender: {admission.body.gender}
                        </Typography>
                      )}
                      {admission.body?.approximateAge && (
                        <Typography level="body-sm">
                          Age: ~{admission.body.approximateAge} years
                        </Typography>
                      )}
                    </Stack>
                  )}

                  {/* Storage Information */}
                  {admission.assignedFridge && (
                    <Typography level="body-sm">
                      Storage: {admission.assignedFridge}
                    </Typography>
                  )}

                  {/* Temperature */}
                  {admission.temperature && (
                    <Typography level="body-sm">
                      Temperature: {admission.temperature}°C
                    </Typography>
                  )}

                  {/* Facility */}
                  <Typography level="body-sm">
                    Facility: {admission.facility?.name || "Unknown"}
                  </Typography>
                </Stack>

                {admission.photo && (
                  <Box
                    sx={{
                      border: "1px solid",
                      borderColor: "divider",
                      borderRadius: "sm",
                      p: 2,
                      mt: 1,
                      display: "flex",
                      flexDirection: "row",
                      gap: 2,
                      alignItems: "center",
                    }}
                  >
                    <Stack
                      direction="column"
                      spacing={1}
                      sx={{ flex: 1, alignItems: "center" }}
                    >
                      <Typography level="body-sm" color="neutral">
                        Fridge
                      </Typography>
                      <MiniBarcode
                        value={admission.assignedFridge || "Not Specified"}
                        size={100}
                        color="neutral"
                        showActions={false}
                      />
                    </Stack>

                    <Divider orientation="vertical" />

                    <Stack
                      direction="column"
                      spacing={1}
                      sx={{ flex: 1, alignItems: "center" }}
                    >
                      <Typography level="body-sm" color="neutral">
                        Barcode
                      </Typography>
                      <MiniBarcode
                        value={admission.barcodeValue || "Not Available"}
                        size={100}
                        color="neutral"
                        showActions={false}
                      />
                    </Stack>
                  </Box>
                )}

                <Stack direction="row" spacing={1} justifyContent="flex-end">
                  <Tooltip title="View Details">
                    <IconButton
                      size="sm"
                      variant="plain"
                      color="neutral"
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(
                          `/dashboard/records/admissions/${admission.id}`
                        );
                      }}
                    >
                      <Icon icon="mdi:eye" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit">
                    <IconButton
                      size="sm"
                      variant="plain"
                      color="primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(
                          `/dashboard/records/admissions/${admission.id}/edit`
                        );
                      }}
                    >
                      <Icon icon="mdi:pencil" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Move">
                    <IconButton
                      size="sm"
                      variant="plain"
                      color="neutral"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedAdmission(admission.id);
                        setMoveModal(true);
                      }}
                    >
                      <Icon icon="mdi:arrow-right" />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderTableView = () => (
    <Table
      borderAxis="both"
      stickyHeader
      stripe="odd"
      hoverRow
      sx={{
        "& tr:hover td": {
          bgcolor: "background.level1",
        },
      }}
    >
      <thead>
        <tr>
          <th style={{ width: 48 }}>
            <Checkbox
              checked={
                selectedAdmissions.length === filteredAndSortedAdmissions.length
              }
              indeterminate={
                selectedAdmissions.length > 0 &&
                selectedAdmissions.length < filteredAndSortedAdmissions.length
              }
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedAdmissions(
                    filteredAndSortedAdmissions.map((a) => a.id)
                  );
                } else {
                  setSelectedAdmissions([]);
                }
              }}
            />
          </th>
          <th>Register Number</th>
          <th>Body Tag</th>
          <th>Status</th>
          <th>Storage Unit</th>
          <th>Admission Date</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {filteredAndSortedAdmissions.map((admission) => (
          <tr
            key={admission.id}
            onClick={() =>
              router.push(`/dashboard/records/admissions/${admission.id}`)
            }
            style={{ cursor: "pointer" }}
          >
            <td onClick={(e) => e.stopPropagation()}>
              <Checkbox
                checked={selectedAdmissions.includes(admission.id)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedAdmissions([
                      ...selectedAdmissions,
                      admission.id,
                    ]);
                  } else {
                    setSelectedAdmissions(
                      selectedAdmissions.filter((id) => id !== admission.id)
                    );
                  }
                }}
              />
            </td>
            <td>{admission.deathRegisterNumber || "N/A"}</td>
            <td>{admission.body?.bodyTag?.tagNumber || "N/A"}</td>
            <td>
              <Chip
                size="sm"
                variant="soft"
                color={getStatusColor(admission.status)}
              >
                {admission.status.replace("_", " ")}
              </Chip>
            </td>
            <td>{admission.assignedFridge || "Not Assigned"}</td>
            <td>{format(new Date(admission.admissionDate), "PPpp")}</td>
            <td>
              <Stack direction="row" spacing={1}>
                <IconButton
                  size="sm"
                  variant="plain"
                  color="neutral"
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push(
                      `/dashboard/records/admissions/${admission.id}`
                    );
                  }}
                >
                  <Icon icon="mdi:eye" />
                </IconButton>
                <IconButton
                  size="sm"
                  variant="plain"
                  color="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push(
                      `/dashboard/records/admissions/${admission.id}/edit`
                    );
                  }}
                >
                  <Icon icon="mdi:pencil" />
                </IconButton>
                <IconButton
                  size="sm"
                  variant="plain"
                  color="neutral"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedAdmission(admission.id);
                    setMoveModal(true);
                  }}
                >
                  <Icon icon="mdi:arrow-right" />
                </IconButton>
              </Stack>
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
  );

  // Pagination controls component
  const PaginationControls = () => {
    if (pagination.pages <= 1) return null;

    return (
      <Card variant="outlined" sx={{ mt: 2 }}>
        <CardContent>
          <Stack
            direction="row"
            spacing={1}
            justifyContent="center"
            alignItems="center"
          >
            <IconButton
              variant="plain"
              color="neutral"
              disabled={pagination.page <= 1}
              onClick={() => handlePageChange(1)}
              size="sm"
            >
              <Icon icon="mdi:page-first" />
            </IconButton>
            <IconButton
              variant="plain"
              color="neutral"
              disabled={pagination.page <= 1}
              onClick={() => handlePageChange(pagination.page - 1)}
              size="sm"
            >
              <Icon icon="mdi:chevron-left" />
            </IconButton>

            <Typography level="body-sm">
              Page {pagination.page} of {pagination.pages}
              {pagination.total > 0 && (
                <Typography
                  level="body-xs"
                  color="neutral"
                  sx={{ display: 'inline', ml: 1 }}
                >
                  ({pagination.total} total)
                </Typography>
              )}
            </Typography>

            <IconButton
              variant="plain"
              color="neutral"
              disabled={pagination.page >= pagination.pages}
              onClick={() => handlePageChange(pagination.page + 1)}
              size="sm"
            >
              <Icon icon="mdi:chevron-right" />
            </IconButton>
            <IconButton
              variant="plain"
              color="neutral"
              disabled={pagination.page >= pagination.pages}
              onClick={() => handlePageChange(pagination.pages)}
              size="sm"
            >
              <Icon icon="mdi:page-last" />
            </IconButton>
          </Stack>
        </CardContent>
      </Card>
    );
  };

  // Filter controls component
  const FilterControls = () => {
    return (
      <Card variant="outlined" sx={{ mb: 2 }}>
        <CardContent>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="flex-end">
            <FormControl sx={{ minWidth: 200 }}>
              <FormLabel>Status</FormLabel>
              <Select
                placeholder="Filter by status"
                value={filterStatus || ''}
                onChange={(_, value) => {
                  setFilterStatus(value as string);
                  loadData(1, pagination.limit); // Reset to page 1 with new filter
                }}
                startDecorator={<Icon icon="mdi:filter-variant" />}
                size="sm"
                sx={{ minWidth: 150 }}
              >
                <Option value="">All Statuses</Option>
                <Option value="ACTIVE">Active</Option>
                <Option value="PENDING">Pending</Option>
                <Option value="RELEASED">Released</Option>
                <Option value="REFERRED">Referred</Option>
              </Select>
            </FormControl>

            {isAdmin && (
              <FormControl sx={{ minWidth: 200 }}>
                <FormLabel>Facility</FormLabel>
                <Select
                  placeholder="Filter by facility"
                  value={selectedFacility || ''}
                  onChange={(_, value) => {
                    setSelectedFacility(value as string);
                    loadData(1, pagination.limit); // Reset to page 1 with new filter
                  }}
                  startDecorator={<Icon icon="mdi:hospital-building" />}
                  size="sm"
                  sx={{ minWidth: 150 }}
                >
                  <Option value="">All Facilities</Option>
                  {facilities.map((facility) => (
                    <Option key={facility.id} value={facility.id}>
                      {facility.name}
                    </Option>
                  ))}
                </Select>
              </FormControl>
            )}

            <FormControl sx={{ flex: 1, minWidth: 200 }}>
              <FormLabel>Search</FormLabel>
              <Input
                placeholder="Search admissions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    loadData(1, pagination.limit);
                  }
                }}
                startDecorator={<Icon icon="mdi:magnify" />}
                endDecorator={
                  searchQuery ? (
                    <IconButton
                      variant="plain"
                      color="neutral"
                      size="sm"
                      onClick={() => {
                        setSearchQuery('');
                        loadData(1, pagination.limit);
                      }}
                    >
                      <Icon icon="mdi:close" />
                    </IconButton>
                  ) : null
                }
                size="sm"
              />
            </FormControl>

            <Button
              variant="outlined"
              color="neutral"
              size="sm"
              onClick={() => {
                setSearchQuery('');
                setFilterStatus(null);
                setSelectedFacility(null);
                loadData(1, pagination.limit);
              }}
              startDecorator={<Icon icon="mdi:filter-remove" />}
            >
              Clear Filters
            </Button>
          </Stack>
        </CardContent>
      </Card>
    );
  };

  // Empty state component
  const EmptyState = () => (
    <Card variant="outlined" sx={{ mt: 2, p: 4 }}>
      <Stack spacing={2} alignItems="center" justifyContent="center">
        <Icon icon="mdi:hospital-box-outline" style={{ fontSize: '4rem', opacity: 0.4 }} />
        <Typography level="title-lg" textAlign="center">
          No Admissions Found
        </Typography>
        <Typography level="body-sm" textAlign="center" color="neutral">
          {searchQuery || filterStatus || selectedFacility
            ? "Try adjusting your filters or search criteria."
            : "There are no admissions recorded in the system yet."}
        </Typography>
        <Button
          variant="soft"
          color="primary"
          onClick={() => setOpenNewAdmission(true)}
          startDecorator={<Icon icon="mdi:plus" />}
        >
          Add New Admission
        </Button>
      </Stack>
    </Card>
  );

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: 400,
        }}
      >
        <CircularProgress size="lg" />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Stack spacing={3}>
        {/* Header */}
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          flexWrap="wrap"
          gap={2}
        >
          <Stack direction="row" alignItems="center" spacing={2}>
            <Typography level="h2">{headerTitle}</Typography>
            <Chip size="sm" variant="soft" color="primary">
              {filteredAndSortedAdmissions.length}{" "}
              {filteredAndSortedAdmissions.length === 1 ? "Record" : "Records"}
            </Chip>
            {isAdmin && (
              <Chip size="sm" variant="soft" color="neutral">
                Admin View
              </Chip>
            )}
          </Stack>

          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              color="neutral"
              startDecorator={
                <Icon icon={showSearch ? "mdi:close" : "mdi:magnify"} />
              }
              onClick={() => setShowSearch(!showSearch)}
            >
              {showSearch ? "Close Search" : "Search"}
            </Button>
            <Button
              variant="outlined"
              color="neutral"
              startDecorator={
                <Icon icon={view === "grid" ? "mdi:table" : "mdi:grid"} />
              }
              onClick={() => setView(view === "grid" ? "table" : "grid")}
            >
              {view === "grid" ? "Table View" : "Grid View"}
            </Button>

            <Link href="/dashboard/records/admissions/new">
              <Button
                //component={Link}
                href="/dashboard/records/admissions/new"
                startDecorator={<Icon icon="mdi:plus" />}
                //onClick={() => setOpenNewAdmission(true)}
              >
                New Admission
              </Button>
            </Link>
          </Stack>
        </Stack>

        {/* Search and Filters */}
        <AnimatePresence>
          {showSearch && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
            >
              <FilterControls />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bulk Actions */}
        {selectedAdmissions.length > 0 && (
          <Sheet
            variant="soft"
            sx={{
              p: 2,
              borderRadius: "sm",
              display: "flex",
              gap: 2,
              alignItems: "center",
            }}
          >
            <Typography level="body-sm">
              {selectedAdmissions.length} admissions selected
            </Typography>
            <Button
              size="sm"
              variant="plain"
              color="neutral"
              onClick={() => setSelectedAdmissions([])}
            >
              Clear Selection
            </Button>
            <Divider orientation="vertical" />
            <Button
              size="sm"
              startDecorator={<Icon icon="mdi:check-circle" />}
              onClick={() => handleBulkAction("verify")}
            >
              Verify Selected
            </Button>
            <Button
              size="sm"
              startDecorator={<Icon icon="mdi:file-export" />}
              onClick={() => handleBulkAction("export")}
            >
              Export Selected
            </Button>
            <Button
              size="sm"
              color="warning"
              startDecorator={<Icon icon="mdi:exit-to-app" />}
              onClick={() => handleBulkAction("release")}
            >
              Release Selected
            </Button>
          </Sheet>
        )}

        {/* Admissions Grid/Table */}
        {filteredAndSortedAdmissions.length === 0 ? (
          <EmptyState />
        ) : view === "grid" ? (
          <>
            {renderGridView()}
            <PaginationControls />
          </>
        ) : (
          <>
            {renderTableView()}
            <PaginationControls />
          </>
        )}
      </Stack>

      {/* Move Body Modal */}
      <Modal open={moveModal} onClose={() => setMoveModal(false)}>
        <ModalDialog variant="outlined">
          <Typography level="h4">Move Body to Different Unit</Typography>
          <Typography level="body-sm" sx={{ mb: 2 }}>
            Select a storage unit to move the body to:
          </Typography>
          <Grid container spacing={2}>
            {fridges
              .filter(
                (f) => f.status === "AVAILABLE" || f.status === "OCCUPIED"
              )
              .filter((f) => f.currentOccupancy < f.capacity)
              .map((fridge) => (
                <Grid key={fridge.id} xs={6}>
                  <Card
                    variant={targetFridge === fridge.id ? "solid" : "outlined"}
                    color={getFridgeColor(fridge.status, fridge.temperature)}
                    sx={{
                      cursor: "pointer",
                      textAlign: "center",
                    }}
                    onClick={() => setTargetFridge(fridge.id)}
                  >
                    <CardContent>
                      <Typography level="title-sm">
                        Unit {fridge.fridgeNumber}
                      </Typography>
                      <Typography level="body-xs">
                        {fridge.currentOccupancy}/{fridge.capacity} occupied
                      </Typography>
                      <Typography level="body-xs">
                        {fridge.temperature}°C
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
          </Grid>
          <Stack
            direction="row"
            spacing={1}
            justifyContent="flex-end"
            sx={{ mt: 2 }}
          >
            <Button
              variant="plain"
              color="neutral"
              onClick={() => {
                setMoveModal(false);
                setSelectedAdmission(null);
                setTargetFridge(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="solid"
              color="primary"
              disabled={!targetFridge}
              loading={loading}
              onClick={handleMoveBody}
            >
              Move Body
            </Button>
          </Stack>
        </ModalDialog>
      </Modal>

      {/* Admission Form Drawer */}
      <Drawer
        open={openNewAdmission}
        onClose={() => {
          if (isAddingAdmission) {
            return;
          }
          setOpenNewAdmission(false);
        }}
        variant="outlined"
        size="lg"
        sx={{
          p: 2,
        }}
      >
        <Suspense
          fallback={
            <Box sx={{ display: "flex", alignItems: "center", gap: 2, p: 2 }}>
              <CircularProgress size="sm" />
              <Typography level="body-sm">
                Preparing admission form...
              </Typography>
            </Box>
          }
        >
          {/* <AdmissionForm
            onAdmissionCreated={handleAdmissionCreated}
            onClose={() => setOpenNewAdmission(false)}
          /> */}
        </Suspense>
      </Drawer>
    </Box>
  );
}
