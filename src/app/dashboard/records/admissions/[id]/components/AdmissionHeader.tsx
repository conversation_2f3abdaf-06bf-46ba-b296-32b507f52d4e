"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@mui/joy";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { differenceInDays } from "date-fns";
import { ComprehensiveAdmission } from "@/types/admission";

interface AdmissionHeaderProps {
  admission: ComprehensiveAdmission;
  onDelete: () => void;
  onStatusTransition: (type: string, bodyTagId?: string, currentStatus?: string) => void;
}

const getStatusColor = (status?: string) => {
  if (!status) return 'neutral';
  
  switch (status) {
    case 'GENERATED': return 'neutral';
    case 'COLLECTED': return 'primary';
    case 'ADMITTED': return 'success';
    case 'REFERRAL_IN': return 'warning';
    case 'REFERRAL_OUT': return 'warning';
    case 'PENDING_RELEASE': return 'danger';
    case 'OVERDUE': return 'danger';
    case 'RELEASED': return 'success';
    case 'ACTIVE': return 'primary';
    case 'CANCELLED': return 'danger';
    default: return 'neutral';
  }
};

const statusColors = {
  ACTIVE: 'primary',
  IN_STORAGE: 'success',
  PENDING_RELEASE: 'warning',
  RELEASED: 'neutral',
  CANCELLED: 'danger',
} as const;

const getDaysSinceLastUpdate = (lastUpdate: Date) => {
  return differenceInDays(new Date(), new Date(lastUpdate));
};

const isOverdue = (status: string, lastUpdate: Date) => {
  const daysSince = getDaysSinceLastUpdate(lastUpdate);
  return ['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(status) && daysSince > 7;
};

const isApproachingOverdue = (status: string, lastUpdate: Date) => {
  const daysSince = getDaysSinceLastUpdate(lastUpdate);
  return ['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(status) && daysSince >= 5 && daysSince <= 7;
};

export const AdmissionHeader: React.FC<AdmissionHeaderProps> = ({
  admission,
  onDelete,
  onStatusTransition,
}) => {
  const getValidTransitions = (currentStatus: string) => {
    const transitions = [];
    if (currentStatus === 'ADMITTED' || currentStatus === 'REFERRAL_IN') {
      transitions.push({ type: 'admission-to-referral-in', label: 'Process Referral In', icon: 'mdi:transfer-right' });
    }
    if (currentStatus === 'REFERRAL_IN') {
      transitions.push({ type: 'referral-in-to-referral-out', label: 'Transfer to Facility', icon: 'mdi:send' });
    }
    if (currentStatus === 'REFERRAL_OUT') {
      transitions.push({ type: 'referral-out-to-pending-release', label: 'Mark Pending Release', icon: 'mdi:clipboard-check' });
    }
    if (['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(currentStatus)) {
      transitions.push({ type: 'auto-overdue', label: 'Mark as Overdue', icon: 'mdi:clock-alert' });
    }
    return transitions;
  };

  return (
    <Stack spacing={2}>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Stack spacing={1}>
          <Typography level="h3">Admission Details</Typography>
          <Typography level="body-sm" color="neutral">
            Tracking Number: {admission.body?.trackingNumber || 'N/A'}
          </Typography>
        </Stack>

        <Stack direction="row" spacing={1} flexWrap="wrap">
          {/* Status Transition Buttons */}
          {admission.body?.bodyTag && getValidTransitions(admission.body.bodyTag.status).map((transition) => (
            <Button
              key={transition.type}
              variant="soft"
              color={transition.type === 'auto-overdue' ? 'danger' : 'primary'}
              size="sm"
              startDecorator={<Icon icon={transition.icon} />}
              onClick={() => onStatusTransition(transition.type, admission.body?.bodyTag?.id, admission.body?.bodyTag?.status)}
            >
              {transition.label}
            </Button>
          ))}

          <Button
            variant="outlined"
            color="primary"
            startDecorator={<Icon icon="mdi:pencil" />}
            component={Link}
            href={`/dashboard/records/admissions/${admission.id}/edit`}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            color="danger"
            startDecorator={<Icon icon="mdi:delete" />}
            onClick={onDelete}
          >
            Delete
          </Button>
        </Stack>
      </Stack>

      {/* Status Information */}
      <Stack direction="row" spacing={2} flexWrap="wrap">
        <Chip
          variant="soft"
          color={statusColors[admission.status as keyof typeof statusColors] || 'neutral'}
          startDecorator={<Icon icon="mdi:information-circle" />}
        >
          {admission.status.replace('_', ' ')}
        </Chip>

        {/* Body Tag Status */}
        {admission.body?.bodyTag && (
          <Chip
            variant="soft"
            color={getStatusColor(admission.body.bodyTag.status)}
            startDecorator={<Icon icon="mdi:qrcode" />}
          >
            Tag: {admission.body.bodyTag.status.replace('_', ' ')}
          </Chip>
        )}

        {/* Overdue Warning */}
        {admission.body?.bodyTag && isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) && (
          <Chip
            variant="soft"
            color="danger"
            startDecorator={<Icon icon="mdi:alert" />}
          >
            OVERDUE ({getDaysSinceLastUpdate(admission.body.bodyTag.updatedAt)} days)
          </Chip>
        )}

        {/* Approaching Overdue Warning */}
        {admission.body?.bodyTag && isApproachingOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) && (
          <Chip
            variant="soft"
            color="warning"
            startDecorator={<Icon icon="mdi:clock-alert" />}
          >
            APPROACHING OVERDUE ({getDaysSinceLastUpdate(admission.body.bodyTag.updatedAt)} days)
          </Chip>
        )}
      </Stack>
    </Stack>
  );
};
