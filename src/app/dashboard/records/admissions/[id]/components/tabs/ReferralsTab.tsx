"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ontent, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "@mui/joy";
import { Icon } from "@iconify/react";
import { format } from "date-fns";
import { ComprehensiveAdmission } from "@/types/admission";
import { ReferralActionPanel } from "@/components/Body/ReferralActionPanel";
import { PathologistVerificationPanel } from "@/components/Body/PathologistVerificationPanel";

interface ReferralsTabProps {
  admission: ComprehensiveAdmission;
  onRefresh: () => void;
}

const ReferralsTab: React.FC<ReferralsTabProps> = ({ admission, onRefresh }) => {
  return (
    <Grid container spacing={3}>
      {/* Referral Action Panel */}
      <Grid xs={12}>
        <ReferralActionPanel
          bodyId={admission.body?.id || ''}
          admissionId={admission.id}
          activeReferrals={admission.body?.referrals?.filter(ref =>
            ref.status === 'PENDING' || ref.status === 'IN_PROGRESS'
          ).map(ref => ({
            id: ref.id,
            referralType: ref.referralType,
            status: ref.status,
            institutionName: ref.institutionName
          })) || []}
          onReferralProcessed={onRefresh}
        />
      </Grid>

      {/* Pathologist Verification Panel */}
      <Grid xs={12}>
        <PathologistVerificationPanel
          bodyId={admission.body?.id || ''}
          releaseId={admission.body?.releases?.[0]?.id}
          releaseStatus={admission.body?.releases?.[0]?.status}
          onVerificationComplete={onRefresh}
        />
      </Grid>

      {/* Referral History */}
      <Grid xs={12}>
        <Card variant="outlined">
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:history" />}
              sx={{ mb: 2 }}
            >
              Referral History
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {admission.body?.referrals?.length ? (
              <Grid container spacing={3}>
                {admission.body.referrals.map((referral) => (
                  <Grid key={referral.id} xs={12}>
                    <Card variant="soft">
                      <CardContent>
                        <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
                          <Typography
                            level="title-md"
                            startDecorator={<Icon icon="mdi:transfer" />}
                          >
                            {referral.referralType} Referral
                          </Typography>
                          <Chip
                            variant="soft"
                            color={
                              referral.status === 'PENDING' ? 'warning' :
                              referral.status === 'IN_PROGRESS' ? 'primary' :
                              referral.status === 'RETURNED' ? 'success' : 'neutral'
                            }
                          >
                            {referral.status}
                          </Chip>
                        </Stack>
                        <Divider sx={{ mb: 2 }} />
                        <Grid container spacing={2}>
                          {[
                            { label: 'Institution', value: referral.institutionName },
                            { label: 'Employee Name', value: referral.employeeName },
                            { label: 'Employee Persal', value: referral.employeePersal },
                            { label: 'Vehicle Registration', value: referral.vehicleReg },
                            { label: 'Referral Date', value: format(new Date(referral.referralDate), 'PPp') },
                            { label: 'Return Date', value: referral.returnDate ? format(new Date(referral.returnDate), 'PPp') : 'N/A' },
                          ].map((item, i) => (
                            <Grid key={i} xs={12} sm={6} md={4}>
                              <Stack spacing={0.5}>
                                <Typography level="body-xs" color="neutral">
                                  {item.label}
                                </Typography>
                                <Typography level="body-md">
                                  {item.value}
                                </Typography>
                              </Stack>
                            </Grid>
                          ))}
                        </Grid>
                        {referral.notes && (
                          <Box mt={2}>
                            <Typography level="body-xs" color="neutral">
                              Notes
                            </Typography>
                            <Typography level="body-md">
                              {referral.notes}
                            </Typography>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Alert
                variant="soft"
                color="neutral"
                startDecorator={<Icon icon="mdi:information" />}
              >
                No referrals found
              </Alert>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default ReferralsTab;
