"use client";

import React from "react";
import { <PERSON><PERSON>, Card, Card<PERSON>ontent, Typo<PERSON>, Stack, Di<PERSON>r, <PERSON><PERSON>, But<PERSON>, <PERSON>, ListItem, ListItemContent, ListDivider } from "@mui/joy";
import { Icon } from "@iconify/react";
import { ComprehensiveAdmission } from "@/types/admission";

interface DocumentsTabProps {
  admission: ComprehensiveAdmission;
}

const DocumentsTab: React.FC<DocumentsTabProps> = ({ admission }) => {
  return (
    <Grid container spacing={3}>
      {/* Collection Documents */}
      {admission.body?.collection?.documents?.length > 0 && (
        <Grid xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography
                level="title-md"
                startDecorator={<Icon icon="mdi:file-document" />}
                sx={{ mb: 2 }}
              >
                Collection Documents
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <List>
                {admission.body.collection.documents.map((doc, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemContent>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Icon icon="mdi:file-document-outline" width="24" height="24" />
                          <Stack spacing={0.5}>
                            <Typography level="title-sm">
                              Document {index + 1}
                            </Typography>
                            <Button
                              variant="plain"
                              color="primary"
                              component="a"
                              href={doc}
                              target="_blank"
                              startDecorator={<Icon icon="mdi:download" />}
                            >
                              Download
                            </Button>
                          </Stack>
                        </Stack>
                      </ListItemContent>
                    </ListItem>
                    {index < admission.body.collection.documents.length - 1 && <ListDivider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      )}

      {/* Referral Documents */}
      {admission.body?.referrals?.some(ref => ref.bodyTagPhoto || ref.vehiclePhoto) && (
        <Grid xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography
                level="title-md"
                startDecorator={<Icon icon="mdi:file-document" />}
                sx={{ mb: 2 }}
              >
                Referral Documents
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <List>
                {admission.body.referrals.map((ref, refIndex) => (
                  <React.Fragment key={ref.id}>
                    {[ref.bodyTagPhoto, ref.vehiclePhoto].map((photo, photoIndex) =>
                      photo && (
                        <React.Fragment key={`${ref.id}-${photoIndex}`}>
                          <ListItem>
                            <ListItemContent>
                              <Stack direction="row" spacing={2} alignItems="center">
                                <Icon icon="mdi:image" width="24" height="24" />
                                <Stack spacing={0.5}>
                                  <Typography level="title-sm">
                                    {photoIndex === 0 ? 'Body Tag Photo' : 'Vehicle Photo'}
                                  </Typography>
                                  <Typography level="body-sm" color="neutral">
                                    Referral {refIndex + 1}
                                  </Typography>
                                  <Button
                                    variant="plain"
                                    color="primary"
                                    component="a"
                                    href={photo}
                                    target="_blank"
                                    startDecorator={<Icon icon="mdi:download" />}
                                  >
                                    Download
                                  </Button>
                                </Stack>
                              </Stack>
                            </ListItemContent>
                          </ListItem>
                          <ListDivider />
                        </React.Fragment>
                      )
                    )}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      )}

      {/* No Documents Message */}
      {(!admission.body?.collection?.documents?.length &&
        !admission.body?.referrals?.some(ref => ref.bodyTagPhoto || ref.vehiclePhoto)) && (
        <Grid xs={12}>
          <Alert
            variant="soft"
            color="neutral"
            startDecorator={<Icon icon="mdi:information" />}
          >
            No documents available
          </Alert>
        </Grid>
      )}
    </Grid>
  );
};

export default DocumentsTab;
