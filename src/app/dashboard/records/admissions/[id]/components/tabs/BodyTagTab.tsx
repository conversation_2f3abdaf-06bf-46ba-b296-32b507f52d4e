"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON>, CardContent, Typography, Grid, Divider, Alert } from "@mui/joy";
import { Icon } from "@iconify/react";
import { ComprehensiveAdmission } from "@/types/admission";
import { BodyTagSelector } from "@/components/Forms/BodyTag/BodyTagSelector";
import { BodyTagScanType } from "@/types/body";
import { differenceInDays } from "date-fns";

interface BodyTagTabProps {
  admission: ComprehensiveAdmission;
  onStatusTransition: (type: string, bodyTagId?: string, currentStatus?: string) => void;
}

const BodyTagTab: React.FC<BodyTagTabProps> = ({ admission, onStatusTransition }) => {
  const getDaysSinceLastUpdate = (lastUpdate: Date) => {
    return differenceInDays(new Date(), new Date(lastUpdate));
  };

  const isOverdue = (status: string, lastUpdate: Date) => {
    const daysSince = getDaysSinceLastUpdate(lastUpdate);
    return ['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(status) && daysSince > 7;
  };

  const isApproachingOverdue = (status: string, lastUpdate: Date) => {
    const daysSince = getDaysSinceLastUpdate(lastUpdate);
    return ['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(status) && daysSince >= 5 && daysSince <= 7;
  };

  const getValidTransitions = (currentStatus: string) => {
    const transitions = [];
    if (currentStatus === 'ADMITTED' || currentStatus === 'REFERRAL_IN') {
      transitions.push({ type: 'admission-to-referral-in', label: 'Process Referral In', icon: 'mdi:transfer-right' });
    }
    if (currentStatus === 'REFERRAL_IN') {
      transitions.push({ type: 'referral-in-to-referral-out', label: 'Transfer to Facility', icon: 'mdi:send' });
    }
    if (currentStatus === 'REFERRAL_OUT') {
      transitions.push({ type: 'referral-out-to-pending-release', label: 'Mark Pending Release', icon: 'mdi:clipboard-check' });
    }
    if (['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(currentStatus)) {
      transitions.push({ type: 'auto-overdue', label: 'Mark as Overdue', icon: 'mdi:clock-alert' });
    }
    return transitions;
  };

  return (
    <Stack spacing={3}>
      {admission.body?.bodyTag ? (
        <>
          {/* Body Tag Information */}
          <Card variant="outlined">
            <CardContent>
              <Typography level="title-md" startDecorator={<Icon icon="mdi:qrcode" />} sx={{ mb: 2 }}>
                Body Tag Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              {/* Enhanced BodyTagSelector in edit mode (read-only) */}
              <BodyTagSelector
                name="bodyTag"
                mode="edit"
                bodyTagId={admission.body.bodyTag.id}
                scanType={BodyTagScanType.ADMISSION}
                showResult={true}
                label="Current Body Tag"
              />
            </CardContent>
          </Card>

          {/* Status Transition Actions */}
          <Card variant="outlined">
            <CardContent>
              <Typography level="title-md" startDecorator={<Icon icon="mdi:swap-horizontal" />} sx={{ mb: 2 }}>
                Status Transition Actions
              </Typography>
              <Divider sx={{ mb: 2 }} />

              {getValidTransitions(admission.body.bodyTag.status).length > 0 ? (
                <Grid container spacing={2}>
                  {getValidTransitions(admission.body.bodyTag.status).map((transition) => (
                    <Grid key={transition.type} xs={12} sm={6} md={4}>
                      <Card 
                        variant="soft" 
                        sx={{ 
                          cursor: 'pointer',
                          '&:hover': {
                            bgcolor: 'background.level2',
                          }
                        }} 
                        onClick={() => onStatusTransition(transition.type, admission.body?.bodyTag?.id, admission.body?.bodyTag?.status)}
                      >
                        <CardContent>
                          <Stack direction="row" spacing={2} alignItems="center">
                            <Icon icon={transition.icon} width={24} height={24} />
                            <Stack>
                              <Typography level="title-sm">{transition.label}</Typography>
                              <Typography level="body-xs">
                                {transition.type === 'admission-to-referral-in' && 'Process body for referral intake'}
                                {transition.type === 'referral-in-to-referral-out' && 'Transfer body to another facility'}
                                {transition.type === 'referral-out-to-pending-release' && 'Mark body as ready for release'}
                                {transition.type === 'auto-overdue' && 'Mark body as overdue for processing'}
                              </Typography>
                            </Stack>
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert color="neutral">
                  No status transitions available for current status: {admission.body.bodyTag.status.replace('_', ' ')}
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Overdue Information */}
          {(isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) ||
            isApproachingOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt)) && (
            <Alert
              color={isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) ? 'danger' : 'warning'}
              startDecorator={<Icon icon="mdi:clock-alert" />}
            >
              <Stack spacing={1}>
                <Typography level="title-sm">
                  {isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) ? 'Overdue Alert' : 'Approaching Overdue'}
                </Typography>
                <Typography level="body-sm">
                  This body has been in {admission.body.bodyTag.status.replace('_', ' ')} status for {getDaysSinceLastUpdate(admission.body.bodyTag.updatedAt)} days.
                  {isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt)
                    ? ' Immediate action required.'
                    : ' Consider taking action soon to avoid overdue status.'}
                </Typography>
              </Stack>
            </Alert>
          )}
        </>
      ) : (
        <Card variant="outlined">
          <CardContent>
            <Stack spacing={2} alignItems="center" sx={{ py: 4 }}>
              <Icon icon="mdi:qrcode-off" width={48} height={48} />
              <Typography level="h4">No Body Tag Associated</Typography>
              <Typography level="body-sm" textAlign="center">
                This admission does not have an associated body tag
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      )}
    </Stack>
  );
};

export default BodyTagTab;
