"use client";

import React from "react";
import { Card, CardContent, Typography, Stack, Grid, Chip, Divider } from "@mui/joy";
import { Icon } from "@iconify/react";
import { format, differenceInDays } from "date-fns";
import { ComprehensiveAdmission } from "@/types/admission";
import AdmissionTimer from "@/components/Forms/BodyAdmission/AdmissionTimer";

interface DetailsTabProps {
  admission: ComprehensiveAdmission;
}

const DetailsTab: React.FC<DetailsTabProps> = ({ admission }) => {
  return (
    <Grid container spacing={3}>
      {/* Admission Timer */}
      <Grid xs={12}>
        <AdmissionTimer
          admissionDate={new Date(admission?.admissionDate || Date.now())}
          referrals={admission?.body?.referrals || []}
          facilityId={admission?.facilityId}
        />
      </Grid>

      {/* Body Information */}
      <Grid xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:account" />}
              sx={{ mb: 2 }}
            >
              Body Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Tracking Number
                </Typography>
                <Typography level="body-md" fontWeight="bold">
                  {admission?.body?.trackingNumber || 'N/A'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Status
                </Typography>
                <Chip
                  color={getBodyStatusColor(admission?.body?.status)}
                  size="sm"
                  variant="soft"
                  sx={{ fontWeight: 'bold' }}
                >
                  {admission?.body?.status || 'N/A'}
                </Chip>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  First Name
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.firstName || 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Last Name
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.lastName || 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Gender
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.gender || 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Approximate Age
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.approximateAge ? `~${admission.body.approximateAge} years` : 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Height
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.height ? `${admission.body.height} cm` : 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Weight
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.weight ? `${admission.body.weight} kg` : 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Death Registration
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.deathRegistration || 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Date of Death
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.dateOfDeath ? format(new Date(admission.body.dateOfDeath), 'PPP') : 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Place of Death
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.placeOfDeath || 'Unknown'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Cause of Death
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.causeOfDeath || 'Unknown'}
                </Typography>
              </Grid>
              {admission?.body?.distinguishingFeatures && (
                <Grid xs={12}>
                  <Typography level="body-sm" color="neutral">
                    Distinguishing Features
                  </Typography>
                  <Typography level="body-md">
                    {admission.body.distinguishingFeatures}
                  </Typography>
                </Grid>
              )}
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Identification Status
                </Typography>
                <Typography level="body-md">
                  {admission?.body?.firstName && admission?.body?.lastName
                    ? 'Identified'
                    : 'Unidentified'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Condition
                </Typography>
                <Typography level="body-md">
                  {admission?.bodyCondition || 'Not recorded'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Days in Storage
                </Typography>
                <Typography
                  level="body-md"
                  color={
                    admission?.admissionDate &&
                    differenceInDays(new Date(), new Date(admission.admissionDate)) >= 7
                      ? 'danger'
                      : 'neutral'
                  }
                  fontWeight={
                    admission?.admissionDate &&
                    differenceInDays(new Date(), new Date(admission.admissionDate)) >= 7
                      ? 'bold'
                      : 'normal'
                  }
                >
                  {admission?.admissionDate
                    ? `${differenceInDays(new Date(), new Date(admission.admissionDate))} days`
                    : 'N/A'}
                </Typography>
              </Grid>
              <Grid xs={12} sm={6}>
                <Typography level="body-sm" color="neutral">
                  Admission Date
                </Typography>
                <Typography level="body-md">
                  {admission?.admissionDate
                    ? format(new Date(admission.admissionDate), 'PPP')
                    : 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Basic Information */}
      <Grid xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:information" />}
              sx={{ mb: 2 }}
            >
              Basic Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              {[
                { label: 'Admission Type', value: admission.admissionType },
                { label: 'Admission Date', value: format(new Date(admission.admissionDate), 'PPp') },
                { label: 'Death Register Number', value: admission.deathRegisterNumber || 'N/A' },
                { label: 'Body Condition', value: admission.bodyCondition || 'N/A' },
                { label: 'Temperature', value: admission.temperature ? `${admission.temperature}°C` : 'N/A' },
                { label: 'Assigned Fridge', value: admission.assignedFridge || 'N/A' },
              ].map((item, index) => (
                <Grid key={index} xs={12} sm={6}>
                  <Stack spacing={0.5}>
                    <Typography level="body-xs" color="neutral">
                      {item.label}
                    </Typography>
                    <Typography level="body-md">
                      {item.value}
                    </Typography>
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Facility Information */}
      <Grid xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:hospital-building" />}
              sx={{ mb: 2 }}
            >
              Facility Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              {[
                { label: 'Facility Name', value: admission.facility?.name || 'N/A' },
                { label: 'Facility Code', value: admission.facility?.code || 'N/A' },
                { label: 'Facility Type', value: admission.facility?.type || 'N/A' },
              ].map((item, index) => (
                <Grid key={index} xs={12} sm={6}>
                  <Stack spacing={0.5}>
                    <Typography level="body-xs" color="neutral">
                      {item.label}
                    </Typography>
                    <Typography level="body-md">
                      {item.value}
                    </Typography>
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

const getBodyStatusColor = (status?: string) => {
  if (!status) return 'neutral';
  
  switch (status) {
    case 'GENERATED': return 'neutral';
    case 'COLLECTED': return 'primary';
    case 'ADMITTED': return 'success';
    case 'REFERRAL_IN': return 'warning';
    case 'REFERRAL_OUT': return 'warning';
    case 'PENDING_RELEASE': return 'danger';
    case 'OVERDUE': return 'danger';
    case 'RELEASED': return 'success';
    case 'ACTIVE': return 'primary';
    case 'CANCELLED': return 'danger';
    default: return 'neutral';
  }
};

export default DetailsTab;
