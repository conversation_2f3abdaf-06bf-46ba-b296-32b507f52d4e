"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, AspectRatio } from "@mui/joy";
import { Icon } from "@iconify/react";
import { format } from "date-fns";
import { ComprehensiveAdmission } from "@/types/admission";
import dynamic from "next/dynamic";

// Dynamic import for Map component
const Map = dynamic(() => import('@/components/Forms/BodyCollection/CurrentLocationMap'), { ssr: false });

interface CollectionTabProps {
  admission: ComprehensiveAdmission;
}

const CollectionTab: React.FC<CollectionTabProps> = ({ admission }) => {
  // Format coordinate data for map
  const getMapCoordinates = () => {
    if (!admission?.body?.collection?.gpsCoords) return null;
    const coords = admission.body.collection.gpsCoords as { lat: number; lng: number; };
    return coords;
  };

  if (!admission.body?.collection) {
    return (
      <Alert
        variant="soft"
        color="neutral"
        startDecorator={<Icon icon="mdi:information" />}
      >
        No collection information available
      </Alert>
    );
  }

  return (
    <Grid container spacing={3}>
      {/* Collection Details */}
      <Grid xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:ambulance" />}
              sx={{ mb: 2 }}
            >
              Collection Details
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              {[
                { label: 'Institution', value: admission.body.collection.institution },
                { label: 'Vehicle Registration', value: admission.body.collection.vehicleReg },
                { label: 'Arrival Time', value: admission.body.collection.arrivalTime ? format(new Date(admission.body.collection.arrivalTime), 'PPp') : 'N/A' },
                { label: 'Collection Type', value: admission.body.collection.collectionType },
                { label: 'Temperature', value: admission.body.collection.temperature ? `${admission.body.collection.temperature}°C` : 'N/A' },
                { label: 'Weather Conditions', value: admission.body.collection.weatherConditions || 'N/A' },
              ].map((item, index) => (
                <Grid key={index} xs={12} sm={6}>
                  <Stack spacing={0.5}>
                    <Typography level="body-xs" color="neutral">
                      {item.label}
                    </Typography>
                    <Typography level="body-md">
                      {item.value}
                    </Typography>
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Collection Location */}
      <Grid xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:map-marker" />}
              sx={{ mb: 2 }}
            >
              Collection Location
            </Typography>
            <Divider sx={{ mb: 2 }} />
            {getMapCoordinates() ? (
              <AspectRatio ratio="16/9">
                <Map
                  onLocationCaptured={() => {}}
                  useMockLocation={true}
                  initialLocation={getMapCoordinates() || undefined}
                />
              </AspectRatio>
            ) : (
              <Alert
                variant="soft"
                color="neutral"
                startDecorator={<Icon icon="mdi:map-marker-off" />}
              >
                No location data available
              </Alert>
            )}
          </CardContent>
        </Card>
      </Grid>

      {/* Collection Notes */}
      <Grid xs={12}>
        <Card>
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:note-text" />}
              sx={{ mb: 2 }}
            >
              Collection Notes
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Typography level="body-md">
              {admission.body.collection.collectionNotes || 'No notes available'}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default CollectionTab;
