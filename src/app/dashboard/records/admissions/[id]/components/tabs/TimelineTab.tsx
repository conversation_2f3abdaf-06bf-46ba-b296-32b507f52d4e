"use client";

import React from "react";
import { <PERSON>rid, Card, CardContent, Typography, Stack, Divider, Chip, Box, List, ListItem, ListItemContent, ListDivider } from "@mui/joy";
import { Icon } from "@iconify/react";
import { format } from "date-fns";
import { ComprehensiveAdmission } from "@/types/admission";

interface TimelineEvent {
  date: Date;
  title: string;
  description: string;
  icon: string;
  color: 'primary' | 'success' | 'warning' | 'danger' | 'neutral';
  type: 'admission' | 'collection' | 'referral' | 'return' | 'release' | 'freezer' | 'other';
  duration?: number;
  endDate?: Date;
  details?: Record<string, any>;
}

interface TimelineTabProps {
  admission: ComprehensiveAdmission;
}

const TimelineTab: React.FC<TimelineTabProps> = ({ admission }) => {
  const getTimelineEvents = (): TimelineEvent[] => {
    if (!admission) return [];

    const events: TimelineEvent[] = [
      {
        date: new Date(admission.createdAt),
        title: 'Admission Created',
        description: `Created by ${admission.createdBy?.name || 'System'}`,
        icon: 'mdi:file-document-plus',
        color: 'primary',
        type: 'admission',
        details: {
          admissionType: admission.admissionType,
          facility: admission.facility?.name,
          status: admission.status
        }
      }
    ];

    if (admission.body?.collection) {
      events.push({
        date: new Date(admission.body.collection.createdAt),
        title: 'Body Collected',
        description: `From ${admission.body.collection.institution || 'Unknown Location'}`,
        icon: 'mdi:ambulance',
        color: 'success',
        type: 'collection',
        details: {
          collectionType: admission.body.collection.collectionType,
          vehicleReg: admission.body.collection.vehicleReg,
          temperature: admission.body.collection.temperature
        }
      });
    }

    events.push({
      date: new Date(admission.admissionDate),
      title: 'Admitted to Storage',
      description: `Admitted to ${admission.facility?.name || 'facility'}`,
      icon: 'mdi:fridge',
      color: 'primary',
      type: 'admission',
      details: {
        fridge: admission.assignedFridge,
        temperature: admission.temperature,
        condition: admission.bodyCondition
      }
    });

    admission.body?.referrals?.forEach(ref => {
      events.push({
        date: new Date(ref.referralDate),
        title: `${ref.referralType} Referral`,
        description: `To ${ref.institutionName}`,
        icon: 'mdi:transfer',
        color: 'warning',
        type: 'referral',
        details: {
          referralType: ref.referralType,
          institution: ref.institutionName,
          employee: ref.employeeName,
          vehicleReg: ref.vehicleReg,
          notes: ref.notes
        }
      });

      if (ref.returnDate) {
        const referralStart = new Date(ref.referralDate);
        const referralEnd = new Date(ref.returnDate);
        const durationDays = Math.floor(
          (referralEnd.getTime() - referralStart.getTime()) / (1000 * 60 * 60 * 24)
        );

        events.push({
          date: referralEnd,
          title: 'Returned from Referral',
          description: `After ${durationDays} day${durationDays !== 1 ? 's' : ''} at ${ref.institutionName}`,
          icon: 'mdi:transfer-down',
          color: 'success',
          type: 'return',
          endDate: referralEnd,
          duration: durationDays,
          details: {
            referralType: ref.referralType,
            institution: ref.institutionName,
            durationDays
          }
        });
      }
    });

    admission.body?.releases?.forEach(release => {
      events.push({
        date: new Date(release.createdAt),
        title: 'Release Initiated',
        description: `By ${release.releasedBy?.name || 'Unknown'}`,
        icon: 'mdi:exit-run',
        color: release.status === 'COMPLETED' ? 'success' : 'warning',
        type: 'release',
        details: {
          status: release.status,
          releasedTo: release.releasedTo,
          releasedBy: release.releasedBy?.name,
          notes: release.notes
        }
      });

      if (release.status === 'COMPLETED') {
        events.push({
          date: new Date(release.updatedAt),
          title: 'Body Released',
          description: `Released to ${release.releasedTo || 'Unknown'}`,
          icon: 'mdi:check-circle',
          color: 'success',
          type: 'release',
          details: {
            releasedTo: release.releasedTo,
            releasedBy: release.releasedBy?.name,
            notes: release.notes
          }
        });
      }
    });

    const admissionDate = new Date(admission.admissionDate);
    const daysSinceAdmission = Math.floor(
      (new Date().getTime() - admissionDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceAdmission >= 7 && admission.status !== 'RELEASED') {
      const freezerDate = new Date(admissionDate);
      freezerDate.setDate(freezerDate.getDate() + 7);

      events.push({
        date: freezerDate,
        title: 'Freezer Storage Required',
        description: '7-day identification period expired',
        icon: 'mdi:snowflake',
        color: 'warning',
        type: 'freezer',
        details: {
          daysSinceAdmission,
          admissionDate: format(admissionDate, 'PPP')
        }
      });
    }

    return events.sort((a, b) => b.date.getTime() - a.date.getTime());
  };

  const events = getTimelineEvents();

  return (
    <Grid container spacing={3}>
      <Grid xs={12}>
        <Card>
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:timeline" />}
              sx={{ mb: 2 }}
            >
              Visual Timeline
            </Typography>
            <Divider sx={{ mb: 3 }} />

            {/* Visual Timeline */}
            <Box sx={{ position: 'relative', py: 2 }}>
              {/* Timeline Line */}
              <Box
                sx={{
                  position: 'absolute',
                  left: '24px',
                  top: 0,
                  bottom: 0,
                  width: '2px',
                  bgcolor: 'divider',
                  zIndex: 0,
                }}
              />

              {/* Timeline Events */}
              <Stack spacing={4}>
                {events.map((event, index) => (
                  <Box
                    key={index}
                    sx={{
                      position: 'relative',
                      pl: 6,
                      pr: 2,
                    }}
                  >
                    {/* Timeline Node */}
                    <Box
                      sx={{
                        position: 'absolute',
                        left: 0,
                        top: '50%',
                        transform: 'translate(-50%, -50%)',
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: `var(--joy-palette-${event.color}-100)`,
                        color: `var(--joy-palette-${event.color}-500)`,
                        border: '2px solid',
                        borderColor: `var(--joy-palette-${event.color}-300)`,
                        zIndex: 1,
                      }}
                    >
                      <Icon icon={event.icon} width="24" height="24" />
                    </Box>

                    {/* Event Content */}
                    <Card
                      variant="outlined"
                      sx={{
                        borderLeft: '4px solid',
                        borderLeftColor: `var(--joy-palette-${event.color}-500)`,
                        boxShadow: 'sm',
                        transition: 'transform 0.2s, box-shadow 0.2s',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: 'md',
                        },
                      }}
                    >
                      <CardContent>
                        <Stack spacing={1}>
                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Typography level="title-md">
                              {event.title}
                            </Typography>
                            <Chip
                              size="sm"
                              variant="soft"
                              color={event.color}
                            >
                              {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                            </Chip>
                          </Stack>

                          <Typography level="body-sm" color="neutral">
                            {format(event.date, 'PPpp')}
                          </Typography>

                          <Typography level="body-md">
                            {event.description}
                          </Typography>

                          {event.duration && (
                            <Chip
                              size="sm"
                              variant="outlined"
                              color="neutral"
                              startDecorator={<Icon icon="mdi:clock-outline" />}
                            >
                              Duration: {event.duration} day{event.duration !== 1 ? 's' : ''}
                            </Chip>
                          )}

                          {event.details && Object.keys(event.details).length > 0 && (
                            <Box
                              sx={{
                                mt: 1,
                                p: 1,
                                borderRadius: 'sm',
                                bgcolor: 'background.level1',
                              }}
                            >
                              <Grid container spacing={1}>
                                {Object.entries(event.details)
                                  .filter(([_, value]) => value !== null && value !== undefined && value !== '')
                                  .map(([key, value]) => (
                                    <Grid key={key} xs={12} sm={6} md={4}>
                                      <Stack direction="row" spacing={0.5} alignItems="center">
                                        <Typography level="body-xs" color="neutral">
                                          {key.charAt(0).toUpperCase() + key.slice(1)}:
                                        </Typography>
                                        <Typography level="body-xs">
                                          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                        </Typography>
                                      </Stack>
                                    </Grid>
                                  ))}
                              </Grid>
                            </Box>
                          )}
                        </Stack>
                      </CardContent>
                    </Card>
                  </Box>
                ))}
              </Stack>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid xs={12}>
        <Card>
          <CardContent>
            <Typography
              level="title-md"
              startDecorator={<Icon icon="mdi:list-status" />}
              sx={{ mb: 2 }}
            >
              Event List
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <List>
              {events.map((event, index) => (
                <React.Fragment key={index}>
                  <ListItem>
                    <ListItemContent>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                          sx={{
                            p: 1,
                            borderRadius: 'md',
                            bgcolor: `var(--joy-palette-${event.color}-100)`,
                            color: `var(--joy-palette-${event.color}-500)`,
                          }}
                        >
                          <Icon icon={event.icon} width="24" height="24" />
                        </Box>
                        <Stack spacing={0.5}>
                          <Typography level="title-sm">
                            {event.title}
                          </Typography>
                          <Typography level="body-sm" color="neutral">
                            {format(event.date, 'PPp')}
                          </Typography>
                          <Typography level="body-sm">
                            {event.description}
                          </Typography>
                        </Stack>
                      </Stack>
                    </ListItemContent>
                  </ListItem>
                  {index < events.length - 1 && <ListDivider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default TimelineTab;
