"use client";

import React from "react";
import { Modal, ModalDialog, Stack, Typography, ModalClose } from "@mui/joy";
import {
  AdmissionToReferralInForm,
  ReferralInToReferralOutForm,
  ReferralOutToPendingReleaseForm,
  AutoOverdueDetectionForm,
} from "@/components/Forms/StatusTransition";
import { StatusTransitionModalState } from "../types/statusTransition";

interface StatusTransitionModalProps {
  modalState: StatusTransitionModalState;
  daysSinceLastUpdate?: number;
  onSuccess: () => void;
  onCancel: () => void;
}

const StatusTransitionModal: React.FC<StatusTransitionModalProps> = ({
  modalState,
  daysSinceLastUpdate = 0,
  onSuccess,
  onCancel,
}) => {
  return (
    <Modal open={modalState.type !== null} onClose={onCancel}>
      <ModalDialog
        variant="outlined"
        layout="center"
        sx={{
          maxWidth: "90vw",
          width: "800px",
          maxHeight: "90vh",
          overflow: 'auto'
        }}
      >
        <ModalClose />
        <Stack spacing={2}>
          <Typography level="h4">
            {modalState.type === 'admission-to-referral-in' && 'Process Referral In'}
            {modalState.type === 'referral-in-to-referral-out' && 'Transfer to Facility'}
            {modalState.type === 'referral-out-to-pending-release' && 'Mark Pending Release'}
            {modalState.type === 'auto-overdue' && 'Mark as Overdue'}
          </Typography>

          {modalState.type === 'admission-to-referral-in' && (
            <AdmissionToReferralInForm
              bodyTagId={modalState.bodyTagId}
              onSuccess={onSuccess}
              onCancel={onCancel}
            />
          )}

          {modalState.type === 'referral-in-to-referral-out' && (
            <ReferralInToReferralOutForm
              bodyTagId={modalState.bodyTagId}
              onSuccess={onSuccess}
              onCancel={onCancel}
            />
          )}

          {modalState.type === 'referral-out-to-pending-release' && (
            <ReferralOutToPendingReleaseForm
              bodyTagId={modalState.bodyTagId}
              onSuccess={onSuccess}
              onCancel={onCancel}
            />
          )}

          {modalState.type === 'auto-overdue' && (
            <AutoOverdueDetectionForm
              bodyTagId={modalState.bodyTagId}
              currentStatus={modalState.currentStatus}
              daysSinceLastUpdate={daysSinceLastUpdate}
              onSuccess={onSuccess}
              onCancel={onCancel}
            />
          )}
        </Stack>
      </ModalDialog>
    </Modal>
  );
};

export default StatusTransitionModal;
