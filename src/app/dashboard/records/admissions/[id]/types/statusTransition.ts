export type BodyTagStatus = 
  | 'GENERATED'
  | 'COLLECTED'
  | 'ADMITTED'
  | 'REFERRAL_IN'
  | 'REFERRAL_OUT'
  | 'PENDING_RELEASE'
  | 'OVERDUE'
  | 'RELEASED'
  | 'ACTIVE'
  | 'CANCELLED';

export type StatusTransitionType = 
  | 'admission-to-referral-in'
  | 'referral-in-to-referral-out'
  | 'referral-out-to-pending-release'
  | 'auto-overdue'
  | null;

export interface StatusTransitionModalState {
  type: StatusTransitionType;
  bodyTagId?: string;
  currentStatus?: BodyTagStatus;
}
