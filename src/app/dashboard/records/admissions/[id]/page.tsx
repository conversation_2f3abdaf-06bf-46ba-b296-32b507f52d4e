"use client";

// Force dynamic rendering
export const dynamic = "force-dynamic";

import React from "react";
import dynamicImport from "next/dynamic";
import {
  <PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Stack,
  Typography,
  Chip,
  Grid,
  Divider,
  Modal,
  ModalDialog,
  ModalClose,
  Alert,
  CircularProgress,
  Tabs,
  TabList,
  Tab,
  TabPanel,
  List,
  ListItem,
  ListItemContent,
  ListDivider,
  AspectRatio
} from "@mui/joy";
import { Icon } from "@iconify/react";
import Link from "next/link";
import { format, differenceInDays } from "date-fns";
import { useRouter } from "next/navigation";
import { ComprehensiveAdmission } from "@/types/admission";

import { ChainOfCustodyTab } from "@/components/Body/ChainOfCustodyTab";
import { ReferralActionPanel } from "@/components/Body/ReferralActionPanel";
import { PathologistVerificationPanel } from "@/components/Body/PathologistVerificationPanel";
import AdmissionTimer from "@/components/Forms/BodyAdmission/AdmissionTimer";
import { BodyTagSelector } from '@/components/Forms/BodyTag/BodyTagSelector';
import { BodyTagScanType } from '@/types/body';
import {
  AdmissionToReferralInForm,
  ReferralInToReferralOutForm,
  ReferralOutToPendingReleaseForm,
  AutoOverdueDetectionForm
} from '@/components/Forms/StatusTransition';
import { validateStatusTransition } from '@/lib/schema/statusTransitionSchemas';

// Dynamic imports
const Map = dynamicImport(() => import('@/components/Forms/BodyCollection/CurrentLocationMap'), { ssr: false });

interface PageProps {
  params: { id: string };
}

interface TimelineEvent {
  date: Date;
  title: string;
  description: string;
  icon: string;
  color: 'primary' | 'success' | 'warning' | 'danger' | 'neutral';
  type: 'admission' | 'collection' | 'referral' | 'return' | 'release' | 'freezer' | 'other';
  duration?: number; // Duration in days (for referrals)
  endDate?: Date; // For events with a duration
  details?: Record<string, any>; // Additional details for the event
}

export default function AdmissionDetailsPage({ params }: PageProps) {
  const router = useRouter();
  const [admission, setAdmission] = React.useState<ComprehensiveAdmission | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = React.useState(false);
  const [selectedTab, setSelectedTab] = React.useState('details');

  // Status transition modal states
  const [statusTransitionModal, setStatusTransitionModal] = React.useState<{
    type: 'admission-to-referral-in' | 'referral-in-to-referral-out' | 'referral-out-to-pending-release' | 'auto-overdue' | null;
    bodyTagId?: string;
    currentStatus?: string;
  }>({ type: null });

  // Status color mapping
  const statusColors = {
    ACTIVE: 'primary',
    IN_STORAGE: 'success',
    PENDING_RELEASE: 'warning',
    RELEASED: 'neutral',
    CANCELLED: 'danger',
  } as const;

  // Fetch admission data
  React.useEffect(() => {
    const fetchAdmission = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/admissions/${params.id}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch admission');
        }

        const data = await response.json();
        setAdmission(data);
      } catch (err) {
        console.error('Error fetching admission:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchAdmission();
  }, [params.id]);

  // Handle admission deletion
  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/admissions/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete admission');
      }

      router.push('/dashboard/records/admissions');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete admission');
    }
  };

  // Format coordinate data for map
  const getMapCoordinates = () => {
    if (!admission?.body?.collection?.gpsCoords) return null;
    const coords = admission.body.collection.gpsCoords as { lat: number; lng: number; };
    return coords;
  };

  // Get color for body status
  const getStatusColor = (status?: string) => {
    if (!status) return 'neutral';

    switch (status) {
      case 'GENERATED': return 'neutral';
      case 'COLLECTED': return 'primary';
      case 'ADMITTED': return 'success';
      case 'REFERRAL_IN': return 'warning';
      case 'REFERRAL_OUT': return 'warning';
      case 'PENDING_RELEASE': return 'danger';
      case 'OVERDUE': return 'danger';
      case 'RELEASED': return 'success';
      case 'ACTIVE': return 'primary';
      case 'CANCELLED': return 'danger';
      default: return 'neutral';
    }
  };

  // Helper functions for overdue detection
  const getDaysSinceLastUpdate = (lastUpdate: Date) => {
    return differenceInDays(new Date(), new Date(lastUpdate));
  };

  const isOverdue = (status: string, lastUpdate: Date) => {
    const daysSince = getDaysSinceLastUpdate(lastUpdate);
    return ['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(status) && daysSince > 7;
  };

  const isApproachingOverdue = (status: string, lastUpdate: Date) => {
    const daysSince = getDaysSinceLastUpdate(lastUpdate);
    return ['ADMITTED', 'REFERRAL_OUT', 'PENDING_RELEASE'].includes(status) && daysSince >= 5 && daysSince <= 7;
  };

  const getValidTransitions = (currentStatus: string) => {
    const transitions = [];
    
    // Note: Main workflow transitions are now handled by dedicated buttons in header
    // Only keep special transitions here
    
    if (validateStatusTransition(currentStatus, 'OVERDUE')) {
      transitions.push({ type: 'auto-overdue', label: 'Mark as Overdue', icon: 'mdi:clock-alert' });
    }
    return transitions;
  };

  // Status transition handlers
  const handleStatusTransition = (type: string, bodyTagId?: string, currentStatus?: string) => {
    setStatusTransitionModal({
      type: type as any,
      bodyTagId,
      currentStatus
    });
  };

  const handleTransitionSuccess = () => {
    setStatusTransitionModal({ type: null });
    // Refresh admission data
    const fetchAdmission = async () => {
      try {
        const response = await fetch(`/api/admissions/${params.id}`);
        if (response.ok) {
          const data = await response.json();
          setAdmission(data);
        }
      } catch (err) {
        console.error('Error refreshing admission:', err);
      }
    };
    fetchAdmission();
  };

  const handleTransitionCancel = () => {
    setStatusTransitionModal({ type: null });
  };

  // Handle body release
  const handleReleaseBody = async () => {
    try {
      // Navigate to release form or trigger release process
      router.push(`/dashboard/records/admissions/${params.id}/release`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initiate body release');
    }
  };

  // Get timeline events
  const getTimelineEvents = () => {
    if (!admission) return [];

    const events: TimelineEvent[] = [
      {
        date: new Date(admission.createdAt),
        title: 'Admission Created',
        description: `Created by ${admission.createdBy?.name || 'System'}`,
        icon: 'mdi:file-document-plus',
        color: 'primary',
        type: 'admission',
        details: {
          admissionType: admission.admissionType,
          facility: admission.facility?.name,
          status: admission.status
        }
      }
    ];

    if (admission.body?.collection) {
      events.push({
        date: new Date(admission.body.collection.createdAt),
        title: 'Body Collected',
        description: `From ${admission.body.collection.institution || 'Unknown Location'}`,
        icon: 'mdi:ambulance',
        color: 'success',
        type: 'collection',
        details: {
          collectionType: admission.body.collection.collectionType,
          vehicleReg: admission.body.collection.vehicleReg,
          temperature: admission.body.collection.temperature
        }
      });
    }

    // Add admission to storage event
    events.push({
      date: new Date(admission.admissionDate),
      title: 'Admitted to Storage',
      description: `Admitted to ${admission.facility?.name || 'facility'}`,
      icon: 'mdi:fridge',
      color: 'primary',
      type: 'admission',
      details: {
        fridge: admission.assignedFridge,
        temperature: admission.temperature,
        condition: admission.bodyCondition
      }
    });

    // Add referrals with return dates when available
    admission.body?.referrals?.forEach(ref => {
      // Add referral event
      events.push({
        date: new Date(ref.referralDate),
        title: `${ref.referralType} Referral`,
        description: `To ${ref.institutionName}`,
        icon: 'mdi:transfer',
        color: 'warning',
        type: 'referral',
        details: {
          referralType: ref.referralType,
          institution: ref.institutionName,
          employee: ref.employeeName,
          vehicleReg: ref.vehicleReg,
          notes: ref.notes
        }
      });

      // Add return event if available
      if (ref.returnDate) {
        const referralStart = new Date(ref.referralDate);
        const referralEnd = new Date(ref.returnDate);
        const durationMs = referralEnd.getTime() - referralStart.getTime();
        const durationDays = Math.floor(durationMs / (1000 * 60 * 60 * 24));

        events.push({
          date: new Date(ref.returnDate),
          title: 'Returned from Referral',
          description: `After ${durationDays} day${durationDays !== 1 ? 's' : ''} at ${ref.institutionName}`,
          icon: 'mdi:transfer-down',
          color: 'success',
          type: 'return',
          endDate: referralEnd,
          duration: durationDays,
          details: {
            referralType: ref.referralType,
            institution: ref.institutionName,
            durationDays
          }
        });
      }
    });

    // Add release events if available
    admission.body?.releases?.forEach(release => {
      events.push({
        date: new Date(release.createdAt),
        title: 'Release Initiated',
        description: `By ${release.releasedBy?.name || 'Unknown'}`,
        icon: 'mdi:exit-run',
        color: release.status === 'COMPLETED' ? 'success' : 'warning',
        type: 'release',
        details: {
          status: release.status,
          releasedTo: release.releasedTo,
          releasedBy: release.releasedBy?.name,
          notes: release.notes
        }
      });

      if (release.status === 'COMPLETED') {
        events.push({
          date: new Date(release.updatedAt),
          title: 'Body Released',
          description: `Released to ${release.releasedTo || 'Unknown'}`,
          icon: 'mdi:check-circle',
          color: 'success',
          type: 'release',
          details: {
            releasedTo: release.releasedTo,
            releasedBy: release.releasedBy?.name,
            notes: release.notes
          }
        });
      }
    });

    // Check if body should be in freezer storage (7+ days)
    const admissionDate = new Date(admission.admissionDate);
    const now = new Date();
    const daysSinceAdmission = Math.floor((now.getTime() - admissionDate.getTime()) / (1000 * 60 * 60 * 24));

    if (daysSinceAdmission >= 7 && admission.status !== 'RELEASED') {
      const freezerDate = new Date(admissionDate);
      freezerDate.setDate(freezerDate.getDate() + 7);

      events.push({
        date: freezerDate,
        title: 'Freezer Storage Required',
        description: '7-day identification period expired',
        icon: 'mdi:snowflake',
        color: 'warning',
        type: 'freezer',
        details: {
          daysSinceAdmission,
          admissionDate: format(admissionDate, 'PPP')
        }
      });
    }

    return events.sort((a, b) => b.date.getTime() - a.date.getTime());
  };

  // Loading state
  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          width: '100%',
        }}
      >
        <CircularProgress size="lg" />
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          variant="soft"
          color="danger"
          startDecorator={<Icon icon="mdi:alert-circle" />}
          endDecorator={
            <Button
              variant="soft"
              color="neutral"
              onClick={() => router.push('/dashboard/records/admissions')}
            >
              Back to Admissions
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  // No data state
  if (!admission) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          variant="soft"
          color="neutral"
          startDecorator={<Icon icon="mdi:information" />}
        >
          No admission found
        </Alert>
      </Box>
    );
  }

  // Delete confirmation modal
  const DeleteConfirmationModal = () => (
    <Modal open={deleteModalOpen} onClose={() => setDeleteModalOpen(false)}>
      <ModalDialog
        variant="outlined"
        role="alertdialog"
        aria-labelledby="delete-confirmation-title"
        aria-describedby="delete-confirmation-description"
      >
        <Typography id="delete-confirmation-title" level="h2">
          Confirm Deletion
        </Typography>
        <Typography id="delete-confirmation-description" level="body-md">
          Are you sure you want to delete this admission? This action cannot be undone.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', mt: 2 }}>
          <Button
            variant="plain"
            color="neutral"
            onClick={() => setDeleteModalOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="solid"
            color="danger"
            onClick={() => {
              handleDelete();
              setDeleteModalOpen(false);
            }}
          >
            Delete Admission
          </Button>
        </Box>
      </ModalDialog>
    </Modal>
  );

  return (
    <Box sx={{ p: 3 }}>
      <DeleteConfirmationModal />

      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack spacing={1}>
            <Typography level="h3">Admission Details</Typography>
            <Typography level="body-sm" color="neutral">
              Tracking Number: {admission.body?.trackingNumber || 'N/A'}
            </Typography>
          </Stack>

          <Stack direction="row" spacing={1} flexWrap="wrap">
            {/* Status Transition Buttons */}
            {admission.body?.bodyTag && getValidTransitions(admission.body.bodyTag.status).map((transition) => (
              <Button
                key={transition.type}
                variant="soft"
                color={transition.type === 'auto-overdue' ? 'danger' : 'primary'}
                size="sm"
                startDecorator={<Icon icon={transition.icon} />}
                onClick={() => handleStatusTransition(transition.type, admission.body?.bodyTag?.id, admission.body?.bodyTag?.status)}
              >
                {transition.label}
              </Button>
            ))}

            {/* Process Referral In button - enable when body tag status is ADMITTED */}
            <Button
              variant="soft"
              color={admission.body?.bodyTag?.status === 'ADMITTED' ? "primary" : "neutral"}
              size="sm"
              startDecorator={<Icon icon="mdi:transfer-right" />}
              onClick={() => handleStatusTransition('admission-to-referral-in', admission.body?.bodyTag?.id, admission.body?.bodyTag?.status)}
              disabled={admission.body?.bodyTag?.status !== 'ADMITTED'}
            >
              Process Referral In
            </Button>

            {/* Transfer to Facility button - enable when body tag status is REFERRAL_IN */}
            <Button
              variant="soft"
              color={admission.body?.bodyTag?.status === 'REFERRAL_IN' ? "warning" : "neutral"}
              size="sm"
              startDecorator={<Icon icon="mdi:send" />}
              onClick={() => handleStatusTransition('referral-in-to-referral-out', admission.body?.bodyTag?.id, admission.body?.bodyTag?.status)}
              disabled={admission.body?.bodyTag?.status !== 'REFERRAL_IN'}
            >
              Transfer to Facility
            </Button>

            {/* Mark Pending Release button - enable when body tag status is REFERRAL_OUT */}
            <Button
              variant="soft"
              color={admission.body?.bodyTag?.status === 'REFERRAL_OUT' ? "warning" : "neutral"}
              size="sm"
              startDecorator={<Icon icon="mdi:clipboard-check" />}
              onClick={() => handleStatusTransition('referral-out-to-pending-release', admission.body?.bodyTag?.id, admission.body?.bodyTag?.status)}
              disabled={admission.body?.bodyTag?.status !== 'REFERRAL_OUT'}
            >
              Mark Pending Release
            </Button>

            {/* Release Body button - enable when body tag status is PENDING_RELEASE */}
            <Button
              variant="soft"
              color={admission.body?.bodyTag?.status === 'PENDING_RELEASE' ? "success" : "neutral"}
              size="sm"
              startDecorator={<Icon icon="mdi:exit-run" />}
              onClick={() => handleReleaseBody()}
              disabled={admission.body?.bodyTag?.status !== 'PENDING_RELEASE'}
            >
              Release Body
            </Button>

            <Button
              variant="outlined"
              color="primary"
              startDecorator={<Icon icon="mdi:pencil" />}
              component={Link}
              href={`/dashboard/records/admissions/${admission.id}/edit`}
            >
              Edit
            </Button>
            <Button
              variant="outlined"
              color="danger"
              startDecorator={<Icon icon="mdi:delete" />}
              onClick={() => setDeleteModalOpen(true)}
            >
              Delete
            </Button>
          </Stack>
        </Stack>

        {/* Status */}
        <Stack direction="row" spacing={2} mt={2} flexWrap="wrap">
          <Chip
            variant="soft"
            color={statusColors[admission.status as keyof typeof statusColors] || 'neutral'}
            startDecorator={<Icon icon="mdi:information-circle" />}
          >
            {admission.status.replace('_', ' ')}
          </Chip>

          {/* Body Tag Status */}
          {admission.body?.bodyTag && (
            <Chip
              variant="soft"
              color={getStatusColor(admission.body.bodyTag.status)}
              startDecorator={<Icon icon="mdi:qrcode" />}
            >
              Tag: {admission.body.bodyTag.status.replace('_', ' ')}
            </Chip>
          )}

          {/* Overdue Warning */}
          {admission.body?.bodyTag && isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) && (
            <Chip
              variant="soft"
              color="danger"
              startDecorator={<Icon icon="mdi:alert" />}
            >
              OVERDUE ({getDaysSinceLastUpdate(admission.body.bodyTag.updatedAt)} days)
            </Chip>
          )}

          {/* Approaching Overdue Warning */}
          {admission.body?.bodyTag && isApproachingOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) && (
            <Chip
              variant="soft"
              color="warning"
              startDecorator={<Icon icon="mdi:clock-alert" />}
            >
              APPROACHING OVERDUE ({getDaysSinceLastUpdate(admission.body.bodyTag.updatedAt)} days)
            </Chip>
          )}
        </Stack>
      </Box>

      {/* Main Content */}
      <Tabs
        value={selectedTab}
        onChange={(_, value) => setSelectedTab(value as string)}
        sx={{ bgcolor: 'background.surface', borderRadius: 'md' }}
      >
        <TabList>
          <Tab value="details">Details</Tab>
          <Tab value="body-tag">
            Body Tag & Status
            {admission.body?.bodyTag && (
              <Chip
                size="sm"
                variant="soft"
                color={getStatusColor(admission.body.bodyTag.status)}
              >
                {admission.body.bodyTag.status.replace('_', ' ')}
              </Chip>
            )}
          </Tab>
          <Tab value="collection">Collection</Tab>
          <Tab value="referrals">Referrals</Tab>
          <Tab value="custody">Chain of Custody</Tab>
          <Tab value="timeline">Timeline</Tab>
          <Tab value="documents">Documents</Tab>
        </TabList>

        {/* Details Tab */}
        <TabPanel value="details">
          <Grid container spacing={3}>
            {/* Admission Timer */}
            <Grid xs={12}>
              <AdmissionTimer
                admissionDate={new Date(admission?.admissionDate || Date.now())}
                referrals={admission?.body?.referrals || []}
                facilityId={admission?.facilityId}
              />
            </Grid>

            {/* Body Information */}
            <Grid xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography
                    level="title-md"
                    startDecorator={<Icon icon="mdi:account" />}
                    sx={{ mb: 2 }}
                  >
                    Body Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Tracking Number
                      </Typography>
                      <Typography level="body-md" fontWeight="bold">
                        {admission?.body?.trackingNumber || 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Status
                      </Typography>
                      <Chip
                        color={getStatusColor(admission?.body?.status)}
                        size="sm"
                        variant="soft"
                        sx={{ fontWeight: 'bold' }}
                      >
                        {admission?.body?.status || 'N/A'}
                      </Chip>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        First Name
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.firstName || 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Last Name
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.lastName || 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Gender
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.gender || 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Approximate Age
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.approximateAge ? `~${admission.body.approximateAge} years` : 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Height
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.height ? `${admission.body.height} cm` : 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Weight
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.weight ? `${admission.body.weight} kg` : 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Death Registration
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.deathRegistration || 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Date of Death
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.dateOfDeath ? format(new Date(admission.body.dateOfDeath), 'PPP') : 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Place of Death
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.placeOfDeath || 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Cause of Death
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.causeOfDeath || 'Unknown'}
                      </Typography>
                    </Grid>
                    {admission?.body?.distinguishingFeatures && (
                      <Grid xs={12}>
                        <Typography level="body-sm" color="neutral">
                          Distinguishing Features
                        </Typography>
                        <Typography level="body-md">
                          {admission.body.distinguishingFeatures}
                        </Typography>
                      </Grid>
                    )}
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Identification Status
                      </Typography>
                      <Typography level="body-md">
                        {admission?.body?.firstName && admission?.body?.lastName
                          ? 'Identified'
                          : 'Unidentified'}
                      </Typography>
                    </Grid>
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Condition
                      </Typography>
                      <Typography level="body-md">
                        {admission?.bodyCondition || 'Not recorded'}
                      </Typography>
                    </Grid>

                    {/* Days in Storage */}
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Days in Storage
                      </Typography>
                      <Typography
                        level="body-md"
                        color={
                          admission?.admissionDate &&
                          differenceInDays(new Date(), new Date(admission.admissionDate)) >= 7
                            ? 'danger'
                            : 'neutral'
                        }
                        fontWeight={
                          admission?.admissionDate &&
                          differenceInDays(new Date(), new Date(admission.admissionDate)) >= 7
                            ? 'bold'
                            : 'normal'
                        }
                      >
                        {admission?.admissionDate
                          ? `${differenceInDays(new Date(), new Date(admission.admissionDate))} days`
                          : 'N/A'}
                      </Typography>
                    </Grid>

                    {/* Admission Date */}
                    <Grid xs={12} sm={6}>
                      <Typography level="body-sm" color="neutral">
                        Admission Date
                      </Typography>
                      <Typography level="body-md">
                        {admission?.admissionDate
                          ? format(new Date(admission.admissionDate), 'PPP')
                          : 'N/A'}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Basic Information */}
            <Grid xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography
                    level="title-md"
                    startDecorator={<Icon icon="mdi:information" />}
                    sx={{ mb: 2 }}
                  >
                    Basic Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    {[
                      { label: 'Admission Type', value: admission.admissionType },
                      { label: 'Admission Date', value: format(new Date(admission.admissionDate), 'PPp') },
                      { label: 'Death Register Number', value: admission.deathRegisterNumber || 'N/A' },
                      { label: 'Body Condition', value: admission.bodyCondition || 'N/A' },
                      { label: 'Temperature', value: admission.temperature ? `${admission.temperature}°C` : 'N/A' },
                      { label: 'Assigned Fridge', value: admission.assignedFridge || 'N/A' },
                    ].map((item, index) => (
                      <Grid key={index} xs={12} sm={6}>
                        <Stack spacing={0.5}>
                          <Typography level="body-xs" color="neutral">
                            {item.label}
                          </Typography>
                          <Typography level="body-md">
                            {item.value}
                          </Typography>
                        </Stack>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Facility Information */}
            <Grid xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography
                    level="title-md"
                    startDecorator={<Icon icon="mdi:hospital-building" />}
                    sx={{ mb: 2 }}
                  >
                    Facility Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    {[
                      { label: 'Facility Name', value: admission.facility?.name || 'N/A' },
                      { label: 'Facility Code', value: admission.facility?.code || 'N/A' },
                      { label: 'Facility Type', value: admission.facility?.type || 'N/A' },
                    ].map((item, index) => (
                      <Grid key={index} xs={12} sm={6}>
                        <Stack spacing={0.5}>
                          <Typography level="body-xs" color="neutral">
                            {item.label}
                          </Typography>
                          <Typography level="body-md">
                            {item.value}
                          </Typography>
                        </Stack>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Body Tag & Status Tab */}
        <TabPanel value="body-tag">
          <Stack spacing={3}>
            {admission.body?.bodyTag ? (
              <>
                {/* Body Tag Information */}
                <Card variant="outlined">
                  <CardContent>
                    <Typography level="title-md" startDecorator={<Icon icon="mdi:qrcode" />} sx={{ mb: 2 }}>
                      Body Tag Information
                    </Typography>
                    <Divider sx={{ mb: 2 }} />

                    {/* Enhanced BodyTagSelector in edit mode (read-only) */}
                    <BodyTagSelector
                      name="bodyTag"
                      mode="edit"
                      bodyTagId={admission.body.bodyTag.id}
                      scanType={BodyTagScanType.ADMISSION}
                      showResult={true}
                      label="Current Body Tag"
                    />
                  </CardContent>
                </Card>

                {/* Status Transition Actions */}
                <Card variant="outlined">
                  <CardContent>
                    <Typography level="title-md" startDecorator={<Icon icon="mdi:swap-horizontal" />} sx={{ mb: 2 }}>
                      Status Transition Actions
                    </Typography>
                    <Divider sx={{ mb: 2 }} />

                    {getValidTransitions(admission.body.bodyTag.status).length > 0 ? (
                      <Grid container spacing={2}>
                        {getValidTransitions(admission.body.bodyTag.status).map((transition) => (
                          <Grid key={transition.type} xs={12} sm={6} md={4}>
                            <Card variant="soft" sx={{ cursor: 'pointer' }} onClick={() => handleStatusTransition(transition.type, admission.body?.bodyTag?.id, admission.body?.bodyTag?.status)}>
                              <CardContent>
                                <Stack direction="row" spacing={2} alignItems="center">
                                  <Icon icon={transition.icon} width={24} height={24} />
                                  <Stack>
                                    <Typography level="title-sm">{transition.label}</Typography>
                                    <Typography level="body-xs">
                                      {transition.type === 'admission-to-referral-in' && 'Process body for referral intake'}
                                      {transition.type === 'referral-in-to-referral-out' && 'Transfer body to another facility'}
                                      {transition.type === 'referral-out-to-pending-release' && 'Mark body as ready for release'}
                                      {transition.type === 'auto-overdue' && 'Mark body as overdue for processing'}
                                    </Typography>
                                  </Stack>
                                </Stack>
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    ) : (
                      <Alert color="neutral">
                        No status transitions available for current status: {admission.body.bodyTag.status.replace('_', ' ')}
                      </Alert>
                    )}
                  </CardContent>
                </Card>

                {/* Overdue Information */}
                {(isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) ||
                  isApproachingOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt)) && (
                  <Alert
                    color={isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) ? 'danger' : 'warning'}
                    startDecorator={<Icon icon="mdi:clock-alert" />}
                  >
                    <Stack spacing={1}>
                      <Typography level="title-sm">
                        {isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt) ? 'Overdue Alert' : 'Approaching Overdue'}
                      </Typography>
                      <Typography level="body-sm">
                        This body has been in {admission.body.bodyTag.status.replace('_', ' ')} status for {getDaysSinceLastUpdate(admission.body.bodyTag.updatedAt)} days.
                        {isOverdue(admission.body.bodyTag.status, admission.body.bodyTag.updatedAt)
                          ? ' Immediate action required.'
                          : ' Consider taking action soon to avoid overdue status.'}
                      </Typography>
                    </Stack>
                  </Alert>
                )}
              </>
            ) : (
              <Card variant="outlined">
                <CardContent>
                  <Stack spacing={2} alignItems="center" sx={{ py: 4 }}>
                    <Icon icon="mdi:qrcode-off" width={48} height={48} />
                    <Typography level="h4">No Body Tag Associated</Typography>
                    <Typography level="body-sm" textAlign="center">
                      This admission does not have an associated body tag
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
            )}
          </Stack>
        </TabPanel>

        {/* Collection Tab */}
        <TabPanel value="collection">
          {admission.body?.collection ? (
            <Grid container spacing={3}>
              {/* Collection Details */}
              <Grid xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography
                      level="title-md"
                      startDecorator={<Icon icon="mdi:ambulance" />}
                      sx={{ mb: 2 }}
                    >
                      Collection Details
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                      {[
                        { label: 'Institution', value: admission.body.collection.institution },
                        { label: 'Vehicle Registration', value: admission.body.collection.vehicleReg },
                        { label: 'Arrival Time', value: admission.body.collection.arrivalTime ? format(new Date(admission.body.collection.arrivalTime), 'PPp') : 'N/A' },
                        { label: 'Collection Type', value: admission.body.collection.collectionType },
                        { label: 'Temperature', value: admission.body.collection.temperature ? `${admission.body.collection.temperature}°C` : 'N/A' },
                        { label: 'Weather Conditions', value: admission.body.collection.weatherConditions || 'N/A' },
                      ].map((item, index) => (
                        <Grid key={index} xs={12} sm={6}>
                          <Stack spacing={0.5}>
                            <Typography level="body-xs" color="neutral">
                              {item.label}
                            </Typography>
                            <Typography level="body-md">
                              {item.value}
                            </Typography>
                          </Stack>
                        </Grid>
                      ))}
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Collection Location */}
              <Grid xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography
                      level="title-md"
                      startDecorator={<Icon icon="mdi:map-marker" />}
                      sx={{ mb: 2 }}
                    >
                      Collection Location
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    {getMapCoordinates() ? (
                      <AspectRatio ratio="16/9">
                        <Map
                          onLocationCaptured={() => {}}
                          useMockLocation={true}
                          initialLocation={getMapCoordinates() || undefined}
                        />
                      </AspectRatio>
                    ) : (
                      <Alert
                        variant="soft"
                        color="neutral"
                        startDecorator={<Icon icon="mdi:map-marker-off" />}
                      >
                        No location data available
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Collection Notes */}
              <Grid xs={12}>
                <Card>
                  <CardContent>
                    <Typography
                      level="title-md"
                      startDecorator={<Icon icon="mdi:note-text" />}
                      sx={{ mb: 2 }}
                    >
                      Collection Notes
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Typography level="body-md">
                      {admission.body.collection.collectionNotes || 'No notes available'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          ) : (
            <Alert
              variant="soft"
              color="neutral"
              startDecorator={<Icon icon="mdi:information" />}
            >
              No collection information available
            </Alert>
          )}
        </TabPanel>

        {/* Referrals Tab */}
        <TabPanel value="referrals">
          <Grid container spacing={3}>
            {/* Referral Action Panel */}
            <Grid xs={12}>
              <ReferralActionPanel
                bodyId={admission.body?.id || ''}
                admissionId={admission.id}
                activeReferrals={admission.body?.referrals?.filter(ref =>
                  ref.status === 'PENDING' || ref.status === 'IN_PROGRESS'
                ).map(ref => ({
                  id: ref.id,
                  referralType: ref.referralType,
                  status: ref.status,
                  institutionName: ref.institutionName
                })) || []}
                onReferralProcessed={() => router.refresh()}
              />
            </Grid>

            {/* Pathologist Verification Panel */}
            <Grid xs={12}>
              <PathologistVerificationPanel
                bodyId={admission.body?.id || ''}
                releaseId={admission.body?.releases?.[0]?.id}
                releaseStatus={admission.body?.releases?.[0]?.status}
                onVerificationComplete={() => router.refresh()}
              />
            </Grid>

            {/* Referral History */}
            <Grid xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography
                    level="title-md"
                    startDecorator={<Icon icon="mdi:history" />}
                    sx={{ mb: 2 }}
                  >
                    Referral History
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  {admission.body?.referrals?.length ? (
                    <Grid container spacing={3}>
                      {admission.body.referrals.map((referral) => (
                        <Grid key={referral.id} xs={12}>
                          <Card variant="soft">
                            <CardContent>
                              <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
                                <Typography
                                  level="title-md"
                                  startDecorator={<Icon icon="mdi:transfer" />}
                                >
                                  {referral.referralType} Referral
                                </Typography>
                                <Chip
                                  variant="soft"
                                  color={
                                    referral.status === 'PENDING' ? 'warning' :
                                    referral.status === 'IN_PROGRESS' ? 'primary' :
                                    referral.status === 'RETURNED' ? 'success' : 'neutral'
                                  }
                                >
                                  {referral.status}
                                </Chip>
                              </Stack>
                              <Divider sx={{ mb: 2 }} />
                              <Grid container spacing={2}>
                                {[
                                  { label: 'Institution', value: referral.institutionName },
                                  { label: 'Employee Name', value: referral.employeeName },
                                  { label: 'Employee Persal', value: referral.employeePersal },
                                  { label: 'Vehicle Registration', value: referral.vehicleReg },
                                  { label: 'Referral Date', value: format(new Date(referral.referralDate), 'PPp') },
                                  { label: 'Return Date', value: referral.returnDate ? format(new Date(referral.returnDate), 'PPp') : 'N/A' },
                                ].map((item, i) => (
                                  <Grid key={i} xs={12} sm={6} md={4}>
                                    <Stack spacing={0.5}>
                                      <Typography level="body-xs" color="neutral">
                                        {item.label}
                                      </Typography>
                                      <Typography level="body-md">
                                        {item.value}
                                      </Typography>
                                    </Stack>
                                  </Grid>
                                ))}
                              </Grid>
                              {referral.notes && (
                                <Box mt={2}>
                                  <Typography level="body-xs" color="neutral">
                                    Notes
                                  </Typography>
                                  <Typography level="body-md">
                                    {referral.notes}
                                  </Typography>
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Alert
                      variant="soft"
                      color="neutral"
                      startDecorator={<Icon icon="mdi:information" />}
                    >
                      No referrals found
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Chain of Custody Tab */}
        <TabPanel value="custody">
          <ChainOfCustodyTab bodyId={admission.body?.id || ''} />
        </TabPanel>


        {/* Timeline Tab */}
        <TabPanel value="timeline">
          <Grid container spacing={3}>
            <Grid xs={12}>
              <Card>
                <CardContent>
                  <Typography
                    level="title-md"
                    startDecorator={<Icon icon="mdi:timeline" />}
                    sx={{ mb: 2 }}
                  >
                    Visual Timeline
                  </Typography>
                  <Divider sx={{ mb: 3 }} />

                  {/* Visual Timeline */}
                  <Box sx={{ position: 'relative', py: 2 }}>
                    {/* Timeline Line */}
                    <Box
                      sx={{
                        position: 'absolute',
                        left: '24px',
                        top: 0,
                        bottom: 0,
                        width: '2px',
                        bgcolor: 'divider',
                        zIndex: 0,
                      }}
                    />

                    {/* Timeline Events */}
                    <Stack spacing={4}>
                      {getTimelineEvents().map((event, index) => (
                        <Box
                          key={index}
                          sx={{
                            position: 'relative',
                            pl: 6,
                            pr: 2,
                          }}
                        >
                          {/* Timeline Node */}
                          <Box
                            sx={{
                              position: 'absolute',
                              left: 0,
                              top: '50%',
                              transform: 'translate(-50%, -50%)',
                              width: 48,
                              height: 48,
                              borderRadius: '50%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              bgcolor: `var(--joy-palette-${event.color}-100)`,
                              color: `var(--joy-palette-${event.color}-500)`,
                              border: '2px solid',
                              borderColor: `var(--joy-palette-${event.color}-300)`,
                              zIndex: 1,
                            }}
                          >
                            <Icon icon={event.icon} width="24" height="24" />
                          </Box>

                          {/* Event Content */}
                          <Card
                            variant="outlined"
                            sx={{
                              borderLeft: '4px solid',
                              borderLeftColor: `var(--joy-palette-${event.color}-500)`,
                              boxShadow: 'sm',
                              transition: 'transform 0.2s, box-shadow 0.2s',
                              '&:hover': {
                                transform: 'translateY(-2px)',
                                boxShadow: 'md',
                              },
                            }}
                          >
                            <CardContent>
                              <Stack spacing={1}>
                                <Stack direction="row" justifyContent="space-between" alignItems="center">
                                  <Typography level="title-md">
                                    {event.title}
                                  </Typography>
                                  <Chip
                                    size="sm"
                                    variant="soft"
                                    color={event.color}
                                  >
                                    {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                                  </Chip>
                                </Stack>

                                <Typography level="body-sm" color="neutral">
                                  {format(event.date, 'PPpp')}
                                </Typography>

                                <Typography level="body-md">
                                  {event.description}
                                </Typography>

                                {/* Duration for referrals */}
                                {event.duration && (
                                  <Chip
                                    size="sm"
                                    variant="outlined"
                                    color="neutral"
                                    startDecorator={<Icon icon="mdi:clock-outline" />}
                                  >
                                    Duration: {event.duration} day{event.duration !== 1 ? 's' : ''}
                                  </Chip>
                                )}

                                {/* Event Details */}
                                {event.details && Object.keys(event.details).length > 0 && (
                                  <Box
                                    sx={{
                                      mt: 1,
                                      p: 1,
                                      borderRadius: 'sm',
                                      bgcolor: 'background.level1',
                                    }}
                                  >
                                    <Grid container spacing={1}>
                                      {Object.entries(event.details)
                                        .filter(([_, value]) => value !== null && value !== undefined && value !== '')
                                        .map(([key, value]) => (
                                          <Grid key={key} xs={12} sm={6} md={4}>
                                            <Stack direction="row" spacing={0.5} alignItems="center">
                                              <Typography level="body-xs" color="neutral">
                                                {key.charAt(0).toUpperCase() + key.slice(1)}:
                                              </Typography>
                                              <Typography level="body-xs">
                                                {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                              </Typography>
                                            </Stack>
                                          </Grid>
                                        ))}
                                    </Grid>
                                  </Box>
                                )}
                              </Stack>
                            </CardContent>
                          </Card>
                        </Box>
                      ))}
                    </Stack>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid xs={12}>
              <Card>
                <CardContent>
                  <Typography
                    level="title-md"
                    startDecorator={<Icon icon="mdi:list-status" />}
                    sx={{ mb: 2 }}
                  >
                    Event List
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <List>
                    {getTimelineEvents().map((event, index) => (
                      <React.Fragment key={index}>
                        <ListItem>
                          <ListItemContent>
                            <Stack direction="row" spacing={2} alignItems="center">
                              <Box
                                sx={{
                                  p: 1,
                                  borderRadius: 'md',
                                  bgcolor: `var(--joy-palette-${event.color}-100)`,
                                  color: `var(--joy-palette-${event.color}-500)`,
                                }}
                              >
                                <Icon icon={event.icon} width="24" height="24" />
                              </Box>
                              <Stack spacing={0.5}>
                                <Typography level="title-sm">
                                  {event.title}
                                </Typography>
                                <Typography level="body-sm" color="neutral">
                                  {format(event.date, 'PPp')}
                                </Typography>
                                <Typography level="body-sm">
                                  {event.description}
                                </Typography>
                              </Stack>
                            </Stack>
                          </ListItemContent>
                        </ListItem>
                        {index < getTimelineEvents().length - 1 && <ListDivider />}
                      </React.Fragment>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
        {/* Documents Tab */}
        <TabPanel value="documents">
          <Grid container spacing={3}>
            {/* Collection Documents */}
            {admission.body?.collection?.documents?.length > 0 && (
              <Grid xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography
                      level="title-md"
                      startDecorator={<Icon icon="mdi:file-document" />}
                      sx={{ mb: 2 }}
                    >
                      Collection Documents
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <List>
                      {admission.body.collection.documents.map((doc, index) => (
                        <React.Fragment key={index}>
                          <ListItem>
                            <ListItemContent>
                              <Stack direction="row" spacing={2} alignItems="center">
                                <Icon icon="mdi:file-document-outline" width="24" height="24" />
                                <Stack spacing={0.5}>
                                  <Typography level="title-sm">
                                    Document {index + 1}
                                  </Typography>
                                  <Button
                                    variant="plain"
                                    color="primary"
                                    component="a"
                                    href={doc}
                                    target="_blank"
                                    startDecorator={<Icon icon="mdi:download" />}
                                  >
                                    Download
                                  </Button>
                                </Stack>
                              </Stack>
                            </ListItemContent>
                          </ListItem>
                          {index <  admission.body.collection.documents.length - 1 && <ListDivider />}
                        </React.Fragment>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* Referral Documents */}
            {admission.body?.referrals?.some(ref => ref.bodyTagPhoto || ref.vehiclePhoto) && (
              <Grid xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography
                      level="title-md"
                      startDecorator={<Icon icon="mdi:file-document" />}
                      sx={{ mb: 2 }}
                    >
                      Referral Documents
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <List>
                      {admission.body.referrals.map((ref, refIndex) => (
                        <React.Fragment key={ref.id}>
                          {[ref.bodyTagPhoto, ref.vehiclePhoto].map((photo, photoIndex) =>
                            photo && (
                              <React.Fragment key={`${ref.id}-${photoIndex}`}>
                                <ListItem>
                                  <ListItemContent>
                                    <Stack direction="row" spacing={2} alignItems="center">
                                      <Icon icon="mdi:image" width="24" height="24" />
                                      <Stack spacing={0.5}>
                                        <Typography level="title-sm">
                                          {photoIndex === 0 ? 'Body Tag Photo' : 'Vehicle Photo'}
                                        </Typography>
                                        <Typography level="body-sm" color="neutral">
                                          Referral {refIndex + 1}
                                        </Typography>
                                        <Button
                                          variant="plain"
                                          color="primary"
                                          component="a"
                                          href={photo}
                                          target="_blank"
                                          startDecorator={<Icon icon="mdi:download" />}
                                        >
                                          Download
                                        </Button>
                                      </Stack>
                                    </Stack>
                                  </ListItemContent>
                                </ListItem>
                                <ListDivider />
                              </React.Fragment>
                            )
                          )}
                        </React.Fragment>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            )}

            {/* No Documents Message */}
            {(!admission.body?.collection?.documents?.length &&
              !admission.body?.referrals?.some(ref => ref.bodyTagPhoto || ref.vehiclePhoto)) && (
              <Grid xs={12}>
                <Alert
                  variant="soft"
                  color="neutral"
                  startDecorator={<Icon icon="mdi:information" />}
                >
                  No documents available
                </Alert>
              </Grid>
            )}
          </Grid>
        </TabPanel>
      </Tabs>

      {/* Status Transition Modals */}
      <Modal
        open={statusTransitionModal.type !== null}
        onClose={handleTransitionCancel}
      >
        <ModalDialog
          variant="outlined"
          layout="center"
          sx={{
            maxWidth: "90vw",
            width: "800px",
            maxHeight: "90vh",
            overflow: 'auto'
          }}
        >
          <ModalClose />
          <Stack spacing={2}>
            <Typography level="h4">
              {statusTransitionModal.type === 'admission-to-referral-in' && 'Process Referral In'}
              {statusTransitionModal.type === 'referral-in-to-referral-out' && 'Transfer to Facility'}
              {statusTransitionModal.type === 'referral-out-to-pending-release' && 'Mark Pending Release'}
              {statusTransitionModal.type === 'auto-overdue' && 'Mark as Overdue'}
            </Typography>

            {statusTransitionModal.type === 'admission-to-referral-in' && (
              <AdmissionToReferralInForm
                bodyTagId={statusTransitionModal.bodyTagId}
                onSuccess={handleTransitionSuccess}
                onCancel={handleTransitionCancel}
              />
            )}

            {statusTransitionModal.type === 'referral-in-to-referral-out' && (
              <ReferralInToReferralOutForm
                bodyTagId={statusTransitionModal.bodyTagId}
                onSuccess={handleTransitionSuccess}
                onCancel={handleTransitionCancel}
              />
            )}

            {statusTransitionModal.type === 'referral-out-to-pending-release' && (
              <ReferralOutToPendingReleaseForm
                bodyTagId={statusTransitionModal.bodyTagId}
                onSuccess={handleTransitionSuccess}
                onCancel={handleTransitionCancel}
              />
            )}

            {statusTransitionModal.type === 'auto-overdue' && (
              <AutoOverdueDetectionForm
                bodyTagId={statusTransitionModal.bodyTagId}
                currentStatus={statusTransitionModal.currentStatus}
                daysSinceLastUpdate={admission?.body?.bodyTag ? getDaysSinceLastUpdate(admission.body.bodyTag.updatedAt) : 0}
                onSuccess={handleTransitionSuccess}
                onCancel={handleTransitionCancel}
              />
            )}
          </Stack>
        </ModalDialog>
      </Modal>
    </Box>
  );
}