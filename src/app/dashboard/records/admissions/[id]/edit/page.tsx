'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";
import {
  Box,
  Typography,
  CircularProgress,
  Snackbar,
  Button,
  Card,
  CardContent,
  Grid,
  Stack,
  Tabs,
  TabList,
  Tab,
  TabPanel,
  FormControl,
  FormLabel,
  Input,
  Select,
  Option,
  Textarea,
  FormHelperText,
  IconButton,
  AspectRatio,
  Modal,
  ModalDialog,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import { format } from 'date-fns';
import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { AdmissionType, BodyStatus } from '@prisma/client';
import { useSession } from 'next-auth/react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  bodyAdmissionEditFormSchema,
  BodyAdmissionEditFormValues
} from '@/lib/schema/bodyAdmissionSchema';

// Interface for comprehensive admission type
interface ComprehensiveAdmission {
  id: string;
  admissionType: AdmissionType;
  admissionDate: string;
  temperature: number | null;
  bodyCondition: string | null;
  notes: string | null;
  assignedFridge: string | null;
  facilityId: string;
  assignedToId: string | null;
  createdById: string;
  bodyId: string;
  riskScore: number | null;
  riskCategory: string | null;
  riskFactors: string[] | null;
  photos: string[] | null;
  admissionPhoto: string | null;
  bodyTagPhoto: string | null;
  gpsLatitude: number | null;
  gpsLongitude: number | null;
  createdAt: string;
  updatedAt: string;
  body: {
    id: string;
    trackingNumber: string;
    firstName: string | null;
    lastName: string | null;
    gender: string | null;
    approximateAge: number | null;
    status: BodyStatus;
  };
  facility: {
    id: string;
    name: string;
    type: string;
  };
  assignedTo?: {
    id: string;
    name: string | null;
    role: string;
  };
  createdBy: {
    id: string;
    name: string | null;
    role: string;
  };
}

export default function EditAdmissionPage() {
  const { data: session } = useSession();
  const params = useParams();
  const router = useRouter();
  const admissionId = params.id as string;

  // State management
  const [facilities, setFacilities] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [admission, setAdmission] = useState<ComprehensiveAdmission | null>(null);
  const [activeTab, setActiveTab] = useState('details');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarColor, setSnackbarColor] = useState<'success' | 'danger'>('success');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [capturePhotoOpen, setCapturePhotoOpen] = useState(false);
  const [capturePhotoType, setCapturePhotoType] = useState<'admission' | 'bodyTag'>('admission');
  const [scanBarcodeOpen, setScanBarcodeOpen] = useState(false);
  const [tabsWithErrors, setTabsWithErrors] = useState<Record<string, boolean>>({});

  // Define field to tab mapping using useMemo to prevent recreation on each render
  const fieldToTabMapping = useMemo<Record<string, string>>(() => ({
    // Basic Details tab
    'admissionType': 'details',
    'admissionDate': 'details',
    'temperature': 'details',
    'bodyCondition': 'details',
    'notes': 'details',

    // Assignment tab
    'facilityId': 'assignment',
    'assignedToId': 'assignment',
    'assignedFridge': 'assignment',

    // Body Information tab
    'body.firstName': 'body',
    'body.lastName': 'body',
    'body.gender': 'body',
    'body.approximateAge': 'body',
    'body.height': 'body',
    'body.weight': 'body',
    'body.distinguishingFeatures': 'body',
    'body.deathRegistration': 'body',
    'body.causeOfDeath': 'body',
    'body.placeOfDeath': 'body',
    'body.dateOfDeath': 'body',
    'body.status': 'body',
  }), []);

  // Function to find which tab has errors using useCallback to prevent recreation on each render
  const findTabWithErrors = useCallback((formErrors: Record<string, any>): string | null => {
    // Check if there are any errors
    if (Object.keys(formErrors).length === 0) return null;

    // Check body nested errors
    if (formErrors.body) {
      const bodyFields = Object.keys(formErrors.body);
      if (bodyFields.length > 0) {
        return 'body';
      }
    }

    // Check other fields
    for (const field in formErrors) {
      if (field !== 'body') {
        const tab = fieldToTabMapping[field];
        if (tab) return tab;
      }
    }

    return null;
  }, [fieldToTabMapping]);

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitted },
    setValue,
    watch,
    reset
  } = useForm<BodyAdmissionEditFormValues>({
    resolver: zodResolver(bodyAdmissionEditFormSchema),
    defaultValues: {
      admissionType: 'INITIAL',
      admissionDate: '',
      facilityId: '',
      temperature: '',
      bodyCondition: '',
      notes: '',
      assignedToId: '',
      assignedFridge: '',
      body: {
        firstName: '',
        lastName: '',
        gender: '',
        approximateAge: '',
        height: '',
        weight: '',
        distinguishingFeatures: '',
        deathRegistration: '',
        causeOfDeath: '',
        placeOfDeath: '',
        dateOfDeath: '',
        status: 'ADMITTED',
      }
    }
  });

  // Fetch facilities and users
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [facilitiesRes, usersRes] = await Promise.all([
          fetch('/api/facilities'),
          fetch('/api/users')
        ]);

        if (!facilitiesRes.ok || !usersRes.ok) {
          throw new Error('Failed to fetch reference data');
        }

        const [facilitiesData, usersData] = await Promise.all([
          facilitiesRes.json(),
          usersRes.json()
        ]);

        setFacilities(facilitiesData);
        setUsers(usersData);
      } catch (err) {
        console.error('Error fetching reference data:', err);
        setError('Failed to load required reference data. Please try refreshing the page.');
      }
    };

    fetchData();
  }, []);

  // Effect to monitor form errors and switch tabs if needed
  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      const tabWithErrors = findTabWithErrors(errors);
      if (tabWithErrors) {
        // Update tabs with errors state
        const newTabsWithErrors = { ...tabsWithErrors };
        Object.keys(fieldToTabMapping).forEach(field => {
          const tab = fieldToTabMapping[field];
          // Check if this field or any field in the body object has errors
          if (field.startsWith('body.')) {
            const bodyField = field.split('.')[1];
            newTabsWithErrors[tab] = !!errors.body?.[bodyField];
          } else {
            newTabsWithErrors[tab] = !!errors[field];
          }
        });
        setTabsWithErrors(newTabsWithErrors);

        // Switch to the tab with errors
        setActiveTab(tabWithErrors);
      }
    } else {
      // Clear tabs with errors state when there are no errors
      setTabsWithErrors({});
    }
  }, [errors, fieldToTabMapping, findTabWithErrors, tabsWithErrors]);

  // Fetch admission details
  useEffect(() => {
    const fetchAdmission = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/admissions/${admissionId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Admission not found');
          }
          throw new Error('Failed to fetch admission details');
        }

        const data = await response.json();
        setAdmission(data);

        // Populate form with admission data
        reset({
          admissionType: data.admissionType,
          admissionDate: format(new Date(data.admissionDate), "yyyy-MM-dd'T'HH:mm"),
          facilityId: data.facilityId,
          temperature: data.temperature ? String(data.temperature) : '',
          bodyCondition: data.bodyCondition || '',
          notes: data.notes || '',
          assignedToId: data.assignedToId || '',
          assignedFridge: data.assignedFridge || '',
          body: {
            firstName: data.body?.firstName || '',
            lastName: data.body?.lastName || '',
            gender: data.body?.gender || '',
            approximateAge: data.body?.approximateAge ? String(data.body.approximateAge) : '',
            height: data.body?.height ? String(data.body.height) : '',
            weight: data.body?.weight ? String(data.body.weight) : '',
            distinguishingFeatures: data.body?.distinguishingFeatures || '',
            deathRegistration: data.body?.deathRegistration || '',
            causeOfDeath: data.body?.causeOfDeath || '',
            placeOfDeath: data.body?.placeOfDeath || '',
            dateOfDeath: data.body?.dateOfDeath ? format(new Date(data.body.dateOfDeath), "yyyy-MM-dd") : '',
            status: data.body?.status || 'ADMITTED',
          }
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (admissionId) {
    fetchAdmission();
    }
  }, [admissionId, reset]);

  // Handle form submission
  const onSubmit = async (data: BodyAdmissionEditFormValues) => {
    // Check for validation errors before submitting
    if (Object.keys(errors).length > 0) {
      const tabWithErrors = findTabWithErrors(errors);
      if (tabWithErrors) {
        // Show error notification
        setSnackbarColor('danger');
        setSnackbarMessage(`Please correct the errors in the ${tabWithErrors === 'details' ? 'Basic Details' :
          tabWithErrors === 'assignment' ? 'Assignment' : 'Body Information'} tab`);
        setSnackbarOpen(true);

        // Switch to the tab with errors
        setActiveTab(tabWithErrors);
        return;
      }
    }

    setSubmitting(true);
    setError(null);

    try {
      // Format the data to match the Prisma schema exactly
      const formattedData = {
        admissionType: data.admissionType,
        admissionDate: data.admissionDate,
        facilityId: data.facilityId,
        temperature: data.temperature && data.temperature.trim() !== '' ? parseFloat(data.temperature) : null,
        bodyCondition: data.bodyCondition || null,
        notes: data.notes || null,
        assignedToId: data.assignedToId || null,
        assignedFridge: data.assignedFridge || null,
        // Only include body data if it has meaningful changes
        body: data.body ? {
          firstName: data.body.firstName || null,
          lastName: data.body.lastName || null,
          gender: data.body.gender || null,
          approximateAge: data.body.approximateAge && data.body.approximateAge.toString().trim() !== '' ?
            parseInt(data.body.approximateAge.toString()) : null,
          height: data.body.height && data.body.height.toString().trim() !== '' ?
            parseFloat(data.body.height.toString()) : null,
          weight: data.body.weight && data.body.weight.toString().trim() !== '' ?
            parseFloat(data.body.weight.toString()) : null,
          distinguishingFeatures: data.body.distinguishingFeatures || null,
          deathRegistration: data.body.deathRegistration || null,
          causeOfDeath: data.body.causeOfDeath || null,
          placeOfDeath: data.body.placeOfDeath || null,
          dateOfDeath: data.body.dateOfDeath ? new Date(data.body.dateOfDeath) : null,
          status: data.body.status
        } : undefined
      };

      const response = await fetch(`/api/admissions/${admissionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formattedData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error response:', errorData);

        // Provide more detailed error information
        if (errorData.details) {
          console.error('API error details:', errorData.details);
          throw new Error(`${errorData.message || errorData.error}: ${errorData.details}`);
        }

        throw new Error(errorData.error || errorData.message || 'Failed to update admission');
      }

      const updatedAdmission = await response.json();

      setSnackbarColor('success');
      setSnackbarMessage('Admission updated successfully');
      setSnackbarOpen(true);

      // Redirect after a short delay
      setTimeout(() => {
        router.push(`/dashboard/records/admissions/${admissionId}`);
      }, 1500);
    } catch (err) {
      console.error('Error updating admission:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      setSnackbarColor('danger');
      setSnackbarMessage(errorMessage);
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    setDeleteLoading(true);

    try {
      const response = await fetch(`/api/admissions/${admissionId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete admission');
      }

      setSnackbarColor('success');
      setSnackbarMessage('Admission deleted successfully');
      setSnackbarOpen(true);

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/dashboard/records/admissions');
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setSnackbarColor('danger');
      setSnackbarMessage(err instanceof Error ? err.message : 'Failed to delete admission');
      setSnackbarOpen(true);
    } finally {
      setDeleteLoading(false);
      setShowDeleteModal(false);
    }
  };

  // Handle photo capture
  const handlePhotoCapture = (photoDataUrl: string) => {
    setCapturePhotoOpen(false);

    if (capturePhotoType === 'admission') {
      // Logic to update admission photo
      // This would typically involve an API call to update the photo
      // Removed console.log for production
    } else if (capturePhotoType === 'bodyTag') {
      // Logic to update body tag photo
      // Removed console.log for production
    }
  };

  // Handle barcode scan
  const handleBarcodeScanned = (barcode: string) => {
    setScanBarcodeOpen(false);
    // Removed console.log for production
    // Logic to handle the scanned barcode
  };

  // Display loading state
  const LoadingDisplay = () => (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2, p: 4 }}>
        <CircularProgress size="lg" />
      <Typography level="body-md">Loading admission details...</Typography>
      </Box>
    );

  // Display error state
  const ErrorDisplay = () => (
    <Card variant="outlined" sx={{ p: 3, maxWidth: 'md', mx: 'auto' }}>
      <CardContent>
        <Stack spacing={2} alignItems="center">
          <Icon icon="mdi:alert-circle" style={{ fontSize: '3rem', color: 'var(--joy-palette-danger-500)' }} />
          <Typography level="h4" color="danger">
            Error Loading Admission
          </Typography>
          <Typography level="body-md" textAlign="center">
            {error}
          </Typography>
            <Button
            variant="solid"
            color="primary"
            onClick={() => router.push('/dashboard/records/admissions')}
            startDecorator={<Icon icon="mdi:arrow-left" />}
          >
            Return to Admissions
            </Button>
        </Stack>
      </CardContent>
    </Card>
  );

  // Display empty state
  const EmptyState = () => (
    <Card variant="outlined" sx={{ p: 3, maxWidth: 'md', mx: 'auto' }}>
      <CardContent>
        <Stack spacing={2} alignItems="center">
          <Icon icon="mdi:folder-open" style={{ fontSize: '3rem', color: 'var(--joy-palette-warning-500)' }} />
          <Typography level="h4" color="warning">
            Admission Not Found
          </Typography>
          <Typography level="body-md" textAlign="center">
            The admission you're looking for doesn't exist or has been removed.
          </Typography>
          <Button
            variant="solid"
            color="primary"
            onClick={() => router.push('/dashboard/records/admissions')}
            startDecorator={<Icon icon="mdi:arrow-left" />}
          >
            Return to Admissions
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );

  if (loading) {
    return <LoadingDisplay />;
  }

  if (error && !admission) {
    return <ErrorDisplay />;
  }

  if (!admission) {
    return <EmptyState />;
  }

  return (
    <Box sx={{ p: 2, maxWidth: 'xl', mx: 'auto' }}>
      {/* Page Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          p: 2,
          borderRadius: 'sm',
          background: 'var(--joy-palette-background-level1)',
        }}
      >
        <Stack direction="column">
          <Typography level="h4" component="h1">
            Edit Admission
          </Typography>
          <Typography level="body-sm" color="primary">
            Tracking Number: {admission?.body?.trackingNumber || 'N/A'}
          </Typography>
        </Stack>

        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            color="neutral"
            onClick={() => router.push(`/dashboard/records/admissions/${admissionId}`)}
            startDecorator={<Icon icon="mdi:arrow-left" />}
          >
            Back
          </Button>
          <Button
            variant="outlined"
            color="danger"
            onClick={() => setShowDeleteModal(true)}
            startDecorator={<Icon icon="mdi:delete" />}
          >
            Delete
          </Button>
          <Button
            variant="solid"
            color="primary"
            onClick={handleSubmit(onSubmit)}
            loading={submitting}
            startDecorator={<Icon icon="mdi:content-save" />}
          >
            Save Changes
          </Button>
        </Stack>
      </Box>

      {/* Form Content */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Tabs
          value={activeTab}
          onChange={(_, value) => setActiveTab(value as string)}
          sx={{ mb: 3 }}
        >
          <TabList>
            <Tab value="details">
              {tabsWithErrors['details'] ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, color: 'danger.500' }}>
                  <Icon icon="mdi:alert-circle" />
                  <span>Basic Details</span>
                </Box>
              ) : (
                'Basic Details'
              )}
            </Tab>
            <Tab value="assignment">
              {tabsWithErrors['assignment'] ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, color: 'danger.500' }}>
                  <Icon icon="mdi:alert-circle" />
                  <span>Assignment</span>
                </Box>
              ) : (
                'Assignment'
              )}
            </Tab>
            <Tab value="body">
              {tabsWithErrors['body'] ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, color: 'danger.500' }}>
                  <Icon icon="mdi:alert-circle" />
                  <span>Body Information</span>
                </Box>
              ) : (
                'Body Information'
              )}
            </Tab>
            <Tab value="photos">Photos & Evidence</Tab>
          </TabList>

          {/* Basic Details Tab */}
          <TabPanel value="details">
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!errors.admissionType}>
                  <FormLabel required>Admission Type</FormLabel>
                  <Controller
                    name="admissionType"
                    control={control}
                    render={({ field }) => (
                  <Select
                        {...field}
                        onChange={(_, value) => field.onChange(value)}
                  >
                    {Object.values(AdmissionType).map((type) => (
                      <Option key={type} value={type}>{type}</Option>
                    ))}
                  </Select>
                    )}
                  />
                  {errors.admissionType && (
                    <FormHelperText>{errors.admissionType.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.admissionDate}>
                  <FormLabel required>Admission Date</FormLabel>
                  <Controller
                    name="admissionDate"
                    control={control}
                    render={({ field }) => (
                  <Input
                        {...field}
                    type="datetime-local"
                        error={!!errors.admissionDate}
                      />
                    )}
                  />
                  {errors.admissionDate && (
                    <FormHelperText>{errors.admissionDate.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.temperature}>
                  <FormLabel>Temperature</FormLabel>
                  <Controller
                    name="temperature"
                    control={control}
                    render={({ field }) => (
                  <Input
                        {...field}
                    type="number"
                    endDecorator="°C"
                        error={!!errors.temperature}
                        slotProps={{ input: { step: 0.1 } }}
                      />
                    )}
                  />
                  {errors.temperature && (
                    <FormHelperText>{errors.temperature.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.bodyCondition}>
                  <FormLabel>Body Condition</FormLabel>
                  <Controller
                    name="bodyCondition"
                    control={control}
                    render={({ field }) => (
                  <Input
                        {...field}
                        error={!!errors.bodyCondition}
                      />
                    )}
                  />
                  {errors.bodyCondition && (
                    <FormHelperText>{errors.bodyCondition.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <FormControl error={!!errors.notes}>
                  <FormLabel>Notes</FormLabel>
                  <Controller
                    name="notes"
                    control={control}
                    render={({ field }) => (
                  <Textarea
                        {...field}
                    minRows={3}
                        error={!!errors.notes}
                      />
                    )}
                  />
                  {errors.notes && (
                    <FormHelperText>{errors.notes.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Body Information Tab */}
          <TabPanel value="body">
            <Grid container spacing={3}>
              {/* Personal Information Section */}
              <Grid xs={12}>
                <Typography level="title-md" sx={{ mb: 2, color: 'primary.500' }}>
                  Personal Information
                </Typography>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.body?.firstName}>
                  <FormLabel>First Name</FormLabel>
                  <Controller
                    name="body.firstName"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Enter first name"
                      />
                    )}
                  />
                  {errors.body?.firstName && (
                    <FormHelperText>{errors.body?.firstName.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.body?.lastName}>
                  <FormLabel>Last Name</FormLabel>
                  <Controller
                    name="body.lastName"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Enter last name"
                      />
                    )}
                  />
                  {errors.body?.lastName && (
                    <FormHelperText>{errors.body?.lastName.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={4}>
                <FormControl error={!!errors.body?.gender}>
                  <FormLabel>Gender</FormLabel>
                  <Controller
                    name="body.gender"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        onChange={(_, value) => field.onChange(value)}
                        placeholder="Select gender"
                      >
                        <Option value="">Unknown</Option>
                        <Option value="Male">Male</Option>
                        <Option value="Female">Female</Option>
                        <Option value="Other">Other</Option>
                      </Select>
                    )}
                  />
                  {errors.body?.gender && (
                    <FormHelperText>{errors.body?.gender.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={4}>
                <FormControl error={!!errors.body?.approximateAge}>
                  <FormLabel>Approximate Age</FormLabel>
                  <Controller
                    name="body.approximateAge"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="number"
                        placeholder="Age in years"
                        endDecorator="years"
                        slotProps={{
                          input: {
                            min: 0,
                            max: 150,
                            step: 1
                          }
                        }}
                      />
                    )}
                  />
                  {errors.body?.approximateAge && (
                    <FormHelperText>{errors.body?.approximateAge.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={4}>
                <FormControl error={!!errors.body?.status}>
                  <FormLabel required>Status</FormLabel>
                  <Controller
                    name="body.status"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        onChange={(_, value) => field.onChange(value)}
                      >
                        {Object.values(BodyStatus).map((status) => (
                          <Option key={status} value={status}>
                            {status.replace('_', ' ')}
                          </Option>
                        ))}
                      </Select>
                    )}
                  />
                  {errors.body?.status && (
                    <FormHelperText>{errors.body?.status.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* Physical Characteristics Section */}
              <Grid xs={12}>
                <Typography level="title-md" sx={{ mb: 2, mt: 3, color: 'primary.500' }}>
                  Physical Characteristics
                </Typography>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.body?.height}>
                  <FormLabel>Height</FormLabel>
                  <Controller
                    name="body.height"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="number"
                        placeholder="Height in centimeters"
                        endDecorator="cm"
                        slotProps={{
                          input: {
                            min: 0,
                            max: 300,
                            step: 0.1
                          }
                        }}
                      />
                    )}
                  />
                  {errors.body?.height && (
                    <FormHelperText>{errors.body?.height.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.body?.weight}>
                  <FormLabel>Weight</FormLabel>
                  <Controller
                    name="body.weight"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="number"
                        placeholder="Weight in kilograms"
                        endDecorator="kg"
                        slotProps={{
                          input: {
                            min: 0,
                            max: 500,
                            step: 0.1
                          }
                        }}
                      />
                    )}
                  />
                  {errors.body?.weight && (
                    <FormHelperText>{errors.body?.weight.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <FormControl error={!!errors.body?.distinguishingFeatures}>
                  <FormLabel>Distinguishing Features</FormLabel>
                  <Controller
                    name="body.distinguishingFeatures"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        minRows={3}
                        placeholder="Describe any distinguishing features, marks, scars, tattoos, etc."
                      />
                    )}
                  />
                  {errors.body?.distinguishingFeatures && (
                    <FormHelperText>{errors.body?.distinguishingFeatures.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* Death Information Section */}
              <Grid xs={12}>
                <Typography level="title-md" sx={{ mb: 2, mt: 3, color: 'primary.500' }}>
                  Death Information
                </Typography>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.body?.deathRegistration}>
                  <FormLabel>Death Registration Number</FormLabel>
                  <Controller
                    name="body.deathRegistration"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Enter death registration number"
                      />
                    )}
                  />
                  {errors.body?.deathRegistration && (
                    <FormHelperText>{errors.body?.deathRegistration.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.body?.dateOfDeath}>
                  <FormLabel>Date of Death</FormLabel>
                  <Controller
                    name="body.dateOfDeath"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="date"
                        placeholder="Select date of death"
                      />
                    )}
                  />
                  {errors.body?.dateOfDeath && (
                    <FormHelperText>{errors.body?.dateOfDeath.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.body?.placeOfDeath}>
                  <FormLabel>Place of Death</FormLabel>
                  <Controller
                    name="body.placeOfDeath"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Enter place of death"
                      />
                    )}
                  />
                  {errors.body?.placeOfDeath && (
                    <FormHelperText>{errors.body?.placeOfDeath.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.body?.causeOfDeath}>
                  <FormLabel>Cause of Death</FormLabel>
                  <Controller
                    name="body.causeOfDeath"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Enter cause of death"
                      />
                    )}
                  />
                  {errors.body?.causeOfDeath && (
                    <FormHelperText>{errors.body?.causeOfDeath.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Assignment Tab */}
          <TabPanel value="assignment">
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!errors.facilityId}>
                  <FormLabel required>Facility</FormLabel>
                  <Controller
                    name="facilityId"
                    control={control}
                    render={({ field }) => (
                  <Select
                        {...field}
                        onChange={(_, value) => field.onChange(value)}
                  >
                        {facilities?.map((facility: any) => (
                      <Option key={facility.id} value={facility.id}>
                        {facility.name} ({facility.type})
                      </Option>
                    ))}
                  </Select>
                    )}
                  />
                  {errors.facilityId && (
                    <FormHelperText>{errors.facilityId.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.assignedToId}>
                  <FormLabel>Assigned To</FormLabel>
                  <Controller
                    name="assignedToId"
                    control={control}
                    render={({ field }) => (
                  <Select
                        {...field}
                        onChange={(_, value) => field.onChange(value)}
                  >
                    <Option value="">None</Option>
                    {users
                          ?.filter((user: any) => ['PATHOLOGIST', 'MORGUE_STAFF'].includes(user.role))
                          .map((user: any) => (
                        <Option key={user.id} value={user.id}>
                          {user.name} ({user.role})
                        </Option>
                    ))}
                  </Select>
                    )}
                  />
                  {errors.assignedToId && (
                    <FormHelperText>{errors.assignedToId.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!errors.assignedFridge}>
                  <FormLabel>Assigned Fridge</FormLabel>
                  <Controller
                    name="assignedFridge"
                    control={control}
                    render={({ field }) => (
                  <Input
                        {...field}
                        slotProps={{ input: { readOnly: true } }}
                      />
                    )}
                  />
                  {errors.assignedFridge && (
                    <FormHelperText>{errors.assignedFridge.message}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Photos & Evidence Tab */}
          <TabPanel value="photos">
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography level="title-md" mb={2}>
                      Admission Photo
                    </Typography>
                    {admission?.admissionPhoto ? (
                      <Box sx={{ position: 'relative' }}>
                        <AspectRatio ratio="4/3">
                          <img
                            src={admission.admissionPhoto}
                            alt="Admission photo"
                            style={{ objectFit: 'cover', borderRadius: '8px' }}
                          />
                        </AspectRatio>
                        <IconButton
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                          color="danger"
                          variant="solid"
                          size="sm"
                          onClick={() => {/* Logic to remove photo */}}
                        >
                          <Icon icon="mdi:delete" />
                        </IconButton>
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: 2,
                          p: 3,
                          border: '1px dashed',
                          borderColor: 'divider',
                          borderRadius: 'sm',
                        }}
                      >
                        <Icon icon="mdi:image" style={{ fontSize: '3rem', opacity: 0.5 }} />
                        <Typography level="body-sm" textAlign="center">
                          No admission photo available
                        </Typography>
                        <Button
                          variant="soft"
                          color="primary"
                          startDecorator={<Icon icon="mdi:camera" />}
                          onClick={() => {
                            setCapturePhotoType('admission');
                            setCapturePhotoOpen(true);
                          }}
                        >
                          Capture Photo
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography level="title-md" mb={2}>
                      Body Tag Photo
                    </Typography>
                    {admission?.bodyTagPhoto ? (
                      <Box sx={{ position: 'relative' }}>
                        <AspectRatio ratio="4/3">
                          <img
                            src={admission.bodyTagPhoto}
                            alt="Body tag photo"
                            style={{ objectFit: 'cover', borderRadius: '8px' }}
                          />
                        </AspectRatio>
                        {/* Removed delete button to prevent modification */}
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: 2,
                          p: 3,
                          border: '1px dashed',
                          borderColor: 'divider',
                          borderRadius: 'sm',
                        }}
                      >
                        <Icon icon="mdi:tag" style={{ fontSize: '3rem', opacity: 0.5 }} />
                        <Typography level="body-sm" textAlign="center">
                          No body tag photo available
                        </Typography>
                        <Typography level="body-sm" color="neutral" textAlign="center">
                          Body tag photos cannot be modified after admission
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography level="title-md" mb={2}>
                      Location
                    </Typography>
                    {admission?.gpsLatitude && admission?.gpsLongitude ? (
                      <Box>
                        <Typography level="body-sm" mb={1}>
                          Coordinates: {admission.gpsLatitude}, {admission.gpsLongitude}
                        </Typography>
                        <AspectRatio ratio="16/9" sx={{ maxHeight: '200px' }}>
                          <div
                            style={{
                              background: '#eee',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                          >
                            <Typography level="body-sm">
                              Map visualization would appear here
                            </Typography>
                          </div>
                        </AspectRatio>
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: 2,
                          p: 3,
                          border: '1px dashed',
                          borderColor: 'divider',
                          borderRadius: 'sm',
                        }}
                      >
                        <Icon icon="mdi:map-marker" style={{ fontSize: '3rem', opacity: 0.5 }} />
                        <Typography level="body-sm" textAlign="center">
                          No location data available
                        </Typography>
                        <Button
                          variant="soft"
                          color="primary"
                          startDecorator={<Icon icon="mdi:crosshairs-gps" />}
                          onClick={() => {/* Logic to capture GPS */}}
                        >
                          Capture Location
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </Tabs>
      </form>

      {/* Delete Confirmation Modal */}
      <Modal open={showDeleteModal} onClose={() => setShowDeleteModal(false)}>
        <ModalDialog variant="outlined" role="alertdialog">
          <Typography level="h4" color="danger">
            Delete Admission
          </Typography>
          <Typography level="body-md" mb={2}>
            Are you sure you want to delete this admission? This action cannot be undone.
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              variant="plain"
              color="neutral"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="solid"
              color="danger"
              onClick={handleDelete}
              loading={deleteLoading}
            >
              Delete
            </Button>
          </Box>
        </ModalDialog>
      </Modal>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={5000}
        onClose={() => setSnackbarOpen(false)}
        color={snackbarColor}
        variant="soft"
        startDecorator={
          <Icon icon={snackbarColor === 'danger' ? 'mdi:alert-circle' : 'mdi:check-circle'} />
        }
      >
        {snackbarMessage}
      </Snackbar>
    </Box>
  );
}
