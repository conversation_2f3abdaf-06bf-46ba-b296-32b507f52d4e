"use client";

import { useState, useEffect } from "react";
import { ComprehensiveAdmission } from "@/types/admission";

export const useAdmission = (id: string) => {
  const [admission, setAdmission] = useState<ComprehensiveAdmission | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusTransitionModal, setStatusTransitionModal] = useState<{
    type: 'admission-to-referral-in' | 'referral-in-to-referral-out' | 'referral-out-to-pending-release' | 'auto-overdue' | null;
    bodyTagId?: string;
    currentStatus?: string;
  }>({ type: null });

  const fetchAdmission = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/admissions/${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch admission');
      }

      const data = await response.json();
      setAdmission(data);
    } catch (err) {
      console.error('Error fetching admission:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const refreshAdmission = () => {
    fetchAdmission();
  };

  useEffect(() => {
    fetchAdmission();
  }, [id]);

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/admissions/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete admission');
      }

      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete admission');
      return false;
    }
  };

  const handleStatusTransition = (type: string, bodyTagId?: string, currentStatus?: string) => {
    setStatusTransitionModal({
      type: type as any,
      bodyTagId,
      currentStatus
    });
  };

  const handleTransitionSuccess = () => {
    setStatusTransitionModal({ type: null });
    refreshAdmission();
  };

  const handleTransitionCancel = () => {
    setStatusTransitionModal({ type: null });
  };

  return {
    admission,
    loading,
    error,
    statusTransitionModal,
    refreshAdmission,
    handleDelete,
    handleStatusTransition,
    handleTransitionSuccess,
    handleTransitionCancel,
  };
};
