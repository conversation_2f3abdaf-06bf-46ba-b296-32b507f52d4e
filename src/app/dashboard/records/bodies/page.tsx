"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardContent, Grid, Chip, Input, IconButton, Tooltip, Select, Option, Divider, CircularProgress } from '@mui/joy';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Icon } from '@iconify/react';
import { useChainOfCustody } from '@/hooks/useChainOfCustody';
import { Body, BodyCollection, BodyStatus, ReferralStatus, ReferralType, TagStatus } from '@prisma/client';
import { motion, AnimatePresence } from 'framer-motion';
import MiniBarcode from '@/components/Barcode/MiniBarcode';

interface ExtendedBody extends Body {
  collection?: BodyCollection | null;
  currentCustodian?: string;
  bodyTag?: {
    tagNumber: string;
    status: TagStatus;
    lastScannedAt: Date;
    lastScannedBy: string;
    facility: {
      name: string;
      code: string;
    };
  } | null;
  admissions?: {
    id: string;
    createdAt: Date;
    facilityId: string;
    facility: {
      name: string;
    };
  }[];
  referrals?: {
    id: string;
    status: ReferralStatus;
    type: ReferralType;
    createdAt: Date;
  }[];
}

type ViewMode = 'grid' | 'list';

export default function BodiesDirectoryPage() {
  const [bodies, setBodies] = useState<ExtendedBody[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<BodyStatus | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState('status');

  const { getCurrentCustodian } = useChainOfCustody();

  const loadBodies = async () => {
    setLoading(true);
    setError(null);
    try {
      const searchParams = new URLSearchParams();
      if (searchTerm) searchParams.append('search', searchTerm);
      if (filterStatus) searchParams.append('status', filterStatus);

      const response = await fetch(`/api/bodies?${searchParams.toString()}`);
      if (!response.ok) throw new Error(`Failed to fetch bodies: ${response.statusText}`);
      
      const data = await response.json();
      if (!Array.isArray(data)) throw new Error('Invalid data format received from server');

      const bodiesWithCustody = await Promise.all(
        data.map(async (body: Body) => {
            const [custodian] = await getCurrentCustodian(body.id);
            return {
              ...body,
              currentCustodian: custodian || 'Unknown'
            } as ExtendedBody;
        })
      );
      setBodies(bodiesWithCustody);
    } catch (err) {
      console.error('Error loading bodies:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBodies();
  }, [searchTerm, filterStatus]);

  const getStatusColor = (status: BodyStatus) => {
    const colors: Record<BodyStatus, 'primary' | 'success' | 'warning' | 'danger' | 'neutral'> = {
      COLLECTED: 'warning',
      ADMITTED: 'primary',
      IN_STORAGE: 'success',
      PENDING_RELEASE: 'warning',
      SECURITY_VERIFIED: 'primary',
      RELEASED: 'neutral',
      REFERRED: 'warning'
    };
    return colors[status] || 'neutral';
  };

  const sortedBodies = [...bodies].sort((a, b) => {
    switch (sortBy) {
      case 'status':
        return (a.status || '').localeCompare(b.status || '');
      case 'date':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'tag':
        return (a.bodyTagId || '').localeCompare(b.bodyTagId || '');
      default:
        return 0;
    }
  });

  const renderGridView = () => (
    <Grid container spacing={2}>
      <AnimatePresence>
        {sortedBodies.map((body) => (
          <Grid key={body.id} xs={12} sm={6} md={4} lg={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <Card 
                component={Link} 
                href={`/dashboard/records/bodies/${body.id}`}
                variant="outlined"
                sx={{ 
                  textDecoration: 'none',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 'md',
                    borderColor: 'primary.500',
                  }
                }}
              >
                <CardContent>
                  <Stack spacing={2}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <MiniBarcode
                        value={body.bodyTagId || 'Unknown'}
                        size={100}
                        metadata={{
                          padding: 0,
                        }}
                      />
                      <Chip
                        size="sm"
                        variant="soft"
                        color={getStatusColor(body.status)}
                        startDecorator={<Icon icon="mdi:clock-outline" />}
                      >
                        {body.status?.replace('_', ' ') || 'Unknown'}
                      </Chip>
                    </Stack>
                    
                    <Divider />
                    
                    <Stack spacing={1}>
                      {body.collection?.name && (
                        <Box>
                          <Typography level="body-xs">Collected By</Typography>
                          <Typography level="body-sm" startDecorator={<Icon icon="mdi:account" />}>
                            {body.collection.name}
                          </Typography>
                        </Box>
                      )}
                      <Box>
                        <Typography level="body-xs">Collection Type</Typography>
                        <Typography level="body-sm" startDecorator={<Icon icon="mdi:folder-outline" />}>
                          {body.collection?.collectionType || 'N/A'}
                        </Typography>
                      </Box>
                      {body.collection?.barcodeValue && (
                        <Box>
                          <Typography level="body-xs">Case Number</Typography>
                          <Typography level="body-sm" startDecorator={<Icon icon="mdi:barcode" />}>
                            {body.collection.barcodeValue}
                          </Typography>
                        </Box>
                      )}
                      <Box>
                        <Typography level="body-xs">Collection Date</Typography>
                        <Typography level="body-sm" startDecorator={<Icon icon="mdi:calendar" />}>
                          {body.collection?.arrivalTime ? 
                            new Date(body.collection.arrivalTime).toLocaleDateString() : 
                            'N/A'
                          }
                        </Typography>
                      </Box>
                      <Box>
                        <Typography level="body-xs">Current Custodian</Typography>
                        <Typography level="body-sm" startDecorator={<Icon icon="mdi:account-key" />}>
                          {body.currentCustodian || 'N/A'}
                        </Typography>
                      </Box>
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </AnimatePresence>
    </Grid>
  );

  const renderListView = () => (
    <Stack spacing={2}>
      <AnimatePresence>
        {sortedBodies.map((body) => (
          <motion.div
            key={body.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
          >
            <Card
              component={Link}
              href={`/dashboard/records/bodies/${body.id}`}
              variant="outlined"
              orientation="horizontal"
              sx={{
                textDecoration: 'none',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateX(4px)',
                  boxShadow: 'sm',
                  borderColor: 'primary.500',
                }
              }}
            >
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid xs={12} sm={3}>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Typography level="title-md">{body.bodyTagId || 'Unknown'}</Typography>
                      <Chip
                        size="sm"
                        variant="soft"
                        color={getStatusColor(body.status)}
                      >
                        {body.status?.replace('_', ' ') || 'Unknown'}
                      </Chip>
                    </Stack>
                  </Grid>
                  <Grid xs={12} sm={3}>
                    <Stack>
                      <Typography level="body-xs">Collection Details</Typography>
                      <Typography level="body-sm">
                        {body.collection?.name || 'N/A'} - {body.collection?.collectionType || 'N/A'}
                      </Typography>
                    </Stack>
                  </Grid>
                  <Grid xs={12} sm={3}>
                    <Stack>
                      <Typography level="body-xs">Case Number</Typography>
                      <Typography level="body-sm">
                        {body.collection?.barcodeValue || 'N/A'}
                      </Typography>
                    </Stack>
                  </Grid>
                  <Grid xs={12} sm={3}>
                    <Stack>
                      <Typography level="body-xs">Current Custodian</Typography>
                      <Typography level="body-sm">
                        {body.currentCustodian || 'N/A'}
                      </Typography>
                    </Stack>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </AnimatePresence>
    </Stack>
  );

  return (
    <Box sx={{ p: { xs: 2, md: 4 }, bgcolor: 'background.surface', minHeight: '100vh' }}>
      <Stack spacing={3}>
        {/* Header */}
        <Stack 
          direction={{ xs: 'column', sm: 'row' }} 
          justifyContent="space-between" 
          alignItems={{ xs: 'stretch', sm: 'center' }}
          spacing={2}
        >
          <Typography level="h3">Body Directory</Typography>
            <Button
              component={Link}
              href="/dashboard/records/collections/new/"
              startDecorator={<Icon icon="mdi:plus" />}
              variant="solid"
            >
              New Body Record
            </Button>
        </Stack>

        {/* Search and Filters */}
        <Card variant="outlined">
          <CardContent>
            <Stack spacing={2}>
              <Stack direction="row" spacing={2} alignItems="center">
              <Input
                  placeholder="Search records..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startDecorator={<Icon icon="mdi:magnify" />}
                  sx={{ flex: 1 }}
                />
                <Select 
                  value={sortBy}
                  onChange={(_, value) => value && setSortBy(value)}
                  placeholder="Sort by"
                  startDecorator={<Icon icon="mdi:sort" />}
                >
                  <Option value="status">Status</Option>
                  <Option value="date">Date</Option>
                  <Option value="tag">Tag Number</Option>
                </Select>
                <Stack direction="row" spacing={1}>
                  <Tooltip title="Grid View">
                    <IconButton
                      variant={viewMode === 'grid' ? 'solid' : 'plain'}
                      color={viewMode === 'grid' ? 'primary' : 'neutral'}
                      onClick={() => setViewMode('grid')}
                    >
                      <Icon icon="mdi:grid" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="List View">
                    <IconButton
                      variant={viewMode === 'list' ? 'solid' : 'plain'}
                      color={viewMode === 'list' ? 'primary' : 'neutral'}
                      onClick={() => setViewMode('list')}
                    >
                      <Icon icon="mdi:format-list-bulleted" />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Stack>
              
              <Stack direction="row" spacing={1} flexWrap="wrap">
                <Chip
                  variant={filterStatus === null ? 'solid' : 'soft'}
                  color="neutral"
                  onClick={() => setFilterStatus(null)}
                >
                  All
                </Chip>
                {Object.values(BodyStatus).map((status) => (
                  <Chip
                    key={status}
                    variant={filterStatus === status ? 'solid' : 'soft'}
                    color={getStatusColor(status)}
                    onClick={() => setFilterStatus(status)}
                  >
                    {status.replace('_', ' ')}
                  </Chip>
                ))}
              </Stack>
            </Stack>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert
            color="danger"
            variant="soft"
            startDecorator={<Icon icon="mdi:alert" />}
            endDecorator={
              <Stack direction="row" spacing={1}>
                <Button size="sm" variant="soft" color="danger" onClick={loadBodies}>
                  Retry
                </Button>
                <Button size="sm" variant="soft" color="danger" onClick={() => setError(null)}>
                  Dismiss
                </Button>
              </Stack>
            }
          >
            {error}
          </Alert>
        )}

        {/* Content */}
        {loading ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <CircularProgress />
          </Box>
        ) : sortedBodies.length === 0 ? (
          <Card variant="outlined">
            <CardContent>
              <Stack spacing={2} alignItems="center" sx={{ py: 4 }}>
                <Icon icon="mdi:folder-open" width={48} height={48} />
                <Typography level="h4">No Records Found</Typography>
                <Typography level="body-sm" color="neutral">
                  {bodies.length === 0 
                    ? "No body records have been added yet."
                    : "No records match your search criteria."}
                </Typography>
                {bodies.length === 0 && (
                  <Button
                    component={Link}
                    href="/dashboard/records/bodies/new"
                    startDecorator={<Icon icon="mdi:plus" />}
                  >
                    Add First Record
                  </Button>
                )}
              </Stack>
            </CardContent>
          </Card>
        ) : (
          viewMode === 'grid' ? renderGridView() : renderListView()
        )}
      </Stack>
    </Box>
  );
} 