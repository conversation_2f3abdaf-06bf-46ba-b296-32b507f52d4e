'use client'; // Error components must be Client Components
 
import { Box, Typography, Button } from '@mui/joy';
import { Icon } from '@iconify/react';
 
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        p: 3,
      }}
    >
      <Icon icon="mdi:alert-circle" width={64} height={64} color="error" />
      <Typography level="h4" color="danger" mt={2}>
        Something went wrong!
      </Typography>
      <Typography color="neutral" mt={1} mb={2}>
        {error.message}
      </Typography>
      <Button
        onClick={reset}
        startDecorator={<Icon icon="mdi:refresh" />}
      >
        Try again
      </Button>
    </Box>
  );
}