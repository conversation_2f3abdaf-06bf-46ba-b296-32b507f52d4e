"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useEffect } from 'react';
import { Box, Stack, Button, Al<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bPanel, CircularProgress } from '@mui/joy';
import { useRouter, useSearchParams } from 'next/navigation';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import type { ExtendedBody } from '@/types/body';

// Import modular components
import { BodyHeader } from '@/components/Body/BodyHeader';
import { BodySummaryCards } from '@/components/Body/BodySummaryCards';
import { OverviewTab } from '@/components/Body/OverviewTab';
import { AdmissionsTab } from '@/components/Body/AdmissionsTab';
import { ReferralsTab } from '@/components/Body/ReferralsTab';
import { DocumentsTab } from '@/components/Body/DocumentsTab';
import { HistoryTab } from '@/components/Body/HistoryTab';
import { CollectionTab } from '@/components/Body/CollectionTab';
import { ReleaseTab } from '@/components/Body/ReleaseTab';
import { PathologyTab } from '@/components/Body/PathologyTab';

export default function BodyDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [body, setBody] = React.useState<ExtendedBody | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [activeTab, setActiveTab] = React.useState<string>('overview');

  // Check for tab parameter in URL
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam && ['overview', 'admissions', 'referrals', 'collection', 'releases', 'documents', 'history', 'pathology'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  const refreshBody = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/bodies/${params.id}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Body record not found');
        }
        throw new Error('Failed to fetch body details');
      }
      const data = await response.json();
      setBody(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching body details');
    } finally {
      setLoading(false);
    }
  }, [params.id]);

  React.useEffect(() => {
    refreshBody();
  }, [refreshBody]);

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress size="lg" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert 
          color="danger" 
          variant="soft"
          endDecorator={
            <Stack direction="row" spacing={1}>
              <Button 
                onClick={refreshBody}
                size="sm"
                variant="soft"
                startDecorator={<Icon icon="mdi:refresh" />}
              >
                Retry
              </Button>
              <Button 
                component={Link}
                href="/dashboard/records/bodies"
                size="sm"
                variant="soft"
              >
                Return to List
              </Button>
            </Stack>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  if (!body) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert 
          color="danger" 
          variant="soft"
          endDecorator={
            <Button 
              component={Link}
              href="/dashboard/records/bodies"
              size="sm"
              variant="soft"
            >
              Return to List
            </Button>
          }
        >
          Body record not found
        </Alert>
      </Box>
    );
  }

  // Helper to check if collection is available
  const hasCollection = !!body.collection;
  // Helper to check if releases are available
  const hasReleases = !!body.releases && body.releases.length > 0;

  return (
    <Box sx={{ p: { xs: 2, md: 4 } }}>
      <Stack spacing={3}>
        {/* Header */}
        <BodyHeader body={body} id={params.id} refreshBody={refreshBody} />

        {/* Quick Info Cards */}
        <BodySummaryCards body={body} />

        {/* Tabs Content */}
        <Tabs
          value={activeTab}
          onChange={(event, value) => {
            setActiveTab(value as string);
            // Update URL with tab parameter for bookmarking and sharing
            const newUrl = `${window.location.pathname}?tab=${value}`;
            window.history.pushState({}, '', newUrl);
          }}
          sx={{ backgroundColor: 'background.surface' }}
        >
          <TabList>
            <Tab value="overview">Overview</Tab>
            <Tab value="admissions">
              Admissions
              <AnimatePresence>
                {body._metadata.totalAdmissions > 0 && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    style={{ display: 'inline-flex', marginLeft: '0.5rem' }}
                  >
                    <TabIndicator count={body._metadata.totalAdmissions} color="primary" />
                  </motion.span>
                )}
              </AnimatePresence>
            </Tab>
            <Tab value="referrals">
              Referrals
              <AnimatePresence>
                {body.activeReferrals.length > 0 && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    style={{ display: 'inline-flex', marginLeft: '0.5rem' }}
                  >
                    <TabIndicator count={body.activeReferrals.length} color="warning" />
                  </motion.span>
                )}
              </AnimatePresence>
            </Tab>
            <Tab value="collection">
              Collection
              {hasCollection && (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  style={{ display: 'inline-flex', marginLeft: '0.5rem' }}
                >
                  <TabIndicator color="success" checkmark />
                </motion.span>
              )}
            </Tab>
            <Tab value="releases">
              Releases
              {hasReleases && (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  style={{ display: 'inline-flex', marginLeft: '0.5rem' }}
                >
                  <TabIndicator count={body.releases.length} color="primary" />
                </motion.span>
              )}
            </Tab>
            <Tab value="documents">Documents</Tab>
            <Tab value="history">History</Tab>
            <Tab value="pathology">Pathology</Tab>
          </TabList>

          <Box sx={{ p: 2 }}>
            <TabPanel value="overview">
              <OverviewTab body={body} id={params.id} />
            </TabPanel>

            <TabPanel value="admissions">
              <AdmissionsTab body={body} id={params.id} />
            </TabPanel>

            <TabPanel value="referrals">
              <ReferralsTab body={body} id={params.id} />
            </TabPanel>

            <TabPanel value="collection">
              <CollectionTab body={body} />
            </TabPanel>

            <TabPanel value="releases">
              <ReleaseTab body={body} id={params.id} />
            </TabPanel>

            <TabPanel value="documents">
              <DocumentsTab body={body} id={params.id} />
            </TabPanel>

            <TabPanel value="history">
              <HistoryTab body={body} />
            </TabPanel>

            <TabPanel value="pathology">
              <PathologyTab body={body} />
            </TabPanel>
          </Box>
        </Tabs>
      </Stack>
    </Box>
  );
}

// Helper component for tab indicators
const TabIndicator = ({ count, color, checkmark = false }: { count?: number; color: 'primary' | 'success' | 'warning' | 'danger'; checkmark?: boolean }) => {
  if (checkmark) {
    return (
      <Box
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          minWidth: '1.5rem',
          height: '1.5rem',
          borderRadius: '1rem',
          bgcolor: `${color}.softBg`,
          color: `${color}.solidColor`,
          fontSize: 'xs',
          fontWeight: 'bold',
          ml: 0.5,
        }}
      >
        <Icon icon="mdi:check" width={14} />
      </Box>
    );
  }
  
  return (
    <Box
      sx={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        minWidth: '1.5rem',
        height: '1.5rem',
        borderRadius: '1rem',
        bgcolor: `${color}.softBg`,
        color: `${color}.solidColor`,
        fontSize: 'xs',
        fontWeight: 'bold',
        ml: 0.5,
      }}
    >
      {count}
    </Box>
  );
};
