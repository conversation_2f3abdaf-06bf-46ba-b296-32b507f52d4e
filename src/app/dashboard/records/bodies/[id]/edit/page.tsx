'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";




import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>ack,
  Typo<PERSON>,
  <PERSON>ton,
  Alert,
  CircularProgress,
  Tabs,
  TabList,
  Tab,
  TabPanel,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  Modal,
  ModalDialog,
  ModalClose,
  FormControl,
  FormLabel,
  Textarea,
  Checkbox,
} from '@mui/joy';
import { useRouter } from 'next/navigation';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import { BodyIdentificationForm } from '@/components/Forms/BodyIdentificationForm';
import { ExtendedBody } from '@/types/body';

interface FormState {
  isEdited: boolean;
  requiresApproval: boolean;
  approvalRequested: boolean;
  approvalMessage: string;
  changeDescription: string;
}

export default function EditBodyPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [body, setBody] = useState<ExtendedBody | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [approvalModalOpen, setApprovalModalOpen] = useState(false);
  const [formState, setFormState] = useState<FormState>({
    isEdited: false,
    requiresApproval: false,
    approvalRequested: false,
    approvalMessage: '',
    changeDescription: '',
  });

  // Fetch the body data
  useEffect(() => {
    const loadBody = async () => {
      try {
        const response = await fetch(`/api/bodies/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch body details');
        }
        const data = await response.json();
        setBody(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    loadBody();
  }, [params.id]);

  // Handle basic body details update
  /* const handleBodyDetailsSubmit = async (data: any) => {
    if (formState.requiresApproval && !formState.approvalRequested) {
      setApprovalModalOpen(true);
      return;
    }

    await saveBodyDetails(data);
  }; */

  // Handle identification update
  const handleIdentificationSubmit = async (data: any) => {
    if (formState.requiresApproval && !formState.approvalRequested) {
      setApprovalModalOpen(true);
      return;
    }

    await saveIdentification(data);
  };

  // Handle collection update
  /* const handleCollectionSubmit = async (data: any) => {
    if (formState.requiresApproval && !formState.approvalRequested) {
      setApprovalModalOpen(true);
      return;
    }

    await saveCollection(data);
  }; */

  // Save body details with approval metadata if needed
  /* const saveBodyDetails = async (data: any) => {
    setIsSaving(true);
    try {
      // Prepare data with approval metadata if requested
      const submitData = {
        ...data,
        _metadata: {
          ...(body?._metadata || {}),
          editApproval: formState.approvalRequested ? {
            requested: true,
            reason: formState.changeDescription,
            message: formState.approvalMessage,
            timestamp: new Date().toISOString(),
          } : undefined
        }
      };

      const response = await fetch(`/api/bodies/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        throw new Error('Failed to update body details');
      }

      // Handle approval flow or redirect
      if (formState.approvalRequested) {
        // If approval was requested, show confirmation but stay on page
        setFormState({
          ...formState,
          isEdited: false,
          approvalRequested: false,
        });
        // Show success message
        setError('Changes submitted for approval. You will be notified when approved.');
      } else {
        // If no approval needed, redirect back to detail page
        router.push(`/dashboard/records/bodies/${params.id}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving');
    } finally {
      setIsSaving(false);
    }
  }; */

  // Save identification with approval metadata if needed
  const saveIdentification = async (data: any) => {
    setIsSaving(true);
    try {
      // Similar pattern for identification
      const submitData = {
        ...data,
        requiresApproval: formState.approvalRequested,
        approvalReason: formState.approvalRequested ? formState.changeDescription : undefined,
        approvalMessage: formState.approvalRequested ? formState.approvalMessage : undefined,
      };

      const method = body?.identification ? 'PUT' : 'POST';

      const response = await fetch(`/api/bodies/${params.id}/identification`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${body?.identification ? 'update' : 'create'} identification`);
      }

      // Handle approval flow or redirect
      if (formState.approvalRequested) {
        setFormState({
          ...formState,
          isEdited: false,
          approvalRequested: false,
        });
        setError('Identification changes submitted for approval.');
      } else {
        router.push(`/dashboard/records/bodies/${params.id}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving identification');
    } finally {
      setIsSaving(false);
    }
  };

  // Save collection with approval metadata if needed
  /* const saveCollection = async (data: any) => {
    setIsSaving(true);
    try {
      // Similar pattern for collection
      const submitData = {
        ...data,
        requiresApproval: formState.approvalRequested,
        approvalReason: formState.approvalRequested ? formState.changeDescription : undefined,
        approvalMessage: formState.approvalRequested ? formState.approvalMessage : undefined,
      };

      const endpoint = body?.collection
        ? `/api/collections/${body.collection.id}`
        : `/api/collections`;

      const method = body?.collection ? 'PATCH' : 'POST';

      // If creating new collection, add bodyId
      if (method === 'POST') {
        submitData.bodyId = params.id;
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${body?.collection ? 'update' : 'create'} collection`);
      }

      // Handle approval flow or redirect
      if (formState.approvalRequested) {
        setFormState({
          ...formState,
          isEdited: false,
          approvalRequested: false,
        });
        setError('Collection changes submitted for approval.');
      } else {
        router.push(`/dashboard/records/bodies/${params.id}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving collection');
    } finally {
      setIsSaving(false);
    }
  }; */

  // Handle approval dialog confirmation
  const handleApprovalConfirm = () => {
    setFormState({
      ...formState,
      approvalRequested: true,
    });
    setApprovalModalOpen(false);

    // Submit the form based on active tab
    switch (activeTab) {
      case 0:
        try {
          const formElement = document.getElementById('body-details-form');
          if (formElement) {
            formElement.dispatchEvent(new Event('submit', { bubbles: true }));
          } else {
            throw new Error('Form element not found');
          }
        } catch (err: any) {
          console.error('Error submitting form:', err);
          setError('Failed to submit form. Please try again.');
        }
        break;
      case 1:
        try {
          const formElement = document.getElementById('identification-form');
          if (formElement) {
            formElement.dispatchEvent(new Event('submit', { bubbles: true }));
          } else {
            throw new Error('Form element not found');
          }
        } catch (err: any) {
          console.error('Error submitting form:', err);
          setError('Failed to submit form. Please try again.');
        }
        break;
      // Add more cases for other form types
    }
  };

  // Track when a form field has changed
  const handleFormChange = () => {
    if (!formState.isEdited) {
      setFormState({
        ...formState,
        isEdited: true,
      });
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size="lg" />
      </Box>
    );
  }

  if (error && !formState.approvalRequested) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="danger"
          variant="soft"
          endDecorator={
            <Button
              component={Link}
              href={`/dashboard/records/bodies/${params.id}`}
              size="sm"
              variant="soft"
            >
              Return to Details
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  // Show success message if changes were submitted for approval
  if (error && formState.approvalRequested) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="success"
          variant="soft"
          endDecorator={
            <Button
              component={Link}
              href={`/dashboard/records/bodies/${params.id}`}
              size="sm"
              variant="soft"
            >
              Return to Details
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  if (!body) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="danger"
          variant="soft"
          endDecorator={
            <Button
              component={Link}
              href="/dashboard/records/bodies"
              size="sm"
              variant="soft"
            >
              Return to List
            </Button>
          }
        >
          Body not found
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 4 } }}>
      <Stack spacing={3}>
        {/* Header with Admin Controls */}
        <Card>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid xs={12} md={6}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <Button
                    component={Link}
                    href={`/dashboard/records/bodies/${params.id}`}
                    variant="plain"
                    startDecorator={<Icon icon="mdi:arrow-left" />}
                  >
                    Back
                  </Button>
                  <Divider orientation="vertical" />
                  <Typography level="h3">Edit Body Record</Typography>
                </Stack>
              </Grid>

              <Grid xs={12} md={6}>
                <Stack
                  direction="row"
                  spacing={2}
                  justifyContent={{ xs: 'flex-start', md: 'flex-end' }}
                  sx={{ mt: { xs: 2, md: 0 } }}
                >
                  <FormControl orientation="horizontal" sx={{ gap: 1 }}>
                    <Checkbox
                      checked={formState.requiresApproval}
                      onChange={(e) => setFormState({
                        ...formState,
                        requiresApproval: e.target.checked
                      })}
                    />
                    <FormLabel>Requires Approval</FormLabel>
                  </FormControl>

                  <Chip
                    variant="soft"
                    color={formState.isEdited ? "warning" : "neutral"}
                    startDecorator={
                      formState.isEdited
                        ? <Icon icon="mdi:pencil" />
                        : <Icon icon="mdi:check" />
                    }
                  >
                    {formState.isEdited ? "Unsaved Changes" : "No Changes"}
                  </Chip>
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Notification for approval requirement */}
        {formState.requiresApproval && (
          <Alert
            color="warning"
            variant="soft"
            startDecorator={<Icon icon="mdi:lock-alert" />}
          >
            Changes to this record will require administrator approval before being applied.
          </Alert>
        )}

        {/* Tabbed Interface */}
        <Tabs
          value={activeTab}
          onChange={(event, value) => setActiveTab(value as number)}
          sx={{ width: '100%' }}
        >
          <TabList>
            <Tab>Identification</Tab>
            <Tab>Collection Details</Tab>
            <Tab>Admission Details</Tab>
            <Tab>Referrals</Tab>
            <Tab>Release Information</Tab>
          </TabList>

          {/* Identification Tab */}
          <TabPanel value={0}>
            <Card>
              <CardContent>
                <form id="identification-form" onSubmit={(e) => {
                  e.preventDefault();
                  handleIdentificationSubmit(body.identification || {});
                }}>
                  <BodyIdentificationForm
                    identification={body.identification}
                    onSubmit={handleIdentificationSubmit}
                    onChange={handleFormChange}
                    isLoading={isSaving}
                    mode={body.identification ? 'edit' : 'add'}
                  />
                </form>
              </CardContent>
            </Card>
          </TabPanel>

          {/* Collection Tab */}
          <TabPanel value={1}>
            <Card>
              <CardContent>
                {body.collection ? (
                  <Box>
                    <Typography level="h4" sx={{ mb: 2 }}>Edit Collection Details</Typography>
                    <Alert color="warning" sx={{ mb: 3 }}>
                      Editing collection details after the fact requires special permissions.
                    </Alert>
                    <Button
                      component={Link}
                      href={`/dashboard/records/collections/${body.collection.id}/edit`}
                      variant="outlined"
                      startDecorator={<Icon icon="mdi:pencil" />}
                    >
                      Edit Collection
                    </Button>
                  </Box>
                ) : (
                  <Box>
                    <Typography level="h4" sx={{ mb: 2 }}>Add Collection Information</Typography>
                    <Alert color="primary" sx={{ mb: 3 }}>
                      This body has no collection information. You can add it now.
                    </Alert>
                    <Button
                      component={Link}
                      href={`/dashboard/records/bodies/${params.id}/collection/new`}
                      variant="outlined"
                      startDecorator={<Icon icon="mdi:plus" />}
                    >
                      Add Collection Details
                    </Button>
                  </Box>
                )}
              </CardContent>
            </Card>
          </TabPanel>

          {/* Admission Tab */}
          <TabPanel value={2}>
            <Card>
              <CardContent>
                {body.currentAdmission ? (
                  <Box>
                    <Typography level="h4" sx={{ mb: 2 }}>Edit Current Admission</Typography>
                    <Alert color="warning" sx={{ mb: 3 }}>
                      Editing admission details after the fact may require special permissions.
                    </Alert>
                    <Button
                      component={Link}
                      href={`/dashboard/records/admissions/${body.currentAdmission.id}/edit`}
                      variant="outlined"
                      startDecorator={<Icon icon="mdi:pencil" />}
                    >
                      Edit Current Admission
                    </Button>
                  </Box>
                ) : (
                  <Box>
                    <Typography level="h4" sx={{ mb: 2 }}>Create Admission</Typography>
                    <Alert color="primary" sx={{ mb: 3 }}>
                      This body has no active admission. You can create one now.
                    </Alert>
                    <Button
                      component={Link}
                      href={`/dashboard/records/bodies/${params.id}/admission/new`}
                      variant="outlined"
                      startDecorator={<Icon icon="mdi:plus" />}
                    >
                      Create New Admission
                    </Button>
                  </Box>
                )}
              </CardContent>
            </Card>
          </TabPanel>

          {/* Referrals Tab */}
          <TabPanel value={3}>
            <Card>
              <CardContent>
                <Box>
                  <Typography level="h4" sx={{ mb: 2 }}>Manage Referrals</Typography>
                  <Alert color="primary" sx={{ mb: 3 }}>
                    {body.activeReferrals && body.activeReferrals.length > 0
                      ? `This body has ${body.activeReferrals.length} active referrals.`
                      : 'No active referrals exist for this body.'}
                  </Alert>
                  <Stack direction="row" spacing={2}>
                    <Button
                      component={Link}
                      href={`/dashboard/records/bodies/${params.id}/referral/new`}
                      variant="outlined"
                      startDecorator={<Icon icon="mdi:plus" />}
                    >
                      Create New Referral
                    </Button>
                    {body.activeReferrals && body.activeReferrals.length > 0 && (
                      <Button
                        component={Link}
                        href={`/dashboard/records/bodies/${params.id}?tab=referrals`}
                        variant="plain"
                        startDecorator={<Icon icon="mdi:view-list" />}
                      >
                        View Referrals
                      </Button>
                    )}
                  </Stack>
                </Box>
              </CardContent>
            </Card>
          </TabPanel>

          {/* Release Tab */}
          <TabPanel value={4}>
            <Card>
              <CardContent>
                {body.releases && body.releases.length > 0 ? (
                  <Box>
                    <Typography level="h4" sx={{ mb: 2 }}>Manage Release Information</Typography>
                    <Alert
                      color={body.status === 'RELEASED' ? 'success' : 'warning'}
                      sx={{ mb: 3 }}
                    >
                      {body.status === 'RELEASED'
                        ? 'This body has been released.'
                        : 'This body has pending release information.'}
                    </Alert>
                    <Stack direction="row" spacing={2}>
                      <Button
                        component={Link}
                        href={`/dashboard/records/releases/${body.releases[0].id}/edit`}
                        variant="outlined"
                        startDecorator={<Icon icon="mdi:pencil" />}
                      >
                        Edit Latest Release
                      </Button>
                      <Button
                        component={Link}
                        href={`/dashboard/records/bodies/${params.id}?tab=releases`}
                        variant="plain"
                        startDecorator={<Icon icon="mdi:view-list" />}
                      >
                        View All Releases
                      </Button>
                    </Stack>
                  </Box>
                ) : (
                  <Box>
                    <Typography level="h4" sx={{ mb: 2 }}>Create Release</Typography>
                    <Alert color="primary" sx={{ mb: 3 }}>
                      No release information exists for this body.
                    </Alert>
                    <Button
                      component={Link}
                      href={`/dashboard/records/bodies/${params.id}/release/new`}
                      variant="outlined"
                      startDecorator={<Icon icon="mdi:plus" />}
                    >
                      Create Release Record
                    </Button>
                  </Box>
                )}
              </CardContent>
            </Card>
          </TabPanel>
        </Tabs>
      </Stack>

      {/* Approval Request Modal */}
      <Modal open={approvalModalOpen} onClose={() => setApprovalModalOpen(false)}>
        <ModalDialog
          variant="outlined"
          role="alertdialog"
          aria-labelledby="approval-modal-title"
          sx={{ maxWidth: 500 }}
        >
          <ModalClose />
          <Typography id="approval-modal-title" level="h2">
            Request Approval
          </Typography>
          <Divider sx={{ my: 2 }} />
          <Typography>
            You are about to submit changes that require administrator approval.
            Please provide details about why these changes are needed.
          </Typography>

          <Stack spacing={2} sx={{ mt: 2 }}>
            <FormControl>
              <FormLabel>Reason for Changes</FormLabel>
              <Select
                value={formState.changeDescription}
                onChange={(e) => setFormState({
                  ...formState,
                  changeDescription: e.target.value as string
                })}
                placeholder="Select a reason"
              >
                <Option value="Error Correction">Error Correction</Option>
                <Option value="Updated Information">Updated Information</Option>
                <Option value="New Documentation">New Documentation</Option>
                <Option value="Administrative Change">Administrative Change</Option>
                <Option value="Other">Other (specify in message)</Option>
              </Select>
            </FormControl>

            <FormControl>
              <FormLabel>Details & Justification</FormLabel>
              <Textarea
                minRows={3}
                value={formState.approvalMessage}
                onChange={(e) => setFormState({
                  ...formState,
                  approvalMessage: e.target.value
                })}
                placeholder="Provide specific details about what is being changed and why approval is needed"
              />
            </FormControl>
          </Stack>

          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', mt: 3 }}>
            <Button variant="plain" color="neutral" onClick={() => setApprovalModalOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="solid"
              color="primary"
              onClick={handleApprovalConfirm}
              disabled={!formState.approvalMessage || !formState.changeDescription}
            >
              Submit for Approval
            </Button>
          </Box>
        </ModalDialog>
      </Modal>
    </Box>
  );
}

// Select component
const Select = ({
  value,
  onChange,
  placeholder,
  children
}: {
  value: string;
  onChange: (e: any) => void;
  placeholder: string;
  children: React.ReactNode;
}) => {
  return (
    <select
      value={value}
      onChange={onChange}
      style={{
        padding: '10px',
        borderRadius: '8px',
        border: '1px solid var(--joy-palette-neutral-300)',
        width: '100%',
        fontSize: '14px'
      }}
    >
      <option value="" disabled>{placeholder}</option>
      {children}
    </select>
  );
};

// Option component
const Option = ({
  value,
  children
}: {
  value: string;
  children: React.ReactNode;
}) => {
  return (
    <option value={value}>{children}</option>
  );
};
