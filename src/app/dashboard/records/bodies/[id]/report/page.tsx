'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";




import React from 'react';
import { Box, Stack, Button, Alert, CircularProgress } from '@mui/joy';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

// Import components
import { ReportHeader } from '@/components/Body/ReportHeader';
import { BodyReport } from '@/components/Reports/BodyReport';
import { Body } from '@/lib/db/types';

export default function BodyReportPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [body, setBody] = React.useState<Body | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadBody = async () => {
      try {
        const response = await fetch(`/api/body/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch body details');
        }
        const data = await response.json();
        setBody(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    loadBody();
  }, [params.id]);

  const handlePrint = () => {
    window.print();
  };

  const handleExport = async () => {
    try {
      // Create a formatted report object
      const report = {
        bodyTag: body?.bodyTag,
        status: body?.status,
        personalDetails: body?.personalDetails,
        collectionDetails: body?.collectionDetails,
        notes: body?.notes,
        exportDate: new Date().toISOString(),
      };

      // Create a blob and download it
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `body-report-${body?.bodyTag}-${new Date().toISOString()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export report');
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="danger"
          variant="soft"
          endDecorator={
            <Button
              component={Link}
              href="/dashboard/records/bodies"
              size="sm"
              variant="soft"
            >
              Return to List
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  if (!body) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          color="danger"
          variant="soft"
          endDecorator={
            <Button
              component={Link}
              href="/dashboard/records/bodies"
              size="sm"
              variant="soft"
            >
              Return to List
            </Button>
          }
        >
          Body not found
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 4 } }}>
      <Stack spacing={3}>
        {/* Header */}
        <ReportHeader id={params.id} />

        {/* Report */}
        <BodyReport
          body={body}
          onPrint={handlePrint}
          onExport={handleExport}
        />
      </Stack>
    </Box>
  );
}
