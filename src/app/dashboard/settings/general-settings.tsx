'use client';

import { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Grid,
  Typography,
  Select,
  Option,
  Textarea,
  Divider,
  Switch,
  Stack,
  Alert,
  IconButton,
} from '@mui/joy';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Business as BusinessIcon,
  ContactPhone as ContactPhoneIcon,
  Schedule as ScheduleIcon,
  VerifiedUser as VerifiedUserIcon,
  LocalHospital as LocalHospitalIcon,
} from '@mui/icons-material';

const SettingsSwitch = ({ 
  label, 
  description, 
  checked, 
  onChange,
  disabled = false 
}: {
  label: string;
  description?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}) => (
  <FormControl orientation="horizontal" sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
    <Box>
      <FormLabel>{label}</FormLabel>
      {description && (
        <Typography level="body-sm" color="neutral">
          {description}
        </Typography>
      )}
    </Box>
    <Switch
      checked={checked}
      onChange={(event) => onChange(event.target.checked)}
      disabled={disabled}
      slotProps={{
        track: {
          sx: {
            backgroundColor: checked ? 'primary.500' : 'neutral.200',
          }
        }
      }}
    />
  </FormControl>
);

const generalSettingsSchema = z.object({
  facilityName: z.string().min(1, 'Facility name is required'),
  facilityCode: z.string().min(1, 'Facility code is required'),
  address: z.string().min(1, 'Address is required'),
  contactPerson: z.string().min(1, 'Contact person is required'),
  contactEmail: z.string().email('Invalid email address'),
  contactPhone: z.string().min(1, 'Phone number is required'),
  operatingHours: z.object({
    start: z.string(),
    end: z.string(),
  }),
  timezone: z.string(),
  language: z.string(),
  facilityType: z.enum(['primary', 'secondary', 'tertiary']),
  maxCapacity: z.number().min(1),
  emergencyContact: z.string(),
  accreditationNumber: z.string(),
  accreditationExpiry: z.string(),
  facilityLicense: z.string(),
  isEmergencyFacility: z.boolean(),
});

type GeneralSettingsValues = z.infer<typeof generalSettingsSchema>;

const SectionHeader = ({ icon: Icon, title }: { icon: React.ElementType; title: string }) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
    <Icon sx={{ color: 'primary.500' }} />
    <Typography level="title-md">{title}</Typography>
  </Box>
);

export async function GeneralSettings({ 
  onComplete 
}: { 
  onComplete?: (data: GeneralSettingsValues) => Promise<void> 
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);

  const form = useForm<GeneralSettingsValues>({
    resolver: zodResolver(generalSettingsSchema),
    defaultValues: {
      facilityName: '',
      facilityCode: '',
      address: '',
      contactPerson: '',
      contactEmail: '',
      contactPhone: '',
      operatingHours: {
        start: '08:00',
        end: '17:00',
      },
      timezone: 'Africa/Johannesburg',
      language: 'en',
      facilityType: 'primary',
      maxCapacity: 100,
      emergencyContact: '',
      accreditationNumber: '',
      accreditationExpiry: '',
      facilityLicense: '',
      isEmergencyFacility: false,
    },
  });

  async function onSubmit(data: GeneralSettingsValues) {
    setIsLoading(true);
    try {
      // If onComplete callback is provided, call it with the form data
      if (onComplete) {
        await onComplete(data);
      }

      // Existing logic for showing success alert
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Box sx={{ p: 3, maxWidth: '1000px', mx: 'auto' }}>
      {showAlert && (
        <Alert 
          color="success" 
          variant="soft"
          sx={{ mb: 2 }}
          endDecorator={
            <IconButton variant="soft" onClick={() => setShowAlert(false)}>
              ×
            </IconButton>
          }
        >
          General settings updated successfully
        </Alert>
      )}

      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Stack spacing={4}>
          {/* Facility Information */}
          <Box>
            <SectionHeader icon={BusinessIcon} title="Facility Information" />
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.facilityName}>
                  <FormLabel>Facility Name</FormLabel>
                  <Input 
                    {...form.register('facilityName')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.facilityCode}>
                  <FormLabel>Facility Code</FormLabel>
                  <Input 
                    {...form.register('facilityCode')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <FormControl error={!!form.formState.errors.address}>
                  <FormLabel>Address</FormLabel>
                  <Textarea 
                    minRows={3} 
                    {...form.register('address')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          {/* Contact Information */}
          <Box>
            <SectionHeader icon={ContactPhoneIcon} title="Contact Information" />
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.contactPerson}>
                  <FormLabel>Contact Person</FormLabel>
                  <Input 
                    {...form.register('contactPerson')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.contactEmail}>
                  <FormLabel>Contact Email</FormLabel>
                  <Input 
                    type="email" 
                    {...form.register('contactEmail')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.contactPhone}>
                  <FormLabel>Contact Phone</FormLabel>
                  <Input 
                    type="tel" 
                    {...form.register('contactPhone')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          {/* Operating Hours */}
          <Box>
            <SectionHeader icon={ScheduleIcon} title="Operating Hours" />
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Start Time</FormLabel>
                  <Input
                    type="time"
                    {...form.register('operatingHours.start')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>End Time</FormLabel>
                  <Input
                    type="time"
                    {...form.register('operatingHours.end')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Timezone</FormLabel>
                  <Select
                    value={form.watch('timezone')}
                    onChange={(_, value) => form.setValue('timezone', value as string)}
                    sx={{ bgcolor: 'background.surface' }}
                  >
                    <Option value="Africa/Johannesburg">South Africa (GMT+2)</Option>
                    {/* Add more timezone options as needed */}
                  </Select>
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Language</FormLabel>
                  <Select
                    value={form.watch('language')}
                    onChange={(_, value) => form.setValue('language', value as string)}
                    sx={{ bgcolor: 'background.surface' }}
                  >
                    <Option value="en">English</Option>
                    <Option value="af">Afrikaans</Option>
                    <Option value="zu">isiZulu</Option>
                    <Option value="xh">isiXhosa</Option>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          {/* Accreditation Details */}
          <Box>
            <SectionHeader icon={VerifiedUserIcon} title="Accreditation Details" />
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Facility Type</FormLabel>
                  <Select
                    value={form.watch('facilityType')}
                    onChange={(_, value) => form.setValue('facilityType', value as any)}
                    sx={{ bgcolor: 'background.surface' }}
                  >
                    <Option value="primary">Primary Pathology Center</Option>
                    <Option value="secondary">Secondary Reference Lab</Option>
                    <Option value="tertiary">Tertiary Specialist Center</Option>
                  </Select>
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Accreditation Number</FormLabel>
                  <Input 
                    {...form.register('accreditationNumber')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Accreditation Expiry</FormLabel>
                  <Input 
                    type="date" 
                    {...form.register('accreditationExpiry')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Facility License Number</FormLabel>
                  <Input 
                    {...form.register('facilityLicense')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          {/* Emergency Services */}
          <Box>
            <SectionHeader icon={LocalHospitalIcon} title="Emergency Services" />
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Emergency Contact Number</FormLabel>
                  <Input 
                    {...form.register('emergencyContact')}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Maximum Facility Capacity</FormLabel>
                  <Input 
                    type="number" 
                    {...form.register('maxCapacity', { valueAsNumber: true })}
                    endDecorator="bodies"
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <SettingsSwitch
                  label="24/7 Emergency Facility"
                  description="Indicate if this facility handles emergency cases 24/7"
                  checked={form.watch('isEmergencyFacility')}
                  onChange={(checked) => form.setValue('isEmergencyFacility', checked)}
                />
              </Grid>
            </Grid>
          </Box>

          <Divider />

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              color="neutral"
              onClick={() => form.reset()}
              startDecorator={<RefreshIcon />}
            >
              Reset
            </Button>
            <Button 
              type="submit" 
              loading={isLoading}
              startDecorator={<SaveIcon />}
            >
              Save Changes
            </Button>
          </Box>
        </Stack>
      </form>
    </Box>
  );
} 