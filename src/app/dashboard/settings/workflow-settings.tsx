'use client'

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"

export default function WorkflowSettings() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Workflow Settings</h3>
        <p className="text-sm text-muted-foreground">
          Configure your workflow preferences and automation settings
        </p>
      </div>
      
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Notifications</CardTitle>
            <CardDescription>Configure workflow notification preferences</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="auto-assign">Auto-assign cases</Label>
              <Switch id="auto-assign" />
            </div>
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="workflow-updates">Workflow updates</Label>
              <Switch id="workflow-updates" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Automation</CardTitle>
            <CardDescription>Configure workflow automation settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="auto-processing">Automatic processing</Label>
              <Switch id="auto-processing" />
            </div>
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="auto-reports">Automatic reports</Label>
              <Switch id="auto-reports" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 