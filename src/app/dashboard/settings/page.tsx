'use client';
// Force dynamic rendering
export const dynamic = "force-dynamic";



import { Box, Typography, Tabs, TabList, Tab, TabPanel, Card, Divider } from '@mui/joy';
import { GeneralSettings } from './general-settings';
import { SecuritySettings } from './security-settings';

export default function Settings() {
  return (
    <Box sx={{ p: 0, mx: 'auto', display: 'flex', flexDirection: 'column', gap: 1.5 }}>
      <Box sx={{ px: 2, py: 1 }}>
        <Typography level="h4">Facility Settings</Typography>
        <Typography level="body-sm" color="neutral">
          Configure your pathology facility settings and workflow preferences
        </Typography>
      </Box>
      
      <Divider />
      
      <Tabs defaultValue={0} sx={{ bgcolor: 'background.surface' }}>
        <TabList>
          <Tab>General</Tab>
          <Tab>Security</Tab>
        </TabList>

        <TabPanel value={0}>
          <Card>
            <GeneralSettings />
          </Card>
        </TabPanel>

        <TabPanel value={1}>
          <Card>
            <SecuritySettings />
          </Card>
        </TabPanel>
      </Tabs>
    </Box>
  );
} 