'use client';

import { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Grid,
  Typography,
  Divider,
  Table,
  Sheet,
  Stack,
  Alert,
  IconButton,
} from '@mui/joy';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Storage as StorageIcon,
  Thermostat as ThermostatIcon,
  MonitorHeart as MonitorIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';

const SectionHeader = ({ icon: Icon, title }: { icon: React.ElementType; title: string }) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
    <Icon sx={{ color: 'primary.500' }} />
    <Typography level="title-md">{title}</Typography>
  </Box>
);

const storageSchema = z.object({
  facilityCapacity: z.number().min(1),
  fridgeCount: z.number().min(1),
  temperatureRange: z.object({
    min: z.number(),
    max: z.number(),
  }),
  alertThreshold: z.number().min(70).max(100),
  scanInterval: z.number().min(5),
});

type StorageFormValues = z.infer<typeof storageSchema>;

// Mock data for the storage units table
const mockStorageUnits = Array.from({ length: 10 }).map((_, index) => ({
  id: index + 1,
  temperature: -15,
  capacity: Math.floor(Math.random() * 100),
  status: 'Operational',
}));

export function StorageSettings() {
  const [isLoading, setIsLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);

  const form = useForm<StorageFormValues>({
    resolver: zodResolver(storageSchema),
    defaultValues: {
      facilityCapacity: 100,
      fridgeCount: 10,
      temperatureRange: {
        min: -20,
        max: 4,
      },
      alertThreshold: 80,
      scanInterval: 15,
    },
  });

  async function onSubmit(data: StorageFormValues) {
    setIsLoading(true);
    try {
      // TODO: Implement API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Box sx={{ p: 3, maxWidth: '1000px', mx: 'auto' }}>
      {showAlert && (
        <Alert 
          color="success" 
          variant="soft"
          sx={{ mb: 2 }}
          endDecorator={
            <IconButton variant="soft" onClick={() => setShowAlert(false)}>
              ×
            </IconButton>
          }
        >
          Storage settings updated successfully
        </Alert>
      )}

      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Stack spacing={4}>
          {/* Capacity Management */}
          <Box>
            <SectionHeader icon={StorageIcon} title="Capacity Management" />
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.facilityCapacity}>
                  <FormLabel>Total Facility Capacity</FormLabel>
                  <Input
                    type="number"
                    {...form.register('facilityCapacity', { valueAsNumber: true })}
                    endDecorator="bodies"
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.fridgeCount}>
                  <FormLabel>Number of Storage Units</FormLabel>
                  <Input
                    type="number"
                    {...form.register('fridgeCount', { valueAsNumber: true })}
                    endDecorator="units"
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          {/* Temperature Control */}
          <Box>
            <SectionHeader icon={ThermostatIcon} title="Temperature Control" />
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.temperatureRange?.min}>
                  <FormLabel>Minimum Temperature (°C)</FormLabel>
                  <Input
                    type="number"
                    {...form.register('temperatureRange.min', { valueAsNumber: true })}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.temperatureRange?.max}>
                  <FormLabel>Maximum Temperature (°C)</FormLabel>
                  <Input
                    type="number"
                    {...form.register('temperatureRange.max', { valueAsNumber: true })}
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          {/* Monitoring Settings */}
          <Box>
            <SectionHeader icon={MonitorIcon} title="Monitoring Settings" />
            <Grid container spacing={3}>
              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.alertThreshold}>
                  <FormLabel>Capacity Alert Threshold (%)</FormLabel>
                  <Input
                    type="number"
                    {...form.register('alertThreshold', { valueAsNumber: true })}
                    endDecorator="%"
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl error={!!form.formState.errors.scanInterval}>
                  <FormLabel>Temperature Scan Interval (minutes)</FormLabel>
                  <Input
                    type="number"
                    {...form.register('scanInterval', { valueAsNumber: true })}
                    endDecorator="min"
                    sx={{ bgcolor: 'background.surface' }}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          {/* Current Storage Status */}
          <Box>
            <SectionHeader icon={AssessmentIcon} title="Current Storage Status" />
            <Sheet 
              variant="outlined"
              sx={{ 
                height: '300px', 
                overflow: 'auto',
                borderRadius: 'sm',
                bgcolor: 'background.surface',
              }}
            >
              <Table stickyHeader>
                <thead>
                  <tr>
                    <th style={{ width: '25%' }}>Storage Unit</th>
                    <th style={{ width: '25%' }}>Current Temperature</th>
                    <th style={{ width: '25%' }}>Capacity Used</th>
                    <th style={{ width: '25%' }}>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {mockStorageUnits.map((unit) => (
                    <tr key={unit.id}>
                      <td>Unit {unit.id}</td>
                      <td>{unit.temperature}°C</td>
                      <td>
                        <Box sx={{ 
                          width: '100%', 
                          bgcolor: 'background.level2',
                          borderRadius: 'sm',
                          overflow: 'hidden'
                        }}>
                          <Box
                            sx={{
                              width: `${unit.capacity}%`,
                              height: '8px',
                              bgcolor: unit.capacity > 80 ? 'danger.500' : 'primary.500',
                            }}
                          />
                        </Box>
                        <Typography level="body-xs" sx={{ mt: 0.5 }}>
                          {unit.capacity}%
                        </Typography>
                      </td>
                      <td>
                        <Typography
                          level="body-sm"
                          color={unit.status === 'Operational' ? 'success' : 'danger'}
                        >
                          {unit.status}
                        </Typography>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Sheet>
          </Box>

          <Divider />

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              color="neutral"
              onClick={() => form.reset()}
              startDecorator={<RefreshIcon />}
            >
              Reset
            </Button>
            <Button 
              type="submit" 
              loading={isLoading}
              startDecorator={<SaveIcon />}
            >
              Save Changes
            </Button>
          </Box>
        </Stack>
      </form>
    </Box>
  );
} 