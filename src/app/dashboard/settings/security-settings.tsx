'use client';

import { useState } from 'react';
import { 
  Box, 
  FormControl, 
  FormLabel, 
  Switch, 
  Input,
  Stack, 
  Typography, 
  Button, 
  Alert,
  IconButton,
  Divider,
  Select,
  Option,
  Card,
  CardOverflow,
  Chip,
  Grid,
  FormHelperText,
} from '@mui/joy';
import { 
  Save as SaveIcon, 
  Security as SecurityIcon, 
  QrCodeScanner as QrCodeScannerIcon, 
  Timer as TimerIcon, 
  Key as KeyIcon,
  Refresh as RefreshIcon,
  Fingerprint as FingerprintIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

const securitySchema = z.object({
  passwordPolicy: z.enum(['basic', 'strong', 'complex']),
  sessionTimeout: z.number().min(5).max(120),
  twoFactorEnabled: z.boolean(),
  officerId: z.string().min(3),
  accessLevel: z.enum(['l1', 'l2', 'l3']),
  biometricRequired: z.boolean(),
  scannerDeviceId: z.string(),
  calibrationSchedule: z.string(),
  entryScanning: z.boolean(),
  exitScanning: z.boolean(),
  auditRetention: z.number().min(30).max(365),
  alertsEnabled: z.boolean(),
  recordFailedAttempts: z.boolean(),
  passwordMinLength: z.number().min(8).max(20),
  passwordExpiry: z.number().min(30).max(90),
  loginAttempts: z.number().min(3).max(10),
});

type SecurityFormValues = z.infer<typeof securitySchema>;

const SettingsSwitch = ({ 
  label, 
  description, 
  checked, 
  onChange,
  disabled = false,
  error = false,
  helperText,
}: {
  label: string;
  description?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
}) => (
  <FormControl 
    orientation="horizontal" 
    sx={{ 
      justifyContent: 'space-between', 
      alignItems: 'center',
      bgcolor: 'background.surface',
      p: 2,
      borderRadius: 'sm',
      border: '1px solid',
      borderColor: error ? 'danger.500' : 'divider',
    }}
  >
    <Box>
      <FormLabel>{label}</FormLabel>
      {description && (
        <Typography level="body-sm" color="neutral">
          {description}
        </Typography>
      )}
      {helperText && (
        <FormHelperText color={error ? 'danger' : 'neutral'}>
          {helperText}
        </FormHelperText>
      )}
    </Box>
    <Switch
      checked={checked}
      onChange={(event) => onChange(event.target.checked)}
      disabled={disabled}
      color={error ? 'danger' : 'primary'}
      slotProps={{
        track: {
          sx: {
            backgroundColor: checked ? 'primary.500' : 'neutral.200',
          }
        }
      }}
    />
  </FormControl>
);

const SectionHeader = ({ icon: Icon, title, subtitle }: { 
  icon: React.ElementType; 
  title: string;
  subtitle?: string;
}) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
    <Icon sx={{ color: 'primary.500' }} />
    <Box>
      <Typography level="title-md">{title}</Typography>
      {subtitle && (
        <Typography level="body-sm" color="neutral">
          {subtitle}
        </Typography>
      )}
    </Box>
  </Box>
);

export function SecuritySettings() {
  const [isLoading, setIsLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const form = useForm<SecurityFormValues>({
    resolver: zodResolver(securitySchema),
    defaultValues: {
      passwordPolicy: 'strong',
      sessionTimeout: 30,
      twoFactorEnabled: true,
      officerId: 'SEC-',
      accessLevel: 'l2',
      biometricRequired: true,
      scannerDeviceId: '',
      calibrationSchedule: '30',
      entryScanning: true,
      exitScanning: true,
      auditRetention: 365,
      alertsEnabled: true,
      recordFailedAttempts: true,
      passwordMinLength: 12,
      passwordExpiry: 90,
      loginAttempts: 5,
    },
  });

  const handleSubmit = async (data: SecurityFormValues) => {
    setIsLoading(true);
    try {
      // TODO: Implement API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box component="form" onSubmit={form.handleSubmit(handleSubmit)} sx={{ maxWidth: '1000px', mx: 'auto' }}>
      {showAlert && (
        <Alert 
          color="success" 
          variant="soft"
          sx={{ mb: 2 }}
          endDecorator={
            <IconButton variant="soft" onClick={() => setShowAlert(false)}>
              ×
            </IconButton>
          }
        >
          Security settings updated successfully
        </Alert>
      )}

      <Stack spacing={4}>
        {/* Authentication Settings */}
        <Card>
          <Box sx={{ p: 2 }}>
            <SectionHeader 
              icon={KeyIcon} 
              title="Authentication Settings" 
              subtitle="Configure password policies and login security"
            />
            <Stack spacing={3}>
              <Grid container spacing={3}>
                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Password Policy</FormLabel>
                    <Select 
                      value={form.watch('passwordPolicy')}
                      onChange={(_, value) => form.setValue('passwordPolicy', value as any)}
                      sx={{ bgcolor: 'background.surface' }}
                      indicator={<KeyIcon />}
                    >
                      <Option value="basic">Basic (8+ characters)</Option>
                      <Option value="strong">Strong (12+ chars, special characters)</Option>
                      <Option value="complex">Complex (14+ chars, numbers, special chars)</Option>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Session Timeout (minutes)</FormLabel>
                    <Input
                      type="number"
                      value={form.watch('sessionTimeout')}
                      onChange={(e) => form.setValue('sessionTimeout', Number(e.target.value))}
                      startDecorator={<TimerIcon />}
                      endDecorator="min"
                      sx={{ bgcolor: 'background.surface' }}
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Password Expiry (days)</FormLabel>
                    <Input
                      type="number"
                      value={form.watch('passwordExpiry')}
                      onChange={(e) => form.setValue('passwordExpiry', Number(e.target.value))}
                      endDecorator="days"
                      sx={{ bgcolor: 'background.surface' }}
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Maximum Login Attempts</FormLabel>
                    <Input
                      type="number"
                      value={form.watch('loginAttempts')}
                      onChange={(e) => form.setValue('loginAttempts', Number(e.target.value))}
                      endDecorator={
                        <Chip 
                          size="sm" 
                          variant="soft" 
                          color="danger"
                          startDecorator={<WarningIcon />}
                        >
                          Auto-lock after
                        </Chip>
                      }
                      sx={{ bgcolor: 'background.surface' }}
                    />
                  </FormControl>
                </Grid>
              </Grid>

              <SettingsSwitch
                label="Two-Factor Authentication"
                description="Require 2FA for all user logins"
                checked={form.watch('twoFactorEnabled')}
                onChange={(checked) => form.setValue('twoFactorEnabled', checked)}
                helperText="Recommended for enhanced security"
              />
            </Stack>
          </Box>
          <CardOverflow sx={{ bgcolor: 'background.level1', p: 1.5 }}>
            <Typography level="body-sm" color="neutral">
              Password must contain at least one uppercase letter, one number, and one special character
            </Typography>
          </CardOverflow>
        </Card>

        {/* Biometric Security */}
        <Card>
          <Box sx={{ p: 2 }}>
            <SectionHeader 
              icon={FingerprintIcon} 
              title="Biometric Security" 
              subtitle="Configure biometric authentication settings"
            />
            <Stack spacing={3}>
              <SettingsSwitch
                label="Biometric Verification"
                description="Require biometric authentication for security officers"
                checked={form.watch('biometricRequired')}
                onChange={(checked) => form.setValue('biometricRequired', checked)}
              />

              <Grid container spacing={3}>
                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Scanner Device ID</FormLabel>
                    <Input
                      value={form.watch('scannerDeviceId')}
                      onChange={(e) => form.setValue('scannerDeviceId', e.target.value)}
                      placeholder="Enter device ID"
                      startDecorator={<QrCodeScannerIcon />}
                      sx={{ bgcolor: 'background.surface' }}
                    />
                  </FormControl>
                </Grid>

                <Grid xs={12} md={6}>
                  <FormControl>
                    <FormLabel>Calibration Schedule</FormLabel>
                    <Select
                      value={form.watch('calibrationSchedule')}
                      onChange={(_, value) => form.setValue('calibrationSchedule', value as string)}
                      sx={{ bgcolor: 'background.surface' }}
                    >
                      <Option value="7">Weekly</Option>
                      <Option value="30">Monthly</Option>
                      <Option value="90">Quarterly</Option>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Stack>
          </Box>
        </Card>

        {/* Audit & Compliance */}
        <Card>
          <Box sx={{ p: 2 }}>
            <SectionHeader 
              icon={SecurityIcon} 
              title="Audit & Compliance" 
              subtitle="Configure security audit and monitoring settings"
            />
            <Stack spacing={3}>
              <FormControl>
                <FormLabel>Audit Log Retention (days)</FormLabel>
                <Input
                  type="number"
                  value={form.watch('auditRetention')}
                  onChange={(e) => form.setValue('auditRetention', Number(e.target.value))}
                  endDecorator="days"
                  sx={{ bgcolor: 'background.surface' }}
                />
              </FormControl>
              
              <SettingsSwitch
                label="Real-time Alert Notifications"
                description="Enable instant notifications for security events"
                checked={form.watch('alertsEnabled')}
                onChange={(checked) => form.setValue('alertsEnabled', checked)}
              />
              
              <SettingsSwitch
                label="Record Failed Access Attempts"
                description="Log all failed authentication attempts"
                checked={form.watch('recordFailedAttempts')}
                onChange={(checked) => form.setValue('recordFailedAttempts', checked)}
              />
            </Stack>
          </Box>
        </Card>

        <Divider />
        
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            color="neutral"
            onClick={() => form.reset()}
            startDecorator={<RefreshIcon />}
          >
            Reset
          </Button>
          <Button
            type="submit"
            startDecorator={<SaveIcon />}
            loading={isLoading}
          >
            Save Changes
          </Button>
        </Box>
      </Stack>
    </Box>
  );
} 