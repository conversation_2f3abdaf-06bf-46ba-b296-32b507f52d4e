'use client';

import { Box, Button, FormControl, FormLabel, Input, Stack, Typography, Alert, FormHelperText, LinearProgress, Sheet } from '@mui/joy';
import { useState } from 'react';
import { validatePassword } from '@/utils/form-validation';
import { formContainerStyles, buttonGroupStyles } from './styles';
import Link from 'next/link';
import { useSession } from 'next-auth/react';

export function SecurityForm() {
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<Record<string, string | string[]>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [passwordStrength, setPasswordStrength] = useState(0);
  const { data: session } = useSession();

  const calculatePasswordStrength = (password: string): number => {
    let strength = 0;
    if (password.length >= 8) strength += 20;
    if (/[A-Z]/.test(password)) strength += 20;
    if (/[a-z]/.test(password)) strength += 20;
    if (/[0-9]/.test(password)) strength += 20;
    if (/[!@#$%^&*]/.test(password)) strength += 20;
    return strength;
  };

  const getStrengthColor = (strength: number): 'danger' | 'warning' | 'success' => {
    if (strength < 40) return 'danger';
    if (strength < 80) return 'warning';
    return 'success';
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setFormData(prev => ({ ...prev, newPassword }));
    setPasswordStrength(calculatePasswordStrength(newPassword));
    if (errors.newPassword) {
      setErrors(prev => ({ ...prev, newPassword: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string | string[]> = {};

    if (!formData.currentPassword) {
      newErrors.currentPassword = 'Current password is required';
    }

    const passwordErrors = validatePassword(formData.newPassword);
    if (passwordErrors.length > 0) {
      newErrors.newPassword = passwordErrors;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // API call would go here
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      setSuccessMessage('Password updated successfully');
      setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      setErrors({ submit: 'Failed to update password. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Box>
      <Box component="form" onSubmit={handleSubmit}>
        <Sheet variant="outlined" sx={formContainerStyles}>
          <Stack spacing={3}>
            <Typography level="h4">Security Settings</Typography>

            {successMessage && (
              <Alert color="success" variant="soft">
                {successMessage}
              </Alert>
            )}

            {errors.submit && (
              <Alert color="danger" variant="soft">
                {errors.submit as string}
              </Alert>
            )}

            <FormControl error={!!errors.currentPassword} required>
              <FormLabel>Current Password</FormLabel>
              <Input
                type="password"
                value={formData.currentPassword}
                onChange={handleChange('currentPassword')}
                placeholder="Enter your current password"
              />
              {errors.currentPassword && (
                <FormHelperText>{errors.currentPassword as string}</FormHelperText>
              )}
            </FormControl>

            <FormControl error={!!errors.newPassword} required>
              <FormLabel>New Password</FormLabel>
              <Input
                type="password"
                value={formData.newPassword}
                onChange={handlePasswordChange}
                placeholder="Enter your new password"
              />
              <LinearProgress
                determinate
                value={passwordStrength}
                color={getStrengthColor(passwordStrength)}
                sx={{ mt: 1 }}
              />
              <Typography level="body-xs" sx={{ mt: 0.5 }}>
                Password strength: {passwordStrength}%
              </Typography>
              {Array.isArray(errors.newPassword) && errors.newPassword.map((error, index) => (
                <FormHelperText key={index}>{error}</FormHelperText>
              ))}
            </FormControl>

            <FormControl error={!!errors.confirmPassword} required>
              <FormLabel>Confirm New Password</FormLabel>
              <Input
                type="password"
                value={formData.confirmPassword}
                onChange={handleChange('confirmPassword')}
              />
              {errors.confirmPassword && (
                <FormHelperText>{errors.confirmPassword as string}</FormHelperText>
              )}
            </FormControl>

            <Box sx={buttonGroupStyles}>
              <Button
                type="submit"
                loading={isLoading}
                disabled={isLoading}
                variant="solid"
                color="primary"
              >
                Update Password
              </Button>
              <Button
                variant="outlined"
                color="neutral"
                onClick={() => {
                  setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' });
                  setErrors({});
                }}
              >
                Reset
              </Button>
            </Box>
          </Stack>
        </Sheet>
      </Box>
      
      {/* Two-Factor Authentication Section */}
      <Sheet variant="outlined" sx={{ ...formContainerStyles, mt: 3 }}>
        <Stack spacing={3}>
          <Typography level="h4">Two-Factor Authentication</Typography>
          
          <Typography level="body-md">
            Two-factor authentication adds an extra layer of security to your account by requiring both your password and a verification code from your phone.
          </Typography>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: 2 
          }}>
            <Box>
              <Typography 
                level="title-md" 
                sx={{ 
                  color: session?.user?.mfaEnabled ? "success.600" : "warning.600" 
                }}
              >
                {session?.user?.mfaEnabled 
                  ? "Two-factor authentication is enabled" 
                  : "Two-factor authentication is not enabled"
                }
              </Typography>
              <Typography level="body-sm" sx={{ mt: 0.5 }}>
                {session?.user?.mfaEnabled 
                  ? "Your account has an extra layer of security." 
                  : "Enable two-factor authentication for enhanced security."
                }
              </Typography>
            </Box>
            
            <Link href="/dashboard/settings/account/mfa" passHref>
              <Button 
                component="a"
                color={session?.user?.mfaEnabled ? "neutral" : "primary"}
                variant={session?.user?.mfaEnabled ? "outlined" : "solid"}
              >
                {session?.user?.mfaEnabled ? "Manage 2FA" : "Set Up 2FA"}
              </Button>
            </Link>
          </Box>
        </Stack>
      </Sheet>
    </Box>
  );
} 