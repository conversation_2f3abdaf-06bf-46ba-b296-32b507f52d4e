import { SxProps } from '@mui/joy/styles/types';

export const formContainerStyles: SxProps = {
  backgroundColor: 'background.surface',
  p: 3,
  borderRadius: 'sm',
  border: '1px solid',
  borderColor: 'divider',
  boxShadow: 'sm',
};

export const formHeaderStyles: SxProps = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  mb: 3,
};

export const buttonGroupStyles: SxProps = {
  display: 'flex',
  gap: 2,
  mt: 3,
};

export const tabPanelStyles: SxProps = {
  p: 3,
  height: '100%',
}; 