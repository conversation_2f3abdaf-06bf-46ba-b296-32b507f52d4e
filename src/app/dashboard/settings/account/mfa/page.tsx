"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Box, <PERSON><PERSON>, Card, Typography, Divider, CircularProgress, Alert } from "@mui/joy"
import { MfaSetup } from "@/components/mfa/MfaSetup"
import { MfaDisable } from "@/components/mfa/MfaDisable"
import { BackupCodesDisplay, RegenerateBackupCodes } from "@/components/mfa/BackupCodesDisplay"

export default function MfaSetupPage() {
  const router = useRouter()
  const { data: session, status, update } = useSession()
  const [isLoading, setIsLoading] = React.useState(true)
  const [setupMode, setSetupMode] = React.useState(false)
  const [disableDialogOpen, setDisableDialogOpen] = React.useState(false)
  const [backupCodesDialogOpen, setBackupCodesDialogOpen] = React.useState(false)
  const [backupCodes, setBackupCodes] = React.useState<string[]>([])
  const [successMessage, setSuccessMessage] = React.useState<string | null>(null)
  
  // Check if user is authenticated and if MFA is already enabled
  React.useEffect(() => {
    if (status === "loading") return
    
    if (status === "unauthenticated") {
      router.push("/auth/login?callbackUrl=/account/mfa")
      return
    }
    
    setIsLoading(false)
  }, [status, router])
  
  const handleSetupComplete = async () => {
    // Update the session to reflect MFA status change
    await update()
    setSetupMode(false)
    setSuccessMessage("Two-factor authentication has been successfully enabled.")
    
    // Clear the success message after a delay
    setTimeout(() => {
      setSuccessMessage(null)
    }, 5000)
  }
  
  const handleDisableSuccess = () => {
    setSuccessMessage("Two-factor authentication has been disabled.")
    
    // Clear the success message after a delay
    setTimeout(() => {
      setSuccessMessage(null)
    }, 5000)
  }
  
  const handleRegenerateSuccess = (codes: string[]) => {
    setBackupCodes(codes)
    setBackupCodesDialogOpen(true)
    setSuccessMessage("New backup codes have been generated.")
    
    // Clear the success message after a delay
    setTimeout(() => {
      setSuccessMessage(null)
    }, 5000)
  }
  
  if (isLoading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
        <CircularProgress size="lg" />
      </Box>
    )
  }

  if (setupMode) {
    return <MfaSetup onSetupComplete={handleSetupComplete} />
  }

  return (
    <Box sx={{ maxWidth: 800, mx: "auto", p: 4 }}>
      <Typography level="h2" component="h1" sx={{ mb: 4 }}>
        Two-Factor Authentication
      </Typography>
      
      {successMessage && (
        <Alert color="success" sx={{ mb: 4 }}>
          {successMessage}
        </Alert>
      )}
      
      {session?.user?.mfaEnabled ? (
        <>
          <Card variant="outlined" sx={{ mb: 4, p: 4 }}>
            <Box sx={{ 
              display: "flex", 
              alignItems: "center", 
              justifyContent: "space-between",
              flexWrap: "wrap",
              gap: 2
            }}>
              <Box>
                <Typography level="title-lg" color="success" sx={{ mb: 1 }}>
                  Two-Factor Authentication is Enabled
                </Typography>
                <Typography level="body-md">
                  Your account is protected with two-factor authentication.
                </Typography>
              </Box>
              
              <Button 
                color="danger" 
                variant="outlined"
                onClick={() => setDisableDialogOpen(true)}
              >
                Disable Two-Factor Authentication
              </Button>
            </Box>
          </Card>
          
          <Card variant="outlined" sx={{ p: 4 }}>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              Recovery Options
            </Typography>
            
            <Divider sx={{ my: 2 }} />
            
            <Box sx={{ mb: 3 }}>
              <Typography level="title-md" sx={{ mb: 1 }}>
                Backup Codes
              </Typography>
              
              <RegenerateBackupCodes onSuccess={handleRegenerateSuccess} />
            </Box>
          </Card>
          
          <MfaDisable 
            open={disableDialogOpen} 
            onClose={() => setDisableDialogOpen(false)}
            onSuccess={handleDisableSuccess}
          />
          
          <BackupCodesDisplay
            open={backupCodesDialogOpen}
            onClose={() => setBackupCodesDialogOpen(false)}
            backupCodes={backupCodes}
          />
        </>
      ) : (
        <Card variant="outlined" sx={{ p: 4 }}>
          <Typography level="title-lg" sx={{ mb: 2 }}>
            Enhance Your Account Security
          </Typography>
          
          <Typography level="body-md" sx={{ mb: 3 }}>
            Two-factor authentication adds an extra layer of security to your account by requiring both your password and a verification code from an authenticator app.
          </Typography>
          
          <Typography level="body-md" sx={{ mb: 3 }}>
            When enabled, you&apos;ll need to provide a verification code from your mobile device when signing in, even if someone has your password.
          </Typography>
          
          <Box sx={{ 
            display: "flex", 
            justifyContent: "space-between",
            alignItems: "center",
            flexWrap: "wrap",
            gap: 2
          }}>
            <Typography level="body-sm" color="warning">
              Two-factor authentication is currently disabled.
            </Typography>
            
            <Button onClick={() => setSetupMode(true)}>
              Set Up Two-Factor Authentication
            </Button>
          </Box>
        </Card>
      )}
    </Box>
  )
} 