'use client';

import {
  <PERSON>,
  <PERSON>box,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  She<PERSON>,
  <PERSON>vider,
  Radio,
  RadioGroup
} from '@mui/joy';
import { useState, useEffect } from 'react';
import { formContainerStyles, buttonGroupStyles } from './styles';
import { useCurrentUser } from '@/hooks/useCurrentUser';

interface NotificationCategory {
  title: string;
  description: string;
  settings: {
    [key: string]: {
      enabled: boolean;
      method: 'email' | 'sms' | 'both';
    };
  };
}

export function NotificationSettings() {
  const { currentUser } = useCurrentUser();
  const [categories, setCategories] = useState<Record<string, NotificationCategory>>({
    bodies: {
      title: 'Body Management',
      description: 'Notifications related to body handling and processing',
      settings: {
        newBodyCollection: { enabled: true, method: 'email' },
        bodyAdmission: { enabled: true, method: 'both' },
        bodyRelease: { enabled: true, method: 'both' },
      }
    },
    specimens: {
      title: 'Specimen Management',
      description: 'Notifications about specimen collection and processing',
      settings: {
        specimenCollection: { enabled: true, method: 'email' },
        specimenResults: { enabled: true, method: 'both' },
      }
    },
    system: {
      title: 'System Notifications',
      description: 'System-related updates and maintenance',
      settings: {
        systemUpdates: { enabled: false, method: 'email' },
        maintenance: { enabled: true, method: 'email' },
      }
    }
  });

  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    const loadSettings = async () => {
      if (!currentUser?.userId) return;
      
      try {
        const response = await fetch(`/api/users/${currentUser.userId}/notifications`);
        const data = await response.json();
        
        if (data.preferences) {
          setCategories(data.preferences);
        }
      } catch (err) {
        setError('Failed to load notification preferences');
      }
    };

    loadSettings();
  }, [currentUser?.userId]);

  const handleSave = async () => {
    if (!currentUser?.userId) {
      setError('No user found');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/users/${currentUser.userId}/notifications`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ preferences: categories }),
      });

      if (!response.ok) {
        throw new Error('Failed to update preferences');
      }

      setSuccessMessage('Notification preferences updated successfully');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      setError('Failed to update notification preferences');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggle = (category: string, setting: string) => {
    setCategories(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        settings: {
          ...prev[category].settings,
          [setting]: {
            ...prev[category].settings[setting],
            enabled: !prev[category].settings[setting].enabled
          }
        }
      }
    }));
  };

  const handleMethodChange = (category: string, setting: string, method: 'email' | 'sms' | 'both') => {
    setCategories(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        settings: {
          ...prev[category].settings,
          [setting]: {
            ...prev[category].settings[setting],
            method
          }
        }
      }
    }));
  };

  return (
    <Box>
      <Stack spacing={3}>
        {successMessage && (
          <Alert color="success" variant="soft">
            {successMessage}
          </Alert>
        )}

        {error && (
          <Alert color="danger" variant="soft">
            {error}
          </Alert>
        )}

        {Object.entries(categories).map(([categoryKey, category]) => (
          <Sheet
            key={categoryKey}
            variant="outlined"
            sx={formContainerStyles}
          >
            <Box sx={{ mb: 2 }}>
              <Typography level="title-md">{category.title}</Typography>
              <Typography level="body-sm" color="neutral">
                {category.description}
              </Typography>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Stack spacing={2}>
              {Object.entries(category.settings).map(([settingKey, setting]) => (
                <Box
                  key={settingKey}
                  sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: 2,
                    flexWrap: 'wrap'
                  }}
                >
                  <Checkbox
                    checked={setting.enabled}
                    onChange={() => handleToggle(categoryKey, settingKey)}
                    label={settingKey.replace(/([A-Z])/g, ' $1').trim()}
                  />
                  
                  {setting.enabled && (
                    <RadioGroup
                      orientation="horizontal"
                      value={setting.method}
                      onChange={(e) => handleMethodChange(
                        categoryKey,
                        settingKey,
                        e.target.value as 'email' | 'sms' | 'both'
                      )}
                      sx={{ ml: 'auto' }}
                    >
                      <Radio value="email" label="Email" />
                      <Radio value="sms" label="SMS" />
                      <Radio value="both" label="Both" />
                    </RadioGroup>
                  )}
                </Box>
              ))}
            </Stack>
          </Sheet>
        ))}

        <Box sx={buttonGroupStyles}>
          <Button
            onClick={handleSave}
            loading={isLoading}
            disabled={isLoading}
            variant="solid"
            color="primary"
          >
            Save Preferences
          </Button>
          <Button
            variant="outlined"
            color="neutral"
            onClick={() => window.location.reload()}
          >
            Reset
          </Button>
        </Box>
      </Stack>
    </Box>
  );
} 