'use client';

import { Box, Button, FormControl, FormLabel, Input, Stack, Typography, FormHelperText, Alert } from '@mui/joy';
import { useState } from 'react';
import { validatePhone, validatePersalNumber } from '@/utils/form-validation';

export function ProfileForm() {
  const [formData, setFormData] = useState({
    persalNumber: '',
    fullName: '',
    institution: '',
    email: '',
    phoneNumber: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!validatePersalNumber(formData.persalNumber)) {
      newErrors.persalNumber = 'Please enter a valid 8-digit PERSAL number';
    }
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }
    if (!formData.institution.trim()) {
      newErrors.institution = 'Institution is required';
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    if (formData.phoneNumber && !validatePhone(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid South African phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // API call would go here
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      setSuccessMessage('Profile updated successfully');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      setErrors({ submit: 'Failed to update profile. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Stack spacing={3} sx={{ maxWidth: 'sm' }}>
        <Typography level="h4">Profile Information</Typography>

        {successMessage && (
          <Alert color="success" variant="soft">
            {successMessage}
          </Alert>
        )}

        {errors.submit && (
          <Alert color="danger" variant="soft">
            {errors.submit}
          </Alert>
        )}

        <FormControl error={!!errors.persalNumber} required>
          <FormLabel>PERSAL Number</FormLabel>
          <Input
            name="persalNumber"
            value={formData.persalNumber}
            onChange={handleChange}
            placeholder="Enter your 8-digit PERSAL number"
          />
          {errors.persalNumber && (
            <FormHelperText>{errors.persalNumber}</FormHelperText>
          )}
        </FormControl>

        <FormControl error={!!errors.fullName} required>
          <FormLabel>Full Name</FormLabel>
          <Input
            name="fullName"
            value={formData.fullName}
            onChange={handleChange}
            placeholder="Enter your full name"
          />
          {errors.fullName && (
            <FormHelperText>{errors.fullName}</FormHelperText>
          )}
        </FormControl>

        <FormControl error={!!errors.institution} required>
          <FormLabel>Institution</FormLabel>
          <Input
            name="institution"
            value={formData.institution}
            onChange={handleChange}
            placeholder="Enter your institution"
          />
          {errors.institution && (
            <FormHelperText>{errors.institution}</FormHelperText>
          )}
        </FormControl>

        <FormControl error={!!errors.email} required>
          <FormLabel>Email</FormLabel>
          <Input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="Enter your email"
          />
          {errors.email && (
            <FormHelperText>{errors.email}</FormHelperText>
          )}
        </FormControl>

        <FormControl error={!!errors.phoneNumber}>
          <FormLabel>Phone Number</FormLabel>
          <Input
            type="tel"
            name="phoneNumber"
            value={formData.phoneNumber}
            onChange={handleChange}
            placeholder="Enter your South African phone number"
          />
          {errors.phoneNumber && (
            <FormHelperText>{errors.phoneNumber}</FormHelperText>
          )}
        </FormControl>

        <Box>
          <Button 
            type="submit" 
            variant="solid" 
            color="primary"
            loading={isLoading}
            disabled={isLoading}
          >
            Save Changes
          </Button>
        </Box>
      </Stack>
    </Box>
  );
} 