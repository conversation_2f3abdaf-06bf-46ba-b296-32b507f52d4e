'use client';
// Force dynamic rendering
export const dynamic = "force-dynamic";



import { Box, Typography, Tabs, TabList, Tab, TabPanel, Sheet } from '@mui/joy';
import { ProfileForm } from './profile-form';
import { SecurityForm } from './security-form';
import { NotificationSettings } from './notification-settings';
import { Divider } from '@mui/joy';
import { useState } from 'react';
import { tabPanelStyles } from './styles';

export default function AccountSettings() {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <Sheet variant="outlined" sx={{ borderRadius: 'sm' }}>
      <Box sx={{ p: 2 }}>
        <Typography level="h4">Account Settings</Typography>
        <Typography level="body-sm" color="neutral">
          Manage your account settings and preferences
        </Typography>
      </Box>
      
      <Divider />
      
      <Tabs
        value={activeTab}
        onChange={(_, value) => setActiveTab(value as number)}
        sx={{ bgcolor: 'background.surface' }}
      >
        <TabList
          variant="plain"
          sx={{
            p: 0,
            gap: 0,
            borderRadius: 0,
            borderBottom: '1px solid',
            borderColor: 'divider',
            [`& .MuiTab-root`]: {
              borderRadius: 0,
              flex: 1,
              [`&.Mui-selected`]: {
                bgcolor: 'transparent',
                borderBottom: '2px solid',
                borderBottomColor: 'primary.500',
              },
            },
          }}
        >
          <Tab>Profile</Tab>
          <Tab>Security</Tab>
          <Tab>Notifications</Tab>
        </TabList>

        <TabPanel value={0} sx={tabPanelStyles}>
          <ProfileForm />
        </TabPanel>

        <TabPanel value={1} sx={tabPanelStyles}>
          <SecurityForm />
        </TabPanel>

        <TabPanel value={2} sx={tabPanelStyles}>
          <NotificationSettings />
        </TabPanel>
      </Tabs>
    </Sheet>
  );
}
