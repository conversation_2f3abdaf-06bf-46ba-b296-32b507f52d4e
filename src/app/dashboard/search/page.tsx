"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  FormControl,
  FormLabel,
  Grid,
  Input,
  Stack,
  Typography,
  Button,
  Divider,
  Table,
} from '@mui/joy';
import { Icon } from '@iconify/react';

interface SearchResult {
  bodyTagNumber: string;
  deathRegisterNumber: string;
  admissionDate: string;
  status: string;
  location: string;
}

export default function SearchPage() {
  const [searchParams, setSearchParams] = useState({
    bodyTagNumber: '',
    deathRegisterNumber: '',
    dateRange: '',
  });

  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSearching(true);

    // Simulate API call
    setTimeout(() => {
      setSearchResults([
        {
          bodyTagNumber: '*********',
          deathRegisterNumber: '*********',
          admissionDate: '2023-12-01',
          status: 'In Processing',
          location: 'Morgue A',
        },
        // Add more mock results as needed
      ]);
      setIsSearching(false);
    }, 1000);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Card>
        <CardContent>
          <Typography level="title-lg" mb={2}>
            Body Search & Lookup
          </Typography>
          
          <form onSubmit={handleSearch}>
            <Grid container spacing={2}>
              <Grid xs={12} md={4}>
                <FormControl>
                  <FormLabel>Body Tag Number</FormLabel>
                  <Input
                    startDecorator={<Icon icon="mdi:tag" />}
                    value={searchParams.bodyTagNumber}
                    onChange={(e) => setSearchParams({...searchParams, bodyTagNumber: e.target.value})}
                  />
                </FormControl>
              </Grid>
              
              <Grid xs={12} md={4}>
                <FormControl>
                  <FormLabel>Death Register Number</FormLabel>
                  <Input
                    startDecorator={<Icon icon="mdi:file-document" />}
                    value={searchParams.deathRegisterNumber}
                    onChange={(e) => setSearchParams({...searchParams, deathRegisterNumber: e.target.value})}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={4}>
                <FormControl>
                  <FormLabel>Date Range</FormLabel>
                  <Input
                    type="date"
                    value={searchParams.dateRange}
                    onChange={(e) => setSearchParams({...searchParams, dateRange: e.target.value})}
                  />
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <Stack direction="row" spacing={2} justifyContent="flex-end">
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:refresh" />}
                    onClick={() => setSearchParams({
                      bodyTagNumber: '',
                      deathRegisterNumber: '',
                      dateRange: '',
                    })}
                  >
                    Clear
                  </Button>
                  <Button
                    type="submit"
                    loading={isSearching}
                    startDecorator={<Icon icon="mdi:magnify" />}
                  >
                    Search
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </form>

          <Divider sx={{ my: 3 }} />

          {searchResults.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography level="title-md" mb={2}>
                Search Results
              </Typography>
              <Table>
                <thead>
                  <tr>
                    <th>Body Tag #</th>
                    <th>Death Register #</th>
                    <th>Admission Date</th>
                    <th>Status</th>
                    <th>Location</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {searchResults.map((result) => (
                    <tr key={result.bodyTagNumber}>
                      <td>{result.bodyTagNumber}</td>
                      <td>{result.deathRegisterNumber}</td>
                      <td>{result.admissionDate}</td>
                      <td>{result.status}</td>
                      <td>{result.location}</td>
                      <td>
                        <Stack direction="row" spacing={1}>
                          <Button
                            size="sm"
                            variant="plain"
                            color="neutral"
                            onClick={() => {/* Handle view details */}}
                          >
                            <Icon icon="mdi:eye" />
                          </Button>
                          <Button
                            size="sm"
                            variant="plain"
                            color="primary"
                            onClick={() => {/* Handle edit */}}
                          >
                            <Icon icon="mdi:pencil" />
                          </Button>
                        </Stack>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
} 