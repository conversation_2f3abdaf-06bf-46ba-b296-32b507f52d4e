'use client';

// Force dynamic rendering
export const dynamic = "force-dynamic";





import { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Tab,
  Tabs,
  Fade,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Drawer,
  IconButton,
  Tooltip,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Alert,
  Breadcrumbs,
  Link,
  Divider,
  Paper
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Close as CloseIcon,
  Print as PrintIcon,
  Save as SaveIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

// Import planning components
import ResourceAllocationDashboard from '@/components/Planning/ResourceAllocationDashboard';
import CapacityPlanningChart from '@/components/Planning/CapacityPlanningChart';
import WorkloadForecastCard from '@/components/Planning/WorkloadForecastCard';
import StaffingPlanCard from '@/components/Planning/StaffingPlanCard';
import ResourceOptimizationCard from '@/components/Planning/ResourceOptimizationCard';
import PeakPeriodAnalysis from '@/components/Planning/PeakPeriodAnalysis';
import AdvancedAnalyticsPanel from '@/components/Planning/AdvancedAnalyticsPanel';
import ResourceOptimizationSettings from '@/components/Planning/ResourceOptimizationSettings';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Fade in={value === index}>
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`planning-tabpanel-${index}`}
        aria-labelledby={`planning-tab-${index}`}
        {...other}
      >
        {value === index && (
          <Box sx={{ py: 3 }}>
            {children}
          </Box>
        )}
      </div>
    </Fade>
  );
}

export default function PlanningDashboard() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const speedDialActions = useMemo(() => [
    { icon: <PrintIcon />, name: 'Print Report', action: () => handlePrint() },
    { icon: <SaveIcon />, name: 'Save Dashboard', action: () => handleSave() },
    { icon: <ShareIcon />, name: 'Share', action: () => handleShare() },
    { icon: <DownloadIcon />, name: 'Export Data', action: () => handleExport() },
  ], []);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Simulate data loading
      await new Promise(resolve => setTimeout(resolve, 1000));
      setLastUpdated(new Date());
    } catch (error) {
      setAlertMessage('Error loading dashboard data');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await loadDashboardData();
      setAlertMessage('Dashboard data refreshed successfully');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleSave = async () => {
    try {
      // Implement dashboard state saving logic
      setAlertMessage('Dashboard configuration saved successfully');
    } catch (error) {
      setAlertMessage('Error saving dashboard configuration');
    }
  };

  const handleShare = () => {
    // Implement sharing functionality
    const url = window.location.href;
    navigator.clipboard.writeText(url);
    setAlertMessage('Dashboard URL copied to clipboard');
  };

  const handleExport = async () => {
    try {
      // Implement data export logic
      setAlertMessage('Data exported successfully');
    } catch (error) {
      setAlertMessage('Error exporting data');
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          gap: 2
        }}
      >
        <CircularProgress />
        <Typography variant="body2" color="text.secondary">
          Loading planning dashboard...
        </Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 4 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 2 }}>
          <Link href="/dashboard" color="inherit">
            Dashboard
          </Link>
          <Typography color="text.primary">Planning</Typography>
        </Breadcrumbs>

        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Resource Planning & Analytics
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Last updated: {lastUpdated.toLocaleString()}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh Data">
              <IconButton onClick={handleRefresh} disabled={isRefreshing}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Optimization Settings">
              <IconButton onClick={() => setSettingsOpen(true)}>
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Alert Messages */}
        {alertMessage && (
          <Alert 
            severity="info" 
            onClose={() => setAlertMessage(null)}
            sx={{ mb: 2 }}
          >
            {alertMessage}
          </Alert>
        )}

        {/* Main Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant={isMobile ? 'scrollable' : 'fullWidth'}
            scrollButtons={isMobile ? 'auto' : false}
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab icon={<TimelineIcon />} label="Overview" />
            <Tab icon={<PeopleIcon />} label="Staffing" />
            <Tab icon={<AssessmentIcon />} label="Analytics" />
            <Tab icon={<TrendingUpIcon />} label="Forecasting" />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <Box sx={{ position: 'relative' }}>
          {/* Overview Tab */}
          <TabPanel value={activeTab} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <ResourceAllocationDashboard />
              </Grid>
              <Grid item xs={12} md={8}>
                <CapacityPlanningChart />
              </Grid>
              <Grid item xs={12} md={4}>
                <WorkloadForecastCard />
              </Grid>
              <Grid item xs={12}>
                <ResourceOptimizationCard />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Staffing Tab */}
          <TabPanel value={activeTab} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <StaffingPlanCard />
              </Grid>
              <Grid item xs={12}>
                <PeakPeriodAnalysis />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Analytics Tab */}
          <TabPanel value={activeTab} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <AdvancedAnalyticsPanel />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Forecasting Tab */}
          <TabPanel value={activeTab} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <WorkloadForecastCard />
              </Grid>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Forecast Accuracy
                    </Typography>
                    {/* Add forecast accuracy metrics here */}
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12}>
                <PeakPeriodAnalysis />
              </Grid>
            </Grid>
          </TabPanel>
        </Box>

        {/* Speed Dial */}
        <SpeedDial
          ariaLabel="Dashboard actions"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          icon={<SpeedDialIcon />}
        >
          {speedDialActions.map((action) => (
            <SpeedDialAction
              key={action.name}
              icon={action.icon}
              tooltipTitle={action.name}
              onClick={action.action}
            />
          ))}
        </SpeedDial>

        {/* Settings Drawer */}
        <Drawer
          anchor="right"
          open={settingsOpen}
          onClose={() => setSettingsOpen(false)}
          PaperProps={{
            sx: { width: { xs: '100%', sm: 600 } }
          }}
        >
          <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Optimization Settings</Typography>
            <IconButton onClick={() => setSettingsOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />
          <Box sx={{ px: 2, pb: 2 }}>
            <ResourceOptimizationSettings />
          </Box>
        </Drawer>
      </Box>
    </Container>
  );
}
