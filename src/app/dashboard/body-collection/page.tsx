"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { Box, Stack, Typography, Button, CircularProgress } from '@mui/joy';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import dynamicImport from 'next/dynamic';
import { Suspense } from 'react';

// Dynamically import the form with no SSR
const BodyCollectionForm = dynamicImport(
  () => import('@/components/Forms/BodyCollection/CollectionForm'),
  { 
    ssr: false,
    loading: () => (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    )
  }
);

export default function BodyCollectionPage() {
  return (
    <Box sx={{ p: 3 }}>
      <Stack spacing={3}>
        {/* Header */}
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Button
              component={Link}
              href="/dashboard"
              variant="plain"
              startDecorator={<Icon icon="mdi:arrow-left" />}
            >
              Back
            </Button>
            <Typography level="h2">Body Collection</Typography>
          </Stack>
        </Stack>

        {/* Form */}
        <Suspense fallback={
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        }>
          <BodyCollectionForm />
        </Suspense>
      </Stack>
    </Box>
  );
}