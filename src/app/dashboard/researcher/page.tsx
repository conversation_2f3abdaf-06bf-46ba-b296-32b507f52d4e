'use client';
// Force dynamic rendering
export const dynamic = "force-dynamic";



import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Stack,
  IconButton,
  Input,
  Chip,
  Divider,
  CircularProgress,
  Alert
} from '@mui/joy';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Science as ScienceIcon
} from '@mui/icons-material';

interface ResearchProject {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'archived';
  lastModified: string;
  collaborators: number;
}

export default function ResearcherProjectsPage() {
  const router = useRouter();
  const [projects, setProjects] = useState<ResearchProject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/researcher/projects');
      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }
      const data = await response.json();
      setProjects(data.projects);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load projects');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!confirm('Are you sure you want to delete this project?')) return;
    
    try {
      const response = await fetch(`/api/researcher/projects/${projectId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete project');
      }
      
      setProjects(projects.filter(project => project.id !== projectId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete project');
    }
  };

  const filteredProjects = projects.filter(project =>
    project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusColor = (status: string): 'success' | 'warning' | 'neutral' => {
    switch (status) {
      case 'active':
        return 'success';
      case 'completed':
        return 'neutral';
      case 'archived':
        return 'warning';
      default:
        return 'neutral';
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size="lg" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 4 }}>
        <Alert color="danger" variant="soft" size="lg">
          Error: {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 4 }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 4
        }}
      >
        <Typography level="h2">Research Projects</Typography>
        <Stack direction="row" spacing={2}>
          <Input
            placeholder="Search projects"
            startDecorator={<SearchIcon />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            size="sm"
          />
          <Button
            size="sm"
            startDecorator={<AddIcon />}
            onClick={() => router.push('/dashboard/researcher/new')}
          >
            New Project
          </Button>
        </Stack>
      </Box>

      <Grid container spacing={2}>
        {filteredProjects.map((project) => (
          <Grid key={project.id} xs={12} md={6} lg={4}>
            <Card
              variant="outlined"
              sx={{
                height: '100%',
                transition: 'transform 0.2s, border-color 0.2s',
                '&:hover': {
                  borderColor: 'primary.500',
                  transform: 'translateY(-2px)',
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography level="title-lg">{project.title}</Typography>
                    <Typography level="body-sm" sx={{ color: 'neutral.500', mb: 1 }}>
                      Last modified: {new Date(project.lastModified).toLocaleDateString()}
                    </Typography>
                    <Chip
                      size="sm"
                      variant="soft"
                      color={getStatusColor(project.status)}
                      sx={{ textTransform: 'capitalize' }}
                    >
                      {project.status}
                    </Chip>
                  </Box>
                  <ScienceIcon sx={{ color: 'primary.500', fontSize: 24 }} />
                </Box>
                
                <Typography level="body-sm" sx={{ mb: 2 }}>
                  {project.description}
                </Typography>

                <Divider sx={{ my: 2 }} />
                
                <Stack direction="row" spacing={1} justifyContent="space-between" alignItems="center">
                  <Typography level="body-xs">
                    {project.collaborators} Collaborators
                  </Typography>
                  <Box>
                    <IconButton
                      variant="plain"
                      color="neutral"
                      size="sm"
                      onClick={() => router.push(`/dashboard/researcher/${project.id}`)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      variant="plain"
                      color="danger"
                      size="sm"
                      onClick={() => handleDeleteProject(project.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {filteredProjects.length === 0 && (
        <Card
          variant="outlined"
          sx={{
            mt: 2,
            p: 4,
            textAlign: 'center',
            bgcolor: 'background.level1'
          }}
        >
          <Typography level="h4" sx={{ mb: 1 }}>
            No projects found
          </Typography>
          <Typography sx={{ mb: 2, color: 'neutral.500' }}>
            {searchQuery ? 'No projects match your search criteria.' : 'Get started by creating your first research project!'}
          </Typography>
          {!searchQuery && (
            <Button
              startDecorator={<AddIcon />}
              size="lg"
              onClick={() => router.push('/dashboard/researcher/new')}
            >
              Create New Project
            </Button>
          )}
        </Card>
      )}
    </Box>
  );
}
