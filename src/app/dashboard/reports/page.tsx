"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  FormControl,
  FormLabel,
  Grid,
  Select,
  Option,
  Stack,
  Typography,
  Button,
  Divider,
} from '@mui/joy';
import { Icon } from '@iconify/react';
import dynamicImport from 'next/dynamic';

// Dynamically import ApexCharts
const Chart = dynamicImport(() => import('react-apexcharts'), { ssr: false });

export default function ReportsPage() {
  const [reportParams, setReportParams] = useState({
    reportType: '',
    timeFrame: '',
    facility: '',
  });

  const [reportData, setReportData] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateReport = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsGenerating(true);

    // Simulate API call
    setTimeout(() => {
      setReportData({
        admissions: [45, 52, 38, 24, 33, 26, 21, 20],
        releases: [41, 48, 35, 21, 30, 24, 19, 18],
        pending: [4, 4, 3, 3, 3, 2, 2, 2],
        dates: ['01/12', '02/12', '03/12', '04/12', '05/12', '06/12', '07/12', '08/12'],
      });
      setIsGenerating(false);
    }, 1500);
  };

  const chartOptions = {
    chart: {
      type: 'line',
      toolbar: {
        show: false,
      },
    },
    xaxis: {
      categories: reportData?.dates || [],
    },
    stroke: {
      curve: 'smooth',
    },
  };

  const chartSeries = [
    {
      name: 'Admissions',
      data: reportData?.admissions || [],
    },
    {
      name: 'Releases',
      data: reportData?.releases || [],
    },
    {
      name: 'Pending',
      data: reportData?.pending || [],
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        <Grid xs={12}>
          <Card>
            <CardContent>
              <Typography level="title-lg" mb={2}>
                Generate Reports
              </Typography>
              
              <form onSubmit={handleGenerateReport}>
                <Grid container spacing={2}>
                  <Grid xs={12} md={4}>
                    <FormControl>
                      <FormLabel>Report Type</FormLabel>
                      <Select
                        value={reportParams.reportType}
                        onChange={(_, value) => setReportParams({...reportParams, reportType: value || ''})}
                        required
                      >
                        <Option value="admissions">Admissions Report</Option>
                        <Option value="releases">Releases Report</Option>
                        <Option value="specimens">Specimens Report</Option>
                        <Option value="summary">Summary Report</Option>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid xs={12} md={4}>
                    <FormControl>
                      <FormLabel>Time Frame</FormLabel>
                      <Select
                        value={reportParams.timeFrame}
                        onChange={(_, value) => setReportParams({...reportParams, timeFrame: value || ''})}
                        required
                      >
                        <Option value="daily">Daily</Option>
                        <Option value="weekly">Weekly</Option>
                        <Option value="monthly">Monthly</Option>
                        <Option value="yearly">Yearly</Option>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid xs={12} md={4}>
                    <FormControl>
                      <FormLabel>Facility</FormLabel>
                      <Select
                        value={reportParams.facility}
                        onChange={(_, value) => setReportParams({...reportParams, facility: value || ''})}
                        required
                      >
                        <Option value="all">All Facilities</Option>
                        <Option value="morgueA">Morgue A</Option>
                        <Option value="morgueB">Morgue B</Option>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid xs={12}>
                    <Stack direction="row" spacing={2} justifyContent="flex-end">
                      <Button
                        variant="outlined"
                        color="neutral"
                        startDecorator={<Icon icon="mdi:refresh" />}
                        onClick={() => setReportParams({
                          reportType: '',
                          timeFrame: '',
                          facility: '',
                        })}
                      >
                        Reset
                      </Button>
                      <Button
                        type="submit"
                        loading={isGenerating}
                        startDecorator={<Icon icon="mdi:file-chart" />}
                      >
                        Generate Report
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>

              {reportData && (
                <>
                  <Divider sx={{ my: 3 }} />
                  <Box sx={{ mt: 2 }}>
                    <Typography level="title-md" mb={2}>
                      Report Visualization
                    </Typography>
                    <Chart
                      options={chartOptions}
                      series={chartSeries}
                      type="line"
                      height={350}
                    />
                    <Stack direction="row" spacing={2} justifyContent="center" mt={2}>
                      <Button
                        variant="outlined"
                        color="neutral"
                        startDecorator={<Icon icon="mdi:download" />}
                      >
                        Download PDF
                      </Button>
                      <Button
                        variant="outlined"
                        color="neutral"
                        startDecorator={<Icon icon="mdi:file-excel" />}
                      >
                        Export Excel
                      </Button>
                    </Stack>
                  </Box>
                </>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
} 