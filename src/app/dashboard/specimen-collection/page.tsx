"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  FormLabel,
  Grid,
  Input,
  Stack,
  Typography,
  Select,
  Option,
} from '@mui/joy';
import { Icon } from '@iconify/react';

export default function SpecimenCollectionPage() {
  const [formData, setFormData] = useState({
    bodyTagNumber: '',
    specimenType: '',
    specimenKitNumber: '',
    collectionDate: '',
    collectedBy: '',
    storageLocation: '',
    notes: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log(formData);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Card>
        <CardContent>
          <Typography level="title-lg" mb={2}>
            Specimen Collection Form
          </Typography>
          
          <form onSubmit={handleSubmit}>
            <Grid container spacing={2}>
              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Body Tag Number</FormLabel>
                  <Input
                    value={formData.bodyTagNumber}
                    onChange={(e) => setFormData({...formData, bodyTagNumber: e.target.value})}
                    required
                  />
                </FormControl>
              </Grid>
              
              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Specimen Type</FormLabel>
                  <Select
                    value={formData.specimenType}
                    onChange={(_, value) => setFormData({...formData, specimenType: value || ''})}
                    required
                  >
                    <Option value="blood">Blood</Option>
                    <Option value="tissue">Tissue</Option>
                    <Option value="organ">Organ</Option>
                    <Option value="other">Other</Option>
                  </Select>
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Specimen Kit Number</FormLabel>
                  <Input
                    value={formData.specimenKitNumber}
                    onChange={(e) => setFormData({...formData, specimenKitNumber: e.target.value})}
                    required
                  />
                </FormControl>
              </Grid>

              <Grid xs={12} md={6}>
                <FormControl>
                  <FormLabel>Storage Location</FormLabel>
                  <Input
                    value={formData.storageLocation}
                    onChange={(e) => setFormData({...formData, storageLocation: e.target.value})}
                    required
                  />
                </FormControl>
              </Grid>

              <Grid xs={12}>
                <Stack direction="row" spacing={2} justifyContent="flex-end">
                  <Button
                    variant="outlined"
                    color="neutral"
                    startDecorator={<Icon icon="mdi:refresh" />}
                    onClick={() => setFormData({
                      bodyTagNumber: '',
                      specimenType: '',
                      specimenKitNumber: '',
                      collectionDate: '',
                      collectedBy: '',
                      storageLocation: '',
                      notes: '',
                    })}
                  >
                    Reset
                  </Button>
                  <Button
                    type="submit"
                    startDecorator={<Icon icon="mdi:content-save" />}
                  >
                    Record Specimen
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
} 