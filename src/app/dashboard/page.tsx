"use client";
// Force dynamic rendering
export const dynamic = "force-dynamic";


import { useState, useEffect } from 'react';
import { Box, Typography, IconButton, Tooltip, Button, Stack } from '@mui/joy';
import { MetricsProvider, useMetrics } from '@/hooks/useMetrics';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
  RadialLinearScale
} from 'chart.js';
import { Icon } from '@iconify/react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { ErrorDisplay } from '@/components/ErrorDisplay';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { MetricsHeader } from '@/components/Dashboard/MetricsHeader';
import { useDashboardData } from '@/hooks/useDashboardData';
import { InterchangeableWidget } from '@/components/Dashboard/InterchangeableWidget';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement,
  RadialLinearScale
);

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

function DashboardContent() {
  const { pathologyMetrics, locationMetrics, isLoading, isError, error } = useMetrics();
  const period = 'weekly'; // Fixed period for consistency
  const { metrics: dashboardMetrics, admissionTrends } = useDashboardData(period);

  // Additional data fetching for widgets
  const [facilityCapacityData, setFacilityCapacityData] = useState<any>(null);
  const [dailyActivityData, setDailyActivityData] = useState<any>(null);
  const [timeDistributionData, setTimeDistributionData] = useState<any>(null);
  const [recentActivitiesData, setRecentActivitiesData] = useState<any>(null);
  const [staffActivityData, setStaffActivityData] = useState<any>(null);

  useEffect(() => {
    // Fetch facility capacity data
    fetch('/api/dashboard/facility-capacity')
      .then(res => res.ok ? res.json() : null)
      .then(data => data && setFacilityCapacityData(data))
      .catch(err => console.error('Error fetching facility capacity:', err));

    // Fetch daily activity data
    fetch('/api/dashboard/daily-activity')
      .then(res => res.ok ? res.json() : null)
      .then(data => data && setDailyActivityData(data))
      .catch(err => console.error('Error fetching daily activity:', err));

    // Fetch time distribution data
    fetch('/api/dashboard/time-distribution')
      .then(res => res.ok ? res.json() : null)
      .then(data => data && setTimeDistributionData(data))
      .catch(err => console.error('Error fetching time distribution:', err));

    // Fetch recent activities data
    fetch('/api/dashboard/recent-activities')
      .then(res => res.ok ? res.json() : null)
      .then(data => data && setRecentActivitiesData(data))
      .catch(err => console.error('Error fetching recent activities:', err));

    // Fetch staff activity data
    fetch('/api/dashboard/staff-activity')
      .then(res => res.ok ? res.json() : null)
      .then(data => data && setStaffActivityData(data))
      .catch(err => console.error('Error fetching staff activity:', err));
  }, []);

  if (isLoading) {
    return <LoadingSpinner message="Loading dashboard metrics..." />;
  }

  if (isError) {
    return <ErrorDisplay error={error} />;
  }

  // Removed unused chart data variables as they're now directly used in the widgets

  return (
    <motion.div
      initial="hidden"
      animate="show"
      variants={container}
    >
      <Box sx={{ p: 4 }}>
        {/* Header Section */}
        <motion.div variants={item}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 4
            }}
          >
            <Box>
              <Typography
                level="h2"
              >
                Forensic Pathology Dashboard
              </Typography>
              <Typography
                level="body-sm"
                sx={{
                  display: {
                    xs: 'none',
                    md: 'flex'
                  }
                }}
              >
                Welcome back! Here's what's happening in your facility.
                Last updated: {format(new Date(), 'PPpp')}
              </Typography>
            </Box>
            <Stack direction="row" spacing={1}>
              <Tooltip title="Refresh Data">
                <IconButton variant="soft" color="neutral">
                  <Icon icon="mdi:refresh" />
                </IconButton>
              </Tooltip>
              <Box sx={{ display: { xs: 'none', sm: 'flex' }, gap: 1 }}>
                <Tooltip title="Export Data">
                  <IconButton variant="soft" color="neutral">
                    <Icon icon="mdi:download" />
                  </IconButton>
                </Tooltip>
                <Button
                  variant="solid"
                  color="primary"
                  startDecorator={<Icon icon="mdi:plus" />}
                >
                  New Admission
                </Button>
              </Box>
            </Stack>
          </Box>
        </motion.div>

        {/* Metrics Header */}
        <MetricsHeader metrics={dashboardMetrics} />

        {/* Bento Grid Layout */}
        <Box
          sx={{
            display: 'grid',
            gap: 2,
            gridTemplateColumns: 'repeat(12, 1fr)',
            my: 3
          }}
        >
          {/* Main Visualization Section - Spans 8 columns */}
          <InterchangeableWidget
            widgets={{
              admissionTrend: {
                data: {
                  currentPeriod: pathologyMetrics?.monthlyAdmissions || [],
                  previousPeriod: admissionTrends?.datasets?.[1]?.data || [120, 115, 130, 125, 140, 135],
                  labels: admissionTrends?.labels || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
                },
                dateRange: "Last 6 months"
              },
              caseStatus: {
                data: {
                  labels: ['Collected', 'In Storage', 'Awaiting Release', 'Referred'],
                  values: [
                    pathologyMetrics?.totalCollected || 0,
                    pathologyMetrics?.totalInStorage || 0,
                    pathologyMetrics?.pendingReleases || 0,
                    pathologyMetrics?.activeReferrals || 0
                  ],
                  colors: [
                    'var(--joy-palette-warning-500)',
                    'var(--joy-palette-primary-500)',
                    'var(--joy-palette-neutral-500)',
                    'var(--joy-palette-success-500)'
                  ]
                }
              },
              timeDistribution: {
                data: {
                  labels: timeDistributionData?.labels || ['Morning', 'Afternoon', 'Evening', 'Night'],
                  values: timeDistributionData?.values || [35, 40, 15, 10],
                  colors: [
                    'var(--joy-palette-warning-300)',
                    'var(--joy-palette-primary-300)',
                    'var(--joy-palette-neutral-300)',
                    'var(--joy-palette-success-300)'
                  ]
                },
                totalTime: timeDistributionData?.totalTime || 100
              }
            }}
            gridSpan={{ xs: 12, md: 8 }}
            autoRotate={true}
            interval={15000}
            defaultWidget="admissionTrend"
          />

          {/* Right Side Widgets - Spans 4 columns */}
          <InterchangeableWidget
            widgets={{
              facilityCapacity: {
                data: {
                  currentOccupancy: facilityCapacityData?.currentOccupancy ||
                    locationMetrics?.facilities.reduce((acc, f) => acc + f.currentOccupancy, 0) || 0,
                  totalCapacity: facilityCapacityData?.totalCapacity ||
                    locationMetrics?.facilities.reduce((acc, f) => acc + f.totalCapacity, 0) || 0,
                  occupancyByType: facilityCapacityData?.occupancyByType || [
                    { type: 'Regular Storage', count: 45, color: 'var(--joy-palette-primary-500)' },
                    { type: 'Cold Storage', count: 30, color: 'var(--joy-palette-success-500)' },
                    { type: 'Temporary', count: 15, color: 'var(--joy-palette-warning-500)' }
                  ]
                }
              },
              staffActivity: {
                data: {
                  staff: staffActivityData?.staff || [
                    {
                      name: 'Dr. Smith',
                      role: 'Pathologist',
                      currentCases: 5,
                      completedToday: 3
                    },
                    {
                      name: 'Dr. Johnson',
                      role: 'Lab Technician',
                      currentCases: 8,
                      completedToday: 4
                    },
                    {
                      name: 'Dr. Williams',
                      role: 'Forensic Expert',
                      currentCases: 3,
                      completedToday: 2
                    }
                  ]
                }
              }
            }}
            gridSpan={{ xs: 12, md: 4 }}
            autoRotate={true}
            interval={20000}
            defaultWidget="facilityCapacity"
          />

          {/* Bottom Left Widget - Spans 6 columns */}
          <InterchangeableWidget
            widgets={{
              dailyActivity: {
                data: {
                  current: dailyActivityData?.current || [12, 8, 15, 10, 9, 11, 13],
                  previous: dailyActivityData?.previous || [10, 7, 14, 9, 8, 10, 12],
                  labels: dailyActivityData?.labels || ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                dateRange: dailyActivityData?.dateRange || "This week"
              },
              timeDistribution: {
                data: {
                  labels: timeDistributionData?.labels || ['Collections', 'Examinations', 'Lab Analysis', 'Reports'],
                  values: timeDistributionData?.values || [30, 25, 35, 10],
                  colors: [
                    'var(--joy-palette-primary-400)',
                    'var(--joy-palette-success-400)',
                    'var(--joy-palette-warning-400)',
                    'var(--joy-palette-neutral-400)'
                  ]
                },
                totalTime: timeDistributionData?.totalTime || 100
              }
            }}
            gridSpan={{ xs: 12, md: 6 }}
            autoRotate={true}
            interval={18000}
          />

          {/* Bottom Right Widget - Spans 6 columns */}
          <InterchangeableWidget
            widgets={{
              recentActivities: {
                activities: recentActivitiesData?.activities || [
                  {
                    id: '1',
                    type: 'ADMISSION',
                    description: 'New body admission processed',
                    date: new Date(),
                    user: {
                      name: 'Dr. Smith',
                      role: 'Pathologist'
                    },
                    status: 'Completed'
                  },
                  {
                    id: '2',
                    type: 'REFERRAL',
                    description: 'Specimen sent for analysis',
                    date: new Date(Date.now() - 3600000),
                    user: {
                      name: 'Dr. Johnson',
                      role: 'Lab Technician'
                    },
                    status: 'In Progress'
                  }
                ],
                dateRange: recentActivitiesData?.dateRange || "Today"
              },
              staffActivity: {
                data: {
                  staff: staffActivityData?.staff || [
                    {
                      name: 'Dr. Brown',
                      role: 'Pathologist',
                      currentCases: 4,
                      completedToday: 2
                    },
                    {
                      name: 'Dr. Davis',
                      role: 'Lab Technician',
                      currentCases: 6,
                      completedToday: 3
                    }
                  ]
                }
              }
            }}
            gridSpan={{ xs: 12, md: 6 }}
            defaultWidget="recentActivities"
            autoRotate={true}
            interval={20000}
          />

        </Box>
      </Box>
    </motion.div>
  );
}

export default function DashboardPage() {
  return (
    <MetricsProvider refreshInterval={30000}>
      <DashboardContent />
    </MetricsProvider>
  );
}