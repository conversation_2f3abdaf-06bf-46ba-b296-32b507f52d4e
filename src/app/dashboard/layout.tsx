"use client";
// Force dynamic rendering for all dashboard pages
export const dynamic = "force-dynamic";

/*DashboardLayout
- This component is a layout component that wraps the dashboard pages.
- It's a "blank" layout that doesn't have any specific styles or components because:
  * It will contain the dashboard pages that will have their own styles and components.
  * These Pages have their own Nested Layouts
- It will be used to wrap the dashboard pages.
*/

// Import dynamic configuration for all dashboard pages

import * as React from "react";
import { Box, Sheet, Drawer } from "@mui/joy";
import Header from "@/components/Dashboard/Header";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import { CssVarsProvider } from "@mui/joy/styles";
import CssBaseline from "@mui/joy/CssBaseline";
import Layout from "@/components/MainLayout";
import { usePathname } from "next/navigation"

interface HeaderProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function DashboardLayout(props: { children: React.ReactNode }) {
  //REFS

  //CONSTANTS
  const pathname = usePathname();

  //STATES
  const [addTableOpen, setAddTableOpen] = React.useState(false);
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [editTableOpen, setEditTableOpen] = React.useState(false);
  const [newTableOpen, setNewTableOpen] = React.useState(false);
  const [quickActionsOpen, setQuickActionsOpen] = React.useState(false);

  //AI
  //-- allows for automated assisted navigation
  //-- form autocomplete and suggestions
  //useAIForm()

  //RENDERS
  return (
      <LocalizationProvider dateAdapter={AdapterMoment}>
        <CssVarsProvider disableTransitionOnChange>
          <CssBaseline />
            <Layout.Root
              sx={{
                display: "flex",
                flexDirection: "row",
                gap: 0,
                overflow: "hidden",
              }}
            >
              {/* Sidebar: Only visible on desktop */}
              <Box sx={{ display: { xs: "none", sm: "block" } }}>
                <Layout.SideBarNav
                  sx={{
                    height: "100%",
                    overflowY: "auto",
                  }}
                  navItems={[
                    {
                      label: "Overview",
                      icon: "mdi:chart-bar",
                      link: `/dashboard`,
                      isActive: pathname === "/dashboard",
                    },
                    {
                      label: "Body Management",
                      icon: "mdi:account-circle",
                      subItems: [
                        {
                          label: "Directory",
                          link: `/dashboard/records/bodies`,
                        },
                        {
                          label: "Collections",
                          link: `/dashboard/records/collections`,
                        },
                        {
                          label: "Admissions",
                          link: `/dashboard/records/admissions`,
                        },
                        {
                          label: "Releases",
                          link: `/dashboard/records/releases`,
                        },
                      ],
                    },
                    {
                      label: "Referrals",
                      icon: "mdi:transit-connection-variant",
                      link: `/dashboard/records/referrals`,
                      isActive: pathname === "/dashboard/records/referrals",
                    },
                    {
                      label: "Knowledge Base",
                      icon: "mdi:book-open",
                      link: `/dashboard/knowledge-base`,
                      subItems: [
                        {
                          label: "Articles",
                          link: `/dashboard/knowledge-base`,
                        },
                        {
                          label: "API Documentation",
                          link: `/dashboard/knowledge-base/api-documentation`,
                        }
                      ],
                    },
                  ]}
                />
              </Box>

              <Box
                sx={{
                  display: "flex",
                  flex: 1,
                  height: "100vh",
                  flexDirection: "column",
                  overflow: "hidden",
                }}
              >
                <Header
                  title="GPathology"
                  navItems={[
                    {
                      title: "Quick Actions",
                      icon: "mdi:speedometer",
                      isCurrent: false,
                      isDisabled: false,
                      subItems: [
                        {
                          title: "Collect New Body",
                          icon: "mdi:hospital-box",
                          link: {
                            route: "/dashboard/records/collections/new",
                          },
                        },
                        {
                          title: "Admit Collected Body",
                          icon: "mdi:clipboard-check",
                          link: {
                            route: "/dashboard/records/admissions/new",
                          },
                        },
                        {
                          title: "Create New Referral",
                          icon: "mdi:file-document-edit",
                          link: {
                            route: "/dashboard/records/referrals/new",
                          },
                        },
                        {
                          title: "Release to Undertaker",
                          icon: "mdi:clipboard-check",
                          link: {
                            route: "/dashboard/records/releases/new",
                          },
                        }
                      ],
                    },
                    {
                      title: "Tools",
                      icon: "mdi:tools",
                      isCurrent: false,
                      isDisabled: false,
                      subItems: [
                        {
                          title: "Body Tag Generator",
                          icon: "mdi:barcode",
                          link: {
                            route: "/dashboard/tools/barcode-generator",
                          },
                        },
                      ],
                    },
                  ]}
                /* profile={{
                  show: true,
                  name: "Thando Zondo",
                  email: "<EMAIL>",
                  avatar: "/svg/avataaars.png",
                  link: {
                    route: "/dashboard/settings/account/",
                  },
                  menuItems: [
                    {
                      title: "Account",
                      icon: "fluent:person-28-filled",
                      link: {
                        route: "/dashboard/settings/account/",
                      },
                      isCurrent: false,
                      isDisabled: false,
                      subItems: [],
                      useAs: "link",
                    },
                    {
                      title: "Admin",
                      icon: "fluent:lock-closed-28-filled",
                      link: {
                        route: "/admin/",
                      },
                      isCurrent: false,
                      isDisabled: false,
                      subItems: [],
                      useAs: "link",
                    },
                    {
                      title: "Settings",
                      icon: "fluent:settings-28-filled",
                      link: {
                        route: "/dashboard/settings/general",
                      },
                      isCurrent: false,
                      isDisabled: false,
                      subItems: [],
                      useAs: "link",
                    },
                  ],
                }} */
                />

                <Box
                  sx={{
                    display: "block",
                    flex: 1,
                    height: "calc(100vh - 64px)",
                    overflow: "auto",
                  }}
                >
                  {props.children}
                </Box>
              </Box>
            </Layout.Root>

            <Drawer
              open={addTableOpen}
              onClose={() => setAddTableOpen(false)}
              sx={{
                "--Drawer-horizontalSize": "clamp(500px, 40%, 100%)",
                ".MuiDrawer-content": {
                  "&::-webkit-scrollbar": {
                    width: "6px",
                    height: "6px",
                  },
                  "&::-webkit-scrollbar-thumb": {
                    backgroundColor: "rgba(0, 0, 0, 0.2)",
                  },
                  "&::-webkit-scrollbar-thumb:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.3)",
                  },
                  "&::-webkit-scrollbar-track": {
                    backgroundColor: "rgba(0, 0, 0, 0.1)",
                  },
                },
              }}
            >
              <Sheet
                sx={{
                  mt: "48px",
                  height: "100vh",
                }}
              ></Sheet>
            </Drawer>

            <Drawer
              open={editTableOpen}
              onClose={() => {
                setEditTableOpen(false);
              }}
              sx={{
                "--Drawer-horizontalSize": "clamp(500px, 40%, 100%)",
                ".MuiDrawer-content": {
                  "&::-webkit-scrollbar": {
                    width: "6px",
                    height: "6px",
                  },
                  "&::-webkit-scrollbar-thumb": {
                    backgroundColor: "rgba(0, 0, 0, 0.2)",
                  },
                  "&::-webkit-scrollbar-thumb:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.3)",
                  },
                  "&::-webkit-scrollbar-track": {
                    backgroundColor: "rgba(0, 0, 0, 0.1)",
                  },
                },
              }}
            >
              <Sheet
                sx={{
                  mt: "48px",
                  height: "100vh",
                }}
              ></Sheet>
            </Drawer>
        </CssVarsProvider>
      </LocalizationProvider>
  );
}
