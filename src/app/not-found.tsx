'use client';

import { Box, Typography, But<PERSON> } from '@mui/joy';
import { useRouter } from 'next/navigation';

export default function NotFound() {
  const router = useRouter();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        textAlign: 'center',
        p: 2,
      }}
    >
      <Typography level="h1" sx={{ mb: 2 }}>404</Typography>
      <Typography level="h2" sx={{ mb: 4 }}>Page Not Found</Typography>
      <Typography sx={{ mb: 4 }}>
        The page you are looking for does not exist or has been moved.
      </Typography>
      <Button
        onClick={() => router.push('/dashboard')}
        variant="solid"
        color="primary"
      >
        Go to Dashboard
      </Button>
    </Box>
  );
}