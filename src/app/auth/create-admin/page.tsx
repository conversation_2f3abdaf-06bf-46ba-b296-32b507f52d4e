"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Typography,
  Alert,
  Sheet,
  CircularProgress,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import AuthLayout from "@/components/Auth/layout/AuthLayout";

export default function CreateAdmin() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.name || !formData.email || !formData.password || !formData.confirmPassword) {
      setError("All fields are required");
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      return false;
    }
    if (formData.password.length < 8) {
      setError("Password must be at least 8 characters long");
      return false;
    }
    if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      setError("Invalid email address");
      return false;
    }
    return true;
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      const res = await fetch("/api/auth/create-admin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
        }),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.error || "Failed to create admin account");
      }

      setSuccess(true);
      setTimeout(() => {
        router.push("/auth/login");
      }, 2000);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <AuthLayout>
        <Sheet
          sx={{
            width: 400,
            mx: "auto",
            py: 3,
            px: 2,
            display: "flex",
            flexDirection: "column",
            gap: 2,
            alignItems: "center",
            borderRadius: "sm",
            boxShadow: "md",
          }}
          variant="outlined"
        >
          <Icon icon="mdi:check-circle" color="green" width={48} height={48} />
          <Typography level="h4" component="h1">
            Admin Account Created!
          </Typography>
          <Typography level="body-sm" textAlign="center">
            Your admin account has been created successfully. Redirecting to login...
          </Typography>
          <CircularProgress size="sm" />
        </Sheet>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout>
      <Sheet
        sx={{
          width: 400,
          mx: "auto",
          py: 3,
          px: 2,
          display: "flex",
          flexDirection: "column",
          gap: 2,
          borderRadius: "sm",
          boxShadow: "md",
        }}
        variant="outlined"
      >
        <div>
          <Typography level="h4" component="h1">
            <b>Create Admin Account</b>
          </Typography>
          <Typography level="body-sm">
            Set up the administrator account for GP Pathology
          </Typography>
        </div>

        <form onSubmit={handleSubmit}>
          <Stack gap={3}>
            {error && (
              <Alert color="danger" variant="soft">
                {error}
              </Alert>
            )}

            <FormControl required>
              <FormLabel>Full Name</FormLabel>
              <Input
                name="name"
                type="text"
                value={formData.name}
                onChange={handleChange}
                placeholder="Admin Name"
                startDecorator={<Icon icon="mdi:account" />}
              />
            </FormControl>

            <FormControl required>
              <FormLabel>Email</FormLabel>
              <Input
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                startDecorator={<Icon icon="mdi:email" />}
              />
            </FormControl>

            <FormControl required>
              <FormLabel>Password</FormLabel>
              <Input
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="••••••••"
                startDecorator={<Icon icon="mdi:lock" />}
              />
            </FormControl>

            <FormControl required>
              <FormLabel>Confirm Password</FormLabel>
              <Input
                name="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="••••••••"
                startDecorator={<Icon icon="mdi:lock" />}
              />
            </FormControl>

            <Button
              type="submit"
              loading={loading}
              loadingPosition="start"
              fullWidth
              startDecorator={<Icon icon="mdi:shield-account" />}
            >
              Create Admin Account
            </Button>
          </Stack>
        </form>
      </Sheet>
    </AuthLayout>
  );
}
