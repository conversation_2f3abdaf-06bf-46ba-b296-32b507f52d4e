'use client';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON>, <PERSON>Item, Link, Divider, <PERSON>rid, <PERSON>ack, Card } from '@mui/joy';
import { useSearchParams } from 'next/navigation';
import { Info, AlertCircle, Lock, RefreshCw, Mail, Phone, ArrowRight } from 'lucide-react';
import NextLink from 'next/link';

// Add error boundary export for Next.js error handling
export function ErrorBoundary({ error }: { error: Error }) {
  return (
    <div>
      <h2>Application Error</h2>
      <p>{error.message}</p>
    </div>
  );
}

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');
  
  // Add fallback for undefined error state
  if (!error) {
    return (
      <Alert color="danger">
        <Typography>No error code provided</Typography>
      </Alert>
    );
  }
  
  // Error code mappings
  const errorMessages: { [key: string]: { title: string; description: string } } = {
    Configuration: {
      title: 'Server Configuration Error',
      description: "There's a problem with the server configuration. Please contact site administrator."
    },
    AccessDenied: {
      title: 'Access Denied',
      description: "You don't have permission to access this resource."
    },
    Verification: {
      title: 'Verification Failed',
      description: 'The security token has expired or is invalid. Please try again.'
    },
    OAuthSignin: {
      title: 'OAuth Error',
      description: 'Error processing OAuth authorization. Try signing in with a different method.'
    },
    Default: {
      title: 'Authentication Error',
      description: 'An unexpected error occurred during authentication. Please try again.'
    }
  };

  const currentError = errorMessages[error || 'Default'];

  return (
    <Grid container sx={{ minHeight: '100vh', p: 3 }}>
      <Grid xs={12} md={6} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Stack spacing={3} sx={{ maxWidth: 600, width: '100%' }}>
          <Alert
            color="danger"
            variant="soft"
            startDecorator={<AlertCircle size={24} />}
            sx={{ alignItems: 'flex-start' }}
          >
            <Stack spacing={1}>
              <Typography level="title-lg">{currentError.title}</Typography>
              <Typography level="body-sm">{currentError.description}</Typography>
            </Stack>
          </Alert>

          <Card variant="outlined">
            <Stack spacing={2}>
              <Typography level="title-md" startDecorator={<Info size={16} />}>
                Troubleshooting Steps
              </Typography>
              
              <List marker="disc">
                <ListItem>
                  <Typography level="body-sm">
                    Ensure you're using valid credentials and have proper permissions
                  </Typography>
                </ListItem>
                <ListItem>
                  <Typography level="body-sm">
                    Check your internet connection and try refreshing the page
                  </Typography>
                </ListItem>
                <ListItem>
                  <Typography level="body-sm">
                    Clear browser cookies and cache for this site
                  </Typography>
                </ListItem>
                <ListItem>
                  <Typography level="body-sm">
                    Try using a different browser or device
                  </Typography>
                </ListItem>
              </List>
            </Stack>
          </Card>

          <Divider>or</Divider>

          <Stack spacing={2}>
            <Typography level="title-md" startDecorator={<Lock size={16} />}>
              Immediate Actions
            </Typography>
            
            <Grid container spacing={2}>
              <Grid xs={12} md={6}>
                <Button
                  fullWidth
                  variant="outlined"
                  color="neutral"
                  component={NextLink}
                  href="/auth/login"
                  startDecorator={<RefreshCw size={16} />}
                >
                  Try Again
                </Button>
              </Grid>
              <Grid xs={12} md={6}>
                <Button
                  fullWidth
                  variant="outlined"
                  color="neutral"
                  component={NextLink}
                  href="/auth/forgot-password"
                  startDecorator={<Lock size={16} />}
                >
                  Reset Password
                </Button>
              </Grid>
            </Grid>
          </Stack>
        </Stack>
      </Grid>

      <Grid xs={12} md={6} sx={{ bgcolor: 'background.level1', p: 4, display: 'flex', alignItems: 'center' }}>
        <Stack spacing={4} sx={{ maxWidth: 500, width: '100%' }}>
          <Typography level="h3" sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            Need Further Assistance?
            <Info size={24} />
          </Typography>

          <List variant="outlined" sx={{ borderRadius: 'sm' }}>
            <ListItem>
              <Link
                component={NextLink}
                href="/support"
                color="neutral"
                sx={{ width: '100%', textDecoration: 'none' }}
              >
                <Stack direction="row" spacing={1.5} alignItems="center">
                  <Mail size={18} />
                  <Typography>Email Support: <EMAIL></Typography>
                  <ArrowRight size={16} style={{ marginLeft: 'auto' }} />
                </Stack>
              </Link>
            </ListItem>
            <ListItem>
              <Link
                href="tel:+1234567890"
                color="neutral"
                sx={{ width: '100%', textDecoration: 'none' }}
              >
                <Stack direction="row" spacing={1.5} alignItems="center">
                  <Phone size={18} />
                  <Typography>Emergency Hotline: +1 (234) 567-890</Typography>
                  <ArrowRight size={16} style={{ marginLeft: 'auto' }} />
                </Stack>
              </Link>
            </ListItem>
          </List>

          <Card variant="soft" color="neutral">
            <Typography level="body-xs" sx={{ mb: 1 }}>
              System Status
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography level="title-sm">Authentication Services</Typography>
              <AlertCircle size={16} color="var(--joy-palette-danger-500)" />
              <Typography level="body-sm" color="danger">
                Partial Outage
              </Typography>
            </Stack>
          </Card>

          <Typography level="body-xs" color="neutral">
            Last updated: {new Date().toLocaleDateString()} -{' '}
            <Link component={NextLink} href="/status" color="neutral">
              View full status page
            </Link>
          </Typography>
        </Stack>
      </Grid>
    </Grid>
  );
}
