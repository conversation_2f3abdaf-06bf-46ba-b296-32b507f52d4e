"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Typography,
  <PERSON><PERSON>,
  Sheet,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import AuthLayout from "@/components/Auth/layout/AuthLayout";

export default function ResetPassword({
  params,
}: {
  params: { token: string };
}) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setLoading(true);
    setError(null);

    const formData = new FormData(event.currentTarget);
    const password = formData.get("password") as string;
    const confirmPassword = formData.get("confirmPassword") as string;

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setLoading(false);
      return;
    }

    try {
      const res = await fetch("/api/auth/reset-password", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token: params.token,
          password,
        }),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.error || "Failed to reset password");
      }

      // Redirect to sign in page
      router.push("/auth/login?message=Password reset successful");
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout title="Reset Password">
      <Sheet
        sx={{
          width: "100%",
          maxWidth: 400,
          mx: "auto",
          my: 4,
          py: 3,
          px: 2,
          display: "flex",
          flexDirection: "column",
          gap: 2,
          borderRadius: "sm",
          boxShadow: "md",
        }}
        variant="outlined"
      >
        <div>
          <Typography level="h4" component="h1" sx={{ mb: 2 }}>
            Reset Password
          </Typography>
          <Typography level="body-sm" sx={{ mb: 3 }}>
            Enter your new password below.
          </Typography>
        </div>

        {error && (
          <Alert
            sx={{ mb: 2 }}
            variant="soft"
            color="danger"
            startDecorator={<Icon icon="solar:danger-circle-bold" />}
          >
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Stack spacing={2}>
            <FormControl required>
              <FormLabel>New Password</FormLabel>
              <Input
                type="password"
                name="password"
                placeholder="••••••••"
                startDecorator={<Icon icon="solar:lock-outline" />}
              />
            </FormControl>

            <FormControl required>
              <FormLabel>Confirm Password</FormLabel>
              <Input
                type="password"
                name="confirmPassword"
                placeholder="••••••••"
                startDecorator={<Icon icon="solar:lock-outline" />}
              />
            </FormControl>

            <Button
              type="submit"
              loading={loading}
              loadingPosition="start"
              variant="solid"
              color="primary"
              fullWidth
            >
              Reset Password
            </Button>
          </Stack>
        </form>
      </Sheet>
    </AuthLayout>
  );
}
