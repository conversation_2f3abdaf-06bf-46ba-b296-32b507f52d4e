"use client"

import * as React from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@mui/joy"
import { Input } from "@mui/joy"
import { FormControl, FormLabel, FormHelperText } from "@mui/joy"

const formSchema = z.object({
  password: z.string().min(8, {
    message: "Password must be at least 8 characters.",
  }),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type FormData = z.infer<typeof formSchema>

export function ResetPasswordForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get("token")
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [success, setSuccess] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
  })

  async function onSubmit(data: FormData) {
    if (!token) {
      setError("Missing reset token")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          password: data.password,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to reset password")
      }

      setSuccess(true)
      setTimeout(() => {
        router.push("/auth/login?reset=true")
      }, 2000)
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message)
      } else {
        setError("An error occurred while resetting your password")
      }
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="text-center">
        <h3 className="text-lg font-medium">Password Reset Successful</h3>
        <p className="mt-2 text-sm text-gray-600">
          Your password has been reset. Redirecting you to login...
        </p>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <FormControl error={!!errors.password}>
        <FormLabel>New Password</FormLabel>
        <Input
          type="password"
          placeholder="Enter your new password"
          {...register("password")}
        />
        {errors.password && (
          <FormHelperText>{errors.password.message}</FormHelperText>
        )}
      </FormControl>

      <FormControl error={!!errors.confirmPassword}>
        <FormLabel>Confirm New Password</FormLabel>
        <Input
          type="password"
          placeholder="Confirm your new password"
          {...register("confirmPassword")}
        />
        {errors.confirmPassword && (
          <FormHelperText>{errors.confirmPassword.message}</FormHelperText>
        )}
      </FormControl>

      {error && (
        <FormHelperText sx={{ color: "danger.main" }}>
          {error}
        </FormHelperText>
      )}

      <Button
        type="submit"
        loading={isLoading}
        fullWidth
        disabled={isLoading}
      >
        Reset Password
      </Button>
    </form>
  )
} 