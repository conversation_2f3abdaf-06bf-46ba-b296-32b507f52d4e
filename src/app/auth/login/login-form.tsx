"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { signIn } from "next-auth/react"
import { <PERSON><PERSON>, <PERSON>, <PERSON>po<PERSON>, Divider, Link, Checkbox } from "@mui/joy"
import { Input, IconButton } from "@mui/joy"
import { FormControl, FormLabel, FormHelperText } from "@mui/joy"
import { Visibility, VisibilityOff } from "@mui/icons-material"
import { MfaVerification } from "@/components/mfa/MfaVerification"

const formSchema = z.object({
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters.",
  }),
  rememberMe: z.boolean().optional(),
})

type FormData = z.infer<typeof formSchema>

export function LoginForm() {
  const router = useRouter()
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [showPassword, setShowPassword] = React.useState(false)
  const [requiresMfa, setRequiresMfa] = React.useState(false)
  const [loginCredentials, setLoginCredentials] = React.useState<{email: string, password: string} | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      rememberMe: false,
    },
  })

  async function onSubmit(data: FormData) {
    setIsLoading(true)
    setError(null)

    try {
      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      })

      if (result?.error) {
        // Check if the error is due to MFA being required
        if (result.error === "CredentialsSignin" && result.url?.includes("requiresMfaVerification=true")) {
          // Store credentials for MFA verification step
          setLoginCredentials({
            email: data.email,
            password: data.password,
          })
          setRequiresMfa(true)
          return
        }
        
        setError("Invalid email or password")
        return
      }

      if (data.rememberMe) {
        // Implement remember me functionality
      }

      router.push("/dashboard")
      router.refresh()
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const togglePasswordVisibility = () => setShowPassword(!showPassword)
  
  const handleMfaCancel = () => {
    setRequiresMfa(false)
    setLoginCredentials(null)
  }

  if (requiresMfa && loginCredentials) {
    return (
      <MfaVerification 
        email={loginCredentials.email}
        password={loginCredentials.password}
        onCancel={handleMfaCancel}
      />
    )
  }

  return (
    <Card
      variant="outlined"
      sx={{
        maxWidth: 400,
        mx: "auto",
        p: 4,
        boxShadow: "md"
      }}
    >
      <Typography level="h4" component="h1" sx={{ mb: 2, textAlign: "center" }}>
        Welcome Back
      </Typography>
      
      <Typography level="body-sm" sx={{ mb: 3, textAlign: "center", color: "neutral.500" }}>
        Please sign in to continue
      </Typography>

      <Divider sx={{ my: 2 }} />

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <FormControl error={!!errors.email} sx={{ mb: 2 }}>
          <FormLabel>Email</FormLabel>
          <Input
            type="email"
            placeholder="<EMAIL>"
            {...register("email")}
            size="lg"
            sx={{ borderRadius: "md" }}
          />
          {errors.email && (
            <FormHelperText>{errors.email.message}</FormHelperText>
          )}
        </FormControl>
        
        <FormControl error={!!errors.password} sx={{ mb: 2 }}>
          <FormLabel>Password</FormLabel>
          <Input
            type={showPassword ? "text" : "password"}
            placeholder="Enter your password"
            {...register("password")}
            size="lg"
            sx={{ borderRadius: "md" }}
            endDecorator={
              <IconButton onClick={togglePasswordVisibility}>
                {showPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            }
          />
          {errors.password && (
            <FormHelperText>{errors.password.message}</FormHelperText>
          )}
        </FormControl>

        <FormControl sx={{ flexDirection: "row", alignItems: "center", gap: 1 }}>
          <Checkbox {...register("rememberMe")} />
          <FormLabel>Remember me</FormLabel>
        </FormControl>

        {error && (
          <FormHelperText sx={{ 
            color: "danger.500",
            textAlign: "center",
            mb: 2,
            fontSize: "sm"
          }}>
            {error}
          </FormHelperText>
        )}

        <Button
          type="submit"
          loading={isLoading}
          fullWidth
          disabled={isLoading}
          size="lg"
          sx={{
            mt: 2,
            borderRadius: "md",
            bgcolor: "primary.500",
            ":hover": {
              bgcolor: "primary.600"
            }
          }}
        >
          Sign In
        </Button>
      </form>

      <Typography level="body-sm" sx={{ mt: 2, textAlign: "center" }}>
        <Link href="/auth/forgot-password">Forgot password?</Link>
      </Typography>

      <Typography level="body-sm" sx={{ mt: 2, textAlign: "center" }}>
        Don't have an account? <Link href="/auth/register">Sign up</Link>
      </Typography>
    </Card>
  )
}