"use client";

import React, { useState } from "react";
import {
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  She<PERSON>,
  <PERSON>,
} from "@mui/joy";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import { logger } from "@/utils/logger";

export default function ForgotPassword() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    const formData = new FormData(event.currentTarget);
    const email = formData.get("email") as string;

    try {
      const res = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.error || "Failed to send reset email");
      }

      logger.info("Password reset email sent successfully");
      setSuccess(true);
    } catch (error) {
      logger.error("Password reset error", { error });
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Sheet
        sx={{
          width: "100%",
          maxWidth: 400,
          mx: "auto",
          my: 4,
          py: 3,
          px: 2,
          display: "flex",
          flexDirection: "column",
          gap: 2,
          borderRadius: "sm",
          boxShadow: "md",
        }}
        variant="outlined"
      >
        <div>
          <Typography level="h4" component="h1" sx={{ mb: 2 }}>
            Forgot Password
          </Typography>
          <Typography level="body-sm" sx={{ mb: 3 }}>
            Enter your email address and we'll send you a link to reset your
            password.
          </Typography>
        </div>

        {error && (
          <Alert
            sx={{ mb: 2 }}
            variant="soft"
            color="danger"
            startDecorator={<Icon icon="solar:danger-circle-bold" />}
          >
            {error}
          </Alert>
        )}

        {success ? (
          <>
            <Alert
              sx={{ mb: 2 }}
              variant="soft"
              color="success"
              startDecorator={<Icon icon="solar:check-circle-bold" />}
            >
              If an account exists with this email, you will receive password
              reset instructions shortly.
            </Alert>
            <Button
              component={Link}
              href="/auth/login"
              variant="outlined"
              color="neutral"
              fullWidth
              startDecorator={<Icon icon="solar:login-2-outline" />}
            >
              Return to Sign In
            </Button>
          </>
        ) : (
          <form onSubmit={handleSubmit}>
            <Stack spacing={2}>
              <FormControl required>
                <FormLabel>Email</FormLabel>
                <Input
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  startDecorator={<Icon icon="solar:letter-outline" />}
                  disabled={loading}
                  required
                />
              </FormControl>

              <Button
                type="submit"
                loading={loading}
                loadingPosition="start"
                variant="solid"
                color="primary"
                fullWidth
                disabled={loading}
              >
                {loading ? "Sending..." : "Send Reset Link"}
              </Button>

              <Button
                component={Link}
                href="/auth/login"
                variant="plain"
                color="neutral"
                fullWidth
                startDecorator={<Icon icon="solar:login-2-outline" />}
              >
                Back to Sign In
              </Button>
            </Stack>
          </form>
        )}
      </Sheet>
    </motion.div>
  );
}
