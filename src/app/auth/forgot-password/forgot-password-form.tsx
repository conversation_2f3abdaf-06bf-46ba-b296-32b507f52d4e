"use client"

import * as React from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResol<PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@mui/joy"
import { Input } from "@mui/joy"
import { FormControl, FormLabel, FormHelperText } from "@mui/joy"
import { OTPConfirmForm } from './otp-confirm-form';

const formSchema = z.object({
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
})

type FormData = z.infer<typeof formSchema>

export function ForgotPasswordForm() {
  const router = useRouter()
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [success, setSuccess] = React.useState(false)
  const [showOTP, setShowOTP] = React.useState(false)
  const [email, setEmail] = React.useState("")

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
  })

  async function onSubmit(data: FormData) {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to send reset email")
      }

      setSuccess(true)
      setShowOTP(true)

      if (response.ok) {
        console.log("Reset Password Response:", response);  
        
        const data = await response.json()
        setEmail(data.email)
      }

    } catch (error) {
      if (error instanceof Error) {
        setError(error.message)
      } else {
        setError("An error occurred while sending the reset email")
      }
    } finally {
      setIsLoading(false)
    }
  }

  async function handleOTPSuccess() {
    router.push('/auth/reset-password')
  }

  async function handleOTPError(error: string) {
    setError(error)
    setShowOTP(false)
    setSuccess(false)
  }

  if (showOTP && email) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Enter verification code</h3>
        <p className="text-sm text-gray-600">
          We've sent a verification code to your email address.
        </p>
        <OTPConfirmForm 
          email={email}
          onSuccess={handleOTPSuccess}
          onError={handleOTPError}
        />
      </div>
    )
  }

  if (success) {
    return (
      <div className="text-center">
        <h3 className="text-lg font-medium">Check your email</h3>
        <p className="mt-2 text-sm text-gray-600">
          We've sent a verification code to your email address.
        </p>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <FormControl error={!!errors.email}>
        <FormLabel>Email</FormLabel>
        <Input
          type="email"
          placeholder="<EMAIL>"
          {...register("email")}
        />
        {errors.email && (
          <FormHelperText>{errors.email.message}</FormHelperText>
        )}
      </FormControl>

      {error && (
        <FormHelperText sx={{ color: "danger.main" }}>
          {error}
        </FormHelperText>
      )}

      <Button
        type="submit"
        loading={isLoading}
        fullWidth
        disabled={isLoading}
      >
        Send Reset Link
      </Button>
    </form>
  )
} 