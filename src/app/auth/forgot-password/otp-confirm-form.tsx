"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON>, Typo<PERSON>, Stack, Box, CircularProgress, Alert } from "@mui/joy"
import { Input } from "@mui/joy"
import { FormControl, FormLabel } from "@mui/joy"
import { motion, AnimatePresence } from "framer-motion"
import useCountdown from "react-countdown-hook"
import { toast } from "react-hot-toast"

const OTP_LENGTH = 6;
const INITIAL_TIME = 60 * 1000; // 60 seconds in milliseconds
const INTERVAL = 1000; // 1 second in milliseconds

const formSchema = z.object({
  otp: z.object(
    Object.fromEntries(
      Array.from({ length: OTP_LENGTH }, (_, i) => [
        i.toString(),
        z.string().length(1).regex(/^\d$/, "Must be a digit")
      ])
    )
  )
});

type FormData = z.infer<typeof formSchema>

interface OTPConfirmFormProps {
  email: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function OTPConfirmForm({ email, onSuccess, onError }: OTPConfirmFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [timeLeft, { start, pause, resume, reset }] = useCountdown(INITIAL_TIME, INTERVAL)
  const [attempts, setAttempts] = React.useState(0)
  const MAX_ATTEMPTS = 3
  const inputRefs = React.useRef<(HTMLInputElement | null)[]>([])
  const [showSuccess, setShowSuccess] = React.useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset: resetForm,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: Object.fromEntries(Array.from({ length: OTP_LENGTH }, (_, i) => [i.toString(), ""])),
    },
  })

  React.useEffect(() => {
    start()
  }, [start])

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData("text")
    const otpRegex = new RegExp(`^\\d{${OTP_LENGTH}}$`)
    
    if (otpRegex.test(pastedData)) {
      [...pastedData].forEach((char, index) => {
        setValue(`otp.${index}`, char)
        if (index < OTP_LENGTH - 1) {
          inputRefs.current[index + 1]?.focus()
        }
      })
    } else {
      toast.error("Please paste a valid 6-digit code")
    }
  }

  const handleResendOTP = async () => {
    if (timeLeft > 0) return
    
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/auth/resend-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      if (!response.ok) {
        throw new Error("Failed to resend OTP")
      }

      reset()
      start()
      resetForm()
      toast.success("New verification code sent!")
      inputRefs.current[0]?.focus()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Something went wrong"
      setError(errorMessage)
      onError?.(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const onSubmit = async (data: FormData) => {
    if (attempts >= MAX_ATTEMPTS) {
      toast.error("Maximum attempts reached. Please request a new code.")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch("/api/auth/verify-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          otp: Object.values(data.otp).join(""),
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Invalid OTP")
      }

      setShowSuccess(true)
      toast.success("OTP verified successfully!")
      onSuccess?.()
      
      setTimeout(() => {
        router.push("/auth/reset-password")
      }, 1000)
    } catch (err) {
      setAttempts(prev => prev + 1)
      const errorMessage = err instanceof Error ? err.message : "Failed to verify OTP"
      setError(errorMessage)
      onError?.(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    return `${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, '0')}`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Stack spacing={2}>
          <Typography level="h4" component={motion.h4} 
            initial={{ opacity: 0 }} 
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            Enter Verification Code
          </Typography>
          
          <Typography level="body-sm" color="neutral">
            We sent a verification code to <strong>{email}</strong>
          </Typography>

          <FormControl error={!!errors.otp}>
            <FormLabel>Verification Code</FormLabel>
            <Stack direction="row" spacing={1} justifyContent="center">
              {Array.from({ length: OTP_LENGTH }).map((_, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Input
                    type="text"
                    inputMode="numeric"
                    maxLength={1}
                    sx={{
                      width: "3rem",
                      height: "3rem",
                      textAlign: "center",
                      fontSize: "1.5rem",
                      "& input": { textAlign: "center" },
                    }}
                    {...register(`otp.${index}`)}
                    onPaste={handlePaste}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9]/g, "")
                      if (value.length <= 1) {
                        setValue(`otp.${index}`, value)
                        if (value && index < OTP_LENGTH - 1) {
                          inputRefs.current[index + 1]?.focus()
                        }
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "Backspace" && !e.currentTarget.value && index > 0) {
                        inputRefs.current[index - 1]?.focus()
                      }
                    }}
                    ref={(el) => {
                      inputRefs.current[index] = el
                    }}
                  />
                </motion.div>
              ))}
            </Stack>
            <AnimatePresence>
              {errors.otp && (
                <motion.p
                  className="text-sm text-red-500"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  {errors.otp.message}
                </motion.p>
              )}
            </AnimatePresence>
          </FormControl>

          {error && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
            >
              <Alert color="danger" variant="soft">
                {error}
              </Alert>
            </motion.div>
          )}

          <Box sx={{ position: 'relative' }}>
            <Button
              type="submit"
              loading={isLoading}
              disabled={isLoading || attempts >= MAX_ATTEMPTS || showSuccess}
              fullWidth
              size="lg"
              sx={{ 
                minHeight: '48px',
                background: showSuccess ? 'var(--joy-palette-success-500)' : undefined,
              }}
            >
              {showSuccess ? 'Verified Successfully!' : 'Verify Code'}
            </Button>
            
            {attempts > 0 && (
              <Typography 
                level="body-xs" 
                sx={{ mt: 1, textAlign: 'center' }}
                color={attempts >= MAX_ATTEMPTS ? "danger" : "neutral"}
              >
                {MAX_ATTEMPTS - attempts} attempts remaining
              </Typography>
            )}
          </Box>

          <Stack direction="row" spacing={1} justifyContent="center" alignItems="center">
            <Typography level="body-sm">Didn't receive the code?</Typography>
            <Button
              variant="plain"
              color="primary"
              onClick={handleResendOTP}
              disabled={timeLeft > 0 || isLoading || showSuccess}
              size="sm"
              endDecorator={timeLeft > 0 && (
                <CircularProgress
                  size="sm"
                  value={((INITIAL_TIME - timeLeft) / INITIAL_TIME) * 100}
                />
              )}
            >
              {timeLeft > 0 ? `Resend in ${formatTime(timeLeft)}` : 'Resend Code'}
            </Button>
          </Stack>
        </Stack>
      </form>
    </motion.div>
  )
}