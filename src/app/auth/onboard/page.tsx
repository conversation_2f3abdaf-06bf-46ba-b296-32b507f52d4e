'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Box, CircularProgress } from '@mui/joy';
import OnboardingFlow from '@/components/Onboarding/OnboardingFlow';
import AdminAccountForm from '@/components/Onboarding/AdminAccountForm';
import OrganizationForm from '@/components/Onboarding/OrganizationForm';
import SystemPreferencesForm from '@/components/Onboarding/PreferencesForm';
import FacilitySetupForm from '@/components/Onboarding/FacilitySetupForm';
import RoleSetupForm from '@/components/Onboarding/RoleSetupForm';
import SecurityConfigForm from '@/components/Onboarding/SecurityConfigForm';
import { useAuth } from '@/hooks/useAuth';

const steps = [
  {
    id: 'admin-account',
    title: 'Admin Account',
    description: 'Set up the primary administrator account',
    icon: 'mdi:account-cog',
    component: AdminAccountForm,
  },
  {
    id: 'organization',
    title: 'Organization Details',
    description: 'Provide information about your organization',
    icon: 'mdi:domain',
    component: OrganizationForm,
  },
  {
    id: 'system-preferences',
    title: 'System Preferences',
    description: 'Configure system-wide settings',
    icon: 'mdi:cog',
    component: SystemPreferencesForm,
  },
  {
    id: 'facility-setup',
    title: 'Facility Setup',
    description: 'Set up your primary facility',
    icon: 'mdi:hospital-building',
    component: FacilitySetupForm,
  },
  {
    id: 'role-setup',
    title: 'Role Configuration',
    description: 'Configure user roles and permissions',
    icon: 'mdi:account-group',
    component: RoleSetupForm,
  },
  {
    id: 'security-config',
    title: 'Security Configuration',
    description: 'Set up security policies and authentication methods',
    icon: 'mdi:shield-lock',
    component: SecurityConfigForm,
  },
];

export default function OnboardingPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <Box>
      <OnboardingFlow
        steps={steps}
        currentStep={currentStep}
        onStepChange={setCurrentStep}
      >
        <CurrentStepComponent />
      </OnboardingFlow>
    </Box>
  );
}

