'use client';

import dynamic from 'next/dynamic';
import React, { useState, useCallback, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import useSWR from 'swr';
import _ from 'lodash';
import {
  CollectionFormData,
  BodyCondition,
} from './types';
import {
  useCollectionStore
} from './collectionStore';
import {
  getCurrentSchema
} from './schemas';
import { CollectionType, BodyStatus } from '@prisma/client';

import {
  Box,
  Typography,
  Button,
  Stack,
  Divider,
  FormControl,
  FormLabel,
  FormHelperText,
  Select,
  Option,
  Sheet,
  Input,
  Textarea,
  Switch,
  CircularProgress,
  Grid,
  LinearProgress,
} from '@mui/joy';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

const FileUploader = dynamic(() => import('./FileUpload'), {
  ssr: false
});
const RealTimeClock = dynamic(() => import('@/components/Forms/BodyCollection/RealTimeClock'), {
  ssr: false
});
const CurrentLocationMap = dynamic(() => import('@/components/Forms/BodyCollection/CurrentLocationMap'), {
  ssr: false
});

const BodyTagSelector = dynamic(() => import('../BodyTag/BodyTagSelector').then(mod => ({ default: mod.BodyTagSelector })), {
  ssr: false
});

import { ArrowLeft, ArrowRight, Check, CheckCircle, Info } from 'lucide-react';
import { BodyTag } from '@prisma/client';

import { BodyTagScanType } from '@/types';
import { toast } from 'sonner';

const BodyCollectionType_formatText = (type: CollectionType) => {
  switch (type) {
    case CollectionType.CRIME_SCENE:
      return 'Crime Scene';
    case CollectionType.HOSPITAL:
      return 'Hospital';
    case CollectionType.OTHER:
      return 'Other';
  }
};

const fetcher = (url: string) => fetch(url).then((res) => {
  console.log(res);
  return res.json();
});

export default function CollectionForm() {
  const {
    formData,
    updateFormData,
    resetForm
  } = useCollectionStore();

  const { data: session } = useSession();
  const router = useRouter();

  const [arrivalConfirmed, setArrivalConfirmed] = useState(false);
  const [locationCaptured, setLocationCaptured] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const formRef = useRef<HTMLFormElement>(null);

  // Form scrollability check removed - handled by CSS

  // Navigation visibility is handled by CSS now

  const {
    data: collection
  } = useSWR(
    formData.id ? `/api/collections/${formData.id}` : null,
    fetcher
  );

  const updateFormDataDebounced = useCallback(
    _.debounce((newData: Partial<CollectionFormData>) => {
      updateFormData(newData);
    }, 100),
    [updateFormData]
  );

  React.useEffect(() => {
    if (collection) {
      const updatedCollection = {
        ...formData,
        ...collection,
        bodyPhotos: collection.bodyPhotos || [],
        gpsCoords: collection.gpsCoords || { lat: 0, lng: 0 },
        bodyTagId: collection.bodyTagId || null,
        bodyCondition: collection.bodyCondition || BodyCondition.NORMAL
      };

      // Prevent unnecessary updates
      if (!_.isEqual(formData, updatedCollection)) {
        updateFormDataDebounced(updatedCollection);
      }
    }
  }, [collection, updateFormDataDebounced, formData]);

  React.useEffect(() => {
    if (collection) {
      updateFormData({
        ...formData,
        ...collection,
        bodyPhotos: collection.bodyPhotos || [],
        gpsCoords: collection.gpsCoords || { lat: 0, lng: 0 },
        bodyTagId: collection.bodyTagId || null,
        bodyCondition: collection.bodyCondition || BodyCondition.NORMAL
      });
    }
  }, [collection, updateFormData]);

  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    watch,
    getValues,
    formState: { errors: formErrors }
  } = useForm<CollectionFormData>({
    resolver: zodResolver(getCurrentSchema(currentStep)),
    defaultValues: {
      ...formData,
      persalNumber: formData.persalNumber || '',
      name: formData.name || '',
      institution: formData.institution || '',
      vehicleReg: formData.vehicleReg || '',
      arrivalTime: formData.arrivalTime || '',
      gpsCoords: formData.gpsCoords || { lat: 0, lng: 0 },
      bodyPhotos: formData.bodyPhotos || [],
      bodyDescription: formData.bodyDescription || '',
      bodyTagId: formData.bodyTagId || null,
      bodyCondition: formData.bodyCondition || undefined,
      bodyCollectionType: formData.bodyCollectionType || undefined,
      isComplete: formData.isComplete || false,
      notes: formData.notes || ''
    },
    mode: 'onChange'
  });

  const watchedValues = watch();

  React.useEffect(() => {
    // Only update if there are actual changes
    if (!_.isEqual(watchedValues, formData)) {
      const updatedValues = {
        ...formData,
        ...watchedValues,
        gpsCoords: watchedValues.gpsCoords || { lat: 0, lng: 0 },
        bodyPhotos: watchedValues.bodyPhotos || [],
      };

      updateFormDataDebounced(updatedValues);
    }
  }, [watchedValues, updateFormDataDebounced, formData]);

  // Unused helper functions removed

  const [submissionState, setSubmissionState] = useState<{
    status: 'idle' | 'submitting' | 'success' | 'error';
    message?: string;
  }>({ status: 'idle' });

  const onSubmit = async (data: CollectionFormData) => {
    try {
      // Step 1: Validate all form fields
      const isValid = await trigger();
      if (!isValid) {
        const errorFields = Object.keys(formErrors).join(', ');
        const errorMessage = `Please complete all required fields: ${errorFields}`;
        console.error('Form validation failed:', formErrors);
        toast.error(errorMessage);
        setSubmissionState({
          status: 'error',
          message: errorMessage
        });
        return;
      }

      // Step 2: Validate user session
      if (!session?.user?.id) {
        const errorMessage = 'You must be logged in to submit a collection';
        toast.error(errorMessage);
        setSubmissionState({
          status: 'error',
          message: errorMessage
        });
        return;
      }

      // Step 3: Validate critical fields
      if (!data.bodyTagId) {
        const errorMessage = 'A body tag is required for collection';
        toast.error(errorMessage);
        setSubmissionState({
          status: 'error',
          message: errorMessage
        });
        return;
      }

      // Step 4: Process photos
      const bodyPhotos = (data.bodyPhotos || [])
        .filter((photo: any) => photo !== undefined && photo !== null)
        .map((photo: any) => typeof photo === 'string' ? photo : photo.url);

      if (bodyPhotos.length === 0) {
        const errorMessage = 'At least one body photo is required';
        toast.error(errorMessage);
        setSubmissionState({
          status: 'error',
          message: errorMessage
        });
        return;
      }

      // Step 5: Prepare submission data according to our schema
      const submissionData = {
        // Employee Details
        persalNumber: data.persalNumber,
        name: data.name,
        institution: data.institution,
        vehicleReg: data.vehicleReg || '',

        // Arrival & Location
        arrivalTime: data.arrivalTime,
        gpsCoords: data.gpsCoords || { lat: 0, lng: 0 },

        // Body Documentation
        photos: bodyPhotos, // Note: API expects 'photos', not 'bodyPhotos'
        bodyTagId: data.bodyTagId,
        bodyDescription: data.bodyDescription,

        // Collection Type & Condition
        collectionType: data.bodyCollectionType || CollectionType.OTHER, // Note: API expects 'collectionType', not 'bodyCollectionType'
        bodyCollectionType: data.bodyCollectionType || CollectionType.OTHER, // Include both for compatibility

        // Metadata fields
        bodyCondition: data.bodyCondition || BodyCondition.NORMAL,
        isComplete: data.isComplete || false,
        notes: data.notes || '',

        // System fields
        userId: session.user.id,
        status: BodyStatus.COLLECTED,

        // Additional fields required by schema
        sceneDescription: null,
        weatherConditions: null,
        temperature: null,
        collectionNotes: data.notes || '',
        documents: [],
        handedOverBy: null,
        handedOverRole: null,
        handedOverContact: null,
        handoverNotes: null,
      };

      // Step 6: Show submission state
      setSubmissionState({
        status: 'submitting',
        message: 'Submitting collection...'
      });

      // Use toast ID to update the same toast
      const toastId = toast.loading('Creating collection record...');

      // Step 7: Submit the data
      const response = await fetch('/api/collections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData)
      });

      // Step 8: Process the response
      const responseData = await response.json();

      if (!response.ok) {
        // Handle API error response
        const errorDetails = responseData.details
          ? (Array.isArray(responseData.details)
              ? responseData.details.map((d: any) => `${d.path}: ${d.message}`).join(', ')
              : responseData.details)
          : 'Unknown error';

        const errorMessage = responseData.message || responseData.error || 'Failed to create collection';

        toast.error(`${errorMessage}: ${errorDetails}`, { id: toastId });
        setSubmissionState({
          status: 'error',
          message: `${errorMessage}: ${errorDetails}`
        });
        return;
      }

      // Step 9: Validate successful response
      if (!responseData.data || !responseData.data.id) {
        toast.error('Invalid response from server: missing collection ID', { id: toastId });
        setSubmissionState({
          status: 'error',
          message: 'Invalid response from server: missing collection ID'
        });
        return;
      }

      // Step 10: Clean up unused generated body tags
      try {
        // Call the API to clean up unused generated body tags
        const cleanupResponse = await fetch('/api/body-tags/remove-generated', {
          method: 'DELETE',
        });

        if (cleanupResponse.ok) {
          const cleanupData = await cleanupResponse.json();
          console.log(`Cleaned up ${cleanupData.count} unused generated body tags`);
        }
      } catch (cleanupError) {
        console.error('Error cleaning up unused body tags:', cleanupError);
        // Don't fail the whole process if cleanup fails
      }

      // Step 11: Success handling
      resetForm();
      
      // Enhanced success toast with navigation info
      toast.success(
        `Collection created successfully! Redirecting to collection details...`, 
        { 
          id: toastId,
          duration: 2000,
          description: `Collection ID: ${responseData.data.id}`
        }
      );
      
      setSubmissionState({
        status: 'success',
        message: 'Collection created successfully!'
      });

      // Step 12: Navigate to the collection details page using Next.js router
      setTimeout(() => {
        router.push(`/dashboard/records/collections/${responseData.data.id}`);
      }, 1500); // Short delay to show success message

    } catch (error) {
      console.error('Collection submission error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

      toast.error(`Submission failed: ${errorMessage}`);
      setSubmissionState({
        status: 'error',
        message: `Submission failed: ${errorMessage}`
      });
    }
  };

  const steps = [
    {
      label: 'Employee Details',
      description: 'Enter collector information'
    },
    {
      label: 'Arrival & Location',
      description: 'Confirm arrival time and location'
    },
    {
      label: 'Body Documentation',
      description: 'Document body and scan tags'
    },
    {
      label: 'Collection Complete',
      description: 'Review and confirm collection'
    }
  ];

  const bodyConditionOptions = [
    { value: BodyCondition.NORMAL, label: 'Normal' },
    { value: BodyCondition.SKELETAL, label: 'Skeletal' },
    { value: BodyCondition.DECOMPOSED, label: 'Decomposed' },
    { value: BodyCondition.CHARRED, label: 'Charred' }
  ];

  // Fetch user data for pre-filling the form
  const { data: userData } = useSWR(
    session?.user?.id ? `/api/users/${session.user.id}` : null,
    fetcher
  );

  React.useEffect(() => {
    if (userData && !formData.persalNumber) {
      updateFormData({
        persalNumber: userData.persalNumber || '',
        name: userData.name || '',
        institution: userData.facility?.name || '',
        vehicleReg: formData.vehicleReg || '',
      });

      setValue('persalNumber', userData.persalNumber || '');
      setValue('name', userData.name || '');
      setValue('institution', userData.facility?.name || '');
      setValue('vehicleReg', formData.vehicleReg || '');
    }
  }, [userData, formData.persalNumber, updateFormData]);


  // Validation schemas are now defined in the central schema file

  // Step validation is now handled directly in the handleNext function

  const handleLocationCapture = useCallback((coords: { lat: number; lng: number }) => {
    setValue('gpsCoords', {
      lat: coords.lat,
      lng: coords.lng,
    }, { shouldValidate: true, shouldDirty: true });
    setLocationCaptured(true);
  }, [setValue]);

  // Handle Next Step
  const handleNext = async () => {
    try {
      // Get current form values
      const currentValues = getValues();

      // Validate using Zod schema
      const schema = getCurrentSchema(currentStep);
      const result = await schema.safeParseAsync(currentValues);

      if (!result.success) {
        const errors = result.error.errors;
        const errorMessages = errors.map(err => err.message).join('\n');
        toast.error(errorMessages || 'Please complete all required fields');
        return;
      }

      // Special validation for scene details step
      if (currentStep === 2) {
        if (!currentValues.bodyPhotos?.length) {
          toast.error('Please upload at least one body photo');
          return;
        }
        if (!currentValues.bodyTagId) {
          toast.error('Please select a body tag');
          return;
        }
        if (!currentValues.bodyCondition) {
          toast.error('Please select a body condition');
          return;
        }
        if (!currentValues.bodyCollectionType) {
          toast.error('Please select a collection type');
          return;
        }
      }

      // Special logic for specific steps
      switch (currentStep) {
        case 0: // Employee Details
          if (!currentValues.persalNumber || !currentValues.name || !currentValues.institution) {
            toast.error('Please complete all employee details');
            return;
          }
          break;

        case 1: // Arrival Details
          if (!arrivalConfirmed || !locationCaptured) {
            toast.error('Please confirm arrival time and capture location');
            return;
          }
          break;

        case 2: // Scene Details
          if (!currentValues.bodyPhotos?.length) {
            toast.error('Please add at least one photo');
            return;
          }
          if (!currentValues.bodyDescription) {
            toast.error('Please provide a body description');
            return;
          }
          if (!currentValues.bodyCondition) {
            toast.error('Please select a body condition');
            return;
          }
          if (!currentValues.bodyCollectionType) {
            toast.error('Please select a collection type');
            return;
          }
          break;

        case 3: // Final Step
          if (!currentValues.isComplete) {
            toast.error('Please confirm the collection is complete');
            return;
          }
          // Submit the form
          await onSubmit(currentValues);
          return;
      }

      // Move to next step if not the final step
      setCurrentStep(prev => prev + 1);
      setSubmissionState({ status: 'idle' });

    } catch (error) {
      console.error('Next step error:', error);
      toast.error('An unexpected error occurred. Please try again.');
      setSubmissionState({
        status: 'error',
        message: 'An unexpected error occurred. Please try again.'
      });
    }
  };

  // Handle Previous Step
  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);

      // Reset any step-specific states
      switch (currentStep) {
        case 1: // Leaving Arrival Details
          setArrivalConfirmed(false);
          setLocationCaptured(false);
          break;

        case 2: // Leaving Scene Details
          // Optional: Reset specific scene details states
          break;
      }

      // Reset submission state
      setSubmissionState({ status: 'idle' });
    }
  };

  // Navigation button rendering with progress tracking
  const renderNavigationButtons = () => {
    const totalSteps = steps.length;
    const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

    return (
      <Box
        sx={{
          position: 'fixed',
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 1000,
          p: 2,
          backgroundColor: 'background.surface',
          boxShadow: 'md'
        }}
      >
        <Grid container spacing={2} alignItems="center">
          {/* Previous Button */}
          <Grid xs={2}>
            <Button
              variant="soft"
              color="neutral"
              startDecorator={<ArrowLeft />}
              onClick={handlePrev}
              disabled={currentStep === 0}
              fullWidth
            >
              Previous
            </Button>
          </Grid>

          {/* Progress Indicator */}
          <Grid xs={8}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ flexGrow: 1 }}>
                <LinearProgress
                  determinate
                  value={progressPercentage}
                  color="primary"
                />
              </Box>
              <Typography>
                Step {currentStep + 1} of {totalSteps}
              </Typography>
            </Box>
          </Grid>

          {/* Next/Submit Button */}
          <Grid xs={2}>
            <Button
              variant="solid"
              color="primary"
              endDecorator={
                currentStep === totalSteps - 1
                  ? <Check />
                  : <ArrowRight />
              }
              onClick={handleNext}
              fullWidth
            >
              {currentStep === totalSteps - 1 ? 'Submit' : 'Next'}
            </Button>
          </Grid>
        </Grid>
      </Box>
    );
  };

  const renderSubmissionFeedback = () => {
    switch (submissionState.status) {
      case 'submitting':
        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              color: 'primary.softColor',
              p: 2,
              borderRadius: 'sm',
              bgcolor: 'primary.softBg'
            }}
          >
            <CircularProgress size="sm" />
            <Typography>{submissionState.message}</Typography>
          </Box>
        );
      case 'success':
        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              color: 'success.softColor',
              p: 2,
              borderRadius: 'sm',
              bgcolor: 'success.softBg'
            }}
          >
            <Check color="success" />
            <Typography>{submissionState.message}</Typography>
          </Box>
        );
      case 'error':
        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              color: 'danger.softColor',
              p: 2,
              borderRadius: 'sm',
              bgcolor: 'danger.softBg'
            }}
          >
            <Info color="error" />
            <Typography>{submissionState.message}</Typography>
          </Box>
        );
      default:
        return null;
    }
  };


  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        marginBottom: '1rem',
      }}
    >
      {collection ? (
        <Sheet
          variant="outlined"
          color="success"
          sx={{
            p: 2,
            borderRadius: 'sm',
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            mb: 2,
          }}
        >
          <CheckCircle color="success" />
          <Typography level="body-md">
            Collection successfully created! You can view the details or start a new collection.
          </Typography>
        </Sheet>

      ) : (
        <>
          <Sheet variant="outlined"
            sx={{
              /*make It Stick to the top but not Obstruct the Content*/
              top: 0,
              position: 'sticky',
              zIndex: 999,
              boxShadow: 'sm',
              p: 2,
              marginBottom: '1rem',
            }}
          >
            <Stack
              direction={"row"}
              justifyContent={"space-between"}
              alignItems={"center"}
              sx={{
                '--Stepper-verticalGap': '1rem',
                '--Step-gap': '1rem',
              }}
            >
              {steps.map((step, index) => (
                <Stack
                  key={step.label}
                  direction="row"
                  spacing={2}
                  sx={{
                    alignItems: 'center',
                    opacity: index < currentStep + 1 ? 1 : 0.5,
                    cursor: index < currentStep + 1 ? 'default' : 'pointer',
                  }}
                >
                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                    <Typography
                      level="title-sm"
                      sx={{
                        mr: 2,
                        width: 32,
                        height: 32,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: '50%',
                        bgcolor: index < (currentStep + 1) ? 'primary.500' : 'neutral.400'
                      }}
                    >{index + 1}</Typography>
                    <Box>
                      <Typography level="body-md"
                        sx={{
                          color: 'text.primary'
                        }}
                      >
                        {step.label}
                      </Typography>
                      <Typography level="body-xs"
                        sx={{
                          color: 'text.secondary'
                        }}
                      >
                        {step.description}
                      </Typography>
                    </Box>

                  </Box>
                </Stack>
              ))}
            </Stack>
          </Sheet>

          <form
            ref={formRef}
            onSubmit={handleSubmit(onSubmit)}
          >
            <Box
              sx={{
                //Scrollable Content
                display: 'flex',
                flex: 1,
                flexGrow: 1,
                overflowY: 'auto',
                position: 'relative',
                placeContent: 'center',
                //height: 'calc(100vh - 200px)'
                //maxHeight: 'calc(100vh - 200px)'
              }}
            >
              <Sheet
                variant="outlined"
                sx={{
                  borderRadius: 'sm',
                  p: { xs: 2, sm: 3 },
                  bgcolor: 'background.surface',
                  alignSelf: 'center',
                  width: {
                    md: '100vw',
                    lg: '60vw',
                  }
                }}
              >
                <Stack spacing={3}>
                  {currentStep === 0 && (
                    <>
                      <Typography level="title-lg" startDecorator={<Info />}>
                        Employee Details
                      </Typography>
                      <Divider />
                      <Stack spacing={3}>
                        <Controller
                          name="persalNumber"
                          control={control}
                          render={({ field, fieldState }) => (
                            <FormControl error={!!fieldState.error}>
                              <FormLabel>Persal Number *</FormLabel>
                              <Input
                                {...field}
                                type="number"
                                aria-label="Persal number"
                                placeholder="Enter your persal number"
                                disabled={!session?.user?.role?.includes('ADMIN')}
                                size="sm"
                              />
                              {fieldState.error && (
                                <FormHelperText>{fieldState.error.message}</FormHelperText>
                              )}
                            </FormControl>
                          )}
                        />

                        <Controller
                          name="name"
                          control={control}
                          render={({ field, fieldState }) => (
                            <FormControl error={!!fieldState.error}>
                              <FormLabel>Full Name *</FormLabel>
                              <Input
                                {...field}
                                aria-label="Full name"
                                placeholder="Enter your full name"
                                disabled={session?.user?.role?.includes('ADMIN')}
                                size="sm"
                              />
                              {fieldState.error && (
                                <FormHelperText>{fieldState.error.message}</FormHelperText>
                              )}
                            </FormControl>
                          )}
                        />

                        <Controller
                          name="institution"
                          control={control}
                          render={({ field, fieldState }) => (
                            <FormControl error={!!fieldState.error}>
                              <FormLabel>Institution *</FormLabel>
                              <Input
                                {...field}
                                aria-label="Institution"
                                placeholder="Enter institution name"
                                disabled={session?.user?.role?.includes('ADMIN')}
                                size="sm"
                              />
                              {fieldState.error && (
                                <FormHelperText>{fieldState.error.message}</FormHelperText>
                              )}
                            </FormControl>
                          )}
                        />

                        <Controller
                          name="vehicleReg"
                          control={control}
                          render={({ field, fieldState }) => (
                            <FormControl error={!!fieldState.error}>
                              <FormLabel>Vehicle Registration *</FormLabel>
                              <Input
                                {...field}
                                aria-label="Vehicle registration"
                                placeholder="Enter vehicle registration"
                                onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                                size="sm"
                              />
                              {fieldState.error && (
                                <FormHelperText>{fieldState.error.message}</FormHelperText>
                              )}
                            </FormControl>
                          )}
                        />
                      </Stack>
                    </>
                  )}

                  {currentStep === 1 && (
                    <>
                      <Typography level="title-lg" startDecorator={<Info />}>
                        Arrival Details
                      </Typography>
                      <Divider />

                      <RealTimeClock
                        onArrivalConfirm={(time: string) => {
                          setValue('arrivalTime', time, {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true
                          });

                          setArrivalConfirmed(true);
                          /* i */
                        }}
                        isConfirmed={arrivalConfirmed}
                      />

                      {arrivalConfirmed && (
                        <>
                          <Typography level="title-md" sx={{ mt: 4 }}>
                            Collection Location
                          </Typography>
                          <CurrentLocationMap
                            onLocationCaptured={handleLocationCapture}
                            useMockLocation={false}
                          />
                        </>
                      )}
                    </>
                  )}

                  {currentStep === 2 && (
                    <>
                      <Typography level="title-lg" startDecorator={<Info />}>
                        Scene Details
                      </Typography>
                      <Divider />
                      <Stack spacing={3}>
                        <Controller
                          name="bodyPhotos"
                          control={control}
                          render={({ field, fieldState }) => (
                            <FormControl error={!!fieldState.error}>
                              <FormLabel>Body Photos *</FormLabel>
                              <FileUploader
                                value={field.value}
                                onChange={(files: string[]) => {
                                  field.onChange(files);
                                  trigger('bodyPhotos');
                                }}
                                onBlur={field.onBlur}
                                error={!!fieldState.error}
                                helperText={fieldState.error?.message || 'Upload photos of the body (JPEG, PNG)'}
                                label="Upload Photos"
                                accept={{ 'image/*': ['.jpeg', '.jpg', '.png'] }}
                              />
                              {fieldState.error && (
                                <FormHelperText>{fieldState.error.message}</FormHelperText>
                              )}
                            </FormControl>
                          )}
                        />

                        <FormControl>
                          <BodyTagSelector
                            name="bodyTagId"
                            control={control as any} // Type cast to avoid type errors
                            scanType={BodyTagScanType.COLLECTION}
                            showResult={true}
                            onChange={(tag: BodyTag | null, _validationResult?: any) => {
                              console.log("SelectedTag", tag);
                              if (tag) {
                                setValue('bodyTagId', tag.id, { shouldValidate: true });
                              } else {
                                setValue('bodyTagId', null, { shouldValidate: true });
                              }
                            }}
                          />
                        </FormControl>

                        <Stack direction={'row'} spacing={2}>
                          <Controller
                            name="bodyCondition"
                            control={control}
                            render={({ field, fieldState }) => (
                              <FormControl error={!!fieldState.error} sx={{ flex: 1 }}>
                                <FormLabel>Body Condition *</FormLabel>
                                <Select
                                  {...field}
                                  value={field.value || null}
                                  onChange={(_, v) => {
                                    field.onChange(v);
                                    trigger('bodyCondition');
                                  }}
                                  required
                                  placeholder="Select body condition"
                                >
                                  {bodyConditionOptions.map((option) => (
                                    <Option key={option.value} value={option.value}>
                                      {option.label}
                                    </Option>
                                  ))}
                                </Select>
                                {fieldState.error && (
                                  <FormHelperText>{fieldState.error.message}</FormHelperText>
                                )}
                              </FormControl>
                            )}
                          />

                          <Controller
                            name="bodyCollectionType"
                            control={control}
                            render={({ field, fieldState }) => (
                              <FormControl error={!!fieldState.error} sx={{ flex: 1 }}>
                                <FormLabel>Body Collection Type *</FormLabel>
                                <Select
                                  {...field}
                                  value={field.value || null}
                                  onChange={(_, v) => {
                                    field.onChange(v);
                                    trigger('bodyCollectionType');
                                  }}
                                  required
                                  placeholder="Select collection type"
                                >
                                  {Object.values(CollectionType).map((type) => (
                                    <Option key={type} value={type}>
                                      {BodyCollectionType_formatText(type)}
                                    </Option>
                                  ))}
                                </Select>
                                {fieldState.error && (
                                  <FormHelperText>{fieldState.error.message}</FormHelperText>
                                )}
                              </FormControl>
                            )}
                          />
                        </Stack>

                        <Controller
                          name="bodyDescription"
                          control={control}
                          render={({ field, fieldState }) => (
                            <FormControl error={!!fieldState.error}>
                              <FormLabel>Body Description *</FormLabel>
                              <Textarea
                                {...field}
                                aria-label="Body description"
                                placeholder="Provide a detailed description of the body"
                                minRows={3}
                                size="sm"
                              />
                              {fieldState.error && (
                                <FormHelperText>{fieldState.error.message}</FormHelperText>
                              )}
                            </FormControl>
                          )}
                        />
                      </Stack>
                    </>
                  )}

                  {currentStep === 3 && (
                    <>
                      <Typography level="title-lg" startDecorator={<Info />}>
                        Collection Complete
                      </Typography>
                      <Divider />
                      <Stack spacing={3}>
                        <Controller
                          name="isComplete"
                          control={control}
                          render={({ field, fieldState }) => (
                            <FormControl
                              error={!!fieldState.error}
                              orientation="horizontal"
                            >
                              <FormLabel>Confirm Collection Complete *</FormLabel>
                              <Switch
                                {...field}
                                checked={field.value}
                                slotProps={{
                                  input: { 'aria-label': 'Collection complete' },
                                }}
                              />
                              {fieldState.error && (
                                <FormHelperText>{fieldState.error.message}</FormHelperText>
                              )}
                            </FormControl>
                          )}
                        />

                        <Controller
                          name="notes"
                          control={control}
                          render={({ field, fieldState }) => (
                            <FormControl error={!!fieldState.error}>
                              <FormLabel>Additional Notes</FormLabel>
                              <Textarea
                                {...field}
                                aria-label="Additional notes"
                                placeholder="Add any additional information"
                                minRows={3}
                                size="sm"
                              />
                              {fieldState.error && (
                                <FormHelperText>{fieldState.error.message}</FormHelperText>
                              )}
                            </FormControl>
                          )}
                        />
                      </Stack>
                    </>
                  )}
                </Stack>
              </Sheet>

            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              {renderNavigationButtons()}
            </Box>
            {renderSubmissionFeedback()}
          </form>
        </>
      )}
    </Box>
  );
}
