"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  DeviceThermostat,
  Kitchen,
  Save,
  Close,
  ArrowBack,
  ArrowForward,
} from "@mui/icons-material";

import {
  Button,
  Card,
  CardContent,
  CardActions,
  Typography,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Select,
  Option,
  Textarea,
  Divider,
  Box,
  Sheet,
  Tabs,
  TabList,
  Tab,
  TabPanel,
  IconButton,
  LinearProgress,
  Stack,
  Grid,
  Alert,
  Modal,
  ModalDialog,
  ModalClose,
  CircularProgress,
} from "@mui/joy";

import { CameraCapture } from "../camera-capture";
import { BarcodeScanner } from "../barcode-scanner";
import { BodyTagSelectorField } from "../BodyTag/BodyTagSelector";
import { BodyTag, BodyTagScanType } from "@/types/body";
import FileUploadComponent from "../BodyCollection/FileUpload";
import { bodyAdmissionFormSchema, BodyAdmissionFormValues } from "@/lib/schema/bodyAdmissionSchema";
import FacilitySelector from "@/components/Shared/FacilitySelector";
import { UnoccupiedFridgeSelector } from "@/components/Selectors/UnoccupiedFridgeSelector";
import UserSelectField from "@/components/UI/FormFields/UserSelect";
import { useSession } from "next-auth/react";
import { format } from "date-fns";

// Component for handling body admission form

interface CollectionData {
  // BodyCollection fields from Prisma schema
  id: string;
  bodyId: string;
  userId?: string;
  name?: string;
  institution?: string;
  vehicleReg?: string;
  arrivalTime?: Date;
  gpsCoords?: any; // Json type
  collectionType?: string;
  bodyDescription?: string;
  sceneDescription?: string;
  weatherConditions?: string;
  temperature?: number;
  collectionNotes?: string;
  photos?: string[];
  documents?: string[];
  handedOverBy?: string;
  handedOverRole?: string;
  handedOverContact?: string;
  handoverNotes?: string;
  status?: string;
  barcodeValue: string;
  metadata?: any; // Json type
  createdAt?: Date;
  updatedAt?: Date;

  // Related Body data
  body?: {
    id: string;
    trackingNumber: string;
    bodyTagId: string;
    status?: string;
    firstName?: string;
    lastName?: string;
    gender?: string;
    approximateAge?: number;
    height?: number;
    weight?: number;
    distinguishingFeatures?: string;
    deathRegistration?: string;
    causeOfDeath?: string;
    placeOfDeath?: string;
    dateOfDeath?: Date;
    photos?: string[];
    documents?: string[];
    metadata?: any;
    bodyTag?: {
      id: string;
      tagNumber: string;
      generatedBy: string;
      generatedAt: Date;
      status: string;
      bodyId?: string;
      collectionId?: string;
      admissionId?: string;
      lastScannedAt?: Date;
      lastScannedBy?: string;
      notes?: string;
      qrCodeValue?: string;
      qrCodeFormat?: string;
      qrCodeMetadata?: any;
      isActive: boolean;
      facilityId?: string;
      createdAt: Date;
      updatedAt: Date;
    };
  };

  // Related User data
  user?: {
    id: string;
    name?: string;
    email?: string;
    role?: string;
    department?: string;
    facilityId?: string;
    facility?: {
      id: string;
      name: string;
      code: string;
      type: string;
    };
  };
}

interface AdmissionFormProps {
  prefilledData?: {
    bodyTagId?: string;
    collectionId?: string;
    collectionDetails?: {
      name?: string;
      institution?: string;
      arrivalTime?: string;
      barcodeValue?: string;
      vehicleReg?: string;
      collectionType?: string;
      location?: string;
      notes?: string;
    };
  };
  collectionData?: CollectionData;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function AdmissionForm({ prefilledData, collectionData, onSuccess, onCancel }: AdmissionFormProps) {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState(0);
  const [formProgress, setFormProgress] = useState(25);
  const [showCamera, setShowCamera] = useState(false);
  const [showScanner, setShowScanner] = useState(false);
  const [photos, setPhotos] = useState<string[]>([]);
  const [scannedBarcode, setScannedBarcode] = useState("");
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState({ title: "", message: "" });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // State for API data
  const [selectedFacilityId, setSelectedFacilityId] = useState<string | null>(session?.user?.facilityId || null);
  const [selectedFridgeId, setSelectedFridgeId] = useState<string | null>(null);
  const [selectedStaffId, setSelectedStaffId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with resolver and default values
  const form = useForm<BodyAdmissionFormValues>({
    resolver: zodResolver(bodyAdmissionFormSchema),
    defaultValues: {
      facilityId: collectionData?.user?.facilityId || session?.user?.facilityId || "",
      bodyId: collectionData?.bodyId || prefilledData?.collectionDetails?.name || "",
      bodyTagId: collectionData?.body?.bodyTagId || prefilledData?.bodyTagId || "",
      admissionType: "INITIAL",
      deathRegisterNumber: collectionData?.body?.deathRegistration || "",
      bodyCondition: collectionData?.bodyDescription || "",
      notes: collectionData?.collectionNotes || prefilledData?.collectionDetails?.notes || "",
      assignedFridge: "",
      temperature: collectionData?.temperature || undefined,
      barcodeValue: collectionData?.barcodeValue || prefilledData?.collectionDetails?.barcodeValue || "",
      assignedToId: collectionData?.user?.id || session?.user?.id || "",
      photos: collectionData?.photos || [],
    },
  });

  const {
    register,
    formState: { errors },
    setValue,
    getValues,
    handleSubmit,
    trigger,
    watch,
  } = form;

  // Update photos field when photos state changes
  useEffect(() => {
    setValue("photos", photos);
  }, [photos, setValue]);

  // Set prefilled data when component mounts or prefilledData/collectionData changes
  useEffect(() => {
    // Handle collection data (priority over prefilledData)
    if (collectionData) {
      if (collectionData.body?.bodyTagId) {
        setValue("bodyTagId", collectionData.body.bodyTagId);
      }
      if (collectionData.bodyId) {
        setValue("bodyId", collectionData.bodyId);
      }
      if (collectionData.barcodeValue) {
        setValue("barcodeValue", collectionData.barcodeValue);
      }
      if (collectionData.collectionNotes) {
        setValue("notes", collectionData.collectionNotes);
      }
      if (collectionData.body?.deathRegistration) {
        setValue("deathRegisterNumber", collectionData.body.deathRegistration);
      }
      if (collectionData.bodyDescription) {
        setValue("bodyCondition", collectionData.bodyDescription);
      }
      if (collectionData.temperature !== undefined) {
        setValue("temperature", collectionData.temperature);
      }
      if (collectionData.photos && collectionData.photos.length > 0) {
        setPhotos(collectionData.photos);
        setValue("photos", collectionData.photos);
      }
      if (collectionData.user?.facilityId) {
        setValue("facilityId", collectionData.user.facilityId);
        setSelectedFacilityId(collectionData.user.facilityId);
      }
      if (collectionData.user?.id) {
        setValue("assignedToId", collectionData.user.id);
        setSelectedStaffId(collectionData.user.id);
      }
    }
    // Handle legacy prefilledData if no collection data
    else if (prefilledData) {
      if (prefilledData.bodyTagId) {
        setValue("bodyTagId", prefilledData.bodyTagId);
      }
      if (prefilledData.collectionDetails?.name) {
        setValue("bodyId", prefilledData.collectionDetails.name);
      }
      if (prefilledData.collectionDetails?.barcodeValue) {
        setValue("barcodeValue", prefilledData.collectionDetails.barcodeValue);
      }
      if (prefilledData.collectionDetails?.notes) {
        setValue("notes", prefilledData.collectionDetails.notes);
      }
    }
  }, [prefilledData, collectionData, setValue, setPhotos]);

  // Set initial values when session is loaded
  useEffect(() => {
    if (session?.user) {
      if (session.user.facilityId) {
        setValue("facilityId", session.user.facilityId);
        setSelectedFacilityId(session.user.facilityId);
      }

      if (session.user.id) {
        setValue("assignedToId", session.user.id);
        setSelectedStaffId(session.user.id);
      }
    }
  }, [session, setValue]);

  // Add form validation before navigating between tabs
  const handleTabChange = async (value: number) => {
    // Validate current tab before proceeding to next
    if (activeTab < value) {
      // Validate specific fields based on current tab
      let fieldsToValidate: (keyof BodyAdmissionFormValues)[] = [];

      switch (activeTab) {
        case 0: // Details tab
          fieldsToValidate = ['facilityId', 'bodyId', 'bodyTagId', 'admissionType'];
          break;
        case 1: // Documentation tab
          fieldsToValidate = ['bodyCondition', 'photos'];
          break;
        case 2: // Storage tab
          fieldsToValidate = ['assignedFridge', 'temperature', 'assignedToId'];
          break;
      }

      // Check for errors in the specified fields
      const result = await trigger(fieldsToValidate);

      // Only proceed if validation passes
      if (!result) {
        // Show error toast
        showToastNotification(
          "Validation Error",
          "Please correct the errors before proceeding"
        );
        return;
      }
    }

    setActiveTab(value);

    // Update progress based on tab
    switch (value) {
      case 0:
        setFormProgress(25);
        break;
      case 1:
        setFormProgress(50);
        break;
      case 2:
        setFormProgress(75);
        break;
      case 3:
        setFormProgress(100);
        break;
      default:
        setFormProgress(25);
    }
  };

  // Enhanced submission handling with pre-submission validation
  async function onSubmit(data: BodyAdmissionFormValues) {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Pre-submission validation
      if (photos.length === 0) {
        throw new Error("Please upload at least one photo document.");
      }

      // Prepare the data to match the API schema
      const admissionData = {
        ...data,
        photo: photos.length > 0 ? photos[0] : undefined, // For backward compatibility
        photos: photos,
        status: "ACTIVE",
        createdById: session?.user?.id || "1", // Use the current user's ID from the session
        assignedToId: selectedStaffId || session?.user?.id || "1", // Use the selected staff ID or fall back to the current user
      };

      console.log("Submitting admission data:", admissionData);

      const response = await fetch("/api/admissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(admissionData),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle specific error cases
        if (errorData.details && Array.isArray(errorData.details)) {
          const errorMessages = errorData.details.map((err: any) =>
            `${err.path.join('.')}: ${err.message}`
          ).join(', ');
          throw new Error(`Validation errors: ${errorMessages}`);
        }

        throw new Error(errorData.error || "Failed to create body admission");
      }

      const result = await response.json();

      // Show success notification
      showToastNotification(
        "Body admission successful",
        `Body ${data.bodyId} has been admitted to facility ${data.facilityId}`
      );

      // Reset form
      form.reset();
      setPhotos([]);
      setActiveTab(0);
      setFormProgress(25);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Or redirect to a different page
      // window.location.href = `/admissions/${result.id}`
    } catch (error) {
      console.error("Error submitting admission:", error);
      setSubmitError(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
      showToastNotification(
        "Submission failed",
        error instanceof Error
          ? error.message
          : "Failed to create body admission"
      );
    } finally {
      setIsSubmitting(false);
    }
  }

  // Toast notification function with auto-hide after 3 seconds
  const showToastNotification = (title: string, message: string) => {
    setToastMessage({ title, message });
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  const handlePhotoCapture = (photoDataUrl: string) => {
    setPhotos([...photos, photoDataUrl]);
    setValue("photos", [...photos, photoDataUrl]);
    setShowCamera(false);
  };

  const handleBarcodeScanned = (barcode: string) => {
    setScannedBarcode(barcode);
    setValue("barcodeValue", barcode);
    setShowScanner(false);
    showToastNotification(
      "Barcode scanned",
      `Barcode ${barcode} has been scanned successfully`
    );
  };

  const removePhoto = (index: number) => {
    const updatedPhotos = photos.filter((_, i) => i !== index);
    setPhotos(updatedPhotos);
    setValue("photos", updatedPhotos);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: "auto", py: 4, px: 2 }}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ mt: 3 }}>
          <LinearProgress
            determinate
            value={formProgress}
            sx={{ height: 8, borderRadius: 4 }}
          />
          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 1 }}>
            <Typography level="body-sm" color="neutral">
              Details
            </Typography>
            <Typography level="body-sm" color="neutral">
              Documentation
            </Typography>
            <Typography level="body-sm" color="neutral">
              Storage
            </Typography>
            <Typography level="body-sm" color="neutral">
              Review
            </Typography>
          </Box>
        </Box>
      </Box>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Tabs
          value={activeTab}
          onChange={(_, value) => handleTabChange(value as number)}
          sx={{ bgcolor: "background.body" }}
        >
          <TabList sx={{ mb: 2 }}>
            <Tab>Details</Tab>
            <Tab>Documentation</Tab>
            <Tab>Storage</Tab>
            <Tab>Review & Submit</Tab>
          </TabList>

          <TabPanel value={0}>
            <Card>
              <CardContent>
                <Typography level="title-lg" sx={{ mb: 2 }}>
                  Body Admission Details
                </Typography>
                <Typography level="body-sm" sx={{ mb: 3 }}>
                  {collectionData ?
                    "Creating admission from collection record. Some fields have been pre-populated." :
                    "Enter the basic information for the body admission"
                  }
                </Typography>

                {/* Collection Information Display */}
                {collectionData && (
                  <Alert variant="soft" color="primary" sx={{ mb: 3 }}>
                    <Box>
                      <Typography level="title-sm" sx={{ mb: 1 }}>
                        Collection Information
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid xs={12} sm={6}>
                          <Typography level="body-xs" color="neutral">Collection ID:</Typography>
                          <Typography level="body-sm" fontWeight="md">{collectionData.id}</Typography>
                        </Grid>
                        {collectionData.name && (
                          <Grid xs={12} sm={6}>
                            <Typography level="body-xs" color="neutral">Name:</Typography>
                            <Typography level="body-sm" fontWeight="md">{collectionData.name}</Typography>
                          </Grid>
                        )}
                        {collectionData.institution && (
                          <Grid xs={12} sm={6}>
                            <Typography level="body-xs" color="neutral">Institution:</Typography>
                            <Typography level="body-sm" fontWeight="md">{collectionData.institution}</Typography>
                          </Grid>
                        )}
                        {collectionData.arrivalTime && (
                          <Grid xs={12} sm={6}>
                            <Typography level="body-xs" color="neutral">Arrival Time:</Typography>
                            <Typography level="body-sm" fontWeight="md">
                              {format(new Date(collectionData.arrivalTime), 'PPp')}
                            </Typography>
                          </Grid>
                        )}
                        {collectionData.body?.trackingNumber && (
                          <Grid xs={12} sm={6}>
                            <Typography level="body-xs" color="neutral">Body Tracking Number:</Typography>
                            <Typography level="body-sm" fontWeight="md">{collectionData.body.trackingNumber}</Typography>
                          </Grid>
                        )}
                        {collectionData.body?.bodyTag?.tagNumber && (
                          <Grid xs={12} sm={6}>
                            <Typography level="body-xs" color="neutral">Body Tag Number:</Typography>
                            <Typography level="body-sm" fontWeight="md">{collectionData.body.bodyTag.tagNumber}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Box>
                  </Alert>
                )}

                <Grid container spacing={3}>
                  <Grid xs={12} md={6}>
                    <FormControl error={!!errors.facilityId}>
                      <FormLabel>Facility *</FormLabel>
                      <FacilitySelector
                        value={getValues("facilityId")}
                        onChange={(facilityId) => {
                          if (facilityId) {
                            setValue("facilityId", facilityId);
                            setSelectedFacilityId(facilityId);
                            showToastNotification(
                              "Facility selected",
                              `Selected facility ID: ${facilityId}`
                            );
                          }
                        }}
                        required={true}
                        error={errors.facilityId?.message}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} md={6}>
                    <FormControl error={!!errors.admissionType}>
                      <FormLabel>Admission Type *</FormLabel>
                      <Select
                        placeholder="Select admission type"
                        defaultValue="INITIAL"
                        {...register("admissionType")}
                        onChange={(_, value) =>
                          setValue(
                            "admissionType",
                            value as "INITIAL" | "POST_REFERRAL" | "TRANSFER"
                          )
                        }
                      >
                        <Option value="INITIAL">Initial</Option>
                        <Option value="POST_REFERRAL">Post Referral</Option>
                        <Option value="TRANSFER">Transfer</Option>
                      </Select>
                      {errors.admissionType && (
                        <FormHelperText>
                          {errors.admissionType.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid xs={12} md={6}>
                    <FormControl error={!!errors.deathRegisterNumber}>
                      <FormLabel>Death Register Number</FormLabel>
                      <Input
                        placeholder="e.g., DR2023-1234"
                        {...register("deathRegisterNumber")}
                      />
                      {errors.deathRegisterNumber && (
                        <FormHelperText>
                          {errors.deathRegisterNumber.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid xs={12} md={6}>
                    <FormControl error={!!errors.bodyId}>
                      <BodyTagSelectorField
                        name="bodyTagId"
                        scanType={BodyTagScanType.ADMISSION}
                        showResult={true}
                        onChange={async (
                          tag: BodyTag | null,
                          validationResult?: any
                        ) => {
                          if (tag) {
                            setScannedBarcode(tag.id);
                            setValue("bodyTagId", tag.id);
                            setValue("barcodeValue", tag.id);
                            console.log("Scanned BodyTag:\n", tag);

                            // Fetch body details associated with the tag
                            try {
                              const response = await fetch(`/api/body-tags/${tag.id}/details`);
                              if (response.ok) {
                                const tagDetails = await response.json();

                                if (tagDetails.bodyDetails && tagDetails.bodyDetails.id) {
                                  // Set the actual body ID from the associated body
                                  setValue("bodyId", tagDetails.bodyDetails.id);
                                  console.log("Associated Body ID:", tagDetails.bodyDetails.id);

                                  // Show success notification
                                  showToastNotification(
                                    "Body tag scanned successfully",
                                    `Body tag associated with body ID: ${tagDetails.bodyDetails.id}`
                                  );
                                } else {
                                  // Handle case where body tag doesn't have an associated body
                                  setValue("bodyId", "");
                                  showToastNotification(
                                    "Warning",
                                    "Selected body tag is not associated with any body"
                                  );
                                }
                              } else {
                                throw new Error("Failed to fetch body tag details");
                              }
                            } catch (error) {
                              console.error("Error fetching body details:", error);
                              showToastNotification(
                                "Error",
                                "Failed to fetch body details for the selected tag"
                              );
                            }
                          }
                        }}
                      />
                      {errors.bodyId && (
                        <FormHelperText color="danger">
                          {errors.bodyId.message}
                        </FormHelperText>
                      )}
                      <FormHelperText>
                        Scan the barcode on the body tag to automatically retrieve the associated body ID
                      </FormHelperText>
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
              <CardActions sx={{ justifyContent: "space-between", p: 2 }}>
                <Button variant="outlined" color="neutral">
                  Cancel
                </Button>
                <Button
                  endDecorator={<ArrowForward />}
                  onClick={() => handleTabChange(1)}
                >
                  Next: Documentation
                </Button>
              </CardActions>
            </Card>
          </TabPanel>

          <TabPanel value={1}>
            <Card>
              <CardContent>
                <Typography level="title-lg" sx={{ mb: 2 }}>
                  Documentation
                </Typography>
                <Typography level="body-sm" sx={{ mb: 3 }}>
                  Capture photos and add notes about the body condition
                </Typography>

                <Box sx={{ mb: 4 }}>
                  <Typography level="title-sm" sx={{ mb: 2 }}>
                    Photos *
                  </Typography>

                  <FileUploadComponent
                    value={photos}
                    onChange={(newPhotos) => {
                      setPhotos(newPhotos);
                      setValue("photos", newPhotos);
                    }}
                  />
                  {errors.photos && (
                    <FormHelperText color="danger">
                      {errors.photos.message}
                    </FormHelperText>
                  )}
                  <FormHelperText>
                    Take photos of the body bag and metallic body tag
                  </FormHelperText>
                </Box>

                <Grid container spacing={3}>
                  <Grid xs={12}>
                    <FormControl error={!!errors.bodyCondition}>
                      <FormLabel>Body Condition</FormLabel>
                      <Textarea
                        placeholder="Describe the condition of the body"
                        minRows={4}
                        {...register("bodyCondition")}
                      />
                      {errors.bodyCondition && (
                        <FormHelperText>
                          {errors.bodyCondition.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid xs={12}>
                    <FormControl error={!!errors.notes}>
                      <FormLabel>Additional Notes</FormLabel>
                      <Textarea
                        placeholder="Add any additional notes or observations"
                        minRows={4}
                        {...register("notes")}
                      />
                      {errors.notes && (
                        <FormHelperText>{errors.notes.message}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
              <CardActions sx={{ justifyContent: "space-between", p: 2 }}>
                <Button
                  variant="outlined"
                  color="neutral"
                  startDecorator={<ArrowBack />}
                  onClick={() => handleTabChange(0)}
                >
                  Previous
                </Button>
                <Button
                  endDecorator={<ArrowForward />}
                  onClick={() => handleTabChange(2)}
                >
                  Next: Storage
                </Button>
              </CardActions>
            </Card>
          </TabPanel>

          <TabPanel value={2}>
            <Card>
              <CardContent>
                <Typography level="title-lg" sx={{ mb: 2 }}>
                  Storage Information
                </Typography>
                <Typography level="body-sm" sx={{ mb: 3 }}>
                  Assign a fridge and staff member for the body
                </Typography>

                <Grid container spacing={3}>
                  <Grid xs={12} md={6}>
                    <FormControl error={!!errors.assignedFridge}>
                      <FormLabel>Assigned Fridge</FormLabel>
                      <UnoccupiedFridgeSelector
                        value={getValues("assignedFridge")}
                        onSelect={(fridgeId) => {
                          if (fridgeId) {
                            setValue("assignedFridge", fridgeId);
                            setSelectedFridgeId(fridgeId);
                            showToastNotification(
                              "Fridge selected",
                              `Selected fridge ID: ${fridgeId}`
                            );
                          }
                        }}
                        error={!!errors.assignedFridge}
                        helperText={errors.assignedFridge?.message}
                      />
                    </FormControl>
                  </Grid>

                  <Grid xs={12} md={6}>
                    <FormControl error={!!errors.temperature}>
                      <FormLabel>Body Temperature (°C)</FormLabel>
                      <Box sx={{ display: "flex", gap: 1 }}>
                        <Input
                          type="number"
                          placeholder="e.g., 4.2"
                          slotProps={{ input: { step: 0.1 } }}
                          {...register("temperature", {
                            valueAsNumber: true,
                          })}
                          sx={{ flexGrow: 1 }}
                        />
                        <IconButton variant="outlined">
                          <DeviceThermostat />
                        </IconButton>
                      </Box>
                      {errors.temperature && (
                        <FormHelperText>
                          {errors.temperature.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid xs={12} md={6}>
                    <FormControl error={!!errors.assignedToId}>
                      <FormLabel>Assigned Staff</FormLabel>
                      <UserSelectField
                        name="assignedToId"
                        control={form.control}
                        label="Select staff member"
                        placeholder="Search for staff members..."
                        onChange={(user) => {
                          if (user && !Array.isArray(user)) {
                            // If a user object is returned, extract the ID
                            setValue("assignedToId", user.id);
                            setSelectedStaffId(user.id);
                            showToastNotification(
                              "Staff member selected",
                              `Selected staff: ${user.name}`
                            );
                          }
                        }}
                        required={false}
                        helperText={errors.assignedToId?.message}
                      />
                    </FormControl>
                  </Grid>
                </Grid>

                <Sheet
                  variant="outlined"
                  color="neutral"
                  sx={{
                    mt: 3,
                    p: 2,
                    borderRadius: "md",
                    bgcolor: "background.level1",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                      mb: 1,
                    }}
                  >
                    <Kitchen color="primary" />
                    <Typography level="title-sm">Storage Guidelines</Typography>
                  </Box>
                  <Box component="ul" sx={{ pl: 2, m: 0 }}>
                    <Typography level="body-sm" component="li">
                      Bodies should be stored at temperatures between -4°C and
                      -2°C
                    </Typography>
                    <Typography level="body-sm" component="li">
                      Ensure proper labeling of the storage location
                    </Typography>
                    <Typography level="body-sm" component="li">
                      Bodies must be stored for a minimum of 7 days before
                      release
                    </Typography>
                    <Typography level="body-sm" component="li">
                      Check fridge temperature daily and report any anomalies
                    </Typography>
                  </Box>
                </Sheet>
              </CardContent>
              <CardActions sx={{ justifyContent: "space-between", p: 2 }}>
                <Button
                  variant="outlined"
                  color="neutral"
                  startDecorator={<ArrowBack />}
                  onClick={() => handleTabChange(1)}
                >
                  Previous
                </Button>
                <Button
                  endDecorator={<ArrowForward />}
                  onClick={() => handleTabChange(3)}
                >
                  Next: Review
                </Button>
              </CardActions>
            </Card>
          </TabPanel>

          <TabPanel value={3}>
            <Card>
              <CardContent>
                <Typography level="title-lg" sx={{ mb: 2 }}>
                  Review & Submit
                </Typography>
                <Typography level="body-sm" sx={{ mb: 3 }}>
                  Review the information before submitting the body admission
                </Typography>

                <Stack spacing={3}>
                  <Box>
                    <Typography level="title-sm" sx={{ mb: 2 }}>
                      Basic Information
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Facility:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {selectedFacilityId ? `Facility ID: ${selectedFacilityId}` : "Not selected"}
                        </Typography>
                      </Grid>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Body Tag ID:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {getValues("bodyTagId") || "Not scanned"}
                        </Typography>
                      </Grid>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Admission Type:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {getValues("admissionType")}
                        </Typography>
                      </Grid>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Death Register Number:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {getValues("deathRegisterNumber") || "Not provided"}
                        </Typography>
                      </Grid>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Barcode:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {getValues("barcodeValue") || "Not scanned"}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>

                  <Divider />

                  <Box>
                    <Typography level="title-sm" sx={{ mb: 2 }}>
                      Documentation
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Photos:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {photos.length} photos captured
                        </Typography>
                      </Grid>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Body Condition:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {getValues("bodyCondition") || "Not provided"}
                        </Typography>
                      </Grid>
                      <Grid xs={12}>
                        <Typography level="body-sm" color="neutral">
                          Notes:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {getValues("notes") || "Not provided"}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>

                  <Divider />

                  <Box>
                    <Typography level="title-sm" sx={{ mb: 2 }}>
                      Storage Information
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Assigned Fridge:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {selectedFridgeId ? `Fridge ID: ${selectedFridgeId}` : "Not assigned"}
                        </Typography>
                      </Grid>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Temperature:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {getValues("temperature") !== undefined
                            ? `${getValues("temperature")}°C`
                            : "Not recorded"}
                        </Typography>
                      </Grid>
                      <Grid xs={12} sm={6}>
                        <Typography level="body-sm" color="neutral">
                          Assigned Staff:
                        </Typography>
                        <Typography level="body-md" fontWeight="md">
                          {selectedStaffId ? `Staff ID: ${selectedStaffId}` : "Not assigned"}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>

                  <Alert
                    variant="soft"
                    color="warning"
                    startDecorator="⚠️"
                    sx={{ alignItems: "flex-start" }}
                  >
                    <Box>
                      <Typography level="title-sm">Important Notice</Typography>
                      <Typography level="body-sm">
                        By submitting this form, you confirm that all
                        information is accurate and complete. This admission
                        will be logged in the system and will be part of the
                        chain of custody.
                      </Typography>
                    </Box>
                  </Alert>

                  {/* Validation Errors */}
                  {!getValues("bodyId") && (
                    <Alert color="danger">
                      Body ID is required. Please go back to the Details tab and scan a valid body tag that is associated with a body.
                    </Alert>
                  )}
                  {!getValues("bodyTagId") && (
                    <Alert color="danger">
                      Body Tag ID is required. Please go back to the Details tab and scan a valid body tag.
                    </Alert>
                  )}
                  {!getValues("facilityId") && (
                    <Alert color="danger">
                      Facility is required. Please go back to the Details tab and select a facility.
                    </Alert>
                  )}
                  {photos.length === 0 && (
                    <Alert color="warning">
                      No photos uploaded. Please go back to the Documentation tab and upload at least one photo.
                    </Alert>
                  )}
                  {submitError && (
                    <Alert color="danger">
                      {submitError}
                    </Alert>
                  )}
                </Stack>
              </CardContent>
              <CardActions sx={{ justifyContent: "space-between", p: 2 }}>
                <Button
                  variant="outlined"
                  color="neutral"
                  startDecorator={<ArrowBack />}
                  onClick={() => handleTabChange(2)}
                >
                  Previous
                </Button>
                <Stack direction="row" spacing={1}>
                  {onCancel && (
                    <Button
                      variant="outlined"
                      color="neutral"
                      startDecorator={<Close />}
                      onClick={onCancel}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                  )}
                  <Button
                    type="submit"
                    startDecorator={isSubmitting ? null : <Save />}
                    loading={isSubmitting}
                    disabled={isSubmitting || !getValues("bodyId") || !getValues("bodyTagId") || !getValues("facilityId") || photos.length === 0}
                    color="primary"
                  >
                    {isSubmitting ? "Submitting..." : "Submit Admission"}
                  </Button>
                </Stack>
              </CardActions>
            </Card>
          </TabPanel>
        </Tabs>
      </form>

      {/* Camera Modal */}
      <Modal open={showCamera} onClose={() => setShowCamera(false)}>
        <ModalDialog size="lg">
          <ModalClose />
          <Typography level="title-lg" sx={{ mb: 1 }}>
            Capture Photo
          </Typography>
          <Typography level="body-sm" sx={{ mb: 3 }}>
            Take a photo of the body bag or metallic tag
          </Typography>
          <CameraCapture
            onCapture={handlePhotoCapture}
            onCancel={() => setShowCamera(false)}
          />
        </ModalDialog>
      </Modal>

      {/* Barcode Scanner Modal */}
      <Modal open={showScanner} onClose={() => setShowScanner(false)}>
        <ModalDialog size="lg">
          <ModalClose />
          <Typography level="title-lg" sx={{ mb: 1 }}>
            Scan Barcode
          </Typography>
          <Typography level="body-sm" sx={{ mb: 3 }}>
            Position the barcode in the scanner view
          </Typography>
          <BarcodeScanner
            onScan={(bodyTagRes) => {
              handleBarcodeScanned(bodyTagRes.barcode);
            }}
            onCancel={() => setShowScanner(false)}
          />
        </ModalDialog>
      </Modal>

      {/* Loading overlay */}
      {isLoading && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            bgcolor: "rgba(255, 255, 255, 0.7)",
            zIndex: 9999,
          }}
        >
          <Card>
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <CircularProgress />
                <Typography>Loading data...</Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Toast Notification */}
      {showToast && (
        <Box
          sx={{
            position: "fixed",
            bottom: 16,
            right: 16,
            zIndex: 9999,
          }}
        >
          <Alert
            variant="soft"
            color="success"
            sx={{ maxWidth: 400 }}
            endDecorator={
              <IconButton
                size="sm"
                variant="plain"
                color="neutral"
                onClick={() => setShowToast(false)}
              >
                <Close />
              </IconButton>
            }
          >
            <Box>
              <Typography level="title-sm">{toastMessage.title}</Typography>
              <Typography level="body-sm">{toastMessage.message}</Typography>
            </Box>
          </Alert>
        </Box>
      )}
    </Box>
  );
}
