"use client";

import { FC, useState } from 'react';
import { Button, Typography, Box, Alert } from '@mui/joy';
import { Icon } from '@iconify/react';

export interface CommandExecutorProps {
  command: string;
  description: string;
  onComplete: () => Promise<void> | void;
}

export const CommandExecutor: FC<CommandExecutorProps> = ({ command, description, onComplete }) => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [output, setOutput] = useState<string>('');

  const handleExecution = async () => {
    setIsExecuting(true);
    setOutput('');

    try {
      const response = await fetch('/api/system/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ command })
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to execute command');
        setIsExecuting(false);
        return;
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          const chunk = decoder.decode(value);
          setOutput(prev => prev + chunk);
        }
      }

      await onComplete();
      setIsCompleted(true);
    } catch (err: any) {
      setError(err.message || 'Failed to execute command');
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <Box sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 'sm' }}>
      <Typography level="body-sm" sx={{ mb: 1, color: 'neutral.500' }}>
        Command to execute:
      </Typography>
      <Box
        component="pre"
        sx={{
          p: 2,
          backgroundColor: 'background.level1',
          borderRadius: 'sm',
          overflow: 'auto',
          fontSize: 'sm',
          mb: 2
        }}
      >
        {command}
      </Box>
      
      <Typography level="body-md" sx={{ mb: 2 }}>
        {description}
      </Typography>

      {error && (
        <Alert color="danger" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {output && (
        <Box
          component="pre"
          sx={{
            p: 2,
            backgroundColor: '#f5f5f5',
            borderRadius: 'sm',
            overflow: 'auto',
            fontSize: 'sm',
            mb: 2,
            maxHeight: '200px'
          }}
        >
          {output}
        </Box>
      )}

      <Button
        onClick={handleExecution}
        disabled={isExecuting || isCompleted}
        loading={isExecuting}
        endDecorator={isCompleted ? <Icon icon="mdi:check" /> : undefined}
        color={isCompleted ? 'success' : 'primary'}
      >
        {isCompleted ? 'Completed' : 'Execute'}
      </Button>
    </Box>
  );
};

export default CommandExecutor; 