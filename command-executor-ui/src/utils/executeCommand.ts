import { spawn } from 'child_process';

/**
 * Executes a shell command and returns a ReadableStream of the output.
 * @param command The command to execute.
 * @returns A ReadableStream that streams the command's output.
 */
export function executeCommand(command: string): ReadableStream<Uint8Array> {
  const encoder = new TextEncoder();

  return new ReadableStream({
    start(controller) {
      const cmdParts = command.split(' ');
      const proc = spawn(cmdParts[0], cmdParts.slice(1), { shell: true });

      proc.stdout.on('data', (chunk) => {
        controller.enqueue(encoder.encode(chunk.toString()));
      });

      proc.stderr.on('data', (chunk) => {
        controller.enqueue(encoder.encode(`ERROR: ${chunk.toString()}`));
      });

      proc.on('close', (code) => {
        if (code === 0) {
          controller.close();
        } else {
          controller.error(new Error(`Command exited with code ${code}`));
        }
      });

      proc.on('error', (err) => {
        controller.error(err);
      });
    }
  });
} 