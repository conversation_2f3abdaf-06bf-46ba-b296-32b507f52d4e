{"compilerOptions": {"target": "ES2018", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "declaration": true, "outDir": "./dist"}, "include": ["src"], "exclude": ["node_modules", "dist"]}