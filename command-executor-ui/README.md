# Command Executor UI

A React component for executing and streaming command outputs with real-time feedback.

## Installation

```bash
npm install command-executor-ui
```

## Dependencies

This package requires the following peer dependencies:

- React ^18.0.0
- @mui/joy ^5.0.0
- @iconify/react ^4.0.0
- Next.js ^13.0.0

## Usage

```tsx
import { CommandExecutor } from 'command-executor-ui';

function MyComponent() {
  const handleComplete = async () => {
    console.log('Command execution completed');
  };

  return (
    <CommandExecutor
      command="npm install"
      description="Install dependencies"
      onComplete={handleComplete}
    />
  );
}
```

## Props

- `command`: The command to execute
- `description`: A description of what the command does
- `onComplete`: Callback function that runs after successful command execution

## Features

- Real-time command output streaming
- Error handling and display
- Loading and completion states
- Modern Material UI design using Joy UI

## License

MIT 