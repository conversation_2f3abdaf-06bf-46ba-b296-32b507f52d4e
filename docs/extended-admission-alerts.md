# Extended Admission Alert System

This documentation outlines the Extended Admission Alert System for the GP Pathology Application. The system is designed to automatically notify relevant staff about bodies that have been in storage for an extended period (by default, more than 7 days).

## Overview

When bodies remain in storage for longer than a defined threshold, it's important to notify the appropriate personnel for proper handling according to facility policy. This system:

1. Identifies bodies that have been in storage for more than the threshold period
2. Groups them by facility
3. Sends email notifications to relevant staff (administrators and pathologists)
4. Provides a dashboard interface to monitor and manage these notifications

## Components

The Extended Admission Alert System consists of several components:

### Backend Components

- **API Routes**:
  - `/api/notifications/extended-admissions`: Fetches and sends notifications for extended admissions
  - `/api/cron/extended-admission-alerts`: Endpoint for automated daily checks and notifications
  - `/api/cron/schedule`: Provides information about scheduled jobs

- **Services**:
  - `extendedAdmissionService`: Service to find bodies admitted longer than the threshold and send notifications
  - `emailTemplates`: Email template rendering for notifications

### Frontend Components

- **Dashboard** (`/dashboard/extended-admissions`): Shows an overview of bodies in extended storage
- **Alert Components**:
  - `ExtendedAdmissionAlert`: Component to display and manage extended admission alerts
  - `ExtendedAdmissionSettings`: Component for configuring alert settings

## Scheduled Jobs

The system uses cron jobs to automate the notification process:

- **Daily Check**: By default, runs daily at 9:00 AM
- **Configuration**: Settings can be adjusted in the admin dashboard
- **Setup**: Run the setup script to configure the scheduled jobs: `npm run setup-cron`

## Setting Up

1. **Environment Variables**:
   ```
   # Email settings
   SMTP_HOST=smtp.example.com
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-password
   SMTP_FROM=<EMAIL>
   
   # Cron job settings
   CRON_API_SECRET=your-secret-key
   NEXT_PUBLIC_APP_URL=https://your-app-url.com
   ```

2. **Install Dependencies**:
   ```bash
   npm install node-cron dotenv node-fetch
   ```

3. **Setup Cron Jobs**:
   ```bash
   npm run setup-cron
   ```

## Notification Workflow

1. The cron job triggers the `/api/cron/extended-admission-alerts` endpoint daily
2. The endpoint calls `extendedAdmissionService.sendNotificationsForAllFacilities()`
3. The service retrieves bodies that have been admitted for longer than the threshold
4. Bodies are grouped by facility
5. For each facility, the service sends email notifications to administrators and pathologists
6. Notification logs are recorded for audit purposes

## Configuration Options

You can configure the following settings:

- **Days Threshold**: Number of days after which alerts are triggered (default: 7)
- **Notification Time**: When daily notifications are sent
- **User Roles**: Which roles receive notifications
- **Notification Methods**: Email and/or dashboard alerts
- **Reminder Frequency**: How often reminders are sent (daily, weekdays, weekly)

## Email Templates

The system uses pre-defined email templates for notifications, which include:

- Body identification details
- Facility information
- Duration of storage
- Action buttons to view details
- List of all affected bodies (for batch notifications)

## Dashboard

The dashboard provides:

- Overview of bodies in extended storage
- Filtering by facility
- Manual notification triggering
- Settings configuration
- Logs of previous notifications

## Troubleshooting

- **Emails Not Sending**: Check SMTP configuration and logs
- **Missing Notifications**: Verify cron job is running properly
- **Dashboard Not Updating**: Clear browser cache or check API responses

## Security Considerations

- The cron job endpoints are protected by an API secret
- Only administrators and pathologists can access the dashboard
- Notifications contain minimal identifying information for privacy
- All actions are logged for audit purposes 