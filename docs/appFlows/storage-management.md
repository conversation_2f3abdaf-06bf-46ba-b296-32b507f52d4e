# Storage Management

## Overview
The Storage Management module provides comprehensive tools for monitoring, managing, and maintaining the cold storage facilities within the pathology system. This module ensures optimal storage conditions, efficient space utilization, and proper temperature control for all stored bodies.

## Key Components

### Fridges Dashboard
```mermaid
flowchart TD
    A[Fridges Dashboard] --> B[Fridge Status Overview]
    A --> C[Temperature Monitoring]
    A --> D[Capacity Utilization]
    A --> E[Maintenance Tracking]
    A --> F[Alert Management]
```

The Fridges Dashboard provides a comprehensive overview of all storage units in the facility:

- **Status Overview**: Visual representation of all fridges with color-coded status indicators
  - Operational (Green)
  - Maintenance (Yellow)
  - Offline (Red)
  - Temperature Warning (Orange)

- **Capacity Visualization**: 
  - Current occupancy rates
  - Available storage slots
  - Capacity planning indicators

- **Quick Actions**:
  - Add new fridge
  - Schedule maintenance
  - Generate reports
  - View alerts

### Fridge Management

#### Adding New Fridges
```mermaid
flowchart LR
    A[Add Fridge Button] --> B[Fridge Details Form]
    B --> C[Configuration Settings]
    C --> D[Temperature Thresholds]
    D --> E[Save & Activate]
```

Process for adding new storage units:
1. Enter fridge specifications
   - Model number
   - Serial number
   - Manufacturer
   - Installation date
   - Capacity (number of storage slots)
   - Physical location in facility
2. Configure operational parameters
   - Target temperature range
   - Alert thresholds
   - Maintenance schedule
3. Activate monitoring system
4. Assign responsible staff

#### Editing Fridge Details
```mermaid
flowchart LR
    A[Select Fridge] --> B[Edit Details]
    B --> C[Update Configuration]
    C --> D[Save Changes]
```

Allows modification of existing fridge information:
- Update specifications
- Adjust temperature thresholds
- Modify maintenance schedule
- Change status (operational, maintenance, offline)
- Reassign responsible staff

### Temperature Monitoring

#### Real-time Monitoring
```mermaid
flowchart TD
    A[Temperature Sensors] -->|Continuous Data| B[Monitoring System]
    B -->|Normal Range| C[Regular Logging]
    B -->|Outside Threshold| D[Alert Generation]
    D --> E[Notification System]
    E --> F[Responsible Staff]
    E --> G[Dashboard Alert]
```

Features of the temperature monitoring system:
- Continuous real-time temperature readings
- Historical temperature graphs
- Trend analysis
- Deviation alerts
- Automated notifications
- Mobile alerts for critical issues

#### Temperature History
```mermaid
graph TD
    A[Select Fridge] --> B[View Temperature History]
    B --> C[Filter by Date Range]
    B --> D[Export Data]
    B --> E[Generate Reports]
```

Comprehensive temperature history features:
- Detailed temperature logs
- Customizable date range selection
- Statistical analysis
- Compliance reporting
- Data export capabilities
- Anomaly highlighting

### Maintenance Management

#### Maintenance Scheduling
```mermaid
flowchart TD
    A[Maintenance Calendar] --> B[Schedule Maintenance]
    B --> C[Assign Technician]
    C --> D[Set Priority]
    D --> E[Generate Work Order]
    E --> F[Notification]
```

Maintenance management capabilities:
- Preventive maintenance scheduling
- Reactive maintenance tracking
- Technician assignment
- Priority setting
- Work order generation
- Maintenance history

#### Maintenance History
```mermaid
flowchart LR
    A[Select Fridge] --> B[View Maintenance History]
    B --> C[Filter by Type]
    B --> D[Filter by Date]
    B --> E[View Details]
```

Comprehensive maintenance records:
- Complete maintenance history
- Service documentation
- Parts replacement tracking
- Cost tracking
- Performance impact analysis
- Compliance documentation

### Alert Management

#### Alert Configuration
```mermaid
flowchart TD
    A[Alert Settings] --> B[Temperature Thresholds]
    A --> C[Notification Recipients]
    A --> D[Alert Priorities]
    A --> E[Escalation Rules]
```

Customizable alert system:
- Configurable temperature thresholds
- Multiple notification methods (email, SMS, in-app)
- Priority levels
- Escalation procedures
- Acknowledgment tracking

#### Alert History
```mermaid
flowchart LR
    A[Alert Log] --> B[Filter by Fridge]
    A --> C[Filter by Severity]
    A --> D[Filter by Date]
    A --> E[View Resolution]
```

Comprehensive alert tracking:
- Complete alert history
- Resolution documentation
- Response time tracking
- Pattern analysis
- Recurring issue identification

## Integration Points

### Body Management Module
- Updates fridge occupancy when bodies are admitted or released
- Links bodies to specific storage locations
- Provides temperature history for specific bodies

### Records Module
- Maintains comprehensive storage history for each body
- Documents temperature conditions during storage
- Records maintenance activities

### Reporting Module
- Generates temperature compliance reports
- Provides maintenance efficiency metrics
- Tracks storage utilization statistics

## User Roles and Permissions

### Admin
- Full access to all storage management functions
- Can add/remove fridges from the system
- Can configure alert thresholds and recipients
- Access to all maintenance records

### Morgue Staff
- View fridge status and availability
- Assign bodies to available storage slots
- Receive and acknowledge alerts
- Request maintenance

### Maintenance Staff
- View maintenance schedules and work orders
- Update maintenance records
- Receive maintenance alerts
- View temperature data

## Mobile Capabilities
- Remote temperature monitoring
- Mobile alerts and notifications
- Maintenance task management
- Quick status checks from anywhere

## Compliance and Reporting
- Automated temperature logging for regulatory compliance
- Customizable reports for different regulatory requirements
- Audit trail for all temperature and maintenance activities
- Documentation of corrective actions for temperature deviations 