# Body Management

## Overview
The Body Management module is a core component of the GP Pathology System, handling the complete lifecycle of body processing from collection to release. This module implements the Integrated Metallic Body Identification and Release Management System for the Gauteng Department of Health Forensic Pathology Service Facilities, ensuring proper tracking, documentation, and handling of all bodies within the facility.

## Key Workflows

### Body Collection from Crime Scene and Hospital
```mermaid
flowchart TD
    A[External Location] -->|Body Collected| B[Collection Form]
    B -->|Details Recorded| C[Chain of Custody]
    C -->|Transport| D[Facility Arrival]
    D -->|Ready for Admission| E[Body Admission]
```

#### Collection Process
1. **Initiation**: Staff receives notification of body requiring collection
2. **Documentation**: Collection details recorded in the system
   - **Capture Employee Information**:
     - PERSAL number
     - Name and Surname
     - Name of Institution
     - Vehicle Registration
   - Capture arrival time at the scene
   - Capture GPS coordinates
   - Capture deceased body and facial picture
   - **Scan metallic body tag** allocated to the deceased body or skeletal/body parts
3. **Special Handling**: For skeletal remains, body parts, decomposed or charred bodies, store in freezer fridge and follow standard procedures
4. **Chain of Custody**: Documentation of all handlers during transport
5. **Transport**: Secure movement to the facility
6. **Arrival Confirmation**: System update upon arrival at facility

#### Collection Screen Features
- Digital collection form with required fields
- Metallic body tag scanning functionality
- Photo documentation capability
- Electronic signature for chain of custody
- GPS location recording
- Offline mode for remote collections

### Body Admission
```mermaid
flowchart TD
    A[Facility Arrival] -->|Begin Admission| B[Admission Form]
    B -->|Details Recorded| C[Body Tag Assignment]
    C -->|Scan Fridge Bar Code| D[Fridge Assignment]
    D -->|Registration Complete| E[Update Records]
    E -->|Notification| F[Relevant Staff]
```

#### Admission Process
1. **Verification**: Confirm collection details are accurate
2. **Registration**: Complete admission form with detailed information
   - Admit the body to the fridge for seven days
   - **Scan the body cooler fridge bar code**
   - Take photo of the body bag reflecting death register number from the metallic body tag
3. **Identification Timeline**: If the body is not identified after seven days, remove to body-to-body freezer fridge
   - Scan the body cooler fridge bar code again
   - Take another photo of the body bag reflecting Death Register number from the metallic body tag
4. **Record Creation**: Generate comprehensive admission record
5. **Notification**: Alert relevant staff of new admission

#### Admission Screen Features
- Integrated barcode/RFID scanning for metallic body tags
- Fridge bar code scanning functionality
- Auto-generation of unique body tags
- Integration with fridge management for capacity checking
- Digital body chart for condition documentation
- Photo documentation capability with death register number verification

### Body Release
```mermaid
flowchart TD
    A[Release Request] -->|Verification| B[Authorization]
    B -->|Approved| C[Locate Body]
    C -->|Scan Fridge Bar Code| D[Verify Identity]
    D -->|Documentation| E[Handover]
    E -->|Update Records| F[Archive Case]
```

#### Release Process
1. **Request**: System receives release request
2. **Verification**: Confirm all required procedures complete
   - Identification confirmed
   - Required examinations completed
   - Documentation finalized
   - Legal requirements satisfied
3. **Body Location**: Locate the body via scanning detector
   - Scan the body cooler fridge bar code
   - Take photo of the body bag reflecting Death Register number from the metallic body tag
4. **Funeral Home Documentation**:
   - Take photo of funeral undertaker vehicle registration
   - Handover the body to the Funeral Undertaker
   - Security Officer to scan the body using portable scanning detector
5. **Record Update**: System updated to reflect release
6. **Case Closure**: Case marked as complete and archived

#### Release Screen Features
- Scanning detector integration
- Fridge bar code scanning
- Comprehensive release form
- Electronic signature capture
- Vehicle registration documentation
- Security verification with portable scanning

### Body Referral for X-Ray Service and Specimen Taking
```mermaid
flowchart TD
    A[Referral Request] -->|Approval| B[Locate Body]
    B -->|Scan Body| C[Prepare for Transport]
    C -->|Document Details| D[Transport to Referral Institution]
    D -->|Service Completed| E[Return to Facility]
    E -->|Follow Admission Steps| F[Update Records]
```

#### Referral Process
1. **Request**: System receives referral request for X-Ray or specimen collection
2. **Body Location**: Locate the body via scanning detector
3. **Documentation**: Capture Employee information
   - PERSAL Number
   - Name and Surname
   - Name of Institution the body referred to
   - Reference of specimen kit number or evidence bag serial number
   - Vehicle Registration
4. **Verification**: Take photo of the body bag reflecting Death Register number as per metallic body tag
5. **Security**: Security Officer to scan the body using portable scanning detector
6. **Return Procedure**: When returning from referral Institution, follow all body admission steps

#### Referral Screen Features
- Integration with X-Ray and laboratory systems
- Specimen kit tracking
- Evidence bag serial number recording
- Chain of custody documentation
- Portable scanning detector integration
- Return workflow automation

## Integration Points

### Records Module
- All body management activities create entries in the Records module
- Comprehensive history available for audit and reference
- Search functionality to locate specific cases

### Reporting
- Generates statistics on processing times
- Tracks collection, admission, release, and referral volumes
- Provides data for operational analysis

## User Roles and Permissions

### Admin
- Full access to all body management functions
- Can override standard procedures in exceptional circumstances
- Access to audit logs and system configurations

### Morgue Staff
- Primary users of body management module
- Can perform collections, admissions, releases, and referrals
- Limited override capabilities

### Medical Examiner
- Can view body details
- Can request specimens and X-Rays
- Can authorize releases

## Security and Compliance

### Data Protection
- All personal information encrypted
- Access logs maintained for all records
- Role-based access controls

### Compliance Features
- Configurable workflows to meet Gauteng Department of Health regulations
- Comprehensive audit trails
- Required field enforcement
- Digital signatures for legal documentation

## Mobile Capabilities
- Collection process available on mobile devices
- Barcode/RFID scanning for metallic body tags
- Fridge bar code scanning
- Photo documentation from mobile devices
- Offline functionality with synchronization

## Metallic Body Tag System

### Tag Features
- Unique identifier for each body
- Scannable via portable detector
- Links to Death Register number
- Resistant to environmental conditions
- Secure attachment methods

### Scanning Technology
- Portable scanning detectors for security officers
- Fixed scanning stations at key locations
- Mobile scanning capability for field operations
- Integration with facility security systems 