# Main Dashboard

## Overview
The Main Dashboard serves as the central command center for the GP Pathology System. It provides users with a comprehensive overview of the facility's operations, key metrics, and quick access to all system modules.

## Key Components

### Metrics and Statistics
The dashboard prominently displays critical operational metrics:

- **Total Bodies**: The current count of bodies in the system
- **Active Cases**: Number of cases currently being processed
- **Pending Identification**: Bodies awaiting formal identification
- **Processing Time**: Average time bodies remain in the facility
- **Storage Utilization**: Percentage of storage capacity currently in use

### Visual Analytics

#### Temperature Monitoring
```mermaid
graph LR
    A[Temperature Sensors] -->|Real-time data| B[Temperature Chart]
    B -->|Alert if outside range| C[Notification System]
```

- Real-time temperature readings from all fridges
- Historical temperature trends
- Color-coded indicators for normal/warning/critical temperatures
- Configurable alert thresholds

#### Admission Trends
```mermaid
line-chart
    title Admissions Over Time
    x-axis Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec
    y-axis 0 10 20 30 40 50
    line Admissions 10 15 20 18 25 30 28 32 38 35 40 45
```

- Daily/weekly/monthly admission rates
- Comparative analysis with previous periods
- Seasonal trend identification
- Capacity planning insights

### Recent Activities Feed
A chronological display of recent system activities:
- New admissions
- Collections
- Releases
- Specimen collections
- X-ray referrals
- System alerts and notifications

### Quick Action Buttons
Prominently displayed buttons for common tasks:
- Register New Admission
- Record Collection
- Process Release
- Create X-ray Referral
- Record Specimen Collection

## User Experience

### Personalization
- Configurable dashboard layouts
- User-specific metric highlighting
- Role-based dashboard views

### Notifications
```mermaid
flowchart TD
    A[System Events] --> B[Notification Center]
    B --> C{Priority Level}
    C -->|High| D[Push Alert]
    C -->|Medium| E[Dashboard Badge]
    C -->|Low| F[Activity Feed]
```

- Real-time system alerts
- Task reminders
- Maintenance notifications
- Temperature warnings

### Navigation
The dashboard provides intuitive navigation to all system modules through:
- Side navigation menu
- Quick access cards
- Recent activity links
- Search functionality

## Technical Implementation
The dashboard utilizes advanced data visualization libraries and real-time data processing to ensure users always have the most current information:

- Real-time data updates without page refresh
- Interactive charts and graphs
- Responsive design for all device sizes
- Optimized performance for quick loading

## Role-Based Views
Different user roles see customized dashboard views:

- **Admin**: Complete metrics, system health, and user activity
- **Morgue Staff**: Body-related metrics, temperature readings, and task lists
- **Researchers**: Research-relevant metrics and knowledge base access

## Integration Points
The dashboard integrates with all other system modules:
- Pulls metrics from Records module
- Displays alerts from Fridges module
- Shows recent entries from all modules
- Provides navigation to all system areas 