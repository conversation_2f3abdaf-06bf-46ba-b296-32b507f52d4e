# Entity Relationship Diagram

## Overview
This diagram shows the key relationships between core entities in the pathology management system.

## Mermaid Diagram

```mermaid
erDiagram
    USER ||--o{ BODY_COLLECTION : "collects"
    USER ||--o{ BODY_ADMISSION : "admits"
    USER ||--o{ BODY_RELEASE : "releases"
    USER ||--o{ BODY_REFERRAL : "refers"
    
    BODY_COLLECTION ||--|| BODY : "documents"
    BODY ||--o{ BODY_ADMISSION : "has"
    BODY ||--o{ BODY_REFERRAL : "undergoes"
    BODY ||--o{ BODY_RELEASE : "is released"
    
    BODY_COLLECTION {
        string id PK
        string userId FK
        string bodyId FK
        datetime collectionTime
        string collectionType
        string status
    }
    
    BODY {
        string id PK
        string tagNumber
        string currentStatus
        string facilityId FK
    }
    
    BODY_ADMISSION {
        string id PK
        string bodyId FK
        string facilityId FK
        string bodyTagId FK
        string status
        datetime admissionDate
    }
    
    BODY_REFERRAL {
        string id PK
        string bodyId FK
        string referredById FK
        string assignedToId FK
        string status
        string referralType
    }
    
    BODY_RELEASE {
        string id PK
        string bodyId FK
        string facilityId FK
        string releasedToName
        string status
        datetime releaseDate
    }
    
    FACILITY ||--o{ BODY_ADMISSION : "processes"
    FACILITY ||--o{ BODY_RELEASE : "manages"
    
    FACILITY {
        string id PK
        string name
        string type
        string status
    }
    
    USER ||--o{ FACILITY : "works at"
```

## Relationship Explanations

1. **User Relationships**
   - Users can collect, admit, release, and refer bodies
   - Users are associated with facilities

2. **Body Relationships**
   - A body goes through multiple stages:
     - Collection
     - Admission
     - Potential Referral
     - Release

3. **Facility Relationships**
   - Facilities process body admissions
   - Facilities manage body releases
   - Users are associated with facilities

## Key Observations
- Flexible, normalized data model
- Clear separation of concerns
- Comprehensive tracking of body movement
- Support for complex workflows

## Advanced Data Modeling

### Data Integrity and Constraints

#### User Model Constraints
- **Email Uniqueness**: Enforced at database level
- **Role-Based Access**: Predefined enum restrictions
- **Account Status**: Managed through `UserStatus`

#### Body Model Validation
- **Unique Identifiers**
  - Cryptographic hash generation
  - Cross-referencing mechanisms
- **State Transition Rules**
  - Enforced through enum-based status tracking

### Complex Relationship Patterns

```mermaid
erDiagram
    USER ||--o{ BODY_COLLECTION : "initiates"
    USER ||--o{ BODY_ADMISSION : "processes"
    USER ||--o{ BODY_REFERRAL : "manages"
    
    BODY ||--o{ BODY_COLLECTION : "documented in"
    BODY ||--o{ BODY_ADMISSION : "enters"
    BODY ||--o{ BODY_REFERRAL : "subject of"
    
    FACILITY ||--o{ BODY_ADMISSION : "receives"
    FACILITY ||--o{ BODY_STORAGE : "maintains"
    
    BODY_COLLECTION }|--|| BODY_TAG : "generates"
    BODY_TAG ||--o{ BODY_ADMISSION : "associates with"
```

## Data Validation Strategies

### Prisma Schema Validation Patterns
1. **Mandatory Fields**
   - Use `@default()` for automatic value generation
   - Implement required field constraints
2. **Unique Constraints**
   - `@unique` decorator for preventing duplicates
3. **Relationship Integrity**
   - Cascading delete options
   - Referential integrity checks

### Advanced Query Optimization

```mermaid
flowchart TD
    QUERY[Initial Query] --> INDEXING{Indexed Fields}
    INDEXING -->|Efficient| FAST_RETRIEVAL[Fast Data Retrieval]
    INDEXING -->|Non-Indexed| FULL_SCAN[Potential Performance Impact]
    
    FAST_RETRIEVAL --> CACHING[Query Result Caching]
    FULL_SCAN --> OPTIMIZATION[Query Optimization]
```

## Performance Considerations
- Selective indexing
- Denormalization for read-heavy operations
- Caching strategies
- Horizontal scaling considerations

## Data Privacy and Security
- **Encryption**
  - At-rest encryption for sensitive fields
  - Tokenization of personal identifiers
- **Audit Trailing**
  - Comprehensive change tracking
  - Immutable log entries

## Recommended Data Architecture Enhancements
- Event-sourcing for critical models
- Implement data anonymization techniques
- Advanced access control matrices
- Microservice-compatible data models

## Potential Future Extensions
- Temporal tables for historical tracking
- Geospatial indexing
- Machine learning feature vectors
- Distributed database compatibility
