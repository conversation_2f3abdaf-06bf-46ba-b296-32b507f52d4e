Certainly! I'll create a route handler for Third Party REST API Requests Authentication using Next.js 13 App Router. I'll reference the existing code and create a new file for this purpose.

First, let's create a new file called `route.ts` in the following directory:

```
src/app/api/api-client/authenticate/route.ts
```

Now, let's implement the route handler:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import ThirdPartyAuthService from './ThirdPartyAuthService';

export async function POST(req: NextRequest) {
  try {
    const { type, credentials } = await req.json();

    const authResult = await ThirdPartyAuthService.authenticate({ type, credentials });

    return NextResponse.json(authResult, { status: 200 });
  } catch (error) {
    console.error('Authentication error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { type, credentials, refreshToken } = await req.json();

    const authResult = await ThirdPartyAuthService.refreshToken({ type, credentials }, refreshToken);

    return NextResponse.json(authResult, { status: 200 });
  } catch (error) {
    console.error('Token refresh error:', error);
    return NextResponse.json(
      { error: 'Token refresh failed' },
      { status: 500 }
    );
  }
}
```

This route handler uses the `ThirdPartyAuthService` we previously defined. Here's a reference to the relevant code:


```19:262:src/app/api/api-client/authenticate/route.ts
class ThirdPartyAuthService {
  private static instance: ThirdPartyAuthService;
  private axiosInstance: AxiosInstance;
  private tokenCache: NodeCache;

  private constructor() {
    this.axiosInstance = axios.create();
    this.tokenCache = new NodeCache({ stdTTL: 3600 }); // Default TTL: 1 hour
  }

  public static getInstance(): ThirdPartyAuthService {
    if (!ThirdPartyAuthService.instance) {
      ThirdPartyAuthService.instance = new ThirdPartyAuthService();
    }
    return ThirdPartyAuthService.instance;
  }

  public async authenticate(config: AuthConfig): Promise<AuthResult> {
    const cacheKey = this.generateCacheKey(config);
    const cachedToken = this.tokenCache.get<AuthResult>(cacheKey);

    if (cachedToken) {
      return cachedToken;
    }

    let result: AuthResult;
    switch (config.type) {
      case 'basic':
        result = await this.handleBasicAuth(config.credentials);
        break;
      case 'oauth2':
        result = await this.handleOAuth2(config.credentials);
        break;
      case 'apiKey':
        result = await this.handleApiKey(config.credentials);
        break;
      case 'bearerToken':
        result = await this.handleBearerToken(config.credentials);
        break;
      case 'jwt':
        result = await this.handleJWT(config.credentials);
        break;
      case 'oidc':
        result = await this.handleOIDC(config.credentials);
        break;
      case 'awsIam':
        result = await this.handleAWSIAM(config.credentials);
        break;
      case 'azureAd':
        result = await this.handleAzureAD(config.credentials);
        break;
      case 'gcpIam':
        result = await this.handleGCPIAM(config.credentials);
        break;
      default:
        throw new Error('Unsupported authentication type');
    }

    this.tokenCache.set(cacheKey, result, result.expiresAt ? result.expiresAt - Date.now() : undefined);
    return result;
  }

  private generateCacheKey(config: AuthConfig): string {
    return `${config.type}_${JSON.stringify(config.credentials)}`;
  }

  private async handleBasicAuth(credentials: Record<string, string>): Promise<AuthResult> {
    const { username, password } = credentials;
    const token = Buffer.from(`${username}:${password}`).toString('base64');
    return { token };
  }

  private async handleOAuth2(credentials: Record<string, string>): Promise<AuthResult> {
    const { clientId, clientSecret, tokenUrl, code, redirectUri, grantType = 'authorization_code' } = credentials;
    const params = new URLSearchParams({
      grant_type: grantType,
      client_id: clientId,
      client_secret: clientSecret,
      code,
      redirect_uri: redirectUri,
    });

    const response = await this.axiosInstance.post(tokenUrl, params.toString(), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    return {
      token: response.data.access_token,
      expiresAt: Date.now() + response.data.expires_in * 1000,
      refreshToken: response.data.refresh_token,
    };
  }

  private async handleApiKey(credentials: Record<string, string>): Promise<AuthResult> {
    return { token: credentials.apiKey };
  }

  private async handleBearerToken(credentials: Record<string, string>): Promise<AuthResult> {
    return { token: credentials.token };
  }

  private async handleJWT(credentials: Record<string, string>): Promise<AuthResult> {
    const { payload, secret, issuer, audience } = credentials;
    const alg = 'RS256';
    const key = await crypto.subtle.importKey(
      'pkcs8',
      Buffer.from(secret, 'base64'),
      { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const jwt = await new SignJWT(JSON.parse(payload))
      .setProtectedHeader({ alg })
      .setIssuedAt()
      .setIssuer(issuer)
      .setAudience(audience)
      .setExpirationTime('1h')
      .sign(key);

    return { token: jwt, expiresAt: Date.now() + 3600000 }; // 1 hour expiration
  }

  private async handleOIDC(credentials: Record<string, string>): Promise<AuthResult> {
    const { clientId, discoveryUrl, redirectUri } = credentials;
    const oidcConfig = await this.fetchOIDCConfiguration(discoveryUrl);
    const authUrl = this.generateOIDCAuthUrl(oidcConfig, clientId, redirectUri);
    return { token: authUrl }; // Return the auth URL for OIDC flow initiation
  }

  private async handleAWSIAM(credentials: Record<string, string>): Promise<AuthResult> {
    const { accessKeyId, secretAccessKey, region } = credentials;
    // In a real-world scenario, you'd use the AWS SDK here
    return {
      token: JSON.stringify({
        accessKeyId,
        secretAccessKey,
        region,
        sessionToken: `placeholder-session-token-${uuidv4()}`,
      }),
      expiresAt: Date.now() + 3600000, // 1 hour expiration
    };
  }

  private async handleAzureAD(credentials: Record<string, string>): Promise<AuthResult> {
    const { tenantId, clientId, clientSecret, resource } = credentials;
    const tokenEndpoint = `https://login.microsoftonline.com/${tenantId}/oauth2/token`;
    const response = await this.axiosInstance.post(tokenEndpoint, new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: clientId,
      client_secret: clientSecret,
      resource: resource
    }));

    return {
      token: response.data.access_token,
      expiresAt: Date.now() + response.data.expires_in * 1000,
    };
  }

  private async handleGCPIAM(credentials: Record<string, string>): Promise<AuthResult> {
    const serviceAccountKey = JSON.parse(credentials.serviceAccountKey);
    const client = new JWT({
      email: serviceAccountKey.client_email,
      key: serviceAccountKey.private_key,
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    });

    const response = await client.authorize();
    return {
      token: response.access_token,
      expiresAt: response.expiry_date,
    };
  }

  private async fetchOIDCConfiguration(discoveryUrl: string): Promise<any> {
    const response = await this.axiosInstance.get(discoveryUrl);
    return response.data;
  }

  private generateOIDCAuthUrl(oidcConfig: any, clientId: string, redirectUri: string): string {
    const authEndpoint = oidcConfig.authorization_endpoint;
    const scope = 'openid profile email';
    const state = crypto.randomBytes(16).toString('hex');
    const nonce = crypto.randomBytes(16).toString('hex');

    return `${authEndpoint}?` +
      `client_id=${encodeURIComponent(clientId)}` +
      `&redirect_uri=${encodeURIComponent(redirectUri)}` +
      `&scope=${encodeURIComponent(scope)}` +
      `&response_type=code` +
      `&state=${state}` +
      `&nonce=${nonce}`;
  }

  public async refreshToken(config: AuthConfig, refreshToken: string): Promise<AuthResult> {
    if (config.type !== 'oauth2') {
      throw new Error('Token refresh is only supported for OAuth2');
    }

    const { clientId, clientSecret, tokenUrl } = config.credentials;
    const params = new URLSearchParams({
      grant_type: 'refresh_token',
      client_id: clientId,
      client_secret: clientSecret,
      refresh_token: refreshToken,
    });

    const response = await this.axiosInstance.post(tokenUrl, params.toString(), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    const result = {
      token: response.data.access_token,
      expiresAt: Date.now() + response.data.expires_in * 1000,
      refreshToken: response.data.refresh_token || refreshToken,
    };

    const cacheKey = this.generateCacheKey(config);
    this.tokenCache.set(cacheKey, result, result.expiresAt - Date.now());

    return result;
  }

  public async verifyToken(token: string, publicKey: string): Promise<boolean> {
    try {
      await jwtVerify(token, await crypto.subtle.importKey(
        'spki',
        Buffer.from(publicKey, 'base64'),
        { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-256' },
        true,
        ['verify']
      ));
      return true;
    } catch (error) {
      console.error('Token verification failed:', error);
      return false;
    }
  }

  public clearCache(): void {
    this.tokenCache.flushAll();
  }
}
```


The route handler includes two functions:

1. `POST`: Handles initial authentication requests.
2. `PUT`: Handles token refresh requests.

Both functions use the `ThirdPartyAuthService` to perform the actual authentication or token refresh operations.

To use this route handler, you would send POST or PUT requests to `/api/api-client/authenticate` with the appropriate body:

For authentication:
```json
{
  "type": "oauth2",
  "credentials": {
    "clientId": "your_client_id",
    "clientSecret": "your_client_secret",
    "tokenUrl": "https://api.example.com/oauth/token",
    "code": "authorization_code",
    "redirectUri": "https://your-app.com/callback"
  }
}
```

For token refresh:
```json
{
  "type": "oauth2",
  "credentials": {
    "clientId": "your_client_id",
    "clientSecret": "your_client_secret",
    "tokenUrl": "https://api.example.com/oauth/token"
  },
  "refreshToken": "your_refresh_token"
}
```

This implementation allows for flexible authentication handling for various third-party APIs, leveraging the `ThirdPartyAuthService` we previously defined.