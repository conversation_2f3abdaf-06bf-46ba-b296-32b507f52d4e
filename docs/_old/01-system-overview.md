# GP Pathology System Overview

## Introduction
The GP Pathology System is an enterprise-grade solution for managing pathology operations, body collections, referrals, and facility management. This document provides a comprehensive overview of the system's components, data models, and functionalities.

## Core Components

### 1. User Management
#### User Profiles
- Unique CUID-based identification
- Personal Information:
  - Name, email, password (encrypted)
  - Persal number for government identification
  - Contact details: phone, address, emergency contact
  - Professional details: position, hire date, department
- Profile customization: profile image, preferences
- Employment tracking: date of birth, hire date

#### Role-Based Access Control
- **Admin**: Full system access and configuration
- **Pathologist**: Medical examination and reporting
- **Morgue Staff**: Body and facility management
- **Security Staff**: Access control and verification
- **Field Employee**: Collections and transport
- Status states: ACTIVE, INACTIVE, SUSPENDED

### 2. Body Management
#### Collection System
- Collection Types:
  - CRIME_SCENE: Evidence preservation protocols
  - HOSPITAL: Medical documentation
  - OTHER: Special circumstances

#### Status Workflow
1. COLLECTED: Initial intake and documentation
2. ADMITTED: Formal registration and processing
3. IN_STORAGE: Active storage management
4. PENDING_RELEASE: Pre-release procedures
5. SECURITY_VERIFIED: Final verification
6. RELEASED: Completion and handover
7. REFERRED: External processing

### 3. Referral System
#### Referral Types
- LODOX: Full-body scanning
- XRAY: Specific area examination
- SPECIMEN: Laboratory analysis
- OTHER: Special procedures

#### Process Management
- Status tracking:
  1. PENDING: Initial request
  2. IN_PROGRESS: Active processing
  3. COMPLETED: Finalized results
  4. RETURNED: Additional requirements
  5. CANCELLED: Terminated process
- Dual assignment system:
  - Referring staff member
  - Assigned pathologist/specialist

### 4. Facility Management
#### Facility Types
- MORGUE: Primary operations
- STORAGE: Long-term preservation
- PROCESSING: Examination and analysis
- TEMPORARY: Overflow and emergency

#### Infrastructure Management
- Fridge Status Monitoring:
  - AVAILABLE: Ready for use
  - OCCUPIED: In active use
  - MAINTENANCE: Under service
  - OFFLINE: Not operational
- Equipment Tracking:
  - SCANNER: Imaging equipment
  - COMPUTER: Data processing
  - PRINTER: Documentation
  - CAMERA: Visual documentation
  - MEDICAL_DEVICE: Specialized equipment

### 5. Document Management
#### Document Categories
- POLICY: Organizational guidelines
- PROCEDURE: Standard operations
- FORM: Data collection
- REPORT: Analysis and findings
- CERTIFICATE: Official documentation

#### System Features
- Version control and tracking
- Digital signature support
- Secure storage and retrieval
- Access control and auditing

### 6. Operations Management
#### Shift Management
- Shift Types:
  - MORNING: Day operations
  - AFTERNOON: Transition period
  - NIGHT: After-hours coverage
  - ON_CALL: Emergency response

#### Notification System
Priority Levels:
- LOW: Information updates
- MEDIUM: Standard alerts
- HIGH: Urgent attention
- URGENT: Immediate action

## System Architecture
Built on modern, scalable technologies:
- **Frontend**: Next.js App Router
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js with session management
- **API**: RESTful endpoints with TypeScript
- **Security**: Role-based access control (RBAC)
- **Monitoring**: Comprehensive audit logging

## Integration Points
- External laboratory systems
- Government databases
- Healthcare information systems
- Emergency services
- Security systems

## Getting Started
Detailed documentation for each component:
- [User Guide](02-user-guide.md)
- [Facility Management](03-facility-management.md)
- [Body Processing](04-body-processing.md)
- [Referral System](05-referral-system.md)
- [Document Management](06-document-management.md)
