# Multi-Factor Authentication (MFA) System

## Table of Contents
- [Overview](#overview)
- [Components](#components)
- [Authentication Flow](#authentication-flow)
- [Security Features](#security-features)
- [API Reference](#api-reference)
- [Database Schema](#database-schema)
- [User Interface](#user-interface)
- [Integration Guide](#integration-guide)
- [Troubleshooting](#troubleshooting)

## Overview

The Multi-Factor Authentication (MFA) system provides an additional layer of security by requiring users to verify their identity using multiple authentication methods. Our implementation uses Time-based One-Time Passwords (TOTP) as the second factor, with backup codes for account recovery.

### Key Features
- TOTP-based authentication via authenticator apps (Google Authenticator, Authy, etc.)
- One-time backup codes system
- QR code setup for easy onboarding
- Secure secret storage
- Session-based verification

## Components

### MFA Setup Component
Located in `src/components/mfa/MfaSetup.tsx`, handles the initial MFA configuration:
- QR code generation
- Secret key management
- Initial verification
- Backup codes generation

### MFA Verification Component
Located in `src/components/mfa/MfaVerification.tsx`, manages the login verification:
- TOTP validation
- Backup code verification
- Session management

### MFA Management Components
- `src/components/mfa/MfaDisable.tsx`: Handles disabling MFA
- `src/components/mfa/BackupCodesDisplay.tsx`: Displays and manages backup codes

## Authentication Flow

1. **Initial Login**
   ```mermaid
   sequenceDiagram
       User->>System: Enter email/password
       System->>Database: Validate credentials
       Database-->>System: MFA status
       System->>User: Request 2FA if enabled
   ```

2. **MFA Setup Process**
   - User initiates setup from settings (`/account/mfa` page)
   - System generates unique secret via `api/auth/mfa/setup`
   - QR code displayed for scanning
   - User verifies with initial token via `api/auth/mfa/verify`
   - Backup codes generated and stored

3. **MFA Verification Process**
   - User submits TOTP or backup code during login
   - System validates token via `api/auth/mfa/validate`
   - Session updated with MFA status
   - Access granted upon success

## Security Features

### TOTP Implementation
- Uses the [otplib](https://github.com/yeojz/otplib) library
- 30-second token validity
- SHA-1 hashing algorithm
- 6-digit tokens
- ±1 window tolerance

### Backup Codes
- 10 one-time use codes generated with [nanoid](https://github.com/ai/nanoid)
- 10 characters each
- Automatically invalidated after use
- Secure storage with database encryption

### Session Security
- MFA verification status in NextAuth.js session
- JWT-based session storage
- Rate limiting on verification attempts
- Protection against replay attacks

## API Reference

### Setup Endpoint
```typescript
// GET /api/auth/mfa/setup
// src/app/api/auth/mfa/setup/route.ts
// Requires authenticated user
// Response:
{
  secret: string,
  qrCode: string  // DataURL for QR code
}
```

### Verify Endpoint
```typescript
// POST /api/auth/mfa/verify
// src/app/api/auth/mfa/verify/route.ts
// Requires authenticated user
// Request:
{
  token: string
}
// Response:
{
  success: boolean,
  backupCodes: string[]
}
```

### Validate Endpoint
```typescript
// POST /api/auth/mfa/validate
// src/app/api/auth/mfa/validate/route.ts
// Request:
{
  email: string,
  token: string,
  isBackupCode?: boolean
}
// Response:
{
  success: boolean,
  userId: string
}
```

### Disable Endpoint
```typescript
// POST /api/auth/mfa/disable
// src/app/api/auth/mfa/disable/route.ts
// Requires authenticated user
// Request:
{
  password: string
}
// Response:
{
  success: boolean,
  message: string
}
```

### Regenerate Backup Codes Endpoint
```typescript
// POST /api/auth/mfa/regenerate-codes
// src/app/api/auth/mfa/regenerate-codes/route.ts
// Requires authenticated user
// Response:
{
  success: boolean,
  backupCodes: string[]
}
```

## Database Schema

```prisma
// From prisma/schema.prisma
model User {
  // other user fields...
  
  // MFA Fields
  mfaEnabled       Boolean    @default(false)
  mfaSecret        String?
  mfaVerified      Boolean    @default(false)
  mfaBackupCodes   String[]   @default([])
}
```

## User Interface

### Setup Wizard
Located in `src/app/account/mfa/page.tsx`:
1. Initial setup screen with information
2. QR code display for authenticator app
3. Token verification input
4. Backup codes display and management

### Account Settings Integration
Located in `src/app/dashboard/settings/account/security-form.tsx`:
- MFA status display
- Link to MFA setup page

### MFA Options
- Enable/disable MFA
- Regenerate backup codes
- View current backup codes

## Integration Guide

### NextAuth.js Integration
Located in `src/auth.ts`:
```typescript
// Credentials provider with MFA support
async authorize(credentials) {
  // Authenticate with email/password
  
  // Check if MFA is enabled
  if (user.mfaEnabled) {
    // If no MFA token, require MFA verification
    if (!mfaToken) {
      return {
        // User info with requiresMfaVerification flag
        requiresMfaVerification: true,
      }
    }
    
    // Validate MFA token
    const response = await fetch(`/api/auth/mfa/validate`, {
      // Validation request
    })
    
    // Return user if valid
  }
  
  // Return user without MFA
}
```

### Login Integration
Located in `src/app/auth/login/login-form.tsx`:
```typescript
// Handle MFA requirement in login flow
if (result.error === "CredentialsSignin" && 
    result.url?.includes("requiresMfaVerification=true")) {
  // Show MFA verification form
  setRequiresMfa(true)
  return
}
```

## Troubleshooting

### Common Issues

1. **Token Validation Failures**
   - Check time synchronization on device
   - Verify correct secret usage in the authenticator app
   - Confirm token freshness (within 30 seconds)

2. **QR Code Scanning Issues**
   - Ensure proper contrast on display
   - Check camera permissions in the authenticator app
   - Try manual entry using the provided secret

3. **Backup Code Problems**
   - Confirm code hasn't been used (one-time use only)
   - Check format matching (uppercase, no spaces)
   - Verify user authentication is correct

### Error Codes

| Code | Description | Resolution |
|------|-------------|------------|
| 401 | Authentication required | Sign in again |
| 400 | Invalid token | Generate a new token and try again |
| 400 | Token is required | Ensure the token field is not empty |
| 404 | User not found or MFA not enabled | Contact support |

## Best Practices

1. **Security**
   - Use a trusted authenticator app (Google Authenticator, Authy, etc.)
   - Save backup codes in a secure location
   - Don't share your QR code or secret with anyone
   - Enable MFA on all accounts that support it

2. **User Experience**
   - Clear error messages to guide users
   - Simple setup process with visual aids
   - Multiple recovery options (backup codes)
   - Easy management of MFA settings

3. **Maintenance**
   - Regular security audits of the MFA system
   - Monitoring for unusual validation patterns
   - Updates to dependencies for security patches
   - Performance optimization for validation speed 