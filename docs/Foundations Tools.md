While [large language models (LLMs)](https://sdk.vercel.ai/docs/foundations/overview#large-language-models) have incredible generation capabilities, they struggle with discrete tasks (e.g. mathematics) and interacting with the outside world (e.g. getting the weather).

Tools are actions that an LLM can invoke. The results of these actions can be reported back to the LLM to be considered in the next response.

For example, when you ask an LLM for the "weather in London", and there is a weather tool available, it could call a tool with London as the argument. The tool would then fetch the weather data and return it to the LLM. The LLM can then use this information in its response.

A tool is an object that can be called by the model to perform a specific task. You can use tools with [`generateText`](https://sdk.vercel.ai/docs/reference/ai-sdk-core/generate-text) and [`streamText`](https://sdk.vercel.ai/docs/reference/ai-sdk-core/stream-text) by passing one or more tools to the `tools` parameter.

A tool consists of three properties:

-   **`description`**: An optional description of the tool that can influence when the tool is picked.
-   **`parameters`**: A [Zod schema](https://sdk.vercel.ai/docs/foundations/tools#schema-specification-and-validation-with-zod) or a [JSON schema](https://sdk.vercel.ai/docs/reference/ai-sdk-core/json-schema) that defines the parameters. The schema is consumed by the LLM, and also used to validate the LLM tool calls.
-   **`execute`**: An optional async function that is called with the arguments from the tool call.

`streamUI` uses UI generator tools with a `generate` function that can return React components.

If the LLM decides to use a tool, it will generate a tool call. Tools with an `execute` function are run automatically when these calls are generated. The results of the tool calls are returned using tool result objects.

You can automatically pass tool results back to the LLM using [multi-step calls](https://sdk.vercel.ai/docs/ai-sdk-core/tools-and-tool-calling#multi-step-calls) with `streamText` and `generateText`.

## [Schemas](https://sdk.vercel.ai/docs/foundations/tools#schemas)

Schemas are used to define the parameters for tools and to validate the [tool calls](https://sdk.vercel.ai/docs/ai-sdk-core/tools-and-tool-calling).

The AI SDK supports both raw JSON schemas (using the `jsonSchema` function) and [Zod](https://zod.dev/) schemas. [Zod](https://zod.dev/) is the most popular JavaScript schema validation library. You can install Zod with:

You can then specify a Zod schema, for example:

```
import z from 'zod';const recipeSchema = z.object({  recipe: z.object({    name: z.string(),    ingredients: z.array(      z.object({        name: z.string(),        amount: z.string(),      }),    ),    steps: z.array(z.string()),  }),});
```

When you work with tools, you typically need a mix of application specific tools and general purpose tools. There are several providers that offer pre-built tools as **toolkits** that you can use out of the box:

-   **[agentic](https://github.com/transitive-bullshit/agentic)** - A collection of 20+ tools. Most tools connect to access external APIs such as [Exa](https://exa.ai/) or [E2B](https://e2b.dev/).
-   **[browserbase](https://github.com/browserbase/js-sdk?tab=readme-ov-file#vercel-ai-sdk-integration)** - Browser tool that runs a headless browser

Do you have open source tools or tool libraries that are compatible with the AI SDK? Please [file a pull request](https://github.com/vercel/ai/pulls) to add them to this list.

## [Learn more](https://sdk.vercel.ai/docs/foundations/tools#learn-more)

The AI SDK Core [Tool Calling](https://sdk.vercel.ai/docs/ai-sdk-core/tools-and-tool-calling) and [Agents](https://sdk.vercel.ai/docs/ai-sdk-core/agents) documentation has more information about tools and tool calling.