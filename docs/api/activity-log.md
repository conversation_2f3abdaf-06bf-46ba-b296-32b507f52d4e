# Activity Log API Documentation

## Overview

The Activity Log API provides endpoints for tracking, retrieving, and managing system activity logs. These logs record important system events, user actions, and audit trails across the application.

## Base URL
```
https://api.gppathology.com/v1/activity-log
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process.

```
Authorization: Bearer {token}
```

## Activity Logs

### List Activity Logs
```typescript
GET /api/activity-log
Authorization: Bearer {token}

Parameters:
- resourceType: string (optional) - Filter by resource type (e.g., "BODY", "USER", "ADMISSION")
- resourceId: string (optional) - Filter by specific resource ID
- actionType: string (optional) - Filter by action type (e.g., "CREATE", "UPDATE", "DELETE")
- userId: string (optional) - Filter by user who performed the action
- dateFrom: string (optional) - Filter by date range start (ISO format)
- dateTo: string (optional) - Filter by date range end (ISO format)
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset
- sort: string (optional) - Sort direction ("asc" or "desc")
- sortBy: string (optional) - Field to sort by (default: "timestamp")

Response:
{
  "total": number,
  "data": [
    {
      "id": string,
      "timestamp": string,
      "resourceType": string,
      "resourceId": string,
      "actionType": string,
      "description": string,
      "details": object,
      "user": {
        "id": string,
        "name": string,
        "role": string
      },
      "ipAddress": string,
      "userAgent": string,
      "metadata": {
        "facility": string,
        "location": string,
        "browser": string,
        "operatingSystem": string
      }
    }
  ]
}
```

### Get Activity Log
```typescript
GET /api/activity-log/:id
Authorization: Bearer {token}

Response:
{
  "id": string,
  "timestamp": string,
  "resourceType": string,
  "resourceId": string,
  "actionType": string,
  "description": string,
  "details": {
    "before": object,
    "after": object,
    "changes": object
  },
  "user": {
    "id": string,
    "name": string,
    "email": string,
    "role": string
  },
  "ipAddress": string,
  "userAgent": string,
  "metadata": {
    "facility": string,
    "location": string,
    "browser": string,
    "operatingSystem": string,
    "sessionId": string
  }
}
```

### Create Activity Log Entry
```typescript
POST /api/activity-log
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "resourceType": string,
  "resourceId": string,
  "actionType": string,
  "description": string,
  "details": {
    "before": object,
    "after": object,
    "changes": object
  },
  "metadata": {
    "facility": string,
    "location": string,
    "additionalInfo": object
  }
}

Response:
{
  "id": string,
  "timestamp": string,
  "resourceType": string,
  "resourceId": string,
  "actionType": string
}
```

### Get Resource Activity History
```typescript
GET /api/activity-log/resource/:resourceType/:resourceId
Authorization: Bearer {token}

Parameters:
- actionType: string (optional) - Filter by action type
- dateFrom: string (optional) - Filter by date range start
- dateTo: string (optional) - Filter by date range end
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "resourceType": string,
  "resourceId": string,
  "total": number,
  "data": [
    {
      "id": string,
      "timestamp": string,
      "actionType": string,
      "description": string,
      "user": {
        "id": string,
        "name": string,
        "role": string
      },
      "details": {
        "changes": object
      }
    }
  ]
}
```

### Get User Activity History
```typescript
GET /api/activity-log/user/:userId
Authorization: Bearer {token}

Parameters:
- resourceType: string (optional) - Filter by resource type
- actionType: string (optional) - Filter by action type
- dateFrom: string (optional) - Filter by date range start
- dateTo: string (optional) - Filter by date range end
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "userId": string,
  "userName": string,
  "total": number,
  "data": [
    {
      "id": string,
      "timestamp": string,
      "resourceType": string,
      "resourceId": string,
      "actionType": string,
      "description": string
    }
  ]
}
```

## Activity Summary

### Get Activity Summary
```typescript
GET /api/activity-log/summary
Authorization: Bearer {token}

Parameters:
- period: string (optional) - "daily" | "weekly" | "monthly" (default: "daily")
- dateFrom: string (optional) - Filter by date range start
- dateTo: string (optional) - Filter by date range end
- facilityId: string (optional) - Filter by facility

Response:
{
  "period": string,
  "from": string,
  "to": string,
  "totalActivities": number,
  "byResourceType": [
    {
      "resourceType": string,
      "count": number,
      "percentage": number
    }
  ],
  "byActionType": [
    {
      "actionType": string,
      "count": number,
      "percentage": number
    }
  ],
  "byUser": [
    {
      "userId": string,
      "userName": string,
      "count": number,
      "percentage": number
    }
  ],
  "timeline": [
    {
      "date": string,
      "count": number,
      "byResourceType": {
        "BODY": number,
        "ADMISSION": number,
        "USER": number
      },
      "byActionType": {
        "CREATE": number,
        "UPDATE": number,
        "DELETE": number,
        "VIEW": number
      }
    }
  ]
}
```

### Get Facility Activity Summary
```typescript
GET /api/activity-log/summary/facility/:facilityId
Authorization: Bearer {token}

Parameters:
- period: string (optional) - "daily" | "weekly" | "monthly" (default: "daily")
- dateFrom: string (optional) - Filter by date range start
- dateTo: string (optional) - Filter by date range end

Response:
{
  "facilityId": string,
  "facilityName": string,
  "period": string,
  "from": string,
  "to": string,
  "totalActivities": number,
  "byResourceType": [
    {
      "resourceType": string,
      "count": number,
      "percentage": number
    }
  ],
  "byActionType": [
    {
      "actionType": string,
      "count": number,
      "percentage": number
    }
  ],
  "byUser": [
    {
      "userId": string,
      "userName": string,
      "count": number,
      "percentage": number
    }
  ]
}
```

## Export

### Export Activity Logs
```typescript
GET /api/activity-log/export
Authorization: Bearer {token}

Parameters:
- format: string - "csv" | "json" | "pdf"
- resourceType: string (optional) - Filter by resource type
- resourceId: string (optional) - Filter by resource ID
- actionType: string (optional) - Filter by action type
- userId: string (optional) - Filter by user who performed the action
- dateFrom: string (optional) - Filter by date range start
- dateTo: string (optional) - Filter by date range end
- includeDetails: boolean (optional) - Whether to include full details in export

Response:
Binary file download with appropriate Content-Type header
```

## Retention

### Configure Log Retention
```typescript
PUT /api/activity-log/retention
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "retentionPeriodDays": number,
  "archiveEnabled": boolean,
  "archiveStorage": string,
  "sensitiveDataRetentionPolicy": {
    "ipAddressRetentionDays": number,
    "userAgentRetentionDays": number,
    "detailedChangesRetentionDays": number
  }
}

Response:
{
  "updated": boolean,
  "retentionPeriodDays": number,
  "archiveEnabled": boolean,
  "effectiveDate": string
}
```

### Get Log Retention Policy
```typescript
GET /api/activity-log/retention
Authorization: Bearer {token}

Response:
{
  "retentionPeriodDays": number,
  "archiveEnabled": boolean,
  "archiveStorage": string,
  "sensitiveDataRetentionPolicy": {
    "ipAddressRetentionDays": number,
    "userAgentRetentionDays": number,
    "detailedChangesRetentionDays": number
  },
  "lastUpdated": string,
  "updatedBy": {
    "id": string,
    "name": string
  }
}
``` 