# Releases API Documentation

This document details the Releases API endpoints for the GP Pathology system.

## Overview

Releases endpoints handle the management of body releases from facilities, including verification, documentation, and final release processes.

## Available Endpoints

- [GET /api/releases](#get-apireleases)
- [GET /api/releases/{id}](#get-apireleasesid)
- [POST /api/releases](#post-apireleases)
- [PUT /api/releases/{id}](#put-apireleasesid)
- [DELETE /api/releases/{id}](#delete-apireleasesid)

## Endpoints

### GET /api/releases

Retrieve a list of body releases with optional filtering.

#### Query Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| status | string | Filter by status (PENDING, VERIFIED, etc.) | No |
| dateFrom | string | Filter by date range start (ISO format) | No |
| dateTo | string | Filter by date range end (ISO format) | No |
| facilityId | string | Filter by facility | No |
| releasedById | string | Filter by releasing staff member | No |

#### Response

```json
{
  "releases": [
    {
      "id": "string",
      "bodyId": "string",
      "facilityId": "string",
      "releaseDate": "2024-02-01T10:00:00Z",
      "releasedTo": "string",
      "relationship": "string",
      "identificationDocument": "string",
      "contactDetails": "string",
      "notes": "string",
      "status": "PENDING",
      "facility": {
        "id": "string",
        "name": "string",
        "code": "string"
      },
      "releasedBy": {
        "id": "string",
        "name": "string",
        "role": "MORGUE_STAFF"
      },
      "body": {
        "id": "string",
        "trackingNumber": "string",
        "status": "PENDING_RELEASE"
      },
      "createdAt": "2024-02-01T10:00:00Z",
      "updatedAt": "2024-02-01T10:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "pageSize": 10
}
```

### GET /api/releases/{id}

Retrieve details of a specific release.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Release ID |

#### Response

```json
{
  "id": "string",
  "bodyId": "string",
  "facilityId": "string",
  "releaseDate": "2024-02-01T10:00:00Z",
  "releasedTo": "string",
  "relationship": "string",
  "identificationDocument": "string",
  "contactDetails": "string",
  "notes": "string",
  "status": "PENDING",
  "facility": {
    "id": "string",
    "name": "string",
    "code": "string",
    "type": "MORGUE"
  },
  "releasedBy": {
    "id": "string",
    "name": "string",
    "role": "MORGUE_STAFF"
  },
  "body": {
    "id": "string",
    "trackingNumber": "string",
    "status": "PENDING_RELEASE",
    "bodyTag": {
      "id": "string",
      "tagNumber": "string",
      "status": "ADMITTED"
    }
  },
  "createdAt": "2024-02-01T10:00:00Z",
  "updatedAt": "2024-02-01T10:00:00Z"
}
```

### POST /api/releases

Create a new body release.

#### Required Role
- MORGUE_STAFF
- ADMIN

#### Request Body

```json
{
  "bodyId": "string",
  "facilityId": "string",
  "releaseDate": "2024-02-01T10:00:00Z",
  "releasedTo": "string",
  "relationship": "string",
  "identificationDocument": "string",
  "contactDetails": "string",
  "notes": "string"
}
```

#### Response

Returns the created release object with HTTP 201 status code.

### PUT /api/releases/{id}

Update an existing release.

#### Required Role
- MORGUE_STAFF
- ADMIN

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Release ID |

#### Request Body

```json
{
  "status": "VERIFIED",
  "notes": "string",
  "releaseDate": "2024-02-01T10:00:00Z"
}
```

#### Response

Returns the updated release object.

### DELETE /api/releases/{id}

Delete a release. This operation is only allowed for pending releases.

#### Required Role
- ADMIN

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Release ID |

#### Response

```json
{
  "message": "Release deleted successfully"
}
```

## Error Responses

### 400 Bad Request

```json
{
  "error": "Invalid request parameters",
  "details": {
    "field": "error description"
  }
}
```

### 401 Unauthorized

```json
{
  "error": "Unauthorized"
}
```

### 403 Forbidden

```json
{
  "error": "Insufficient permissions"
}
```

### 404 Not Found

```json
{
  "error": "Release not found"
}
```

### 409 Conflict

```json
{
  "error": "Release cannot be deleted - already verified"
}
```

### 500 Internal Server Error

```json
{
  "error": "Internal Server Error"
}
```

## Release Status Flow

1. `PENDING` - Initial status when release is created
2. `VERIFIED` - When release documentation is verified
3. `COMPLETED` - When body is physically released
4. `CANCELLED` - When release is cancelled

## Required Documentation

For a release to be verified, the following documentation is required:
- Valid identification document of the person collecting the body
- Proof of relationship to the deceased
- Death certificate or burial order
- Any additional documentation required by local regulations

## Notes

- Release date must be a future date
- Identification document details must be verified before status can be changed to VERIFIED
- Contact details should include at least one phone number
- Only pending releases can be deleted
- Completed releases cannot be modified
- Body status is automatically updated when release status changes
- Security verification is required before physical release
- An audit log entry is created for all status changes 