# Notifications API Documentation

## Overview

The Notifications API provides endpoints for managing system notifications, alerts, and user communication preferences.

## Base URL
```
https://api.gppathology.com/v1/notifications
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process.

```
Authorization: Bearer {token}
```

## Notifications

### List Notifications
```typescript
GET /api/notifications
Authorization: Bearer {token}

Parameters:
- status: string (optional) - Filter by status ("read", "unread", "all")
- type: string (optional) - Filter by notification type
- priority: string (optional) - Filter by priority level
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "total": number,
  "unreadCount": number,
  "data": [
    {
      "id": string,
      "userId": string,
      "type": string,
      "title": string,
      "message": string,
      "priority": "LOW" | "MEDIUM" | "HIGH" | "URGENT",
      "status": "READ" | "UNREAD",
      "category": string,
      "createdAt": string,
      "readAt": string,
      "expiresAt": string,
      "metadata": {
        "resourceType": string,
        "resourceId": string,
        "actionUrl": string,
        "iconUrl": string
      },
      "sender": {
        "id": string,
        "name": string,
        "role": string
      }
    }
  ]
}
```

### Get Notification
```typescript
GET /api/notifications/:id
Authorization: Bearer {token}

Response:
{
  "id": string,
  "userId": string,
  "type": string,
  "title": string,
  "message": string,
  "priority": "LOW" | "MEDIUM" | "HIGH" | "URGENT",
  "status": "READ" | "UNREAD",
  "category": string,
  "createdAt": string,
  "readAt": string,
  "expiresAt": string,
  "metadata": {
    "resourceType": string,
    "resourceId": string,
    "actionUrl": string,
    "iconUrl": string,
    "details": object
  },
  "sender": {
    "id": string,
    "name": string,
    "role": string
  }
}
```

### Create Notification
```typescript
POST /api/notifications
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "recipients": string[],
  "type": string,
  "title": string,
  "message": string,
  "priority": "LOW" | "MEDIUM" | "HIGH" | "URGENT",
  "category": string,
  "expiresAt": string,
  "metadata": {
    "resourceType": string,
    "resourceId": string,
    "actionUrl": string,
    "iconUrl": string,
    "details": object
  }
}

Response:
{
  "success": boolean,
  "count": number,
  "notificationIds": string[]
}
```

### Mark as Read
```typescript
PUT /api/notifications/:id/read
Authorization: Bearer {token}

Response:
{
  "id": string,
  "status": "READ",
  "readAt": string
}
```

### Mark All as Read
```typescript
PUT /api/notifications/read-all
Authorization: Bearer {token}

Parameters:
- category: string (optional) - Mark as read only for a specific category

Response:
{
  "success": boolean,
  "count": number
}
```

### Delete Notification
```typescript
DELETE /api/notifications/:id
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "id": string
}
```

## User Preferences

### Get Notification Preferences
```typescript
GET /api/notifications/preferences
Authorization: Bearer {token}

Response:
{
  "userId": string,
  "channels": {
    "email": boolean,
    "sms": boolean,
    "push": boolean,
    "inApp": boolean
  },
  "categories": {
    "system": {
      "enabled": boolean,
      "channels": string[]
    },
    "admissions": {
      "enabled": boolean,
      "channels": string[]
    },
    "releases": {
      "enabled": boolean,
      "channels": string[]
    },
    "collections": {
      "enabled": boolean,
      "channels": string[]
    },
    "alerts": {
      "enabled": boolean,
      "channels": string[]
    },
    "tasks": {
      "enabled": boolean,
      "channels": string[]
    }
  },
  "schedules": {
    "quietHours": {
      "enabled": boolean,
      "start": string,
      "end": string
    },
    "digest": {
      "enabled": boolean,
      "frequency": "DAILY" | "WEEKLY",
      "time": string,
      "day": string
    }
  },
  "contactInfo": {
    "email": string,
    "phone": string,
    "deviceIds": string[]
  }
}
```

### Update Notification Preferences
```typescript
PUT /api/notifications/preferences
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "channels": {
    "email": boolean,
    "sms": boolean,
    "push": boolean,
    "inApp": boolean
  },
  "categories": {
    "system": {
      "enabled": boolean,
      "channels": string[]
    },
    "admissions": {
      "enabled": boolean,
      "channels": string[]
    },
    "releases": {
      "enabled": boolean,
      "channels": string[]
    },
    "collections": {
      "enabled": boolean,
      "channels": string[]
    },
    "alerts": {
      "enabled": boolean,
      "channels": string[]
    },
    "tasks": {
      "enabled": boolean,
      "channels": string[]
    }
  },
  "schedules": {
    "quietHours": {
      "enabled": boolean,
      "start": string,
      "end": string
    },
    "digest": {
      "enabled": boolean,
      "frequency": "DAILY" | "WEEKLY",
      "time": string,
      "day": string
    }
  },
  "contactInfo": {
    "email": string,
    "phone": string,
    "deviceIds": string[]
  }
}

Response:
{
  "success": boolean,
  "updated": string[]
}
```

## System Alerts

### List System Alerts
```typescript
GET /api/notifications/system-alerts
Authorization: Bearer {token}

Parameters:
- status: string (optional) - Filter by status ("active", "resolved", "all")
- severity: string (optional) - Filter by severity level
- type: string (optional) - Filter by alert type

Response:
{
  "total": number,
  "activeCount": number,
  "data": [
    {
      "id": string,
      "type": string,
      "title": string,
      "message": string,
      "severity": "INFO" | "WARNING" | "ERROR" | "CRITICAL",
      "status": "ACTIVE" | "RESOLVED" | "ACKNOWLEDGED",
      "source": string,
      "createdAt": string,
      "acknowledgedAt": string,
      "acknowledgedBy": string,
      "resolvedAt": string,
      "resolvedBy": string,
      "metadata": {
        "resourceType": string,
        "resourceId": string,
        "detailsUrl": string,
        "affectedUsers": string[]
      }
    }
  ]
}
```

### Create System Alert
```typescript
POST /api/notifications/system-alerts
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "type": string,
  "title": string,
  "message": string,
  "severity": "INFO" | "WARNING" | "ERROR" | "CRITICAL",
  "source": string,
  "notifyUsers": boolean,
  "metadata": {
    "resourceType": string,
    "resourceId": string,
    "detailsUrl": string,
    "affectedUsers": string[]
  }
}

Response:
{
  "id": string,
  "type": string,
  "title": string,
  "severity": string,
  "status": "ACTIVE",
  "createdAt": string,
  "notifiedUsers": number
}
```

### Acknowledge Alert
```typescript
PUT /api/notifications/system-alerts/:id/acknowledge
Authorization: Bearer {token}

Response:
{
  "id": string,
  "status": "ACKNOWLEDGED",
  "acknowledgedAt": string,
  "acknowledgedBy": string
}
```

### Resolve Alert
```typescript
PUT /api/notifications/system-alerts/:id/resolve
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "resolution": string,
  "notes": string
}

Response:
{
  "id": string,
  "status": "RESOLVED",
  "resolvedAt": string,
  "resolvedBy": string,
  "resolution": string
}
``` 