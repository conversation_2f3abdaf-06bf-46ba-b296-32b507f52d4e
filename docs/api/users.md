# Users API Documentation

## Overview

The Users API provides endpoints for managing user accounts, including creation, authentication, profile management, and access control.

## Base URL
```
https://api.gppathology.com/v1/users
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process, except for the public endpoints like registration.

```
Authorization: Bearer {token}
```

## Users

### List Users
```typescript
GET /api/users
Authorization: Bearer {token}

Parameters:
- role: string (optional) - Filter by user role
- facility: string (optional) - Filter by assigned facility
- status: string (optional) - Filter by user status
- search: string (optional) - Search by name or email
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "total": number,
  "data": [
    {
      "id": string,
      "email": string,
      "name": string,
      "role": string,
      "status": "ACTIVE" | "INACTIVE" | "PENDING" | "SUSPENDED",
      "createdAt": string,
      "updatedAt": string,
      "lastLogin": string,
      "facilities": [
        {
          "id": string,
          "name": string
        }
      ],
      "permissions": string[],
      "metadata": {
        "position": string,
        "department": string,
        "employeeId": string
      }
    }
  ]
}
```

### Get User
```typescript
GET /api/users/:id
Authorization: Bearer {token}

Response:
{
  "id": string,
  "email": string,
  "name": string,
  "role": string,
  "status": "ACTIVE" | "INACTIVE" | "PENDING" | "SUSPENDED",
  "createdAt": string,
  "updatedAt": string,
  "lastLogin": string,
  "facilities": [
    {
      "id": string,
      "name": string
    }
  ],
  "permissions": string[],
  "contactInfo": {
    "phone": string,
    "alternateEmail": string,
    "address": {
      "street": string,
      "city": string,
      "state": string,
      "postalCode": string,
      "country": string
    }
  },
  "metadata": {
    "position": string,
    "department": string,
    "employeeId": string,
    "hireDate": string,
    "reportsTo": string,
    "certifications": string[]
  },
  "preferences": {
    "theme": string,
    "language": string,
    "notifications": object
  }
}
```

### Create User
```typescript
POST /api/users
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "email": string,
  "name": string,
  "password": string,
  "role": string,
  "status": "ACTIVE" | "INACTIVE" | "PENDING",
  "facilities": string[],
  "contactInfo": {
    "phone": string,
    "alternateEmail": string,
    "address": {
      "street": string,
      "city": string,
      "state": string,
      "postalCode": string,
      "country": string
    }
  },
  "metadata": {
    "position": string,
    "department": string,
    "employeeId": string,
    "hireDate": string,
    "reportsTo": string,
    "certifications": string[]
  },
  "sendInvitation": boolean
}

Response:
{
  "id": string,
  "email": string,
  "name": string,
  "role": string,
  "status": string,
  "createdAt": string,
  "invitationSent": boolean
}
```

### Update User
```typescript
PUT /api/users/:id
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "role": string,
  "status": "ACTIVE" | "INACTIVE" | "SUSPENDED",
  "facilities": string[],
  "contactInfo": {
    "phone": string,
    "alternateEmail": string,
    "address": {
      "street": string,
      "city": string,
      "state": string,
      "postalCode": string,
      "country": string
    }
  },
  "metadata": {
    "position": string,
    "department": string,
    "employeeId": string,
    "reportsTo": string,
    "certifications": string[]
  }
}

Response:
{
  "id": string,
  "email": string,
  "name": string,
  "role": string,
  "status": string,
  "updatedAt": string
}
```

### Delete User
```typescript
DELETE /api/users/:id
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

### Change Password
```typescript
PUT /api/users/:id/password
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "currentPassword": string,
  "newPassword": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

### Reset Password
```typescript
POST /api/users/reset-password
Content-Type: application/json

Request:
{
  "email": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

### Confirm Reset Password
```typescript
POST /api/users/confirm-reset-password
Content-Type: application/json

Request:
{
  "token": string,
  "newPassword": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

## User Profile

### Get Current User Profile
```typescript
GET /api/users/me
Authorization: Bearer {token}

Response:
{
  "id": string,
  "email": string,
  "name": string,
  "role": string,
  "status": string,
  "facilities": [
    {
      "id": string,
      "name": string
    }
  ],
  "permissions": string[],
  "contactInfo": {
    "phone": string,
    "alternateEmail": string
  },
  "metadata": {
    "position": string,
    "department": string
  },
  "preferences": {
    "theme": string,
    "language": string,
    "notifications": object
  }
}
```

### Update Current User Profile
```typescript
PUT /api/users/me
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "contactInfo": {
    "phone": string,
    "alternateEmail": string,
    "address": {
      "street": string,
      "city": string,
      "state": string,
      "postalCode": string,
      "country": string
    }
  },
  "preferences": {
    "theme": string,
    "language": string
  }
}

Response:
{
  "id": string,
  "name": string,
  "updatedAt": string
}
```

### Upload Profile Picture
```typescript
POST /api/users/me/profile-picture
Authorization: Bearer {token}
Content-Type: multipart/form-data

Request:
{
  "file": File
}

Response:
{
  "id": string,
  "profilePicture": string,
  "updatedAt": string
}
```

## User Sessions

### List User Sessions
```typescript
GET /api/users/me/sessions
Authorization: Bearer {token}

Response:
{
  "sessions": [
    {
      "id": string,
      "deviceInfo": {
        "type": string,
        "browser": string,
        "os": string,
        "ip": string,
        "location": string
      },
      "createdAt": string,
      "lastActiveAt": string,
      "isCurrent": boolean
    }
  ]
}
```

### Revoke Session
```typescript
DELETE /api/users/me/sessions/:sessionId
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

### Revoke All Sessions
```typescript
DELETE /api/users/me/sessions
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "count": number,
  "message": string
}
```

## User Activity

### Get User Activity
```typescript
GET /api/users/:id/activity
Authorization: Bearer {token}

Parameters:
- type: string (optional) - Filter by activity type
- from: string (optional) - Filter by start date
- to: string (optional) - Filter by end date
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "total": number,
  "data": [
    {
      "id": string,
      "userId": string,
      "type": string,
      "description": string,
      "metadata": {
        "resourceType": string,
        "resourceId": string,
        "action": string,
        "details": object
      },
      "timestamp": string,
      "ip": string,
      "userAgent": string
    }
  ]
}
```

## User Invitations

### List Invitations
```typescript
GET /api/users/invitations
Authorization: Bearer {token}

Parameters:
- status: string (optional) - Filter by invitation status
- search: string (optional) - Search by email or name

Response:
{
  "total": number,
  "data": [
    {
      "id": string,
      "email": string,
      "name": string,
      "role": string,
      "status": "PENDING" | "ACCEPTED" | "EXPIRED",
      "createdAt": string,
      "expiresAt": string,
      "sentBy": {
        "id": string,
        "name": string
      }
    }
  ]
}
```

### Create Invitation
```typescript
POST /api/users/invitations
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "email": string,
  "name": string,
  "role": string,
  "facilities": string[],
  "metadata": {
    "position": string,
    "department": string,
    "employeeId": string
  },
  "message": string,
  "expiresIn": number
}

Response:
{
  "id": string,
  "email": string,
  "name": string,
  "role": string,
  "status": "PENDING",
  "createdAt": string,
  "expiresAt": string
}
```

### Resend Invitation
```typescript
POST /api/users/invitations/:id/resend
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string,
  "expiresAt": string
}
```

### Cancel Invitation
```typescript
DELETE /api/users/invitations/:id
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

### Accept Invitation
```typescript
POST /api/users/invitations/accept
Content-Type: application/json

Request:
{
  "token": string,
  "password": string,
  "name": string
}

Response:
{
  "success": boolean,
  "message": string,
  "user": {
    "id": string,
    "email": string,
    "name": string
  },
  "token": string,
  "refreshToken": string
}
``` 