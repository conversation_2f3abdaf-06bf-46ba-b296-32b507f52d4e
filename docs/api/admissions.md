# Admissions API Documentation

## Overview

The Admissions API provides endpoints for managing body admissions to facilities, including creating admissions, tracking details, and managing the admission process.

## Base URL
```
https://api.gppathology.com/v1/admissions
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process.

```
Authorization: Bearer {token}
```

## Admissions

### List Admissions
```typescript
GET /api/admissions
Authorization: Bearer {token}

Parameters:
- facilityId: string (optional) - Filter by facility
- status: string (optional) - Filter by admission status
- dateFrom: string (optional) - Filter by admission date (from)
- dateTo: string (optional) - Filter by admission date (to)
- search: string (optional) - Search by body tracking number or reference
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "total": number,
  "data": [
    {
      "id": string,
      "bodyId": string,
      "facilityId": string,
      "admissionDate": string,
      "status": "PENDING" | "COMPLETED" | "CANCELLED",
      "fridgeBarCode": string,
      "deathRegisterNumber": string,
      "estimatedTimeOfDeath": string,
      "bodyCondition": string,
      "personalBelongings": string[],
      "notes": string,
      "assignedFridge": string,
      "temperature": string,
      "barcodeValue": string,
      "createdAt": string,
      "updatedAt": string,
      "body": {
        "id": string,
        "trackingNumber": string,
        "status": string,
        "bodyTag": {
          "tagNumber": string
        }
      },
      "facility": {
        "id": string,
        "name": string
      },
      "admittedBy": {
        "id": string,
        "name": string,
        "role": string
      }
    }
  ]
}
```

### Get Admission
```typescript
GET /api/admissions/:id
Authorization: Bearer {token}

Response:
{
  "id": string,
  "bodyId": string,
  "facilityId": string,
  "admissionDate": string,
  "status": "PENDING" | "COMPLETED" | "CANCELLED",
  "fridgeBarCode": string,
  "deathRegisterNumber": string,
  "estimatedTimeOfDeath": string,
  "bodyCondition": string,
  "personalBelongings": string[],
  "notes": string,
  "photo": string,
  "assignedFridge": string,
  "temperature": string,
  "barcodeValue": string,
  "createdAt": string,
  "updatedAt": string,
  "body": {
    "id": string,
    "trackingNumber": string,
    "status": string,
    "bodyTag": {
      "tagNumber": string,
      "id": string
    },
    "collection": {
      "id": string,
      "collectionDate": string,
      "collectionPoint": string,
      "collectedBy": {
        "id": string,
        "name": string
      }
    }
  },
  "facility": {
    "id": string,
    "name": string,
    "address": string
  },
  "storage": {
    "unitId": string,
    "unitName": string,
    "position": string,
    "assignedAt": string,
    "assignedBy": {
      "id": string,
      "name": string
    }
  },
  "admittedBy": {
    "id": string,
    "name": string,
    "role": string
  },
  "documents": [
    {
      "id": string,
      "type": string,
      "filename": string,
      "uploadDate": string,
      "uploadedBy": string
    }
  ],
  "verificationDetails": {
    "isVerified": boolean,
    "verifiedBy": {
      "id": string,
      "name": string
    },
    "verifiedAt": string
  }
}
```

### Create Admission
```typescript
POST /api/admissions
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "bodyId": string,
  "facilityId": string,
  "admissionDate": string,
  "fridgeBarCode": string,
  "deathRegisterNumber": string,
  "estimatedTimeOfDeath": string,
  "bodyCondition": string,
  "personalBelongings": string[],
  "notes": string,
  "photo": string,
  "assignedFridge": string,
  "temperature": string,
  "barcodeValue": string
}

Response:
{
  "id": string,
  "bodyId": string,
  "status": "PENDING",
  "admissionDate": string,
  "createdAt": string,
  "body": {
    "id": string,
    "trackingNumber": string,
    "status": string
  },
  "facility": {
    "id": string,
    "name": string
  }
}
```

### Update Admission
```typescript
PUT /api/admissions/:id
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "status": "PENDING" | "COMPLETED" | "CANCELLED",
  "fridgeBarCode": string,
  "deathRegisterNumber": string,
  "estimatedTimeOfDeath": string,
  "bodyCondition": string,
  "personalBelongings": string[],
  "notes": string,
  "photo": string,
  "assignedFridge": string,
  "temperature": string
}

Response:
{
  "id": string,
  "bodyId": string,
  "status": string,
  "updatedAt": string
}
```

### Complete Admission
```typescript
POST /api/admissions/:id/complete
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "storageDetails": {
    "unitId": string,
    "position": string
  },
  "notes": string,
  "documents": string[]
}

Response:
{
  "id": string,
  "bodyId": string,
  "status": "COMPLETED",
  "completedAt": string,
  "completedBy": {
    "id": string,
    "name": string
  },
  "body": {
    "id": string,
    "trackingNumber": string,
    "status": "ADMITTED"
  },
  "storage": {
    "unitId": string,
    "unitName": string,
    "position": string
  }
}
```

### Cancel Admission
```typescript
POST /api/admissions/:id/cancel
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "reason": string,
  "notes": string
}

Response:
{
  "id": string,
  "bodyId": string,
  "status": "CANCELLED",
  "cancelledAt": string,
  "cancelledBy": {
    "id": string,
    "name": string
  },
  "reason": string
}
```

### Upload Admission Documents
```typescript
POST /api/admissions/:id/documents
Authorization: Bearer {token}
Content-Type: multipart/form-data

Request:
{
  "documentType": string,
  "files": File[],
  "notes": string
}

Response:
{
  "id": string,
  "documents": [
    {
      "id": string,
      "type": string,
      "filename": string,
      "size": number,
      "mimeType": string,
      "uploadDate": string,
      "uploadedBy": string,
      "url": string
    }
  ]
}
```

### Verify Admission
```typescript
POST /api/admissions/:id/verify
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "verificationNotes": string
}

Response:
{
  "id": string,
  "isVerified": true,
  "verifiedBy": {
    "id": string,
    "name": string
  },
  "verifiedAt": string,
  "verificationNotes": string
}
```

## Admission Stats

### Get Admission Statistics
```typescript
GET /api/admissions/stats
Authorization: Bearer {token}

Parameters:
- facilityId: string (optional) - Filter by facility
- period: string (optional) - "daily" | "weekly" | "monthly" | "yearly"
- from: string (optional) - Start date of period
- to: string (optional) - End date of period

Response:
{
  "period": string,
  "from": string,
  "to": string,
  "totalAdmissions": number,
  "completedAdmissions": number,
  "pendingAdmissions": number,
  "cancelledAdmissions": number,
  "averageProcessingTime": number,
  "byFacility": [
    {
      "facilityId": string,
      "facilityName": string,
      "totalAdmissions": number,
      "completedAdmissions": number,
      "pendingAdmissions": number
    }
  ],
  "timeline": [
    {
      "date": string,
      "admissions": number,
      "completed": number,
      "pending": number,
      "cancelled": number
    }
  ]
}
```

## Batch Operations

### Batch Update Admissions
```typescript
POST /api/admissions/batch
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "ids": string[],
  "action": "COMPLETE" | "CANCEL",
  "details": {
    "notes": string,
    "reason": string
  }
}

Response:
{
  "success": boolean,
  "processed": number,
  "failed": number,
  "results": [
    {
      "id": string,
      "success": boolean,
      "message": string
    }
  ]
}
``` 