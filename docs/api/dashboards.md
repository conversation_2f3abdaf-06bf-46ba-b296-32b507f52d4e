# Dashboard API Documentation

This document details the Dashboard API endpoints for the GP Pathology system.

## Overview

Dashboard endpoints provide aggregated statistics, metrics, and data visualizations for various aspects of the system.

## Available Endpoints

- [GET /api/dashboard/overview](#get-apidashboardoverview)
- [GET /api/dashboard/facilities](#get-apidashboardfacilities)
- [GET /api/dashboard/workload](#get-apidashboardworkload)
- [GET /api/dashboard/analytics](#get-apidashboardanalytics)
- [GET /api/dashboard/reports](#get-apidashboardreports)

## Endpoints

### GET /api/dashboard/overview

Retrieve an overview of system statistics and current status.

#### Query Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| dateFrom | string | Filter by date range start (ISO format) | No |
| dateTo | string | Filter by date range end (ISO format) | No |
| facilityId | string | Filter by facility | No |

#### Response

```json
{
  "statistics": {
    "totalBodies": {
      "current": 150,
      "change": 5
    },
    "activeCases": {
      "current": 120,
      "change": -2
    },
    "pendingReleases": {
      "current": 15,
      "change": 3
    },
    "activeReferrals": {
      "current": 25,
      "change": 0
    }
  },
  "recentActivity": {
    "collections": [
      {
        "id": "string",
        "trackingNumber": "string",
        "type": "CRIME_SCENE",
        "timestamp": "2024-02-01T10:00:00Z"
      }
    ],
    "admissions": [
      {
        "id": "string",
        "trackingNumber": "string",
        "facility": "string",
        "timestamp": "2024-02-01T10:00:00Z"
      }
    ],
    "releases": [
      {
        "id": "string",
        "trackingNumber": "string",
        "facility": "string",
        "timestamp": "2024-02-01T10:00:00Z"
      }
    ]
  },
  "alerts": [
    {
      "type": "CAPACITY_WARNING",
      "message": "Facility XYZ at 90% capacity",
      "severity": "WARNING",
      "timestamp": "2024-02-01T10:00:00Z"
    }
  ]
}
```

### GET /api/dashboard/facilities

Retrieve facility-specific metrics and status information.

#### Query Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| facilityId | string | Filter by facility | No |
| type | string | Filter by facility type | No |

#### Response

```json
{
  "facilities": [
    {
      "id": "string",
      "name": "string",
      "type": "MORGUE",
      "metrics": {
        "totalCapacity": 200,
        "currentOccupancy": 150,
        "occupancyRate": 75,
        "availableFridges": 10,
        "maintenanceFridges": 2
      },
      "activity": {
        "admissionsToday": 5,
        "releasesToday": 3,
        "pendingReferrals": 8
      },
      "staff": {
        "onDuty": 12,
        "total": 20
      },
      "alerts": [
        {
          "type": "MAINTENANCE_REQUIRED",
          "message": "Fridge maintenance due",
          "severity": "INFO"
        }
      ]
    }
  ],
  "summary": {
    "totalFacilities": 5,
    "totalCapacity": 1000,
    "currentOccupancy": 750,
    "averageOccupancyRate": 75
  }
}
```

### GET /api/dashboard/workload

Retrieve staff workload and assignment metrics.

#### Query Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| facilityId | string | Filter by facility | No |
| role | string | Filter by staff role | No |
| dateFrom | string | Filter by date range start (ISO format) | No |
| dateTo | string | Filter by date range end (ISO format) | No |

#### Response

```json
{
  "staffWorkload": [
    {
      "userId": "string",
      "name": "string",
      "role": "MORGUE_STAFF",
      "metrics": {
        "assignedCases": 15,
        "completedToday": 5,
        "pendingTasks": 8,
        "utilization": 85
      },
      "caseTypes": {
        "admissions": 5,
        "referrals": 7,
        "releases": 3
      }
    }
  ],
  "departmentMetrics": {
    "pathology": {
      "totalCases": 50,
      "averageProcessingTime": "2.5 days",
      "backlog": 10
    },
    "morgue": {
      "totalBodies": 150,
      "averageStayDuration": "5 days",
      "capacityUtilization": 75
    }
  },
  "timeDistribution": {
    "collections": 20,
    "admissions": 30,
    "referrals": 25,
    "releases": 25
  }
}
```

### GET /api/dashboard/analytics

Retrieve detailed analytics and trends.

#### Query Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| dateFrom | string | Filter by date range start (ISO format) | No |
| dateTo | string | Filter by date range end (ISO format) | No |
| type | string | Type of analytics (OCCUPANCY, PROCESSING_TIME, etc.) | No |
| interval | string | Time interval for grouping (DAY, WEEK, MONTH) | No |

#### Response

```json
{
  "trends": {
    "occupancy": [
      {
        "date": "2024-02-01",
        "value": 75,
        "benchmark": 70
      }
    ],
    "processingTime": [
      {
        "type": "ADMISSION",
        "averageTime": "2 hours",
        "trend": "+5%"
      }
    ],
    "caseDistribution": {
      "crimeScene": 40,
      "hospital": 45,
      "other": 15
    }
  },
  "performance": {
    "slaCompliance": 95,
    "averageResponseTime": "30 minutes",
    "qualityMetrics": {
      "documentationAccuracy": 98,
      "procedureCompliance": 97
    }
  },
  "forecasts": {
    "expectedOccupancy": [
      {
        "date": "2024-02-08",
        "value": 80,
        "confidence": 0.85
      }
    ],
    "resourceRequirements": {
      "staff": 25,
      "fridges": 180
    }
  }
}
```

### GET /api/dashboard/reports

Generate and retrieve system reports.

#### Query Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| type | string | Report type (DAILY, WEEKLY, MONTHLY) | Yes |
| dateFrom | string | Filter by date range start (ISO format) | Yes |
| dateTo | string | Filter by date range end (ISO format) | Yes |
| format | string | Output format (JSON, CSV, PDF) | No |

#### Response

```json
{
  "reportMetadata": {
    "type": "MONTHLY",
    "period": "February 2024",
    "generatedAt": "2024-02-01T10:00:00Z",
    "format": "JSON"
  },
  "summary": {
    "totalCases": 450,
    "completedCases": 380,
    "pendingCases": 70,
    "averageProcessingTime": "3.2 days"
  },
  "details": {
    "collections": {
      "total": 150,
      "byType": {
        "crimeScene": 60,
        "hospital": 80,
        "other": 10
      }
    },
    "admissions": {
      "total": 145,
      "byFacility": [
        {
          "facility": "Main Morgue",
          "count": 85
        }
      ]
    },
    "referrals": {
      "total": 75,
      "byType": {
        "lodox": 30,
        "xray": 25,
        "specimen": 20
      }
    },
    "releases": {
      "total": 130,
      "averageProcessingTime": "2.5 days"
    }
  },
  "compliance": {
    "slaAdherence": 95,
    "documentationCompleteness": 98,
    "procedureCompliance": 97
  }
}
```

## Error Responses

### 400 Bad Request

```json
{
  "error": "Invalid request parameters",
  "details": {
    "field": "error description"
  }
}
```

### 401 Unauthorized

```json
{
  "error": "Unauthorized"
}
```

### 403 Forbidden

```json
{
  "error": "Insufficient permissions"
}
```

### 500 Internal Server Error

```json
{
  "error": "Internal Server Error"
}
```

## Access Control

Dashboard access is role-based with the following permissions:

- `ADMIN`: Full access to all dashboard features
- `MORGUE_STAFF`: Access to facility-specific dashboards and workload metrics
- `PATHOLOGIST`: Access to workload and analytics related to pathology
- `FIELD_EMPLOYEE`: Limited access to workload metrics
- `SECURITY_STAFF`: Access to facility occupancy and security-related metrics

## Notes

- All time-series data is provided in UTC
- Performance metrics are calculated based on the last 30 days by default
- Real-time updates are available through WebSocket connections
- Dashboard data is cached for 5 minutes to optimize performance
- Custom date ranges are limited to a maximum of 12 months
- Report generation for large date ranges may be processed asynchronously
- Trend analysis requires at least 7 days of historical data
- Forecasting accuracy improves with more historical data 