# Facilities API Documentation

## Overview

The Facilities API provides endpoints for managing mortuary facilities, including their equipment, storage units, and capacity.

## Base URL
```
https://api.gppathology.com/v1/facilities
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process.

```
Authorization: Bearer {token}
```

## Facilities

### List Facilities
```typescript
GET /api/facilities
Authorization: Bearer {token}

Parameters:
- region: string (optional) - Filter by geographic region
- status: string (optional) - Filter by operational status
- type: string (optional) - Filter by facility type

Response:
[
  {
    "id": string,
    "name": string,
    "type": "MORTUARY" | "HOSPITAL" | "LABORATORY" | "CLINIC",
    "address": {
      "street": string,
      "city": string,
      "state": string,
      "postalCode": string,
      "country": string,
      "coordinates": {
        "latitude": number,
        "longitude": number
      }
    },
    "contact": {
      "phone": string,
      "email": string,
      "website": string,
      "primaryContact": string
    },
    "status": "ACTIVE" | "INACTIVE" | "MAINTENANCE",
    "capacity": {
      "totalUnits": number,
      "availableUnits": number,
      "occupancy": number
    },
    "metadata": {
      "licenseNumber": string,
      "certifications": string[],
      "operatingHours": object,
      "establishedDate": string
    }
  }
]
```

### Get Facility Details
```typescript
GET /api/facilities/:id
Authorization: Bearer {token}

Response:
{
  "id": string,
  "name": string,
  "type": "MORTUARY" | "HOSPITAL" | "LABORATORY" | "CLINIC",
  "address": {
    "street": string,
    "city": string,
    "state": string,
    "postalCode": string,
    "country": string,
    "coordinates": {
      "latitude": number,
      "longitude": number
    }
  },
  "contact": {
    "phone": string,
    "email": string,
    "website": string,
    "primaryContact": string
  },
  "status": "ACTIVE" | "INACTIVE" | "MAINTENANCE",
  "capacity": {
    "totalUnits": number,
    "availableUnits": number,
    "occupancy": number
  },
  "metadata": {
    "licenseNumber": string,
    "certifications": string[],
    "operatingHours": object,
    "establishedDate": string
  },
  "staff": [
    {
      "id": string,
      "name": string,
      "role": string,
      "contactInfo": {
        "email": string,
        "phone": string
      }
    }
  ],
  "equipment": [
    {
      "id": string,
      "type": string,
      "status": string,
      "lastMaintenance": string,
      "nextMaintenance": string
    }
  ],
  "storageUnits": [
    {
      "id": string,
      "type": string,
      "capacity": number,
      "occupied": number,
      "temperature": number,
      "status": string
    }
  ]
}
```

### Create Facility
```typescript
POST /api/facilities
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "type": "MORTUARY" | "HOSPITAL" | "LABORATORY" | "CLINIC",
  "address": {
    "street": string,
    "city": string,
    "state": string,
    "postalCode": string,
    "country": string,
    "coordinates": {
      "latitude": number,
      "longitude": number
    }
  },
  "contact": {
    "phone": string,
    "email": string,
    "website": string,
    "primaryContact": string
  },
  "status": "ACTIVE" | "INACTIVE" | "MAINTENANCE",
  "capacity": {
    "totalUnits": number
  },
  "metadata": {
    "licenseNumber": string,
    "certifications": string[],
    "operatingHours": object,
    "establishedDate": string
  }
}

Response:
{
  "id": string,
  "name": string,
  "type": string,
  "status": string,
  "createdAt": string
}
```

### Update Facility
```typescript
PUT /api/facilities/:id
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "status": string,
  "contact": {
    "phone": string,
    "email": string,
    "website": string,
    "primaryContact": string
  },
  "capacity": {
    "totalUnits": number
  },
  "metadata": {
    "licenseNumber": string,
    "certifications": string[],
    "operatingHours": object
  }
}

Response:
{
  "id": string,
  "updatedAt": string,
  "name": string,
  "status": string
}
```

### Delete Facility
```typescript
DELETE /api/facilities/:id
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

## Storage Units

### List Storage Units
```typescript
GET /api/facilities/:facilityId/storage-units
Authorization: Bearer {token}

Parameters:
- type: string (optional) - Filter by unit type
- status: string (optional) - Filter by operational status
- available: boolean (optional) - Filter by availability

Response:
[
  {
    "id": string,
    "facilityId": string,
    "name": string,
    "type": "FRIDGE" | "FREEZER" | "CABINET" | "SHELF",
    "capacity": number,
    "occupied": number,
    "temperature": number,
    "status": "ACTIVE" | "INACTIVE" | "MAINTENANCE" | "ALARM",
    "lastCheck": string,
    "metadata": {
      "manufacturer": string,
      "model": string,
      "installDate": string,
      "lastMaintenance": string,
      "nextMaintenance": string
    },
    "alerts": [
      {
        "type": string,
        "message": string,
        "timestamp": string,
        "resolved": boolean
      }
    ],
    "positions": [
      {
        "id": string,
        "code": string,
        "occupied": boolean,
        "bodyId": string
      }
    ]
  }
]
```

### Create Storage Unit
```typescript
POST /api/facilities/:facilityId/storage-units
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "type": "FRIDGE" | "FREEZER" | "CABINET" | "SHELF",
  "capacity": number,
  "temperature": number,
  "status": "ACTIVE" | "INACTIVE" | "MAINTENANCE",
  "metadata": {
    "manufacturer": string,
    "model": string,
    "installDate": string,
    "lastMaintenance": string,
    "nextMaintenance": string
  }
}

Response:
{
  "id": string,
  "facilityId": string,
  "name": string,
  "type": string,
  "capacity": number,
  "status": string,
  "createdAt": string
}
```

### Update Storage Unit
```typescript
PUT /api/facilities/:facilityId/storage-units/:unitId
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "status": string,
  "temperature": number,
  "metadata": {
    "lastMaintenance": string,
    "nextMaintenance": string
  }
}

Response:
{
  "id": string,
  "name": string,
  "status": string,
  "updatedAt": string
}
```

### Log Temperature
```typescript
POST /api/facilities/:facilityId/storage-units/:unitId/temperature
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "temperature": number,
  "timestamp": string,
  "recordedBy": string,
  "notes": string
}

Response:
{
  "id": string,
  "unitId": string,
  "temperature": number,
  "timestamp": string,
  "recordedBy": string,
  "status": "NORMAL" | "WARNING" | "CRITICAL"
}
```

## Equipment

### List Equipment
```typescript
GET /api/facilities/:facilityId/equipment
Authorization: Bearer {token}

Parameters:
- type: string (optional) - Filter by equipment type
- status: string (optional) - Filter by operational status

Response:
[
  {
    "id": string,
    "facilityId": string,
    "name": string,
    "type": string,
    "serialNumber": string,
    "status": "OPERATIONAL" | "MAINTENANCE" | "REPAIR" | "DECOMMISSIONED",
    "location": string,
    "purchaseDate": string,
    "warranty": {
      "provider": string,
      "expiryDate": string,
      "details": string
    },
    "maintenance": {
      "lastMaintenance": string,
      "nextMaintenance": string,
      "maintenanceProvider": string,
      "contactInfo": string
    },
    "notes": string
  }
]
```

### Create Equipment
```typescript
POST /api/facilities/:facilityId/equipment
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "type": string,
  "serialNumber": string,
  "status": "OPERATIONAL" | "MAINTENANCE" | "REPAIR" | "DECOMMISSIONED",
  "location": string,
  "purchaseDate": string,
  "warranty": {
    "provider": string,
    "expiryDate": string,
    "details": string
  },
  "maintenance": {
    "nextMaintenance": string,
    "maintenanceProvider": string,
    "contactInfo": string
  },
  "notes": string
}

Response:
{
  "id": string,
  "facilityId": string,
  "name": string,
  "type": string,
  "status": string,
  "createdAt": string
}
```

### Log Maintenance
```typescript
POST /api/facilities/:facilityId/equipment/:equipmentId/maintenance
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "date": string,
  "performedBy": string,
  "type": "ROUTINE" | "REPAIR" | "CALIBRATION" | "INSPECTION",
  "details": string,
  "parts": [
    {
      "name": string,
      "partNumber": string,
      "cost": number
    }
  ],
  "cost": number,
  "nextMaintenanceDate": string,
  "attachments": string[]
}

Response:
{
  "id": string,
  "equipmentId": string,
  "date": string,
  "type": string,
  "status": "COMPLETED" | "PENDING",
  "nextMaintenanceDate": string
}
``` 