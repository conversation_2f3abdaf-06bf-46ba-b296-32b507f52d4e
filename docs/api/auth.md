# Authentication API Documentation

## Overview

The Authentication API provides endpoints for user authentication, session management, and security-related operations including login, logout, token refresh, and multi-factor authentication.

## Base URL
```
https://api.gppathology.com/v1/auth
```

## Authentication Flow

Most endpoints in this API do not require authentication. The purpose of these endpoints is to establish authentication for use with other API endpoints.

## Login and Session Management

### Login
```typescript
POST /api/auth/login
Content-Type: application/json

Request:
{
  "username": string,
  "password": string,
  "deviceId": string,
  "deviceInfo": {
    "type": string,
    "os": string,
    "browser": string,
    "appVersion": string
  }
}

Response:
{
  "token": string,
  "refreshToken": string,
  "user": {
    "id": string,
    "email": string,
    "name": string,
    "role": string,
    "permissions": string[],
    "facilities": string[]
  },
  "expiresIn": number,
  "requiresMfa": boolean,
  "mfaType": "APP" | "SMS" | "EMAIL"
}
```

### Refresh Token
```typescript
POST /api/auth/refresh
Content-Type: application/json

Request:
{
  "refreshToken": string
}

Response:
{
  "token": string,
  "refreshToken": string,
  "expiresIn": number
}
```

### Logout
```typescript
POST /api/auth/logout
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

### Logout All Sessions
```typescript
POST /api/auth/logout-all
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "count": number,
  "message": string
}
```

### Get Current Session
```typescript
GET /api/auth/session
Authorization: Bearer {token}

Response:
{
  "id": string,
  "userId": string,
  "createdAt": string,
  "lastActiveAt": string,
  "expiresAt": string,
  "deviceInfo": {
    "type": string,
    "os": string,
    "browser": string,
    "ip": string,
    "location": string
  },
  "permissions": string[],
  "role": string
}
```

## Multi-Factor Authentication

### Initiate MFA
```typescript
POST /api/auth/mfa/initiate
Content-Type: application/json

Request:
{
  "userId": string,
  "mfaType": "APP" | "SMS" | "EMAIL"
}

Response:
{
  "success": boolean,
  "mfaToken": string,
  "expiresIn": number,
  "destination": string, // Masked phone or email
  "message": string
}
```

### Verify MFA
```typescript
POST /api/auth/mfa/verify
Content-Type: application/json

Request:
{
  "mfaToken": string,
  "code": string
}

Response:
{
  "success": boolean,
  "token": string,
  "refreshToken": string,
  "expiresIn": number,
  "user": {
    "id": string,
    "email": string,
    "name": string,
    "role": string,
    "permissions": string[]
  }
}
```

### Setup MFA
```typescript
POST /api/auth/mfa/setup
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "mfaType": "APP" | "SMS" | "EMAIL",
  "phoneNumber": string,  // Required for SMS
  "email": string         // Required for EMAIL
}

Response:
{
  "success": boolean,
  "secretKey": string,    // For APP type
  "qrCodeUrl": string,    // For APP type
  "message": string
}
```

### Confirm MFA Setup
```typescript
POST /api/auth/mfa/confirm
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "code": string
}

Response:
{
  "success": boolean,
  "backupCodes": string[],
  "message": string
}
```

### Disable MFA
```typescript
POST /api/auth/mfa/disable
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "password": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

### Use Recovery Code
```typescript
POST /api/auth/mfa/recovery
Content-Type: application/json

Request:
{
  "mfaToken": string,
  "recoveryCode": string
}

Response:
{
  "success": boolean,
  "token": string,
  "refreshToken": string,
  "expiresIn": number,
  "newRecoveryCodes": string[],
  "message": string
}
```

## Password Management

### Forgot Password
```typescript
POST /api/auth/forgot-password
Content-Type: application/json

Request:
{
  "email": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

### Reset Password
```typescript
POST /api/auth/reset-password
Content-Type: application/json

Request:
{
  "token": string,
  "newPassword": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

### Change Password
```typescript
POST /api/auth/change-password
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "currentPassword": string,
  "newPassword": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

## Security Settings

### Get Security Settings
```typescript
GET /api/auth/security
Authorization: Bearer {token}

Response:
{
  "mfaEnabled": boolean,
  "mfaType": "APP" | "SMS" | "EMAIL",
  "lastPasswordChange": string,
  "passwordExpiresAt": string,
  "activeSessions": number,
  "lastLogin": {
    "timestamp": string,
    "ip": string,
    "device": string,
    "location": string
  },
  "recentActivity": [
    {
      "type": string,
      "timestamp": string,
      "ip": string,
      "device": string,
      "location": string
    }
  ],
  "loginNotificationsEnabled": boolean
}
```

### Update Security Settings
```typescript
PUT /api/auth/security
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "loginNotificationsEnabled": boolean
}

Response:
{
  "success": boolean,
  "updated": string[],
  "message": string
}
```

## Social Authentication

### Social Login Redirect
```typescript
GET /api/auth/social/:provider
```

### Social Login Callback
```typescript
GET /api/auth/social/:provider/callback
```

### Link Social Account
```typescript
POST /api/auth/social/:provider/link
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "provider": string,
  "linked": boolean,
  "message": string
}
```

### Unlink Social Account
```typescript
POST /api/auth/social/:provider/unlink
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "provider": string,
  "unlinked": boolean,
  "message": string
}
```

## API Keys

### List API Keys
```typescript
GET /api/auth/api-keys
Authorization: Bearer {token}

Response:
{
  "apiKeys": [
    {
      "id": string,
      "name": string,
      "createdAt": string,
      "expiresAt": string,
      "lastUsed": string,
      "permissions": string[],
      "status": "ACTIVE" | "EXPIRED" | "REVOKED"
    }
  ]
}
```

### Create API Key
```typescript
POST /api/auth/api-keys
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "expiresIn": number, // days
  "permissions": string[]
}

Response:
{
  "id": string,
  "key": string, // Only shown once
  "name": string,
  "createdAt": string,
  "expiresAt": string,
  "permissions": string[],
  "status": "ACTIVE"
}
```

### Revoke API Key
```typescript
DELETE /api/auth/api-keys/:id
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

## Service Accounts

### List Service Accounts
```typescript
GET /api/auth/service-accounts
Authorization: Bearer {token}

Response:
{
  "serviceAccounts": [
    {
      "id": string,
      "name": string,
      "description": string,
      "createdAt": string,
      "createdBy": string,
      "permissions": string[],
      "status": "ACTIVE" | "INACTIVE",
      "lastAuthenticated": string
    }
  ]
}
```

### Create Service Account
```typescript
POST /api/auth/service-accounts
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "description": string,
  "permissions": string[]
}

Response:
{
  "id": string,
  "clientId": string,
  "clientSecret": string, // Only shown once
  "name": string,
  "description": string,
  "permissions": string[],
  "status": "ACTIVE"
}
```

### Update Service Account
```typescript
PUT /api/auth/service-accounts/:id
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "description": string,
  "permissions": string[],
  "status": "ACTIVE" | "INACTIVE"
}

Response:
{
  "id": string,
  "name": string,
  "description": string,
  "permissions": string[],
  "status": string,
  "updatedAt": string
}
```

### Delete Service Account
```typescript
DELETE /api/auth/service-accounts/:id
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

### Service Account Authentication
```typescript
POST /api/auth/service-accounts/token
Content-Type: application/json

Request:
{
  "clientId": string,
  "clientSecret": string,
  "grantType": "client_credentials"
}

Response:
{
  "token": string,
  "expiresIn": number,
  "tokenType": "Bearer"
}
``` 