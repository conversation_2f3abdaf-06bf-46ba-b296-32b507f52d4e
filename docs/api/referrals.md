# Referrals API Documentation

This document details the Referrals API endpoints for the GP Pathology system.

## Overview

Referrals endpoints handle the management of body referrals to external facilities or departments for specialized procedures or examinations.

## Available Endpoints

- [GET /api/referrals](#get-apireferrals)
- [GET /api/referrals/{id}](#get-apireferralsid)
- [POST /api/referrals](#post-apireferrals)
- [PUT /api/referrals/{id}](#put-apireferralsid)
- [DELETE /api/referrals/{id}](#delete-apireferralsid)

## Endpoints

### GET /api/referrals

Retrieve a list of body referrals with optional filtering.

#### Query Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| status | string | Filter by status (PENDING, IN_PROGRESS, etc.) | No |
| dateFrom | string | Filter by date range start (ISO format) | No |
| dateTo | string | Filter by date range end (ISO format) | No |
| type | string | Filter by referral type | No |
| priority | string | Filter by priority level | No |
| assignedToId | string | Filter by assigned staff member | No |
| referredById | string | Filter by referring staff member | No |

#### Response

```json
{
  "referrals": [
    {
      "id": "string",
      "bodyId": "string",
      "referralType": "LODOX",
      "status": "PENDING",
      "referralDate": "2024-02-01T10:00:00Z",
      "priority": "MEDIUM",
      "reason": "string",
      "notes": "string",
      "attachments": ["string"],
      "deadline": "2024-02-03T10:00:00Z",
      "requiresReadmission": true,
      "referredBy": {
        "id": "string",
        "name": "string",
        "role": "PATHOLOGIST"
      },
      "assignedTo": {
        "id": "string",
        "name": "string",
        "role": "MORGUE_STAFF"
      },
      "body": {
        "id": "string",
        "trackingNumber": "string",
        "status": "REFERRED"
      },
      "createdAt": "2024-02-01T10:00:00Z",
      "updatedAt": "2024-02-01T10:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "pageSize": 10
}
```

### GET /api/referrals/{id}

Retrieve details of a specific referral.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Referral ID |

#### Response

```json
{
  "id": "string",
  "bodyId": "string",
  "referralType": "LODOX",
  "status": "PENDING",
  "referralDate": "2024-02-01T10:00:00Z",
  "priority": "MEDIUM",
  "reason": "string",
  "notes": "string",
  "attachments": ["string"],
  "deadline": "2024-02-03T10:00:00Z",
  "requiresReadmission": true,
  "referredBy": {
    "id": "string",
    "name": "string",
    "role": "PATHOLOGIST"
  },
  "assignedTo": {
    "id": "string",
    "name": "string",
    "role": "MORGUE_STAFF"
  },
  "body": {
    "id": "string",
    "trackingNumber": "string",
    "status": "REFERRED",
    "bodyTag": {
      "id": "string",
      "tagNumber": "string",
      "status": "IN_TRANSIT"
    }
  },
  "createdAt": "2024-02-01T10:00:00Z",
  "updatedAt": "2024-02-01T10:00:00Z"
}
```

### POST /api/referrals

Create a new body referral.

#### Required Role
- PATHOLOGIST
- MORGUE_STAFF
- ADMIN

#### Request Body

```json
{
  "bodyId": "string",
  "referralType": "LODOX",
  "assignedToId": "string",
  "priority": "MEDIUM",
  "reason": "string",
  "notes": "string",
  "attachments": ["string"],
  "deadline": "2024-02-03T10:00:00Z",
  "requiresReadmission": true
}
```

#### Response

Returns the created referral object with HTTP 201 status code.

### PUT /api/referrals/{id}

Update an existing referral.

#### Required Role
- PATHOLOGIST
- MORGUE_STAFF
- ADMIN

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Referral ID |

#### Request Body

```json
{
  "status": "IN_PROGRESS",
  "priority": "HIGH",
  "notes": "string",
  "attachments": ["string"],
  "deadline": "2024-02-03T10:00:00Z"
}
```

#### Response

Returns the updated referral object.

### DELETE /api/referrals/{id}

Delete a referral. This operation is only allowed for pending referrals.

#### Required Role
- ADMIN

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Referral ID |

#### Response

```json
{
  "message": "Referral deleted successfully"
}
```

## Error Responses

### 400 Bad Request

```json
{
  "error": "Invalid request parameters",
  "details": {
    "field": "error description"
  }
}
```

### 401 Unauthorized

```json
{
  "error": "Unauthorized"
}
```

### 403 Forbidden

```json
{
  "error": "Insufficient permissions"
}
```

### 404 Not Found

```json
{
  "error": "Referral not found"
}
```

### 409 Conflict

```json
{
  "error": "Referral cannot be deleted - already in progress"
}
```

### 500 Internal Server Error

```json
{
  "error": "Internal Server Error"
}
```

## Referral Types

Available referral types:
- `LODOX` - LODOX scan
- `XRAY` - X-ray examination
- `SPECIMEN` - Specimen collection
- `OTHER` - Other procedures

## Referral Status Flow

1. `PENDING` - Initial status when referral is created
2. `IN_PROGRESS` - When procedure has started
3. `COMPLETED` - When procedure is completed
4. `RETURNED` - When body is returned to facility
5. `CANCELLED` - When referral is cancelled

## Priority Levels

- `LOW` - Standard priority
- `MEDIUM` - Elevated priority
- `HIGH` - Urgent priority
- `URGENT` - Immediate attention required

## Notes

- Attachments should be uploaded separately using the file upload endpoint before being referenced
- Deadline is optional but recommended for proper scheduling
- Body status is automatically updated when referral status changes
- Only pending referrals can be deleted
- Completed referrals cannot be modified
- If `requiresReadmission` is true, a new admission must be created when the body returns 