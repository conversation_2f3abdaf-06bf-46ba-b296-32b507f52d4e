# Dashboard API Documentation

## Overview

The Dashboard API provides endpoints for retrieving aggregated data, key performance indicators, and visualizations for the GP Pathology dashboard interfaces.

## Base URL
```
https://api.gppathology.com/v1/dashboard
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process.

```
Authorization: Bearer {token}
```

## Dashboard Data

### Get Dashboard Summary
```typescript
GET /api/dashboard/summary
Authorization: Bearer {token}

Parameters:
- facilityId: string (optional) - Filter by specific facility
- period: string (optional) - "daily" | "weekly" | "monthly" | "yearly" (default: "daily")
- dateFrom: string (optional) - Filter by start date (ISO format)
- dateTo: string (optional) - Filter by end date (ISO format)

Response:
{
  "period": string,
  "dateRange": {
    "from": string,
    "to": string
  },
  "facilities": {
    "total": number,
    "active": number
  },
  "bodies": {
    "total": number,
    "collected": number,
    "admitted": number,
    "inStorage": number,
    "released": number,
    "transferred": number
  },
  "collections": {
    "total": number,
    "pending": number,
    "completed": number,
    "byType": {
      "FIELD": number,
      "HOSPITAL": number,
      "MORTUARY": number,
      "SCENE": number
    }
  },
  "admissions": {
    "total": number,
    "pending": number,
    "completed": number,
    "cancelled": number
  },
  "releases": {
    "total": number,
    "pending": number,
    "completed": number,
    "cancelled": number
  },
  "storage": {
    "totalCapacity": number,
    "occupied": number,
    "available": number,
    "utilization": number,
    "byFacility": [
      {
        "facilityId": string,
        "facilityName": string,
        "capacity": number,
        "occupied": number,
        "utilization": number
      }
    ]
  },
  "alerts": {
    "critical": number,
    "warning": number,
    "info": number
  }
}
```

### Get Activity Timeline
```typescript
GET /api/dashboard/timeline
Authorization: Bearer {token}

Parameters:
- type: string (optional) - Type of data to include ("collections", "admissions", "releases", "all")
- facilityId: string (optional) - Filter by specific facility
- interval: string (optional) - "hourly" | "daily" | "weekly" | "monthly" (default: "daily")
- dateFrom: string (optional) - Filter by start date (ISO format)
- dateTo: string (optional) - Filter by end date (ISO format)

Response:
{
  "interval": string,
  "dateRange": {
    "from": string,
    "to": string
  },
  "dataPoints": [
    {
      "timestamp": string,
      "collections": number,
      "admissions": number,
      "releases": number,
      "total": number
    }
  ]
}
```

### Get Facility Statistics
```typescript
GET /api/dashboard/facilities
Authorization: Bearer {token}

Parameters:
- dateFrom: string (optional) - Filter by start date
- dateTo: string (optional) - Filter by end date
- sortBy: string (optional) - Property to sort by (e.g., "utilization", "collections")
- order: string (optional) - Sort order ("asc" or "desc")

Response:
{
  "facilities": [
    {
      "id": string,
      "name": string,
      "type": string,
      "status": string,
      "capacity": {
        "total": number,
        "occupied": number,
        "available": number,
        "utilization": number
      },
      "activity": {
        "collections": number,
        "admissions": number,
        "releases": number,
        "transfers": number
      },
      "alerts": {
        "critical": number,
        "warning": number,
        "info": number
      }
    }
  ],
  "totals": {
    "facilities": number,
    "capacity": number,
    "occupied": number,
    "utilization": number,
    "collections": number,
    "admissions": number,
    "releases": number
  }
}
```

### Get User Activity Dashboard
```typescript
GET /api/dashboard/user-activity
Authorization: Bearer {token}

Parameters:
- dateFrom: string (optional) - Filter by start date
- dateTo: string (optional) - Filter by end date
- limit: number (optional) - Number of top users to return

Response:
{
  "totalUsers": number,
  "activeUsers": number,
  "dateRange": {
    "from": string,
    "to": string
  },
  "topUsers": [
    {
      "userId": string,
      "name": string,
      "role": string,
      "activity": {
        "total": number,
        "collections": number,
        "admissions": number,
        "releases": number,
        "other": number
      },
      "lastActive": string
    }
  ],
  "activityByRole": [
    {
      "role": string,
      "usersCount": number,
      "activityCount": number,
      "percentage": number
    }
  ],
  "activityByFacility": [
    {
      "facilityId": string,
      "facilityName": string,
      "usersCount": number,
      "activityCount": number,
      "percentage": number
    }
  ]
}
```

## Body Management

### Get Body Status Overview
```typescript
GET /api/dashboard/bodies
Authorization: Bearer {token}

Parameters:
- facilityId: string (optional) - Filter by specific facility
- dateFrom: string (optional) - Filter by start date
- dateTo: string (optional) - Filter by end date

Response:
{
  "total": number,
  "byStatus": {
    "COLLECTED": number,
    "ADMITTED": number,
    "IN_STORAGE": number,
    "RELEASED": number,
    "TRANSFERRED": number
  },
  "byFacility": [
    {
      "facilityId": string,
      "facilityName": string,
      "total": number,
      "byStatus": {
        "COLLECTED": number,
        "ADMITTED": number,
        "IN_STORAGE": number,
        "RELEASED": number,
        "TRANSFERRED": number
      }
    }
  ],
  "timeline": [
    {
      "date": string,
      "collected": number,
      "admitted": number,
      "released": number,
      "transferred": number
    }
  ],
  "averageTimes": {
    "collectionToAdmission": number,
    "admissionToRelease": number,
    "totalProcessingTime": number
  }
}
```

### Get Storage Utilization
```typescript
GET /api/dashboard/storage
Authorization: Bearer {token}

Parameters:
- facilityId: string (optional) - Filter by specific facility
- type: string (optional) - Filter by storage type ("FRIDGE", "FREEZER", etc.)

Response:
{
  "overall": {
    "totalUnits": number,
    "totalCapacity": number,
    "occupied": number,
    "available": number,
    "utilization": number
  },
  "byFacility": [
    {
      "facilityId": string,
      "facilityName": string,
      "totalUnits": number,
      "totalCapacity": number,
      "occupied": number,
      "available": number,
      "utilization": number
    }
  ],
  "byType": [
    {
      "type": string,
      "totalUnits": number,
      "totalCapacity": number,
      "occupied": number,
      "available": number,
      "utilization": number
    }
  ],
  "units": [
    {
      "id": string,
      "name": string,
      "type": string,
      "facilityId": string,
      "facilityName": string,
      "capacity": number,
      "occupied": number,
      "available": number,
      "utilization": number,
      "status": string,
      "temperature": number
    }
  ]
}
```

## Performance Metrics

### Get KPI Dashboard
```typescript
GET /api/dashboard/kpi
Authorization: Bearer {token}

Parameters:
- facilityId: string (optional) - Filter by specific facility
- period: string (optional) - "daily" | "weekly" | "monthly" | "yearly" | "custom"
- dateFrom: string (optional) - Filter by start date
- dateTo: string (optional) - Filter by end date
- compareWithPrevious: boolean (optional) - Include comparison with previous period

Response:
{
  "period": string,
  "dateRange": {
    "from": string,
    "to": string
  },
  "kpis": {
    "collections": {
      "value": number,
      "change": number,
      "trend": string,
      "goal": number,
      "performance": number
    },
    "admissions": {
      "value": number,
      "change": number,
      "trend": string,
      "goal": number,
      "performance": number
    },
    "releases": {
      "value": number,
      "change": number,
      "trend": string,
      "goal": number,
      "performance": number
    },
    "storageUtilization": {
      "value": number,
      "change": number,
      "trend": string,
      "goal": number,
      "performance": number
    },
    "averageProcessingTime": {
      "value": number,
      "change": number,
      "trend": string,
      "goal": number,
      "performance": number
    }
  },
  "comparison": {
    "previousPeriod": {
      "from": string,
      "to": string,
      "collections": number,
      "admissions": number,
      "releases": number,
      "storageUtilization": number,
      "averageProcessingTime": number
    }
  }
}
```

### Get Performance Dashboard
```typescript
GET /api/dashboard/performance
Authorization: Bearer {token}

Parameters:
- facilityId: string (optional) - Filter by specific facility
- period: string (optional) - "daily" | "weekly" | "monthly" | "yearly"
- dateFrom: string (optional) - Filter by start date
- dateTo: string (optional) - Filter by end date

Response:
{
  "period": string,
  "dateRange": {
    "from": string,
    "to": string
  },
  "processingTimes": {
    "collectionToAdmission": {
      "average": number,
      "min": number,
      "max": number,
      "median": number,
      "trend": [
        {
          "date": string,
          "value": number
        }
      ]
    },
    "admissionToRelease": {
      "average": number,
      "min": number,
      "max": number,
      "median": number,
      "trend": [
        {
          "date": string,
          "value": number
        }
      ]
    },
    "totalProcessingTime": {
      "average": number,
      "min": number,
      "max": number,
      "median": number,
      "trend": [
        {
          "date": string,
          "value": number
        }
      ]
    }
  },
  "throughput": {
    "daily": {
      "average": number,
      "min": number,
      "max": number,
      "trend": [
        {
          "date": string,
          "value": number
        }
      ]
    },
    "weekly": {
      "average": number,
      "min": number,
      "max": number,
      "trend": [
        {
          "date": string,
          "value": number
        }
      ]
    }
  },
  "efficiency": {
    "resourceUtilization": number,
    "processingRate": number,
    "backlogRate": number
  },
  "facilityComparison": [
    {
      "facilityId": string,
      "facilityName": string,
      "processingTime": number,
      "throughput": number,
      "efficiency": number,
      "ranking": number
    }
  ]
}
```

## Alerts Dashboard

### Get Alert Dashboard
```typescript
GET /api/dashboard/alerts
Authorization: Bearer {token}

Parameters:
- facilityId: string (optional) - Filter by specific facility
- severity: string (optional) - Filter by severity level ("critical", "warning", "info")
- status: string (optional) - Filter by status ("active", "acknowledged", "resolved")
- dateFrom: string (optional) - Filter by start date
- dateTo: string (optional) - Filter by end date

Response:
{
  "summary": {
    "total": number,
    "active": number,
    "acknowledged": number,
    "resolved": number,
    "bySeverity": {
      "critical": number,
      "warning": number,
      "info": number
    }
  },
  "byFacility": [
    {
      "facilityId": string,
      "facilityName": string,
      "total": number,
      "active": number,
      "critical": number,
      "warning": number,
      "info": number
    }
  ],
  "byType": [
    {
      "type": string,
      "total": number,
      "active": number,
      "critical": number,
      "warning": number,
      "info": number
    }
  ],
  "timeline": [
    {
      "date": string,
      "total": number,
      "critical": number,
      "warning": number,
      "info": number
    }
  ],
  "topAlerts": [
    {
      "id": string,
      "type": string,
      "severity": string,
      "message": string,
      "facilityId": string,
      "facilityName": string,
      "status": string,
      "timestamp": string,
      "count": number
    }
  ]
}
```

## User Dashboard

### Get User Dashboard
```typescript
GET /api/dashboard/user
Authorization: Bearer {token}

Response:
{
  "user": {
    "id": string,
    "name": string,
    "role": string,
    "facilities": [
      {
        "id": string,
        "name": string
      }
    ]
  },
  "recentActivity": [
    {
      "id": string,
      "type": string,
      "resourceType": string,
      "resourceId": string,
      "timestamp": string,
      "details": string
    }
  ],
  "assignedTasks": {
    "total": number,
    "overdue": number,
    "dueToday": number,
    "upcoming": number,
    "recentlyCompleted": number
  },
  "performance": {
    "collections": number,
    "admissions": number,
    "releases": number,
    "completedTasks": number,
    "avgCompletionTime": number
  },
  "notifications": {
    "unread": number,
    "total": number,
    "recent": [
      {
        "id": string,
        "type": string,
        "title": string,
        "timestamp": string,
        "read": boolean
      }
    ]
  }
}
```

### Get Custom Dashboard 
```typescript
GET /api/dashboard/custom/:dashboardId
Authorization: Bearer {token}

Response:
{
  "id": string,
  "name": string,
  "owner": {
    "id": string,
    "name": string
  },
  "layout": [
    {
      "id": string,
      "type": string,
      "title": string,
      "position": {
        "x": number,
        "y": number,
        "w": number,
        "h": number
      },
      "config": {
        "dataSource": string,
        "visualization": string,
        "filters": object,
        "refreshInterval": number
      },
      "data": object
    }
  ],
  "filters": {
    "dateRange": {
      "from": string,
      "to": string
    },
    "facilityId": string,
    "customFilters": object
  },
  "permissions": {
    "canEdit": boolean,
    "canShare": boolean,
    "isPublic": boolean
  },
  "createdAt": string,
  "updatedAt": string
}
```

### Create/Update Custom Dashboard
```typescript
POST /api/dashboard/custom
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "layout": [
    {
      "id": string,
      "type": string,
      "title": string,
      "position": {
        "x": number,
        "y": number,
        "w": number,
        "h": number
      },
      "config": {
        "dataSource": string,
        "visualization": string,
        "filters": object,
        "refreshInterval": number
      }
    }
  ],
  "filters": {
    "dateRange": {
      "from": string,
      "to": string
    },
    "facilityId": string,
    "customFilters": object
  },
  "permissions": {
    "isPublic": boolean,
    "sharedWith": string[]
  }
}

Response:
{
  "id": string,
  "name": string,
  "createdAt": string,
  "updatedAt": string
}
```

### Delete Custom Dashboard
```typescript
DELETE /api/dashboard/custom/:dashboardId
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
``` 