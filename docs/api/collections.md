# Collections API Documentation

## Overview

The Collections API provides endpoints for managing body collections, including field collection details, documentation, and chain of custody initialization.

## Base URL
```
https://api.gppathology.com/v1/collections
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process.

```
Authorization: Bearer {token}
```

## Collections

### List Collections
```typescript
GET /api/collections
Authorization: Bearer {token}

Parameters:
- type: string (optional) - Filter by collection type
- status: string (optional) - Filter by collection status
- dateFrom: string (optional) - Filter by collection date (from)
- dateTo: string (optional) - Filter by collection date (to)
- search: string (optional) - Search by location or reference
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "total": number,
  "data": [
    {
      "id": string,
      "name": string,
      "institution": string,
      "vehicleReg": string,
      "arrivalTime": string,
      "gpsCoords": string,
      "collectionType": "FIELD" | "HOSPITAL" | "MORTUARY" | "SCENE",
      "bodyDescription": string,
      "photos": string[],
      "status": "COLLECTED" | "PROCESSING" | "COMPLETED",
      "barcodeValue": string,
      "createdAt": string,
      "updatedAt": string,
      "user": {
        "id": string,
        "name": string,
        "role": string
      },
      "body": {
        "id": string,
        "trackingNumber": string,
        "status": string,
        "bodyTag": {
          "tagNumber": string,
          "status": string
        }
      }
    }
  ]
}
```

### Get Collection Details
```typescript
GET /api/collections/:id
Authorization: Bearer {token}

Response:
{
  "id": string,
  "name": string,
  "institution": string,
  "vehicleReg": string,
  "arrivalTime": string,
  "gpsCoords": string,
  "collectionType": "FIELD" | "HOSPITAL" | "MORTUARY" | "SCENE",
  "caseNumber": string,
  "hospitalRef": string,
  "collectionPoint": string,
  "bodyDescription": string,
  "weatherConditions": string,
  "temperature": string,
  "identificationMarks": string[],
  "personalEffects": string[],
  "witnessStatements": string[],
  "photos": string[],
  "status": "COLLECTED" | "PROCESSING" | "COMPLETED",
  "barcodeValue": string,
  "documents": string[],
  "collectedBy": string,
  "collectionNotes": string,
  "isVerified": boolean,
  "verifiedBy": string,
  "verifiedAt": string,
  "createdAt": string,
  "updatedAt": string,
  "user": {
    "id": string,
    "name": string,
    "role": string,
    "contactInfo": {
      "email": string,
      "phone": string
    }
  },
  "body": {
    "id": string,
    "trackingNumber": string,
    "status": string,
    "bodyTag": {
      "id": string,
      "tagNumber": string,
      "status": string
    },
    "metadata": {
      "bodyCondition": string,
      "bodyDescription": string,
      "gpsCoords": string
    }
  },
  "metadata": {
    "persalNumber": string,
    "isComplete": boolean,
    "notes": string
  }
}
```

### Create Collection
```typescript
POST /api/collections
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "institution": string,
  "vehicleReg": string,
  "arrivalTime": string,
  "gpsCoords": string,
  "bodyCollectionType": "FIELD" | "HOSPITAL" | "MORTUARY" | "SCENE",
  "bodyTagId": string,
  "bodyCondition": string,
  "bodyDescription": string,
  "bodyPhotos": string[],
  "persalNumber": string,
  "isComplete": boolean,
  "notes": string
}

Response:
{
  "data": {
    "id": string,
    "bodyId": string,
    "userId": string,
    "name": string,
    "institution": string,
    "vehicleReg": string,
    "arrivalTime": string,
    "gpsCoords": string,
    "collectionType": string,
    "bodyDescription": string,
    "photos": string[],
    "status": "COLLECTED",
    "barcodeValue": string,
    "createdAt": string
  }
}
```

### Update Collection
```typescript
PUT /api/collections/:id
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "name": string,
  "institution": string,
  "vehicleReg": string,
  "arrivalTime": string,
  "gpsCoords": string,
  "bodyCollectionType": string,
  "bodyCondition": string,
  "bodyDescription": string,
  "bodyPhotos": string[],
  "persalNumber": string,
  "isComplete": boolean,
  "notes": string,
  "status": "COLLECTED" | "PROCESSING" | "COMPLETED"
}

Response:
{
  "id": string,
  "name": string,
  "status": string,
  "updatedAt": string
}
```

### Delete Collection
```typescript
DELETE /api/collections/:id
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

### Upload Collection Photos
```typescript
POST /api/collections/:id/photos
Authorization: Bearer {token}
Content-Type: multipart/form-data

Request:
{
  "photos": File[],
  "description": string
}

Response:
{
  "success": boolean,
  "uploadedPhotos": [
    {
      "id": string,
      "url": string,
      "filename": string,
      "size": number,
      "mimeType": string,
      "description": string,
      "uploadedAt": string
    }
  ]
}
```

### Verify Collection
```typescript
POST /api/collections/:id/verify
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "verificationNotes": string
}

Response:
{
  "id": string,
  "isVerified": true,
  "verifiedBy": {
    "id": string,
    "name": string
  },
  "verifiedAt": string,
  "verificationNotes": string
}
```

## Collection Tags

### Get Available Tags
```typescript
GET /api/collections/tags/available
Authorization: Bearer {token}

Response:
{
  "tags": [
    {
      "id": string,
      "tagNumber": string,
      "status": "GENERATED",
      "createdAt": string
    }
  ],
  "total": number
}
```

### Validate Tag
```typescript
POST /api/collections/tags/validate
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "tagNumber": string
}

Response:
{
  "valid": boolean,
  "tag": {
    "id": string,
    "tagNumber": string,
    "status": string,
    "createdAt": string,
    "lastScannedAt": string,
    "lastScannedBy": string,
    "bodyId": string,
    "collectionId": string
  }
}
```

## Collection Reports

### Generate Collection Report
```typescript
POST /api/collections/:id/report
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "format": "PDF" | "DOCX" | "CSV",
  "includePhotos": boolean,
  "includeWitnessStatements": boolean,
  "includePersonalEffects": boolean
}

Response:
{
  "reportUrl": string,
  "expiresAt": string,
  "fileSize": number,
  "format": string
}
```

### Get Collection Statistics
```typescript
GET /api/collections/stats
Authorization: Bearer {token}

Parameters:
- period: string (optional) - "daily" | "weekly" | "monthly" | "yearly"
- from: string (optional) - Start date of period
- to: string (optional) - End date of period

Response:
{
  "period": string,
  "from": string,
  "to": string,
  "totalCollections": number,
  "byType": {
    "FIELD": number,
    "HOSPITAL": number,
    "MORTUARY": number,
    "SCENE": number
  },
  "byStatus": {
    "COLLECTED": number,
    "PROCESSING": number,
    "COMPLETED": number
  },
  "timeline": [
    {
      "date": string,
      "collections": number,
      "byType": {
        "FIELD": number,
        "HOSPITAL": number,
        "MORTUARY": number,
        "SCENE": number
      }
    }
  ],
  "topCollectors": [
    {
      "userId": string,
      "name": string,
      "count": number
    }
  ]
}
```

## Batch Operations

### Batch Update Collections
```typescript
POST /api/collections/batch
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "ids": string[],
  "action": "VERIFY" | "COMPLETE" | "PROCESS",
  "notes": string
}

Response:
{
  "success": boolean,
  "processed": number,
  "failed": number,
  "results": [
    {
      "id": string,
      "success": boolean,
      "message": string
    }
  ]
}
```