# GP Pathology API Documentation

This document provides detailed information about the GP Pathology API endpoints.

## Table of Contents

- [Authentication](#authentication)
- [Bodies](#bodies)
- [Collections](#collections)
- [Admissions](#admissions)
- [Referrals](#referrals)
- [Releases](#releases)

## Authentication

All API endpoints require authentication. Use the session token in the request header:

```http
Authorization: Bearer <session_token>
```

## Bodies

Endpoints for managing bodies in the system.

### GET /api/bodies

Retrieve a list of bodies with their related data.

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| status | string | Filter by body status (COLLECTED, ADMITTED, etc.) |
| dateFrom | string | Filter by date range start (ISO format) |
| dateTo | string | Filter by date range end (ISO format) |
| trackingNumber | string | Filter by tracking number |
| tagNumber | string | Filter by tag number |

#### Response

```json
{
  "bodies": [
    {
      "id": "string",
      "trackingNumber": "string",
      "bodyTagId": "string",
      "status": "COLLECTED",
      "deathRegistration": "string",
      "bodyTag": {
        "id": "string",
        "tagNumber": "string",
        "status": "GENERATED"
      },
      "collection": {
        "id": "string",
        "user": {
          "id": "string",
          "name": "string",
          "role": "FIELD_EMPLOYEE"
        }
      },
      "admissions": [...],
      "referrals": [...]
    }
  ]
}
```

### POST /api/bodies

Register a new body in the system.

#### Required Role
- FIELD_EMPLOYEE
- MORGUE_STAFF
- ADMIN

#### Request Body

```json
{
  "trackingNumber": "string",
  "tagNumber": "string",
  "deathRegistration": "string"
}
```

#### Response

```json
{
  "id": "string",
  "trackingNumber": "string",
  "bodyTagId": "string",
  "status": "COLLECTED",
  "deathRegistration": "string",
  "bodyTag": {
    "id": "string",
    "tagNumber": "string",
    "status": "GENERATED"
  }
}
```

### PUT /api/bodies

Update body details.

#### Required Role
- MORGUE_STAFF
- ADMIN

#### Request Body

```json
{
  "id": "string",
  "tagNumber": "string",
  "status": "string",
  "deathRegistration": "string"
}
```

#### Response

```json
{
  "id": "string",
  "trackingNumber": "string",
  "bodyTagId": "string",
  "status": "string",
  "deathRegistration": "string",
  "bodyTag": {
    "id": "string",
    "tagNumber": "string",
    "status": "string"
  }
}
```

## Collections

Endpoints for managing body collections.

### GET /api/collections

Retrieve a list of body collections.

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| type | string | Filter by collection type |
| status | string | Filter by status |
| dateFrom | string | Filter by date range start (ISO format) |
| dateTo | string | Filter by date range end (ISO format) |

#### Response

```json
{
  "collections": [
    {
      "id": "string",
      "bodyId": "string",
      "userId": "string",
      "collectionType": "CRIME_SCENE",
      "status": "COLLECTED",
      "user": {
        "id": "string",
        "name": "string",
        "role": "FIELD_EMPLOYEE"
      },
      "body": {
        "id": "string",
        "trackingNumber": "string",
        "status": "COLLECTED"
      }
    }
  ]
}
```

### POST /api/collections

Create a new body collection.

#### Required Role
- FIELD_EMPLOYEE
- ADMIN

#### Request Body

```json
{
  "bodyId": "string",
  "name": "string",
  "institution": "string",
  "vehicleReg": "string",
  "arrivalTime": "string",
  "gpsCoords": {
    "lat": number,
    "lng": number
  },
  "collectionType": "CRIME_SCENE",
  "bodyDescription": "string",
  "photos": ["string"],
  "barcodeValue": "string"
}
```

## Admissions

Endpoints for managing body admissions.

### GET /api/admissions

Retrieve a list of body admissions.

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| status | string | Filter by status |
| dateFrom | string | Filter by date range start (ISO format) |
| dateTo | string | Filter by date range end (ISO format) |
| facilityId | string | Filter by facility |

#### Response

```json
{
  "admissions": [
    {
      "id": "string",
      "bodyId": "string",
      "admissionType": "INITIAL",
      "admissionDate": "string",
      "status": "ACTIVE",
      "facility": {
        "id": "string",
        "name": "string"
      },
      "assignedTo": {
        "id": "string",
        "name": "string",
        "role": "MORGUE_STAFF"
      }
    }
  ]
}
```

### POST /api/admissions

Create a new body admission.

#### Required Role
- MORGUE_STAFF
- ADMIN

#### Request Body

```json
{
  "bodyId": "string",
  "facilityId": "string",
  "admissionType": "INITIAL",
  "admissionDate": "string",
  "assignedToId": "string",
  "storageLocation": "string",
  "notes": "string",
  "bodyCondition": "string",
  "temperature": "string",
  "barcodeValue": "string"
}
```

## Referrals

Endpoints for managing body referrals.

### GET /api/referrals

Retrieve a list of body referrals.

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| status | string | Filter by status |
| dateFrom | string | Filter by date range start (ISO format) |
| dateTo | string | Filter by date range end (ISO format) |
| type | string | Filter by referral type |

#### Response

```json
{
  "referrals": [
    {
      "id": "string",
      "bodyId": "string",
      "referralType": "LODOX",
      "status": "PENDING",
      "priority": "MEDIUM",
      "referredBy": {
        "id": "string",
        "name": "string",
        "role": "PATHOLOGIST"
      },
      "assignedTo": {
        "id": "string",
        "name": "string",
        "role": "MORGUE_STAFF"
      }
    }
  ]
}
```

### POST /api/referrals

Create a new body referral.

#### Required Role
- PATHOLOGIST
- MORGUE_STAFF
- ADMIN

#### Request Body

```json
{
  "bodyId": "string",
  "referralType": "LODOX",
  "assignedToId": "string",
  "priority": "MEDIUM",
  "reason": "string",
  "notes": "string",
  "attachments": ["string"],
  "deadline": "string"
}
```

## Releases

Endpoints for managing body releases.

### GET /api/releases

Retrieve a list of body releases.

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| status | string | Filter by status |
| dateFrom | string | Filter by date range start (ISO format) |
| dateTo | string | Filter by date range end (ISO format) |
| facilityId | string | Filter by facility |

#### Response

```json
{
  "releases": [
    {
      "id": "string",
      "bodyId": "string",
      "facilityId": "string",
      "releaseDate": "string",
      "releasedTo": "string",
      "status": "PENDING",
      "facility": {
        "id": "string",
        "name": "string"
      },
      "releasedBy": {
        "id": "string",
        "name": "string",
        "role": "MORGUE_STAFF"
      }
    }
  ]
}
```

### POST /api/releases

Create a new body release.

#### Required Role
- MORGUE_STAFF
- ADMIN

#### Request Body

```json
{
  "bodyId": "string",
  "facilityId": "string",
  "releaseDate": "string",
  "releasedTo": "string",
  "relationship": "string",
  "identificationDocument": "string",
  "contactDetails": "string",
  "notes": "string"
}
```

## Error Responses

All endpoints may return the following error responses:

### 401 Unauthorized

```json
{
  "error": "Unauthorized"
}
```

### 400 Bad Request

```json
{
  "error": "Error message describing the issue"
}
```

### 404 Not Found

```json
{
  "error": "Resource not found"
}
```

### 500 Internal Server Error

```json
{
  "error": "Internal Server Error"
}
``` 