# GP Pathology API Documentation

Welcome to the GP Pathology API Documentation. This repository contains comprehensive documentation for all available API endpoints in the GP Pathology system.

## Base URL

All API endpoints are prefixed with the following base URL:

```
https://api.gppathology.com/v1
```

## Authentication

Most API endpoints require authentication. Authentication is handled via JSON Web Tokens (JWT).

### Login

```typescript
POST /auth/login
Content-Type: application/json

Request:
{
  "username": string,
  "password": string,
  "deviceId": string
}

Response:
{
  "token": string,
  "refreshToken": string,
  "user": {
    "id": string,
    "role": UserRole,
    "permissions": string[],
    "facilities": string[]
  },
  "expiresIn": number
}
```

### Using the Token

Include the token in the `Authorization` header for all subsequent requests:

```
Authorization: Bearer {token}
```

## Rate Limits

API requests are subject to rate limiting to ensure system stability. Rate limits are specified in the response headers:

- `X-RateLimit-Limit`: Maximum number of requests allowed per window
- `X-RateLimit-Remaining`: Number of requests remaining in the current window
- `X-RateLimit-Reset`: Time when the current rate limit window resets (Unix timestamp)

When rate limits are exceeded, a `429 Too Many Requests` response will be returned.

## Available Endpoints

### Core Resources

| Resource | Description | Documentation |
|----------|-------------|---------------|
| Authentication | User authentication and authorization | [Authentication](./auth.md) |
| Collections | Managing body collections | [Collections](./collections.md) |
| Admissions | Facility admissions management | [Admissions](./admissions.md) |
| Bodies | Body tracking and management | [Bodies](./bodies.md) |
| Releases | Body release and transfer | [Releases](./releases.md) |
| Referrals | Case referrals between facilities | [Referrals](./referrals.md) |

### Facility Management

| Resource | Description | Documentation |
|----------|-------------|---------------|
| Facilities | Facility management | [Facilities](./facilities.md) |
| Equipment | Equipment tracking and maintenance | [Equipment](./equipment.md) |
| Storage | Storage units and temperature logging | [Storage](./storage.md) |

### Pathology

| Resource | Description | Documentation |
|----------|-------------|---------------|
| Pathology | Pathology workflows and procedures | [Pathology](./pathology.md) |
| Specimens | Specimen management | [Specimens](./specimens.md) |
| Reports | Pathology reports | [Reports](./reports.md) |

### User Management

| Resource | Description | Documentation |
|----------|-------------|---------------|
| Users | User management | [Users](./users.md) |
| Roles | Role-based access control | [Roles](./roles.md) |
| Permissions | Permission management | [Permissions](./permissions.md) |

### Communication

| Resource | Description | Documentation |
|----------|-------------|---------------|
| Notifications | User notifications | [Notifications](./notifications.md) |
| Email | Email management | [Email](./email.md) |
| Chat | Secure messaging | [Chat](./chat.md) |

### System Management

| Resource | Description | Documentation |
|----------|-------------|---------------|
| Dashboard | Dashboard data | [Dashboard](./dashboard.md) |
| Activity Log | System audit logs | [Activity Log](./activity-log.md) |
| AI | AI-powered features | [AI](./ai.md) |

## Status Codes

The API uses the following HTTP status codes:

| Status Code | Description |
|-------------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created successfully |
| 204 | No Content - Request successful, no content to return |
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource conflict |
| 422 | Unprocessable Entity - Validation error |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

## Errors

Error responses follow a standard format:

```json
{
  "error": "Error type",
  "message": "Human-readable error message",
  "details": {
    "field": "Specific field with error",
    "reason": "Reason for error"
  }
}
```

## Versioning

The API is versioned using the URL path. The current version is `v1`. When a new version is released, the previous version will remain accessible for a deprecation period.

## Support

For API support, please contact:

- Email: <EMAIL>
- Documentation: https://docs.gppathology.com/api
- Status: https://status.gppathology.com 