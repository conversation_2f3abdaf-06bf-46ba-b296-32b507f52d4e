# Pathology API Documentation

## Overview

The Pathology API provides endpoints for managing pathology-related operations, including reports, specimens, procedures, and workflows.

## Base URL
```
https://api.gppathology.com/v1/pathology
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process.

```
Authorization: Bearer {token}
```

## Reports

### Get Reports
```typescript
GET /api/pathology/reports
Authorization: Bearer {token}

Parameters:
- bodyId: string (required) - ID of the body
- procedureId: string (optional) - ID of the procedure
- workflowId: string (optional) - ID of the workflow
- reportType: string (optional) - Type of report

Response:
[
  {
    "id": string,
    "bodyId": string,
    "procedureId": string,
    "workflowId": string,
    "reportType": string,
    "status": "draft" | "completed" | "verified" | "archived",
    "pathologistId": string,
    "pathologistName": string,
    "dateCreated": string,
    "dateCompleted": string,
    "findings": string,
    "conclusion": string,
    "attachments": string[],
    "signatures": {
      "pathologist": string,
      "reviewer": string,
      "timestamp": string
    },
    "isVerified": boolean,
    "verifiedById": string,
    "verifiedByName": string,
    "verificationDate": string
  }
]
```

### Create Report
```typescript
POST /api/pathology/reports
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "bodyId": string,
  "procedureId": string,
  "workflowId": string,
  "reportType": string,
  "status": string,
  "pathologistId": string,
  "pathologistName": string,
  "findings": string,
  "conclusion": string,
  "attachments": string[]
}

Response:
{
  "id": string,
  "bodyId": string,
  "procedureId": string,
  "workflowId": string,
  "reportType": string,
  "status": string,
  "pathologistId": string,
  "pathologistName": string,
  "dateCreated": string,
  "findings": string,
  "conclusion": string,
  "attachments": string[],
  "signatures": {
    "pathologist": string
  },
  "isVerified": boolean
}
```

### Update Report
```typescript
PUT /api/pathology/reports?id={reportId}
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "status": string,
  "findings": string,
  "conclusion": string,
  "attachments": string[],
  "isVerified": boolean,
  "verifiedById": string,
  "verifiedByName": string
}

Response:
{
  "id": string,
  "updated": true,
  ... // Updated report data
}
```

## Specimens

### Get Specimens
```typescript
GET /api/pathology/specimens
Authorization: Bearer {token}

Parameters:
- bodyId: string (optional) - ID of the body
- procedureId: string (optional) - ID of the procedure
- status: string (optional) - Status of specimens

Response:
[
  {
    "id": string,
    "bodyId": string,
    "procedureId": string,
    "specimenType": string,
    "collectionDate": string,
    "collectedBy": string,
    "location": string,
    "description": string,
    "status": string,
    "storageInfo": {
      "containerType": string,
      "containerCode": string,
      "temperature": number,
      "location": string
    },
    "metadata": {
      "weight": number,
      "dimensions": string,
      "preservationMethod": string,
      "notes": string
    }
  }
]
```

### Create Specimen
```typescript
POST /api/pathology/specimens
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "bodyId": string,
  "procedureId": string,
  "specimenType": string,
  "collectionDate": string,
  "collectedBy": string,
  "location": string,
  "description": string,
  "status": string,
  "storageInfo": {
    "containerType": string,
    "containerCode": string,
    "temperature": number,
    "location": string
  },
  "metadata": {
    "weight": number,
    "dimensions": string,
    "preservationMethod": string,
    "notes": string
  }
}

Response:
{
  "id": string,
  ... // Created specimen data
}
```

## Procedures

### Get Procedures
```typescript
GET /api/pathology/procedures
Authorization: Bearer {token}

Parameters:
- bodyId: string (optional) - ID of the body
- status: string (optional) - Status of procedures
- type: string (optional) - Type of procedure

Response:
[
  {
    "id": string,
    "bodyId": string,
    "workflowId": string,
    "procedureType": string,
    "status": string,
    "scheduledDate": string,
    "completedDate": string,
    "assignedTo": string,
    "performedBy": string,
    "location": string,
    "notes": string,
    "attachments": string[],
    "results": {
      "summary": string,
      "details": object,
      "recommendations": string[]
    }
  }
]
```

### Create Procedure
```typescript
POST /api/pathology/procedures
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "bodyId": string,
  "workflowId": string,
  "procedureType": string,
  "status": string,
  "scheduledDate": string,
  "assignedTo": string,
  "location": string,
  "notes": string
}

Response:
{
  "id": string,
  ... // Created procedure data
}
```

### Update Procedure
```typescript
PUT /api/pathology/procedures?id={procedureId}
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "status": string,
  "completedDate": string,
  "performedBy": string,
  "notes": string,
  "attachments": string[],
  "results": {
    "summary": string,
    "details": object,
    "recommendations": string[]
  }
}

Response:
{
  "id": string,
  ... // Updated procedure data
}
```

## Workflows

### Get Workflows
```typescript
GET /api/pathology/workflows
Authorization: Bearer {token}

Parameters:
- bodyId: string (optional) - ID of the body
- status: string (optional) - Status of workflows
- type: string (optional) - Type of workflow

Response:
[
  {
    "id": string,
    "bodyId": string,
    "workflowType": string,
    "status": string,
    "startDate": string,
    "completionDate": string,
    "assignedTo": string,
    "priority": string,
    "steps": [
      {
        "id": string,
        "name": string,
        "status": string,
        "order": number,
        "assignedTo": string,
        "deadline": string,
        "procedureId": string
      }
    ],
    "notes": string,
    "attachments": string[]
  }
]
```

### Create Workflow
```typescript
POST /api/pathology/workflows
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "bodyId": string,
  "workflowType": string,
  "status": string,
  "assignedTo": string,
  "priority": string,
  "steps": [
    {
      "name": string,
      "order": number,
      "assignedTo": string,
      "deadline": string
    }
  ],
  "notes": string
}

Response:
{
  "id": string,
  ... // Created workflow data
}
```

### Update Workflow
```typescript
PUT /api/pathology/workflows?id={workflowId}
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "status": string,
  "completionDate": string,
  "priority": string,
  "steps": [
    {
      "id": string,
      "status": string,
      "assignedTo": string,
      "deadline": string,
      "procedureId": string
    }
  ],
  "notes": string,
  "attachments": string[]
}

Response:
{
  "id": string,
  ... // Updated workflow data
}
``` 