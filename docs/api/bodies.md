# Bodies API Documentation

## Overview

The Bodies API provides endpoints for managing deceased bodies, including tracking, status updates, and chain of custody management.

## Base URL
```
https://api.gppathology.com/v1/bodies
```

## Authentication

All endpoints require a valid JWT token obtained through the authentication process.

```
Authorization: Bearer {token}
```

## Bodies

### List Bodies
```typescript
GET /api/bodies
Authorization: Bearer {token}

Parameters:
- status: string (optional) - Filter by body status
- facility: string (optional) - Filter by current facility
- dateFrom: string (optional) - Filter by admission date (from)
- dateTo: string (optional) - Filter by admission date (to)
- search: string (optional) - Search by tracking number or description
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "total": number,
  "data": [
    {
      "id": string,
      "trackingNumber": string,
      "status": "COLLECTED" | "ADMITTED" | "IN_STORAGE" | "RELEASED" | "TRANSFERRED",
      "bodyTagId": string,
      "createdAt": string,
      "updatedAt": string,
      "currentLocation": {
        "facilityId": string,
        "facilityName": string,
        "unitId": string,
        "unitName": string,
        "position": string
      },
      "metadata": {
        "bodyCondition": string,
        "bodyDescription": string,
        "gpsCoords": string
      },
      "bodyTag": {
        "id": string,
        "tagNumber": string,
        "status": string
      }
    }
  ]
}
```

### Get Body Details
```typescript
GET /api/bodies/:id
Authorization: Bearer {token}

Response:
{
  "id": string,
  "trackingNumber": string,
  "status": "COLLECTED" | "ADMITTED" | "IN_STORAGE" | "RELEASED" | "TRANSFERRED",
  "bodyTagId": string,
  "createdAt": string,
  "updatedAt": string,
  "currentLocation": {
    "facilityId": string,
    "facilityName": string,
    "unitId": string,
    "unitName": string,
    "position": string,
    "temperature": number,
    "lastUpdated": string
  },
  "metadata": {
    "bodyCondition": string,
    "bodyDescription": string,
    "gpsCoords": string,
    "identificationMarks": string[],
    "personalEffects": string[]
  },
  "bodyTag": {
    "id": string,
    "tagNumber": string,
    "status": string,
    "lastScannedAt": string,
    "lastScannedBy": {
      "id": string,
      "name": string,
      "role": string
    }
  },
  "collection": {
    "id": string,
    "date": string,
    "collectionType": string,
    "collectedBy": {
      "id": string,
      "name": string,
      "role": string
    },
    "location": string
  },
  "admission": {
    "id": string,
    "date": string,
    "admittedBy": {
      "id": string,
      "name": string,
      "role": string
    },
    "notes": string
  },
  "release": {
    "id": string,
    "date": string,
    "releasedBy": {
      "id": string,
      "name": string,
      "role": string
    },
    "releasedTo": {
      "name": string,
      "organization": string,
      "contactInfo": string
    },
    "reason": string
  },
  "procedures": [
    {
      "id": string,
      "type": string,
      "status": string,
      "date": string,
      "performer": {
        "id": string,
        "name": string,
        "role": string
      }
    }
  ],
  "documents": [
    {
      "id": string,
      "type": string,
      "filename": string,
      "uploadDate": string,
      "uploadedBy": string
    }
  ],
  "chainOfCustody": [
    {
      "id": string,
      "action": string,
      "timestamp": string,
      "performedBy": {
        "id": string,
        "name": string,
        "role": string
      },
      "location": string,
      "notes": string
    }
  ]
}
```

### Update Body Status
```typescript
PUT /api/bodies/:id/status
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "status": "COLLECTED" | "ADMITTED" | "IN_STORAGE" | "RELEASED" | "TRANSFERRED",
  "reason": string,
  "notes": string,
  "locationUpdate": {
    "facilityId": string,
    "unitId": string,
    "position": string
  }
}

Response:
{
  "id": string,
  "trackingNumber": string,
  "status": string,
  "updatedAt": string,
  "previousStatus": string,
  "chainOfCustody": {
    "id": string,
    "action": string,
    "timestamp": string,
    "performedBy": string
  }
}
```

### Update Body Location
```typescript
PUT /api/bodies/:id/location
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "facilityId": string,
  "unitId": string,
  "position": string,
  "notes": string
}

Response:
{
  "id": string,
  "trackingNumber": string,
  "currentLocation": {
    "facilityId": string,
    "facilityName": string,
    "unitId": string,
    "unitName": string,
    "position": string,
    "updateTimestamp": string
  },
  "chainOfCustody": {
    "id": string,
    "action": "LOCATION_UPDATE",
    "timestamp": string,
    "performedBy": string
  }
}
```

### Upload Body Documents
```typescript
POST /api/bodies/:id/documents
Authorization: Bearer {token}
Content-Type: multipart/form-data

Request:
{
  "documentType": string,
  "files": File[],
  "notes": string
}

Response:
{
  "id": string,
  "trackingNumber": string,
  "documents": [
    {
      "id": string,
      "type": string,
      "filename": string,
      "size": number,
      "mimeType": string,
      "uploadDate": string,
      "uploadedBy": string,
      "url": string
    }
  ]
}
```

## Body Tags

### List Body Tags
```typescript
GET /api/body-tags
Authorization: Bearer {token}

Parameters:
- status: string (optional) - Filter by tag status
- limit: number (optional) - Limit the number of results
- offset: number (optional) - Pagination offset

Response:
{
  "total": number,
  "data": [
    {
      "id": string,
      "tagNumber": string,
      "status": "GENERATED" | "COLLECTED" | "ADMITTED" | "RELEASED",
      "createdAt": string,
      "lastScannedAt": string,
      "lastScannedBy": string,
      "bodyId": string,
      "collectionId": string
    }
  ]
}
```

### Generate Body Tags
```typescript
POST /api/body-tags/generate
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "count": number,
  "prefix": string,
  "facilityId": string
}

Response:
{
  "success": boolean,
  "count": number,
  "tags": [
    {
      "id": string,
      "tagNumber": string,
      "status": "GENERATED",
      "createdAt": string
    }
  ]
}
```

### Validate Body Tag
```typescript
POST /api/body-tags/validate
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "tagNumber": string
}

Response:
{
  "valid": boolean,
  "tagInfo": {
    "id": string,
    "tagNumber": string,
    "status": string,
    "createdAt": string,
    "lastScannedAt": string,
    "bodyId": string
  }
}
```

## Chain of Custody

### Get Chain of Custody
```typescript
GET /api/bodies/:id/chain-of-custody
Authorization: Bearer {token}

Parameters:
- actionType: string (optional) - Filter by action type
- from: string (optional) - Filter by start date
- to: string (optional) - Filter by end date

Response:
{
  "bodyId": string,
  "trackingNumber": string,
  "entries": [
    {
      "id": string,
      "action": string,
      "timestamp": string,
      "performedBy": {
        "id": string,
        "name": string,
        "role": string
      },
      "location": {
        "facilityId": string,
        "facilityName": string,
        "unitId": string,
        "unitName": string,
        "position": string
      },
      "notes": string,
      "metadata": {
        "previousStatus": string,
        "newStatus": string,
        "reason": string
      }
    }
  ]
}
```

### Add Chain of Custody Entry
```typescript
POST /api/bodies/:id/chain-of-custody
Authorization: Bearer {token}
Content-Type: application/json

Request:
{
  "action": string,
  "location": {
    "facilityId": string,
    "unitId": string,
    "position": string
  },
  "notes": string,
  "timestamp": string,
  "metadata": {
    "reason": string,
    "referenceId": string
  }
}

Response:
{
  "id": string,
  "bodyId": string,
  "action": string,
  "timestamp": string,
  "performedBy": string,
  "success": boolean
}
``` 