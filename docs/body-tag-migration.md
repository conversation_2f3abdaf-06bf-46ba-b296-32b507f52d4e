# Body Tag Migration Tool

This tool helps migrate existing body tags to the new standardized format: `GP/[FACILITY_CODE]/[SEQUENTIAL_NUMBER]/[YEAR]`

## Format Description

The new format follows this structure:
- **GP**: Province code (fixed)
- **FACILITY_CODE**: 2-4 uppercase letters (e.g., PTA for Pretoria)
- **SEQUENTIAL_NUMBER**: 4-digit number with leading zeros (e.g., 0001)
- **YEAR**: 4-digit year (e.g., 2025)

Example: `GP/PTA/0001/2025`

## Usage

The migration tool can be run in two modes:

### 1. Dry Run Mode (Recommended First Step)
```bash
npm run migrate:body-tags -- --dry-run
```
This will:
- Show what changes would be made
- Not modify any data in the database
- Display a summary of all potential changes

### 2. Live Migration Mode
```bash
npm run migrate:body-tags -- --force
```
This will:
- Actually update the tags in the database
- Create a log of all changes made
- Require the `--force` flag to prevent accidental runs

## Migration Process

1. Scans all existing body tags in the database
2. For each tag:
   - Checks if it already matches the new format
   - If not, attempts to convert it using facility information
   - Validates the new format before saving
3. Provides a summary of:
   - Total tags processed
   - Successfully migrated tags
   - Skipped tags (already valid)
   - Failed migrations with error details

## Error Handling

The tool handles several cases:
- Tags without facility IDs
- Invalid number formats
- Missing or incorrect years
- Validation failures

Failed migrations are logged with details for manual review.

## Recovery

If you need to undo the migration:
1. The tool creates a backup of changed tags
2. Use the backup to restore previous values if needed
3. Contact system administrator for assistance with reversals

## Best Practices

1. Always run with `--dry-run` first to preview changes
2. Schedule migrations during low-usage periods
3. Inform all users before running live migrations
4. Keep the migration summary for your records

## Support

For help or to report issues:
1. Check the error log in the migration output
2. Contact the system administrator
3. Raise an issue in the project repository

## Format Validation

The tool uses the same validation rules as the rest of the system:
```typescript
const pattern = /^GP\/[A-Z]{2,4}\/\d{4}\/\d{4}$/;
```

This ensures:
- Province code is exactly "GP"
- Facility code is 2-4 uppercase letters
- Sequential number is exactly 4 digits
- Year is exactly 4 digits
