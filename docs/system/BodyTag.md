# BodyTag System Documentation

The BodyTag system is a crucial component of the pathology management system that handles the tracking and processing of bodies through various stages. This document outlines how BodyTags work, including generation, scanning, and selection processes.

## Overview

A BodyTag represents a unique identifier assigned to each body in the system. It tracks the body's journey through different stages:

- Collection
- Admission
- Storage
- Referral
- Release

## Tag Statuses

BodyTags can have the following statuses:

- `GENERATED`: Initial state when tag is created
- `COLLECTED`: Body has been collected from the scene
- `ADMITTED`: Body has been admitted to the facility
- `IN_TRANSIT`: Body is being transferred
- `RELEASED`: Body has been released

## Components

### 1. Barcode Generation (`BarcodeGen.tsx`)

The barcode generation component creates visual representations of BodyTags:

- Uses QR code format for barcode generation
- Includes text representation below barcode
- Supports downloading and copying
- Configurable dimensions and margins
- Error handling capabilities
- Includes other barcode types (e.g., PDF417, Data Matrix, etc)

### 2. Body Tag Scanner (`BodyTagScanner.tsx`)

The scanner component handles tag reading and validation:

- Supports QR code scanning
- Real-time camera feed processing
- Validation based on scan type:
  - Collection validation
  - Admission validation
  - Storage validation
  - Release validation
  - Referral validation
- Provides feedback on scan success/failure
- Error boundary protection

### 3. Body Tag Selector (`BodyTagSelector.tsx`)

The selector component provides a user interface for tag selection:

- Autocomplete functionality for tag lookup
- Integrated scanner capability
- Form control integration
- Validation feedback
- Size variants (sm, md, lg)
- Optional result display

## Validation / Workflow Process

The system includes comprehensive validation logic (`bodyTag.ts`) that ensures:

1. **Collection Validation**
   - The BodyTag does not already have a collection or Body associated with it
   - The BodyTag is in the correct state (GENERATED)

2. **Admission Validation**
   - Confirms that the BodyTag is associated with a Collection
   - Checks If the BodyTag has already been admitted
   -- if yes, Then this must be a Re-admission from a REFERRAL
   ---- Meaning it should have a previous admission and Referral associated with it
   -- if no, Then this must be a NEW admission
   ---- Meaning it should NOT have a previous admission or referral
   *Otherwise, it is an Invalid BodyTag*

3. **Release Validation**
   - Verifies that the BodyTag has an associated
   -- Collection or Body
   -- Admitted
   -- At least one Referral (Optional)
   -- No pending Referrals
   - Checks if the BodyTag is in the correct state (ADMITTED or IN_TRANSIT)

4. **Referral Validation**
   - Verifies that the bodyTag has an associated
   -- Collection or Body
   -- is currently Admitted
   -- No pending Referrals

5. **Re-Admission Validation**
   *a re-admission from a REFERRAL*
   - Verifies that the bodyTag has an associated
   -- Collection or Body
   -- is currently **NOT ADMITTED**
   -- HAS a previous admission
   -- HAS a Pending Referral

6. **Release Validation**
   - Verifies that the bodyTag has an associated
   -- Collection or Body
   -- is currently **ADMITTED**
   -- No pending Referrals

7. **Storage Validation**
   - Verifies that the bodyTag has an associated
   -- Collection or Body
   -- is currently **ADMITTED**
   -- No pending Referrals

## Best Practices

1. Always scan tags in appropriate lighting conditions
2. Verify scan results before proceeding
3. Use the selector component when manual entry is needed
4. Ensure proper validation type is set for the intended operation
5. Handle errors appropriately using provided error callbacks

## Error Handling

The system provides comprehensive error handling:

- Scan validation errors
- Network-related issues
- Invalid tag formats
- Authorization errors
- Status transition errors

## Integration

To integrate BodyTag components:

1. For barcode generation:

```tsx
<BarcodeGen 
  id="body-tag-id"
  onError={(error) => handleError(error)}
/>
```

2. For tag scanning:

```tsx
<BodyTagScanner
  onScan={(bodyTag) => handleScan(bodyTag)}
  scanType="admission"
  enableFeedback={true}
/>
```

3. For tag selection:

```tsx
<BodyTagSelector
  name="bodyTag"
  required={true}
  scanType="collection"
  onChange={(value) => handleChange(value)}
/>
```
