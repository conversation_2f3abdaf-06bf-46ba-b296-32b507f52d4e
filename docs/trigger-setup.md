# Trigger.dev Setup for GP Pathology

This document outlines the setup and configuration of Trigger.dev for handling background tasks in the GP Pathology system, specifically focusing on the extended admission alert system.

## Overview

Trigger.dev is used to manage and execute background tasks with features like:
- Reliable task execution
- Automatic retries
- Detailed logging and monitoring
- Error notifications
- Webhook handling

## Prerequisites

Before setting up Trigger.dev, ensure you have:

1. A Trigger.dev account (Sign up at [trigger.dev](https://trigger.dev))
2. Access to your application's environment variables
3. Node.js and pnpm installed

## Installation

Install the required dependencies:

```bash
pnpm add @trigger.dev/sdk@latest
pnpm add -D @types/node-cron @types/nodemailer
```

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```env
# Trigger.dev Configuration
TRIGGER_SECRET_KEY=sk_your_secret_key
TRIGGER_API_URL=https://api.trigger.dev
```

### Task Definition

The extended admission alerts task is defined in `src/tasks/extended-admission-alerts.ts`:

```typescript
interface ExtendedAdmissionPayload {
  facilityId?: string;
}

export const sendExtendedAdmissionAlerts = task({
  id: "extended-admission-alerts",
  description: "Sends alerts for bodies that have been in storage for more than 7 days",
  retry: {
    maxAttempts: 5,
    minTimeoutInMs: 1000,
    maxTimeoutInMs: 10000,
    factor: 2,
  },
  run: async (payload: ExtendedAdmissionPayload) => {
    // Task implementation
  }
});
```

### Client Setup

The Trigger.dev client is configured in `src/trigger.ts`:

```typescript
import { configure, tasks } from "@trigger.dev/sdk/v3";

// Configure the client
configure({
  secretKey: process.env.TRIGGER_SECRET_KEY,
});

// Function to trigger the task
export async function triggerExtendedAdmissionAlerts(facilityId?: string) {
  return await tasks.trigger<typeof sendExtendedAdmissionAlerts>(
    "extended-admission-alerts",
    { facilityId }
  );
}
```

### Webhook Handler

The webhook handler is set up in `src/app/api/trigger/route.ts`:

```typescript
export async function POST(request: Request) {
  const headersList = headers();
  const signature = headersList.get("x-trigger-signature");

  if (!signature) {
    return new Response("No signature", { status: 401 });
  }

  try {
    const payload = await request.json();
    await client.verify({
      payload,
      signature,
    });

    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("Error verifying webhook:", error);
    return new Response("Invalid signature", { status: 401 });
  }
}
```

## Task Functionality

The extended admission alerts task:

1. Retrieves bodies in storage > 7 days
2. Groups them by facility
3. Identifies facility administrators
4. Sends batch email notifications
5. Logs all activities and errors

### Error Handling

The task includes:
- Automatic retries with exponential backoff
- Detailed error logging
- Facility-level error isolation
- Webhook signature verification

### Monitoring

Monitor task execution through:
1. Trigger.dev dashboard
2. Application logs
3. Email notifications for failures

## Scheduling

Note: Task scheduling should be configured through the Trigger.dev dashboard. The dashboard provides a user interface for:
- Setting up cron schedules
- Managing task triggers
- Monitoring scheduled executions
- Adjusting schedule parameters

To schedule the extended admission alerts task:
1. Log into the Trigger.dev dashboard
2. Navigate to the "Tasks" section
3. Select the "extended-admission-alerts" task
4. Click "Add Schedule"
5. Set the cron expression to "0 9 * * *" (runs daily at 9 AM)
6. Save the schedule

## Deployment

1. Set up environment variables in your production environment
2. Deploy your application
3. Configure the webhook URL in Trigger.dev dashboard:
   ```
   https://your-domain.com/api/trigger
   ```

## Security

The system implements several security measures:
1. Webhook signature verification
2. Environment-based API keys
3. Role-based access for notifications
4. Secure error handling

## Troubleshooting

Common issues and solutions:

1. **Task Not Running**
   - Check environment variables
   - Verify task configuration in dashboard
   - Check Trigger.dev dashboard for errors

2. **Webhook Errors**
   - Verify signature configuration
   - Check webhook URL
   - Review server logs

3. **Email Notification Issues**
   - Verify SMTP configuration
   - Check administrator role assignments
   - Review email service logs

## Best Practices

1. **Monitoring**
   - Regularly check Trigger.dev dashboard
   - Set up alerts for task failures
   - Monitor email delivery rates

2. **Maintenance**
   - Keep dependencies updated
   - Review and adjust retry settings
   - Monitor task execution times

3. **Development**
   - Use development API keys for testing
   - Test with sample data
   - Review logs frequently

## Additional Resources

- [Trigger.dev Documentation](https://trigger.dev/docs)
- [API Reference](https://trigger.dev/docs/api)
- [Best Practices Guide](https://trigger.dev/docs/best-practices)

## Support

For issues or questions:
1. Check the [Trigger.dev documentation](https://trigger.dev/docs)
2. Contact the development team
3. Submit issues in the project repository

## Changelog

- Initial setup: Extended admission alerts implementation
- Added webhook handling
- Configured email notifications
- Implemented error handling and retries
- Updated scheduling configuration to use dashboard 