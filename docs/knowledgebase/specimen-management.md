# Specimen Management

## Overview
The Specimen Management module provides comprehensive tools for collecting, tracking, and managing specimens taken from bodies within the pathology system. This module ensures proper documentation, chain of custody, and integration with laboratory processes for all specimen-related activities.

## Key Components

### Specimen Collection Dashboard
```mermaid
flowchart TD
    A[Specimen Collection Dashboard] --> B[New Collection]
    A --> C[Active Specimens]
    A --> D[Completed Specimens]
    A --> E[Pending Results]
    A --> F[Collection Statistics]
```

The Specimen Collection Dashboard provides a comprehensive overview of all specimen activities:

- **Collection Statistics**: 
  - Total specimens collected
  - Specimens by type
  - Pending analysis count
  - Completion rates
  - Turnaround times

- **Status Overview**:
  - Color-coded status indicators
  - Priority indicators
  - Age of specimens
  - Approaching deadline alerts

- **Quick Actions**:
  - Create new specimen collection
  - View specimen details
  - Update specimen status
  - Generate reports

### Specimen Collection Process

#### New Specimen Collection
```mermaid
flowchart LR
    A[Select Body] --> B[Collection Form]
    B --> C[Specimen Details]
    C --> D[Collection Method]
    D --> E[Storage Information]
    E --> F[Submit Collection]
```

Process for recording new specimen collections:

1. **Body Selection**:
   - Search for body by tag number
   - Verify identity
   - Check collection authorization

2. **Collection Details**:
   - Specimen type selection
   - Collection date and time
   - Collecting staff information
   - Reason for collection
   - Required tests/analysis

3. **Specimen Documentation**:
   - Unique specimen ID generation
   - Barcode/label creation
   - Photo documentation
   - Physical description
   - Quantity/volume recording

4. **Storage Information**:
   - Initial storage location
   - Storage requirements
   - Preservation method
   - Temperature requirements

5. **Chain of Custody**:
   - Initial handler documentation
   - Transfer documentation
   - Electronic signatures

### Specimen Tracking

#### Specimen List View
```mermaid
flowchart TD
    A[Specimen List] --> B[Filter Options]
    A --> C[Sort Options]
    A --> D[Specimen Details]
    D --> E[Status Updates]
    D --> F[Chain of Custody]
    D --> G[Test Results]
```

Features of the specimen tracking system:

- **Specimen List**:
  - Sortable and filterable table of all specimens
  - Key information displayed (ID, type, collection date, status)
  - Color-coded status indicators
  - Quick action buttons

- **Specimen Details**:
  - Comprehensive view of specimen information
  - Collection details
  - Current location
  - Processing status
  - Related documents and photos
  - Complete timeline of specimen history

- **Chain of Custody**:
  - Complete handling history
  - Transfer documentation
  - Electronic signatures
  - Timestamp for all transfers
  - Location history

#### Status Updates
```mermaid
flowchart TD
    A[Specimen] --> B[In Storage]
    B --> C[Sent to Lab]
    C --> D[Analysis in Progress]
    D --> E[Results Available]
    E --> F[Reviewed]
    F --> G[Archived/Disposed]
```

Specimen status workflow:

1. **Collection**: Initial specimen collection recorded
2. **In Storage**: Specimen stored in facility
3. **Sent to Lab**: Specimen transferred to laboratory
4. **Analysis in Progress**: Laboratory processing specimen
5. **Results Available**: Analysis complete, results recorded
6. **Reviewed**: Results reviewed by appropriate staff
7. **Archived/Disposed**: Specimen archived or properly disposed

### Laboratory Integration

#### Test Requests
```mermaid
flowchart LR
    A[Specimen Collection] --> B[Test Request Form]
    B --> C[Lab Selection]
    C --> D[Test Selection]
    D --> E[Priority Setting]
    E --> F[Submit Request]
```

Process for laboratory test requests:

1. **Test Selection**:
   - Choose from available test types
   - Multiple tests per specimen
   - Special instructions
   - Reference ranges

2. **Laboratory Assignment**:
   - Internal or external lab selection
   - Lab contact information
   - Delivery method
   - Expected turnaround time

3. **Priority Setting**:
   - Routine, urgent, or stat priority
   - Justification for priority
   - Notification settings

#### Results Management
```mermaid
flowchart TD
    A[Results Received] --> B[Results Entry]
    B --> C[Attachment Upload]
    C --> D[Notification]
    D --> E[Review Process]
```

Process for managing test results:

1. **Results Entry**:
   - Manual entry or electronic import
   - Structured data fields
   - Free text comments
   - Abnormal result flagging

2. **Documentation**:
   - Attach laboratory reports
   - Upload supporting images
   - Link related documents
   - Version control for updates

3. **Notification**:
   - Automatic alerts to requesting staff
   - Escalation for critical results
   - Read receipts for critical notifications

4. **Review Process**:
   - Results review workflow
   - Electronic sign-off
   - Follow-up actions
   - Additional test requests

## X-Ray Referral Management

### Referral Dashboard
```mermaid
flowchart TD
    A[X-Ray Referral Dashboard] --> B[New Referral]
    A --> C[Active Referrals]
    A --> D[Completed Referrals]
    A --> E[Pending Results]
    A --> F[Referral Statistics]
```

The X-Ray Referral Dashboard provides a comprehensive overview of all X-ray activities:

- **Referral Statistics**: 
  - Total referrals created
  - Referrals by type
  - Pending results count
  - Completion rates
  - Turnaround times

- **Status Overview**:
  - Color-coded status indicators
  - Priority indicators
  - Age of referrals
  - Approaching deadline alerts

- **Quick Actions**:
  - Create new referral
  - View referral details
  - Update referral status
  - Generate reports

### Referral Process

#### New X-Ray Referral
```mermaid
flowchart LR
    A[Select Body] --> B[Referral Form]
    B --> C[Referral Details]
    C --> D[X-Ray Type]
    D --> E[Priority Setting]
    E --> F[Submit Referral]
```

Process for creating new X-ray referrals:

1. **Body Selection**:
   - Search for body by tag number
   - Verify identity
   - Check referral authorization

2. **Referral Details**:
   - Referral date and time
   - Referring staff information
   - Reason for referral
   - Clinical questions to answer

3. **X-Ray Specifications**:
   - X-ray type selection
   - Views required
   - Special instructions
   - Previous imaging reference

4. **Priority Setting**:
   - Routine, urgent, or stat priority
   - Justification for priority
   - Notification settings

### Referral Tracking

#### Referral List View
```mermaid
flowchart TD
    A[Referral List] --> B[Filter Options]
    A --> C[Sort Options]
    A --> D[Referral Details]
    D --> E[Status Updates]
    D --> F[Transport Tracking]
    D --> G[Results Management]
```

Features of the referral tracking system:

- **Referral List**:
  - Sortable and filterable table of all referrals
  - Key information displayed (ID, type, date, status)
  - Color-coded status indicators
  - Quick action buttons

- **Referral Details**:
  - Comprehensive view of referral information
  - Referral reason and specifications
  - Current status
  - Scheduled appointment (if applicable)
  - Related documents and photos
  - Complete timeline of referral history

#### Status Updates
```mermaid
flowchart TD
    A[Referral Created] --> B[Scheduled]
    B --> C[In Transit]
    C --> D[X-Ray in Progress]
    D --> E[Results Available]
    E --> F[Reviewed]
    F --> G[Completed]
```

Referral status workflow:

1. **Created**: Initial referral recorded
2. **Scheduled**: Appointment scheduled with X-ray facility
3. **In Transit**: Body in transit to X-ray facility
4. **X-Ray in Progress**: Imaging procedure underway
5. **Results Available**: Images and reports available
6. **Reviewed**: Results reviewed by appropriate staff
7. **Completed**: Referral process complete

### Results Management

#### X-Ray Results
```mermaid
flowchart LR
    A[Results Received] --> B[Image Upload]
    B --> C[Report Entry]
    C --> D[Notification]
    D --> E[Review Process]
```

Process for managing X-ray results:

1. **Image Management**:
   - Upload DICOM or other image formats
   - Image viewer integration
   - Annotation capabilities
   - Image series organization

2. **Report Documentation**:
   - Structured report entry
   - Findings documentation
   - Impression/conclusion
   - Recommendations

3. **Notification**:
   - Automatic alerts to requesting staff
   - Escalation for critical findings
   - Read receipts for critical notifications

4. **Review Process**:
   - Results review workflow
   - Electronic sign-off
   - Follow-up actions
   - Additional referral requests

## Integration Points

### Body Management Module
- Links specimens and referrals to specific bodies
- Updates body records with specimen/referral status
- Provides complete history of all specimens and imaging

### Records Module
- Creates comprehensive records of all specimen and referral activities
- Maintains complete chain of custody documentation
- Stores all results and findings

### Reporting Module
- Generates statistics on specimen and referral volumes
- Tracks turnaround times and completion rates
- Provides data for operational analysis

## User Roles and Permissions

### Admin
- Full access to all specimen and referral functions
- Can configure test types and referral options
- Access to all results and findings

### Morgue Staff
- Create specimen collections and referrals
- Update specimen and referral status
- View results and findings

### Medical Examiner
- Request specific specimens and imaging
- View all results and findings
- Add interpretations and follow-up requests

### Laboratory Staff
- Update specimen status
- Enter test results
- Upload supporting documentation

## Mobile Capabilities
- Mobile specimen collection documentation
- Barcode/QR scanning for specimen tracking
- Photo documentation from mobile devices
- Status updates from the field

## Compliance and Reporting
- Complete chain of custody documentation
- Comprehensive audit trails
- Configurable workflows to meet regulatory requirements
- Specimen retention policy enforcement 