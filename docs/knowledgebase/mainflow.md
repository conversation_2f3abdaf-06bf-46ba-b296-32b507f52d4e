# Application Flows

This document outlines the flow of the application for the Integrated Metallic Body Identification and Release Management System for the Gauteng Department of Health Forensic Pathology Service Facilities. It mentions the Pages, Components and the data flow. This is a high level overview of the Flows using a user with an Admin Role.

The Admin role can Access the Whole Application Without restrictions.

> **Note**: For a more comprehensive and detailed guide to the GP Pathology System, please refer to the [Complete Application Guide](main-entry.md) which includes detailed documentation for each module, user roles, workflows, and integration points.

## Main Application Flows

The GP Pathology System consists of several interconnected modules that work together to provide a complete pathology management solution:

1. **Dashboard** - Central command center with metrics, alerts, and quick access
2. **Body Management** - Handling the complete lifecycle from collection to release
   - Body Collection from Crime Scene and Hospital
   - Body Admission with Metallic Tag Tracking
   - Body Release to Funeral Undertakers
   - Body Referral for X-Ray and Specimen Taking
3. **Records Management** - Central repository for all system data
4. **Knowledge Base** - Repository of procedures and reference materials
5. **Reports & Settings** - Data analysis and system configuration

## Detailed Module Documentation

Each module has its own detailed documentation:

- [Dashboard](dashboard.md)
- [Body Management](body-management.md)
- [Records Management](records-management.md)
- [Knowledge Base](knowledge-base.md)
- [Reports & Settings](reports-settings.md)

## Primary Workflow

The primary workflow in the system follows this general pattern:

```mermaid
flowchart TD
    A[Body Collection] -->|Metallic Tag Assigned| B[Body Admission]
    B -->|Fridge Bar Code Scanned| C[Update Records]
    C -->|Referral Needed| D[X-Ray/Specimen Referral]
    D -->|Return to Facility| E[Re-admission]
    C -->|Release Approved| F[Body Release]
    F -->|Security Verification| G[Handover to Undertaker]
    H[Knowledge Base] -->|Reference| B
    I[Reports] -->|Analysis| J[Process Improvement]
```

## Metallic Body Tag System

The core of the system is the Integrated Metallic Body Identification and Release Management System, which uses metallic body tags to track and manage bodies throughout the process:

1. **Body Collection**:
   - Capture employee information (PERSAL number, name, institution)
   - Capture scene details (time, GPS coordinates, photos)
   - Scan metallic body tag allocated to the deceased body

2. **Body Admission**:
   - Scan the body cooler fridge bar code
   - Take photo of body bag with death register number from metallic tag
   - If not identified after seven days, move to body-to-body freezer fridge

3. **Body Release**:
   - Locate body via scanning detector
   - Scan fridge bar code
   - Verify death register number on metallic tag
   - Document funeral undertaker details
   - Security officer scans body using portable detector

4. **Body Referral**:
   - Locate body via scanning detector
   - Document referral details and specimen information
   - Security verification with portable scanner
   - Follow admission steps upon return

For complete details on all workflows, user roles, and system features, please refer to the [Complete Application Guide](main-entry.md).
