# Body Tag Format Documentation

## Overview

The Gauteng Pathology system uses a standardized body tag format to uniquely identify bodies across all facilities. This document describes the new format and migration process.

## New Format: GP/facility/number/year

### Format Structure
```
GP/[FACILITY_CODE]/[SEQUENTIAL_NUMBER]/[YEAR]
```

### Examples
- `GP/PTA/1222/2025` - Body #1222 from Pretoria facility in 2025
- `GP/JHB/0001/2025` - Body #1 from Johannesburg facility in 2025 (with leading zeros)
- `GP/CPT/9999/2024` - Body #9999 from Cape Town facility in 2024

### Components

1. **Province Code**: Always "GP" for Gauteng Province
2. **Facility Code**: 2-4 letter abbreviation for the facility
3. **Sequential Number**: 4-digit sequential number with leading zeros (0001-9999, resets each year per facility)
4. **Year**: 4-digit year

## Facility Codes

### Predefined Mappings
| Full Facility Code | Short Code | Facility Name |
|-------------------|------------|---------------|
| PTA-FPS | PTA | Pretoria Forensic Pathology Services |
| JHB-FPS | JHB | Johannesburg Forensic Pathology Services |
| CPT-FPS | CPT | Cape Town Forensic Pathology Services |
| DBN-FPS | DBN | Durban Forensic Pathology Services |
| PE-FPS | PE | Port Elizabeth Forensic Pathology Services |
| BFN-FPS | BFN | Bloemfontein Forensic Pathology Services |

### Automatic Code Generation
For facilities not in the predefined mapping, the system automatically extracts short codes:
- `PTA-FPS` → `PTA` (takes first part before dash)
- `FC-ABC123` → `ABC` (takes first 3 chars of second part)
- `MORGUE` → `MORG` (takes first 4 chars)

## Sequential Numbering

### Database Implementation
- Each facility has its own sequence counter per year
- Sequences are stored in the `BodyNumberSequence` table
- Counters automatically increment when new tags are generated
- Sequences reset to 1 each new year

### Concurrency Safety
- Uses database `UPSERT` operations to handle concurrent requests
- Atomic increment operations prevent duplicate numbers
- Transaction-safe generation

## API Endpoints

### Generate Body Tags
```http
POST /api/body-tags/generate
Content-Type: application/json

{
  "facilityId": "facility-uuid",
  "count": 1,
  "qrCodeFormat": "QR_CODE",
  "metadata": {}
}
```

### Response
```json
{
  "success": true,
  "message": "Successfully generated 1 body tag(s)",
  "tags": [
    {
      "id": "tag-uuid",
      "tagNumber": "GP/PTA/1222/2025",
      "status": "GENERATED",
      "qrCodeValue": "GP/PTA/1222/2025",
      "facility": {
        "id": "facility-uuid",
        "name": "Pretoria Forensic Pathology Services",
        "code": "PTA-FPS"
      }
    }
  ],
  "count": 1
}
```

## Validation

### Format Validation
The system validates body tags using the regex pattern:
```regex
^GP\/[A-Z]{2,4}\/\d{4}\/\d{4}$
```

### Validation Rules
- Must start with "GP/"
- Facility code: 2-4 uppercase letters
- Sequential number: exactly 4 digits (with leading zeros)
- Year: exactly 4 digits
- Forward slashes as separators

## Migration

### Migration Script
Use the migration utility to convert existing body tags:

```bash
# Dry run (preview changes)
npm run migrate-body-tags migrate

# Live migration
npm run migrate-body-tags migrate --live

# Validate all tags
npm run migrate-body-tags validate
```

### Migration Process
1. **Backup**: Always backup the database before migration
2. **Dry Run**: Run migration in dry-run mode first
3. **Validation**: Check facility code mappings
4. **Live Migration**: Execute actual migration
5. **Verification**: Validate all tags after migration

### Migration Safety
- Detects existing new-format tags and skips them
- Handles conflicts by generating alternative numbers
- Preserves QR code values
- Logs all changes and errors

## Usage in Code

### Generate New Tag
```typescript
import { generateBodyTag } from '@/utils/generateBodyTag';

// Async generation with facility lookup
const bodyTag = await generateBodyTag(facilityId);
// Returns: "GP/PTA/1222/2025" (sequential with 4-digit padding)

// Sync generation (fallback)
import { generateBodyTagSync } from '@/utils/generateBodyTag';
const bodyTag = generateBodyTagSync('PTA');
// Returns: "GP/PTA/3847/2025" (random number with 4-digit padding)
```

### Validate Tag Format
```typescript
import { isValidBodyTag, parseBodyTag } from '@/utils/generateBodyTag';

// Validate format
const isValid = isValidBodyTag('GP/PTA/1222/2025'); // true
const isValidPadded = isValidBodyTag('GP/PTA/0001/2025'); // true

// Parse components
const parsed = parseBodyTag('GP/PTA/1222/2025');
// Returns: { province: 'GP', facility: 'PTA', number: 1222, year: 2025 }
const parsedPadded = parseBodyTag('GP/PTA/0001/2025');
// Returns: { province: 'GP', facility: 'PTA', number: 1, year: 2025 }
```

### Get Facility Code
```typescript
import { getFacilityCodeForBodyTag } from '@/utils/facilityCodeUtils';

const { shortCode, fullCode, facilityName } = await getFacilityCodeForBodyTag(facilityId);
// Returns: { shortCode: 'PTA', fullCode: 'PTA-FPS', facilityName: 'Pretoria...' }
```

## Benefits

### Improved Organization
- Clear facility identification
- Sequential numbering per facility
- Year-based organization
- Consistent format across system

### Better Tracking
- Easy to identify facility of origin
- Chronological ordering within facilities
- Reduced conflicts between facilities
- Audit trail friendly

### Scalability
- Supports multiple facilities
- Handles high volume per facility
- Future-proof format
- Database-optimized storage

## Troubleshooting

### Common Issues
1. **Duplicate Numbers**: Very rare with sequential numbering
2. **Invalid Facility Codes**: Check facility code mappings
3. **Migration Conflicts**: Use dry-run mode first
4. **Format Validation**: Ensure proper regex pattern

### Support
For issues with body tag generation or migration, check:
1. Database connectivity
2. Facility existence in database
3. Proper facility code mapping
4. Sequential number table integrity
