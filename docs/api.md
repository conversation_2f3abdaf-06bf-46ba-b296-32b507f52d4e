# GP Pathology API Documentation

## Base URL
```
https://api.gppathology.com/v1
```

## Authentication

### Login
```typescript
POST /auth/login
Content-Type: application/json

Request:
{
  "username": string,
  "password": string,
  "deviceId": string
}

Response:
{
  "token": string,
  "refreshToken": string,
  "user": {
    "id": string,
    "role": UserRole,
    "permissions": string[],
    "facilities": string[]
  },
  "expiresIn": number
}
```

### Refresh Token
```typescript
POST /auth/refresh
Authorization: Bearer {refreshToken}

Response:
{
  "token": string,
  "expiresIn": number
}
```

### Logout
```typescript
POST /auth/logout
Authorization: Bearer {token}

Response:
{
  "success": boolean
}
```

## Body Management

### Collection
```typescript
POST /api/collections
Authorization: Bearer {token}
Content-Type: multipart/form-data

Request:
{
  "name": string,
  "institution": string,
  "vehicleReg": string,
  "arrivalTime": string,
  "gpsCoords": string,
  "collectionType": CollectionType,
  "caseNumber": string,
  "hospitalRef": string,
  "collectionPoint": string,
  "bodyDescription": string,
  "weatherConditions": string,
  "temperature": string,
  "identificationMarks": string[],
  "personalEffects": string[],
  "witnessStatements": string[],
  "photos": File[],
  "status": string,
  "barcodeValue": string,
  "documents": string[],
  "collectedBy": string,
  "collectionNotes": string,
  "isVerified": boolean,
  "verifiedBy": string,
  "verifiedAt": string
}

Response:
{
  "id": string,
  "bodyId": string,
  "status": string,
  "timestamp": string,
  // ... all collection details
}
```

### Admission
```typescript
POST /api/admissions
Authorization: Bearer {token}

Request:
{
  "bodyId": string,
  "admissionDate": string,
  "fridgeBarCode": string,
  "deathRegisterNumber": string,
  "estimatedTimeOfDeath": string,
  "bodyCondition": BodyCondition,
  "personalBelongings": string[],
  "notes": string,
  "photo": string,
  "assignedFridge": string,
  "temperature": string,
  "barcodeValue": string
}

Response:
{
  "id": string,
  "status": BodyStatus,
  "body": {
    "id": string,
    "status": BodyStatus,
    "state": {
      "temperature": number,
      "fridgeBarCode": string,
      "currentLocation": string
    },
    "chainOfCustody": ChainOfCustody[]
  }
}
```

### Status Update
```typescript
PUT /api/bodies/:id/status
Authorization: Bearer {token}

Request:
{
  "status": BodyStatus,
  "reason": string,
  "notes": string,
  "updatedBy": string
}

Response:
{
  "id": string,
  "status": BodyStatus,
  "timestamp": string,
  "updatedBy": string
}
```

### Location Tracking
```typescript
GET /api/bodies/:id/location
Authorization: Bearer {token}

Response:
{
  "currentLocation": {
    "facility": string,
    "unit": string,
    "position": string,
    "temperature": number,
    "lastUpdated": string
  },
  "history": [
    {
      "location": string,
      "timestamp": string,
      "handler": string,
      "action": string,
      "notes": string
    }
  ]
}
```

## Storage Management

### Units List
```typescript
GET /api/storage/units
Authorization: Bearer {token}

Response:
{
  "units": [
    {
      "id": string,
      "type": "fridge" | "freezer",
      "capacity": number,
      "occupied": number,
      "temperature": number,
      "status": "active" | "maintenance" | "offline",
      "lastCheck": string,
      "alerts": Alert[]
    }
  ]
}
```

### Temperature Logging
```typescript
POST /api/storage/units/:id/temperature
Authorization: Bearer {token}

Request:
{
  "temperature": number,
  "timestamp": string,
  "recordedBy": string
}

Response:
{
  "id": string,
  "temperature": number,
  "status": string,
  "alerts": Alert[]
}
```

## Pathology Workflow

### Examination Records
```typescript
POST /api/pathology/examinations
Authorization: Bearer {token}

Request:
{
  "bodyId": string,
  "examination": {
    "type": ExaminationType,
    "findings": Finding[],
    "measurements": Measurement[],
    "specimens": Specimen[]
  },
  "photos": File[],
  "notes": string
}

Response:
{
  "examinationId": string,
  "status": ExaminationStatus,
  "timestamp": string
}
```

### Laboratory Tests
```typescript
POST /api/pathology/tests
Authorization: Bearer {token}

Request:
{
  "bodyId": string,
  "testType": TestType,
  "specimens": {
    "id": string,
    "type": SpecimenType,
    "location": string,
    "notes": string
  }[],
  "priority": Priority
}

Response:
{
  "testId": string,
  "status": TestStatus,
  "estimatedCompletion": string
}
```

## Chain of Custody

### Transfer Initiation
```typescript
POST /api/custody/transfers
Authorization: Bearer {token}

Request:
{
  "bodyId": string,
  "fromLocation": string,
  "toLocation": string,
  "reason": string,
  "handler": string,
  "witness": string,
  "type": CustodyRecordType
}

Response:
{
  "transferId": string,
  "status": TransferStatus,
  "timestamp": string
}
```

## Data Types

### Enums
```typescript
enum BodyStatus {
  COLLECTED = 'COLLECTED',
  ADMITTED = 'ADMITTED',
  IN_STORAGE = 'IN_STORAGE',
  UNDER_EXAMINATION = 'UNDER_EXAMINATION',
  PENDING_RELEASE = 'PENDING_RELEASE',
  RELEASED = 'RELEASED',
  REFERRED = 'REFERRED'
}

enum CollectionType {
  CRIME_SCENE = 'CRIME_SCENE',
  HOSPITAL = 'HOSPITAL',
  RESIDENCE = 'RESIDENCE',
  PUBLIC_PLACE = 'PUBLIC_PLACE',
  OTHER = 'OTHER'
}

enum BodyCondition {
  COMPLETE = 'complete',
  SKELETAL = 'skeletal',
  DECOMPOSED = 'decomposed',
  CHARRED = 'charred',
  BODY_PARTS = 'bodyParts'
}

enum CustodyRecordType {
  INITIAL_COLLECTION = 'INITIAL_COLLECTION',
  SCENE_COLLECTION = 'SCENE_COLLECTION',
  MORGUE_INTAKE = 'MORGUE_INTAKE',
  PATHOLOGY_TRANSFER = 'PATHOLOGY_TRANSFER',
  LAB_TRANSFER = 'LAB_TRANSFER',
  RELEASE_TO_FAMILY = 'RELEASE_TO_FAMILY',
  RELEASE_TO_AUTHORITIES = 'RELEASE_TO_AUTHORITIES'
}
```

### Interfaces
```typescript
interface Finding {
  id: string;
  category: string;
  description: string;
  location: string;
  severity: string;
  photos?: string[];
}

interface Measurement {
  type: string;
  value: number;
  unit: string;
  location?: string;
}

interface Alert {
  id: string;
  type: string;
  severity: string;
  message: string;
  timestamp: string;
}

interface ChainOfCustody {
  id: string;
  type: CustodyRecordType;
  fromPerson: string;
  toPerson: string;
  transferTime: Date;
  reason: string;
  notes?: string;
  status: CustodyStatus;
}
```

## Error Handling

### Error Response Format
```typescript
{
  "error": {
    "code": string,
    "message": string,
    "details"?: any
  }
}
```

### Common Error Codes
```typescript
enum ErrorCode {
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}
```

## Rate Limiting
- Rate limit: 100 requests per minute per IP
- Rate limit headers included in response:
  - X-RateLimit-Limit
  - X-RateLimit-Remaining
  - X-RateLimit-Reset

## Versioning
- API version included in URL path
- Current version: v1
- Version header: X-API-Version

## Transaction Handling

### Transaction Format
```typescript
interface Transaction {
  // Transaction metadata
  id: string;
  timestamp: Date;
  type: TransactionType;
  status: TransactionStatus;
  
  // Operation details
  operations: {
    type: OperationType;
    model: string;
    data: any;
    rollback?: () => Promise<void>;
  }[];
  
  // Audit
  initiatedBy: string;
  completedAt?: Date;
  rollbackAt?: Date;
}

enum TransactionStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  ROLLED_BACK = 'ROLLED_BACK'
}
```

### Transaction Examples

#### Body Admission Transaction
```typescript
// Example of a body admission transaction
const admissionTransaction = {
  id: 'tx_123',
  type: 'BODY_ADMISSION',
  operations: [
    {
      type: 'CREATE',
      model: 'bodyAdmission',
      data: { /* admission data */ }
    },
    {
      type: 'UPDATE',
      model: 'body',
      data: { status: 'ADMITTED' }
    },
    {
      type: 'CREATE',
      model: 'chainOfCustody',
      data: { /* custody record */ }
    }
  ]
};
```

## Webhooks

### Webhook Configuration
```typescript
POST /api/webhooks/configure
Authorization: Bearer {token}

Request:
{
  "url": string,
  "events": WebhookEvent[],
  "secret": string,
  "isActive": boolean,
  "retryPolicy": {
    "maxAttempts": number,
    "backoffStrategy": "linear" | "exponential",
    "initialDelay": number
  }
}

Response:
{
  "id": string,
  "status": "active" | "inactive",
  "createdAt": string
}
```

### Webhook Events
```typescript
enum WebhookEvent {
  // Body Events
  BODY_COLLECTED = 'body.collected',
  BODY_ADMITTED = 'body.admitted',
  BODY_RELEASED = 'body.released',
  
  // Storage Events
  STORAGE_TEMPERATURE_ALERT = 'storage.temperature.alert',
  STORAGE_CAPACITY_WARNING = 'storage.capacity.warning',
  
  // Examination Events
  EXAMINATION_COMPLETED = 'examination.completed',
  TEST_RESULTS_READY = 'test.results.ready',
  
  // System Events
  SYSTEM_ALERT = 'system.alert',
  MAINTENANCE_REQUIRED = 'maintenance.required'
}
```

### Webhook Payload Format
```typescript
interface WebhookPayload {
  id: string;
  timestamp: string;
  event: WebhookEvent;
  data: any;
  signature: string; // HMAC signature
}
```

## Advanced Endpoints

### Batch Operations
```typescript
POST /api/batch
Authorization: Bearer {token}

Request:
{
  "operations": [
    {
      "method": "POST" | "PUT" | "DELETE",
      "path": string,
      "body": any
    }
  ],
  "options": {
    "stopOnError": boolean,
    "parallel": boolean
  }
}

Response:
{
  "results": [
    {
      "status": number,
      "data": any,
      "error?: string
    }
  ],
  "summary": {
    "total": number,
    "succeeded": number,
    "failed": number
  }
}
```

### Search and Filters
```typescript
GET /api/bodies/search
Authorization: Bearer {token}

Query Parameters:
{
  "query": string,
  "filters": {
    "status": BodyStatus[],
    "dateRange": {
      "start": string,
      "end": string
    },
    "location": string[],
    "condition": BodyCondition[]
  },
  "pagination": {
    "page": number,
    "limit": number
  },
  "sort": {
    "field": string,
    "order": "asc" | "desc"
  }
}

Response:
{
  "items": Body[],
  "total": number,
  "page": number,
  "totalPages": number
}
```

### Reporting
```typescript
POST /api/reports/generate
Authorization: Bearer {token}

Request:
{
  "type": ReportType,
  "parameters": {
    "dateRange": {
      "start": string,
      "end": string
    },
    "facilities": string[],
    "metrics": string[],
    "format": "PDF" | "CSV" | "EXCEL"
  }
}

Response:
{
  "reportId": string,
  "status": "processing" | "completed",
  "downloadUrl?: string
}
```

## Error Handling

### Detailed Error Scenarios
```typescript
// Authentication Errors
{
  "error": {
    "code": "AUTH_ERROR",
    "message": "Authentication failed",
    "details": {
      "reason": "invalid_token" | "expired_token" | "insufficient_permissions",
      "requiredPermissions": string[]
    }
  }
}

// Validation Errors
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": {
      "fields": [
        {
          "field": string,
          "error": string,
          "constraints": any
        }
      ]
    }
  }
}

// Business Logic Errors
{
  "error": {
    "code": "BUSINESS_RULE_VIOLATION",
    "message": "Operation cannot be completed",
    "details": {
      "rule": string,
      "currentState": any,
      "allowedTransitions": string[]
    }
  }
}

// System Errors
{
  "error": {
    "code": "SYSTEM_ERROR",
    "message": "Internal server error",
    "details": {
      "errorId": string,
      "component": string,
      "retry": boolean
    }
  }
}
```

### Error Recovery Procedures
```typescript
// Retry Failed Operation
POST /api/operations/:id/retry
Authorization: Bearer {token}

Request:
{
  "maxAttempts": number,
  "backoffDelay": number
}

Response:
{
  "operationId": string,
  "status": "queued" | "processing" | "completed",
  "attempts": number
}

// Rollback Operation
POST /api/operations/:id/rollback
Authorization: Bearer {token}

Response:
{
  "operationId": string,
  "status": "rolled_back",
  "timestamp": string
}
```

## Performance Optimization

### Caching
```typescript
// Cache Headers
Cache-Control: max-age=3600, must-revalidate
ETag: "33a64df551425fcc55e4d42a148795d9f25f89d4"

// Conditional Requests
GET /api/bodies/:id
If-None-Match: "33a64df551425fcc55e4d42a148795d9f25f89d4"

Response (304 Not Modified):
{}
```

### Pagination
```typescript
// Cursor-based Pagination
GET /api/bodies?cursor=eyJpZCI6IjEyMyIsImNyZWF0ZWRBdCI6IjIwMjQtMDEtMjBUMDA6MDA6MDBaIn0&limit=20

Response:
{
  "items": Body[],
  "nextCursor": string,
  "hasMore": boolean
}

// Time-based Pagination
GET /api/bodies?after=2024-01-20T00:00:00Z&limit=20

Response:
{
  "items": Body[],
  "lastTimestamp": string,
  "hasMore": boolean
}
```

## Real-time Updates

### WebSocket Connection
```typescript
// Connection
WebSocket: wss://api.gppathology.com/v1/ws
Authorization: Bearer {token}

// Subscribe to Updates
{
  "type": "subscribe",
  "channels": [
    "body.updates",
    "storage.temperature",
    "alerts"
  ]
}

// Message Format
{
  "type": "update" | "alert" | "error",
  "channel": string,
  "data": any,
  "timestamp": string
}
```

### Server-Sent Events
```typescript
GET /api/events
Authorization: Bearer {token}
Accept: text/event-stream

// Event Format
event: bodyUpdate
data: {
  "id": string,
  "type": "status_change" | "location_update",
  "data": any
}

event: alert
data: {
  "type": "temperature" | "security" | "system",
  "severity": "info" | "warning" | "critical",
  "message": string
}
``` 