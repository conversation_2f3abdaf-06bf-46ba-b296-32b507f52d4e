# AI Development Guide with Vercel AI SDK

A comprehensive guide for implementing AI features using the Vercel AI SDK.

## Table of Contents
- [Project Setup](#project-setup)
- [API Implementation](#api-implementation)
- [Prompt Engineering](#prompt-engineering)
- [Tool Development](#tool-development)
- [Multistep Interfaces](#multistep-interfaces)
- [UI Components](#ui-components)
- [<PERSON>rro<PERSON>ling](#error-handling)
- [Performance](#performance)
- [Security](#security)
- [Testing](#testing)
- [Documentation](#documentation)
- [Building Agentic Systems](#building-agentic-systems)

## Project Setup

### Environment Configuration

Create a `.env.local` file with the following variables:

```bash
OPENAI_API_KEY=your_key_here
AI_MODEL_PROVIDER=openai # or anthropic, cohere, etc.
AI_MODEL_NAME=gpt-4-turbo # or specific model name
MAX_TOKENS=2000 # adjust based on needs
TEMPERATURE=0.7 # default creativity setting
```

### Dependencies

Add these to your `package.json`:

```json
{
  "dependencies": {
    "ai": "^4.1.0",
    "@ai-sdk/openai": "^1.0.0",
    "zod": "^3.0.0",
    "@vercel/ai": "^1.0.0"
  }
}
```

Install dependencies:
```bash
pnpm add ai @ai-sdk/openai zod @vercel/ai

# Optional providers
pnpm add @ai-sdk/anthropic # for Claude models
pnpm add @ai-sdk/cohere # for Cohere models
```

## API Implementation

### Basic Route Handler

```typescript
// app/api/chat/route.ts
import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { NextResponse } from 'next/server';

export const runtime = 'edge';
export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    const { messages, options } = await req.json();
    const result = streamText({
      model: openai(process.env.AI_MODEL_NAME || 'gpt-4-turbo'),
      messages,
      temperature: parseFloat(process.env.TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.MAX_TOKENS || '2000'),
      ...options
    });
    return result.toDataStreamResponse();
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
```

## Prompt Engineering

### System Prompt Template

```typescript
interface PromptContext {
  role?: string;
  background: string;
  constraints: string[];
  goals: string[];
  outputFormat: string;
  additionalInstructions?: string;
}

const createSystemPrompt = (context: PromptContext) => `
Role: ${context.role || 'AI Assistant'}

Background:
${context.background}

Constraints:
${context.constraints.join('\n')}

Goals:
${context.goals.join('\n')}

Output Format:
${context.outputFormat}

Additional Instructions:
${context.additionalInstructions}
`;
```

### Temperature Settings

```typescript
type AITask = 'creative' | 'balanced' | 'precise' | 'factual';

const temperatureMap = {
  creative: 0.8,  // For generation tasks
  balanced: 0.5,  // For general tasks
  precise: 0.2,   // For specific tasks
  factual: 0      // For fact-based tasks
};
```

## Tool Development

### Tool Template

```typescript
import { z } from 'zod';
import { tool } from 'ai';

export const createTool = tool({
  name: 'toolName',
  description: 'Clear description of tool purpose',
  version: '1.0.0',
  parameters: z.object({
    param1: z.string().describe('Parameter description'),
    param2: z.number().optional().describe('Optional parameter')
  }),
  execute: async ({ param1, param2 }) => {
    try {
      // Implementation
      return { result: 'value' };
    } catch (error) {
      throw new Error(`Tool error: ${error.message}`);
    }
  }
});
```

### Result Processing

```typescript
interface ProcessingOptions {
  metadata?: Record<string, any>;
  formatting?: 'json' | 'text' | 'markdown';
}

interface ProcessedResult<T> {
  data: T;
  metadata: {
    processedAt: string;
    success: boolean;
    [key: string]: any;
  };
  formatting: string;
}

const processToolResult = <T>(
  result: T,
  options: ProcessingOptions
): ProcessedResult<T> => {
  return {
    data: result,
    metadata: {
      processedAt: new Date().toISOString(),
      success: true,
      ...options.metadata
    },
    formatting: options.formatting || 'json'
  };
};
```

## Multistep Interfaces

### Overview
Multistep interfaces enable complex interactions through multiple independent steps. They rely on two key concepts:
- **Tool Composition**: Combining multiple tools for complex tasks
- **Application Context**: Managing state and conversation history

### Application Context Management

```typescript
// types/context.ts
interface ApplicationContext {
  conversation: Message[];
  toolResults: Record<string, any>;
  userState: {
    lastAction?: string;
    currentStep?: string;
    completedSteps: string[];
  };
}

// utils/contextManager.ts
export class ContextManager {
  private context: ApplicationContext;

  constructor() {
    this.context = {
      conversation: [],
      toolResults: {},
      userState: {
        completedSteps: []
      }
    };
  }

  updateContext(step: string, result: any) {
    this.context.toolResults[step] = result;
    this.context.userState.lastAction = step;
    this.context.userState.completedSteps.push(step);
  }

  getContextForStep(step: string) {
    return {
      previousResults: this.context.toolResults,
      currentStep: step,
      conversation: this.context.conversation
    };
  }
}
```

### Tool Composition Example

```typescript
// tools/flightBooking/index.ts
import { z } from 'zod';
import { tool } from 'ai';

// Search Flights Tool
export const searchFlightsTool = tool({
  name: 'searchFlights',
  description: 'Search available flights',
  parameters: z.object({
    origin: z.string().describe('Departure city'),
    destination: z.string().describe('Arrival city'),
    date: z.string().optional().describe('Travel date')
  }),
  execute: async ({ origin, destination, date }) => {
    // Implementation
    return { flights: [] };
  }
});

// Lookup Flight Tool
export const lookupFlightTool = tool({
  name: 'lookupFlight',
  description: 'Get detailed flight information',
  parameters: z.object({
    flightNumber: z.string().describe('Flight number to look up')
  }),
  execute: async ({ flightNumber }) => {
    // Implementation
    return { flightDetails: {} };
  }
});

// Book Flight Tool
export const bookFlightTool = tool({
  name: 'bookFlight',
  description: 'Book a flight',
  parameters: z.object({
    flightNumber: z.string(),
    passengers: z.array(z.object({
      name: z.string(),
      email: z.string().email()
    })),
    date: z.string()
  }),
  execute: async ({ flightNumber, passengers, date }) => {
    // Implementation
    return { bookingReference: '' };
  }
});
```

### Multistep Flow Implementation

```typescript
// components/FlightBooking.tsx
'use client';

import { useChat } from 'ai/react';
import { useState } from 'react';

interface BookingStep {
  id: string;
  completed: boolean;
  result?: any;
}

export function FlightBooking() {
  const [steps, setSteps] = useState<BookingStep[]>([
    { id: 'search', completed: false },
    { id: 'select', completed: false },
    { id: 'book', completed: false }
  ]);

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading
  } = useChat({
    api: '/api/flight-booking',
    onResponse: (response) => {
      // Handle step completion
      updateStepProgress(response);
    }
  });

  const updateStepProgress = (response: any) => {
    setSteps(currentSteps =>
      currentSteps.map(step =>
        step.id === response.step
          ? { ...step, completed: true, result: response.result }
          : step
      )
    );
  };

  return (
    <div className="booking-flow">
      {/* Step Progress Indicator */}
      <div className="steps-progress">
        {steps.map(step => (
          <div
            key={step.id}
            className={`step ${step.completed ? 'completed' : ''}`}
          >
            {step.id}
          </div>
        ))}
      </div>

      {/* Chat Interface */}
      <div className="chat-interface">
        {messages.map(m => (
          <div key={m.id} className={`message ${m.role}`}>
            {m.content}
          </div>
        ))}
      </div>

      {/* Input Form */}
      <form onSubmit={handleSubmit}>
        <input
          value={input}
          onChange={handleInputChange}
          placeholder="Tell me about your travel plans..."
          disabled={isLoading}
        />
        <button type="submit" disabled={isLoading}>
          Send
        </button>
      </form>
    </div>
  );
}
```

### API Route for Multistep Flow

```typescript
// app/api/flight-booking/route.ts
import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { NextResponse } from 'next/server';
import { searchFlightsTool, lookupFlightTool, bookFlightTool } from '@/tools/flightBooking';

export const runtime = 'edge';
export const maxDuration = 60; // Longer timeout for multi-step processes

export async function POST(req: Request) {
  try {
    const { messages, options } = await req.json();
    
    const result = streamText({
      model: openai(process.env.AI_MODEL_NAME || 'gpt-4-turbo'),
      messages,
      temperature: 0.7,
      maxSteps: 5, // Allow multiple steps for the booking process
      tools: {
        searchFlights: searchFlightsTool,
        lookupFlight: lookupFlightTool,
        bookFlight: bookFlightTool
      }
    });

    return result.toDataStreamResponse();
  } catch (error) {
    return NextResponse.json(
      { error: 'Booking Process Error' },
      { status: 500 }
    );
  }
}
```

### Best Practices for Multistep Interfaces

1. **Context Management**
   - Maintain clear state transitions
   - Preserve important context between steps
   - Handle interruptions gracefully
   - Allow users to go back or modify previous steps

2. **Tool Design**
   - Keep tools focused and single-purpose
   - Design tools that can be composed
   - Include validation at each step
   - Provide clear error messages

3. **User Experience**
   - Show clear progress indicators
   - Allow for natural language interactions
   - Provide fallback options
   - Maintain conversation context

4. **Error Handling**
   - Implement step-specific error handling
   - Allow for retry mechanisms
   - Preserve partial progress
   - Provide clear recovery paths

### Common Patterns

1. **Sequential Flow**
   ```typescript
   const steps = ['search', 'select', 'confirm', 'book'];
   ```

2. **Branching Flow**
   ```typescript
   interface StepDefinition {
     id: string;
     next: (result: any) => string[];
     validate: (input: any) => boolean;
   }
   ```

3. **Conditional Flow**
   ```typescript
   const determineNextStep = (currentStep: string, result: any) => {
     switch (currentStep) {
       case 'search':
         return result.flights.length > 0 ? 'select' : 'refine';
       case 'select':
         return result.selected ? 'confirm' : 'search';
       default:
         return 'complete';
     }
   };
   ```

## UI Components

### Chat Interface

```typescript
// components/Chat.tsx
'use client';

import { useChat } from 'ai/react';

export function Chat() {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error
  } = useChat({
    api: '/api/chat',
    onResponse: (response) => {
      // Handle stream start
    },
    onFinish: () => {
      // Handle completion
    },
    onError: (error) => {
      console.error('Chat error:', error);
    }
  });

  return (
    <div className="chat-container">
      {/* Implement your chat UI */}
    </div>
  );
}
```

## Error Handling

### Error Types

```typescript
export class AIBaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public status: number,
    public details?: any
  ) {
    super(message);
    this.name = 'AIBaseError';
  }
}

export class ModelError extends AIBaseError {
  constructor(message: string, details?: any) {
    super(message, 'MODEL_ERROR', 500, details);
  }
}

export class ToolError extends AIBaseError {
  constructor(message: string, toolName: string, details?: any) {
    super(message, 'TOOL_ERROR', 500, { toolName, ...details });
  }
}
```

## Performance

### Caching

```typescript
import { Redis } from 'ioredis';

export class AICache {
  private redis: Redis;
  private prefix: string;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
    this.prefix = 'ai:';
  }

  async get<T>(key: string): Promise<T | null> {
    const data = await this.redis.get(this.prefix + key);
    return data ? JSON.parse(data) : null;
  }

  async set<T>(
    key: string,
    value: T,
    ttlSeconds: number = 3600
  ): Promise<void> {
    await this.redis.setex(
      this.prefix + key,
      ttlSeconds,
      JSON.stringify(value)
    );
  }
}
```

## Security

### Input Validation

```typescript
import { z } from 'zod';

export const messageSchema = z.object({
  role: z.enum(['user', 'assistant', 'system']),
  content: z.string().min(1).max(4000),
  name: z.string().optional(),
  toolCalls: z.array(z.any()).optional()
});

export const chatRequestSchema = z.object({
  messages: z.array(messageSchema),
  options: z.object({
    temperature: z.number().min(0).max(1).optional(),
    maxTokens: z.number().positive().optional(),
    tools: z.array(z.any()).optional()
  }).optional()
});
```

### Output Sanitization

```typescript
import DOMPurify from 'dompurify';
import { JSDOM } from 'jsdom';

const window = new JSDOM('').window;
const purify = DOMPurify(window);

export const sanitizeOutput = (content: string): string => {
  return purify.sanitize(content, {
    ALLOWED_TAGS: ['p', 'b', 'i', 'em', 'strong', 'code'],
    ALLOWED_ATTR: []
  });
};
```

## Testing

### Unit Tests

```typescript
// __tests__/tools/example.test.ts
import { exampleTool } from '../tools/example';

describe('Example Tool', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should process valid input', async () => {
    const result = await exampleTool.execute({
      param: 'test'
    });
    expect(result).toBeDefined();
  });

  it('should handle errors', async () => {
    await expect(
      exampleTool.execute({
        param: 'invalid'
      })
    ).rejects.toThrow();
  });
});
```

## Best Practices

### 1. Error Handling
- Implement proper error boundaries
- Use typed error classes
- Provide user-friendly error messages
- Log errors with context

### 2. Performance
- Enable streaming for long responses
- Implement caching for frequent requests
- Use appropriate model sizes
- Monitor token usage

### 3. Security
- Validate all inputs
- Sanitize all outputs
- Implement rate limiting
- Use proper authentication

### 4. Testing
- Write unit tests for tools
- Test error scenarios
- Implement integration tests
- Test streaming functionality

## Common Issues and Solutions

### Rate Limiting
**Problem**: Too many requests (429 errors)
**Solutions**:
- Implement exponential backoff
- Cache frequent requests
- Increase rate limits if needed

### Timeout Issues
**Problem**: Requests timing out
**Solutions**:
- Use streaming responses
- Reduce prompt complexity
- Implement request chunking

### Token Usage
**Problem**: High API costs
**Solutions**:
- Implement token counting
- Use context windowing
- Cache common responses

## Monitoring

### Logging
```typescript
const logger = {
  error: (err: Error, context?: any) => {
    console.error({
      timestamp: new Date().toISOString(),
      error: err.message,
      stack: err.stack,
      ...context
    });
  }
};
```

### Key Metrics to Track
- Request latency
- Token usage
- Error rates
- Cache hit rates
- Model performance
- Cost per request

## Additional Resources

- [Vercel AI SDK Documentation](https://sdk.vercel.ai/docs)
- [OpenAI Documentation](https://platform.openai.com/docs)
- [Zod Documentation](https://zod.dev/)
- [Next.js Documentation](https://nextjs.org/docs)

## License

MIT License - Feel free to use this guide in your projects.

## Building Agentic Systems

### Agent Architecture

```typescript
// types/agent.ts
interface AgentState {
  memory: {
    shortTerm: Map<string, any>;
    longTerm: Map<string, any>;
  };
  context: {
    workspace: WorkspaceContext;
    conversation: ConversationContext;
    development: DevelopmentContext;
  };
  tools: Map<string, Tool>;
  config: AgentConfig;
}

interface WorkspaceContext {
  currentFile?: string;
  openFiles: string[];
  recentFiles: string[];
  gitStatus?: GitStatus;
  fileSystem: FileSystemState;
}

interface ConversationContext {
  messages: Message[];
  intent?: string;
  lastCommand?: string;
  userPreferences: Map<string, any>;
}

interface DevelopmentContext {
  language?: string;
  framework?: string;
  dependencies: Map<string, string>;
  projectStructure: ProjectNode[];
}

// agent/core.ts
export class DevelopmentAgent {
  private state: AgentState;
  private toolRegistry: ToolRegistry;
  private decisionEngine: DecisionEngine;

  constructor(config: AgentConfig) {
    this.state = this.initializeState(config);
    this.toolRegistry = new ToolRegistry();
    this.decisionEngine = new DecisionEngine(this.state);
  }

  async process(input: UserInput): Promise<AgentResponse> {
    // 1. Update context
    await this.updateContext(input);

    // 2. Analyze intent
    const intent = await this.analyzeIntent(input);

    // 3. Plan actions
    const plan = await this.createActionPlan(intent);

    // 4. Execute plan
    const result = await this.executePlan(plan);

    // 5. Update state
    await this.updateState(result);

    return this.formatResponse(result);
  }

  private async updateContext(input: UserInput) {
    // Update workspace context
    await this.updateWorkspaceContext();
    
    // Update development context
    await this.updateDevelopmentContext();
    
    // Update conversation context
    this.updateConversationContext(input);
  }

  private async createActionPlan(intent: Intent): Promise<ActionPlan> {
    return this.decisionEngine.planActions(intent, this.state);
  }
}
```

### Code Analysis System

```typescript
// analysis/parser.ts
import * as parser from '@babel/parser';
import traverse from '@babel/traverse';
import * as t from '@babel/types';

export class CodeAnalyzer {
  async analyzeFile(filePath: string): Promise<CodeAnalysis> {
    const content = await fs.readFile(filePath, 'utf-8');
    const ast = parser.parse(content, {
      sourceType: 'module',
      plugins: ['typescript', 'jsx']
    });

    const analysis: CodeAnalysis = {
      imports: [],
      exports: [],
      functions: [],
      classes: [],
      dependencies: new Set()
    };

    traverse(ast, {
      ImportDeclaration(path) {
        analysis.imports.push({
          source: path.node.source.value,
          specifiers: path.node.specifiers.map(spec => ({
            type: spec.type,
            name: t.isIdentifier(spec.local) ? spec.local.name : undefined
          }))
        });
        analysis.dependencies.add(path.node.source.value);
      },
      ExportNamedDeclaration(path) {
        // Handle exports
      },
      FunctionDeclaration(path) {
        // Analyze functions
      },
      ClassDeclaration(path) {
        // Analyze classes
      }
    });

    return analysis;
  }
}
```

### Project Context Manager

```typescript
// context/workspace.ts
export class WorkspaceManager {
  private fileSystem: FileSystemAccess;
  private git: GitManager;
  private watcher: FileWatcher;

  constructor(workspacePath: string) {
    this.fileSystem = new FileSystemAccess(workspacePath);
    this.git = new GitManager(workspacePath);
    this.watcher = new FileWatcher(workspacePath);
  }

  async getProjectStructure(): Promise<ProjectStructure> {
    const files = await this.fileSystem.listFiles();
    const gitStatus = await this.git.getStatus();
    const dependencies = await this.analyzeDependencies();

    return {
      files,
      gitStatus,
      dependencies,
      structure: await this.buildProjectTree(files)
    };
  }

  async watchForChanges(callback: (changes: FileChange[]) => void) {
    this.watcher.onChange(async (changes) => {
      const analysis = await this.analyzeChanges(changes);
      callback(analysis);
    });
  }
}
```

### Decision Engine

```typescript
// agent/decision.ts
export class DecisionEngine {
  private state: AgentState;
  private toolSelector: ToolSelector;
  private contextAnalyzer: ContextAnalyzer;

  constructor(state: AgentState) {
    this.state = state;
    this.toolSelector = new ToolSelector();
    this.contextAnalyzer = new ContextAnalyzer();
  }

  async planActions(intent: Intent, context: AgentContext): Promise<ActionPlan> {
    // 1. Analyze context relevance
    const relevantContext = await this.contextAnalyzer.getRelevantContext(intent, context);

    // 2. Determine required capabilities
    const requiredCapabilities = this.analyzeRequiredCapabilities(intent);

    // 3. Select appropriate tools
    const selectedTools = await this.toolSelector.selectTools(requiredCapabilities);

    // 4. Create execution plan
    return this.createExecutionPlan(intent, selectedTools, relevantContext);
  }

  private async createExecutionPlan(
    intent: Intent,
    tools: Tool[],
    context: RelevantContext
  ): Promise<ActionPlan> {
    return {
      steps: this.planSteps(intent, tools),
      fallbackSteps: this.createFallbackSteps(),
      validations: this.createValidations(),
      context: this.prepareExecutionContext(context)
    };
  }
}
```

### Tool Orchestration

```typescript
// agent/orchestrator.ts
export class ToolOrchestrator {
  private tools: Map<string, Tool>;
  private executionContext: ExecutionContext;

  async executePlan(plan: ActionPlan): Promise<ExecutionResult> {
    const results: StepResult[] = [];

    for (const step of plan.steps) {
      try {
        // 1. Prepare step context
        const stepContext = await this.prepareStepContext(step, results);

        // 2. Execute step
        const result = await this.executeStep(step, stepContext);

        // 3. Validate result
        await this.validateStepResult(result, step.validations);

        // 4. Update context
        await this.updateExecutionContext(result);

        results.push(result);
      } catch (error) {
        // Handle step failure
        if (await this.canRetry(step, error)) {
          // Retry step
          continue;
        }
        if (plan.fallbackSteps) {
          // Execute fallback
          return this.executeFallback(plan.fallbackSteps, error);
        }
        throw error;
      }
    }

    return this.consolidateResults(results);
  }
}
```

### Memory Management

```typescript
// agent/memory.ts
export class AgentMemory {
  private shortTermStorage: Map<string, any>;
  private longTermStorage: Database;
  private contextWindow: CircularBuffer<ContextItem>;

  constructor(config: MemoryConfig) {
    this.shortTermStorage = new Map();
    this.longTermStorage = new Database(config.storageConfig);
    this.contextWindow = new CircularBuffer(config.contextWindowSize);
  }

  async remember(key: string, value: any, options: MemoryOptions = {}) {
    if (options.temporary) {
      this.shortTermStorage.set(key, value);
      return;
    }

    await this.longTermStorage.store(key, value, options);
    this.contextWindow.push({ key, value, timestamp: Date.now() });
  }

  async recall(key: string, options: RecallOptions = {}): Promise<any> {
    // Check short-term memory first
    if (this.shortTermStorage.has(key)) {
      return this.shortTermStorage.get(key);
    }

    // Check context window
    const contextItem = this.contextWindow.find(item => item.key === key);
    if (contextItem) {
      return contextItem.value;
    }

    // Fall back to long-term storage
    return this.longTermStorage.retrieve(key, options);
  }
}
```

### Best Practices for Agentic Systems

1. **State Management**
   - Maintain clear separation between short-term and long-term memory
   - Implement efficient context windowing
   - Use appropriate persistence strategies
   - Handle state conflicts and race conditions

2. **Decision Making**
   - Implement clear decision hierarchies
   - Use fallback strategies
   - Maintain action history
   - Implement undo/redo capabilities

3. **Tool Management**
   - Implement tool discovery and registration
   - Handle tool versioning
   - Manage tool dependencies
   - Implement tool composition strategies

4. **Context Awareness**
   - Monitor workspace changes
   - Track user preferences
   - Maintain development context
   - Handle context switches

5. **Error Recovery**
   - Implement graceful degradation
   - Maintain system stability
   - Provide clear error feedback
   - Support manual intervention

### Common Patterns for Agentic Systems

1. **Observer Pattern for Workspace Changes**
   ```typescript
   class WorkspaceObserver {
     private watchers: Set<FileSystemWatcher>;
     private handlers: Map<string, Handler[]>;

     addWatcher(pattern: string, handler: Handler) {
       // Implementation
     }

     removeWatcher(pattern: string, handler: Handler) {
       // Implementation
     }

     notify(changes: FileSystemChange[]) {
       // Implementation
     }
   }
   ```

2. **Command Pattern for Actions**
   ```typescript
   interface Command {
     execute(): Promise<void>;
     undo(): Promise<void>;
     redo(): Promise<void>;
   }

   class CommandManager {
     private undoStack: Command[] = [];
     private redoStack: Command[] = [];

     async executeCommand(command: Command) {
       await command.execute();
       this.undoStack.push(command);
       this.redoStack = [];
     }

     async undo() {
       const command = this.undoStack.pop();
       if (command) {
         await command.undo();
         this.redoStack.push(command);
       }
     }
   }
   ```

3. **Strategy Pattern for Tool Selection**
   ```typescript
   interface ToolSelectionStrategy {
     selectTool(intent: Intent, available: Tool[]): Tool;
   }

   class ContextBasedSelection implements ToolSelectionStrategy {
     selectTool(intent: Intent, available: Tool[]): Tool {
       // Implementation
     }
   }
   ``` 