import { PrismaClient, UserR<PERSON>, UserStatus } from '@prisma/client';
import CryptoJS from 'crypto-js';

// Copy of the hashPassword function from src/auth/utils/password.ts
async function hashPassword(password: string): Promise<string> {
  // Generate a random salt
  const salt = CryptoJS.lib.WordArray.random(16).toString();

  // Hash the password with the salt
  let hashedPassword = password;

  // Apply multiple rounds of hashing (10 rounds)
  for (let i = 0; i < 10; i++) {
    hashedPassword = CryptoJS.SHA256(hashedPassword + salt).toString();
  }

  // Return the salt and hashed password
  return `${salt}:${hashedPassword}`;
}

const prisma = new PrismaClient();

async function main() {
  try {
    // Check if test user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
    });

    if (existingUser) {
      console.log('Test user already exists. Updating password...');

      // Hash the password
      const hashedPassword = await hashPassword('Password123!');

      // Update the user
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          password: hashedPassword,
          status: UserStatus.ACTIVE,
        },
      });

      console.log('Test user password updated successfully.');
    } else {
      console.log('Creating test user...');

      // Hash the password
      const hashedPassword = await hashPassword('Password123!');

      // Create the user
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          password: hashedPassword,
          role: UserRole.ADMIN,
          status: UserStatus.ACTIVE,
        },
      });

      console.log('Test user created successfully.');
    }

    // Print login credentials
    console.log('\nTest User Login Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Password123!');

  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
