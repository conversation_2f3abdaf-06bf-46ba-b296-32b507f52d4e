import { PrismaClient } from '@prisma/client';
import CryptoJ<PERSON> from 'crypto-js';

const prisma = new PrismaClient();
const PASSWORD_SALT_ROUNDS = 10;

// Copy of the hashPassword function from src/auth/utils/password.ts
async function hashPassword(password: string): Promise<string> {
  // Generate a random salt
  const salt = CryptoJS.lib.WordArray.random(16).toString();

  // Hash the password with the salt
  let hashedPassword = password;

  // Apply multiple rounds of hashing
  for (let i = 0; i < PASSWORD_SALT_ROUNDS; i++) {
    hashedPassword = CryptoJS.SHA256(hashedPassword + salt).toString();
  }

  // Return the salt and hashed password
  return `${salt}:${hashedPassword}`;
}

async function main() {
  try {
    // Get all users
    const users = await prisma.user.findMany();
    
    console.log(`Found ${users.length} users`);
    
    // Check each user's password format
    for (const user of users) {
      const isValidFormat = user.password.includes(':');
      
      console.log(`User ${user.email}: Password format ${isValidFormat ? 'valid' : 'invalid'}`);
      
      // If the password format is invalid, fix it
      if (!isValidFormat) {
        // Use a default password for all users with invalid password format
        const defaultPassword = 'Password123!';
        const hashedPassword = await hashPassword(defaultPassword);
        
        // Update the user's password
        await prisma.user.update({
          where: { id: user.id },
          data: { password: hashedPassword },
        });
        
        console.log(`Updated password for ${user.email}`);
        console.log(`New login credentials: Email: ${user.email}, Password: ${defaultPassword}`);
      }
    }
    
    console.log('Password fix completed');
    
  } catch (error) {
    console.error('Error fixing passwords:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
