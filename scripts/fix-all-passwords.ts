import { PrismaClient } from '@prisma/client';
import Crypt<PERSON>J<PERSON> from 'crypto-js';

const prisma = new PrismaClient();
const PASSWORD_SALT_ROUNDS = 10;

/**
 * Hash a password using CryptoJS
 * @param password Plain text password
 * @returns Hashed password
 */
async function hashPassword(password: string): Promise<string> {
  // Generate a random salt
  const salt = CryptoJS.lib.WordArray.random(16).toString();

  // Hash the password with the salt
  let hashedPassword = password;

  // Apply multiple rounds of hashing
  for (let i = 0; i < PASSWORD_SALT_ROUNDS; i++) {
    hashedPassword = CryptoJS.SHA256(hashedPassword + salt).toString();
  }

  // Return the salt and hashed password
  return `${salt}:${hashedPassword}`;
}

/**
 * Verify a password using CryptoJS
 * @param password Plain text password
 * @param hashedPassword Hashed password
 * @returns Whether the password is valid
 */
async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  // Split the hashed password into salt and hash
  const [salt, hash] = hashedPassword.split(':');

  if (!salt || !hash) {
    console.error('Invalid hashed password format:', { hashedPassword });
    return false;
  }

  // Hash the password with the salt
  let passwordToVerify = password;

  // Apply multiple rounds of hashing
  for (let i = 0; i < PASSWORD_SALT_ROUNDS; i++) {
    passwordToVerify = CryptoJS.SHA256(passwordToVerify + salt).toString();
  }

  // Compare the hashes
  return passwordToVerify === hash;
}

async function main() {
  try {
    // Get all users
    const users = await prisma.user.findMany();
    
    console.log(`Found ${users.length} users`);
    
    // Default password for all users
    const defaultPassword = 'Password123!';
    
    // Process each user
    for (const user of users) {
      console.log(`Processing user: ${user.email}`);
      
      // Check if password format is valid
      const isValidFormat = user.password && user.password.includes(':');
      console.log(`  Password format: ${isValidFormat ? 'valid' : 'invalid'}`);
      
      // Hash the default password
      const hashedPassword = await hashPassword(defaultPassword);
      
      // Update the user's password
      await prisma.user.update({
        where: { id: user.id },
        data: { 
          password: hashedPassword,
          status: 'ACTIVE' // Ensure user is active
        },
      });
      
      // Verify the password works
      const isValid = await verifyPassword(defaultPassword, hashedPassword);
      console.log(`  Password verification: ${isValid ? 'PASSED' : 'FAILED'}`);
      
      console.log(`  Updated password for ${user.email}`);
      console.log(`  New login credentials: Email: ${user.email}, Password: ${defaultPassword}`);
      console.log('-----------------------------------');
    }
    
    console.log('\nAll passwords have been reset to: Password123!');
    
  } catch (error) {
    console.error('Error fixing passwords:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
