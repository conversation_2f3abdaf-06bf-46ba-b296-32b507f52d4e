#!/bin/bash

# <PERSON>ript to fix the "use client" directive order and duplicate dynamic imports

# Function to fix a file with "use client" directive issues
fix_client_directive() {
  local file=$1
  
  # Check if the file has the "use client" directive after other expressions
  if grep -q "// Force dynamic rendering" "$file" && grep -q "'use client';" "$file"; then
    echo "Fixing client directive in: $file"
    
    # Remove the dynamic export and "use client" directive
    sed -i '/\/\/ Force dynamic rendering/d' "$file"
    sed -i '/export const dynamic = "force-dynamic";/d' "$file"
    sed -i '/\'use client\';/d' "$file"
    
    # Add them back in the correct order
    sed -i '1i\'use client\';\n\n// Force dynamic rendering\nexport const dynamic = "force-dynamic";' "$file"
  fi
}

# Function to fix duplicate dynamic imports
fix_duplicate_dynamic() {
  local file=$1
  
  # Check if the file has both dynamic export and dynamic import
  if grep -q "export const dynamic = " "$file" && grep -q "import dynamic from 'next/dynamic';" "$file"; then
    echo "Fixing duplicate dynamic in: $file"
    
    # Rename the dynamic import to dynamicImport
    sed -i 's/import dynamic from/import dynamicImport from/g' "$file"
    
    # Update any usage of dynamic() to dynamicImport()
    sed -i 's/dynamic(/dynamicImport(/g' "$file"
  fi
}

# Fix all page files in admin directory
find src/app/admin -name "*.tsx" | while read -r file; do
  fix_client_directive "$file"
  fix_duplicate_dynamic "$file"
done

# Fix all page files in dashboard directory
find src/app/dashboard -name "*.tsx" | while read -r file; do
  fix_client_directive "$file"
  fix_duplicate_dynamic "$file"
done

echo "Fixed client directive and duplicate dynamic imports in all files"
