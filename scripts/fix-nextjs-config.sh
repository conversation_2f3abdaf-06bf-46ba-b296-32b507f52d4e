#!/bin/bash

# Enhanced script to fix Next.js configuration issues
# Features:
# - Dry run mode to preview changes
# - Proper handling of 'use client' directive
# - Fixing duplicate dynamic imports
# - Follows Next.js best practices
# - Backup creation before making changes

# Default settings
DRY_RUN=false
VERBOSE=false
CREATE_BACKUP=true
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  -d, --dry-run     Preview changes without modifying files"
  echo "  -v, --verbose     Show detailed information about changes"
  echo "  -n, --no-backup   Skip creating backups of modified files"
  echo "  -h, --help        Display this help message"
  exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -v|--verbose)
      VERBOSE=true
      shift
      ;;
    -n|--no-backup)
      CREATE_BACKUP=false
      shift
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done

# Create backup directory if needed
if [ "$CREATE_BACKUP" = true ] && [ "$DRY_RUN" = false ]; then
  mkdir -p "$BACKUP_DIR"
  echo "Backup directory created: $BACKUP_DIR"
fi

# Function to create a backup of a file
backup_file() {
  local file=$1
  if [ "$CREATE_BACKUP" = true ] && [ "$DRY_RUN" = false ]; then
    # Create directory structure in backup folder
    local dir_path=$(dirname "$file")
    mkdir -p "$BACKUP_DIR/$dir_path"
    cp "$file" "$BACKUP_DIR/$file"
    if [ "$VERBOSE" = true ]; then
      echo "Created backup: $BACKUP_DIR/$file"
    fi
  fi
}

# Function to fix 'use client' directive order
fix_client_directive() {
  local file=$1
  local modified=false
  local content

  # Read file content
  content=$(cat "$file")

  # Check if file has 'use client' directive in wrong order
  if (echo "$content" | grep -q "export const dynamic" && echo "$content" | grep -q "'use client';" &&
     (echo "$content" | grep -n "'use client';" | cut -d: -f1) > (echo "$content" | grep -n "export const dynamic" | cut -d: -f1)); then
    if [ "$VERBOSE" = true ] || [ "$DRY_RUN" = true ]; then
      echo "Fixing 'use client' directive order in: $file"
    fi

    # Create backup
    backup_file "$file"

    # Fix the file if not in dry run mode
    if [ "$DRY_RUN" = false ]; then
      # Create a temporary file with corrected content
      local temp_file=$(mktemp)

      # Add 'use client' at the top
      echo "'use client';" > "$temp_file"
      echo "" >> "$temp_file"

      # Add dynamic export
      echo "// Force dynamic rendering" >> "$temp_file"
      echo "export const dynamic = \"force-dynamic\";" >> "$temp_file"
      echo "" >> "$temp_file"

      # Add the rest of the content, skipping the original directives
      echo "$content" | grep -v "^'use client';" | grep -v "^// Force dynamic rendering" | grep -v "^export const dynamic = " >> "$temp_file"

      # Replace the original file
      mv "$temp_file" "$file"
    fi

    modified=true
  fi

  echo "$modified"
}

# Function to fix duplicate dynamic imports
fix_duplicate_dynamic() {
  local file=$1
  local modified=false
  local content

  # Read file content
  content=$(cat "$file")

  # Check if file has both dynamic export and dynamic import
  if echo "$content" | grep -q "export const dynamic = " && echo "$content" | grep -q "import dynamic from 'next/dynamic'"; then
    if [ "$VERBOSE" = true ] || [ "$DRY_RUN" = true ]; then
      echo "Fixing duplicate dynamic imports in: $file"
    fi

    # Create backup
    backup_file "$file"

    # Fix the file if not in dry run mode
    if [ "$DRY_RUN" = false ]; then
      # Replace dynamic import with dynamicImport
      sed -i 's/import dynamic from/import dynamicImport from/g' "$file"

      # Replace dynamic() calls with dynamicImport()
      sed -i 's/dynamic(/dynamicImport(/g' "$file"
    fi

    modified=true
  fi

  echo "$modified"
}

# Function to fix Next.js config in a file
fix_nextjs_config() {
  local file=$1
  local client_fixed
  local dynamic_fixed

  client_fixed=$(fix_client_directive "$file")
  dynamic_fixed=$(fix_duplicate_dynamic "$file")

  if [ "$client_fixed" = true ] || [ "$dynamic_fixed" = true ]; then
    echo "Fixed: $file"
  fi
}

# Main function to process files
process_files() {
  local dir=$1
  local count=0

  echo "Processing files in $dir..."

  # Find all .tsx files in the directory
  find "$dir" -name "*.tsx" | while read -r file; do
    fix_nextjs_config "$file"
    ((count++))
  done

  echo "Processed $count files in $dir"
}

# Main execution
echo "Next.js Configuration Fix Tool"
echo "Mode: $([ "$DRY_RUN" = true ] && echo "Dry Run" || echo "Live Run")"
echo "Verbose: $([ "$VERBOSE" = true ] && echo "Yes" || echo "No")"
echo "Creating Backups: $([ "$CREATE_BACKUP" = true ] && echo "Yes" || echo "No")"
echo ""

# Process admin and dashboard directories
process_files "src/app/admin"
process_files "src/app/dashboard"

# Fix next.config.js if needed
if [ -f "next.config.js" ]; then
  if grep -q "isrMemoryCacheSize" "next.config.js"; then
    echo "Fixing next.config.js..."

    # Create backup
    backup_file "next.config.js"

    # Fix the file if not in dry run mode
    if [ "$DRY_RUN" = false ]; then
      # Remove the isrMemoryCacheSize option
      sed -i '/isrMemoryCacheSize/d' "next.config.js"
    fi
  fi
fi

echo ""
if [ "$DRY_RUN" = true ]; then
  echo "Dry run completed. No files were modified."
else
  echo "Configuration fix completed."
  if [ "$CREATE_BACKUP" = true ]; then
    echo "Backups created in: $BACKUP_DIR"
  fi
fi
