#!/usr/bin/env node

import { migrateAllBodyTags } from '../src/utils/migrateBodyTags';

const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run');
const force = args.includes('--force');

// Color codes for console output
const colors = {
  blue: (text: string) => `\x1b[34m${text}\x1b[0m`,
  green: (text: string) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text: string) => `\x1b[33m${text}\x1b[0m`,
  red: (text: string) => `\x1b[31m${text}\x1b[0m`,
  cyan: (text: string) => `\x1b[36m${text}\x1b[0m`,
};

async function runMigration() {
  console.log(colors.blue(`\nBody Tag Migration Tool`));
  console.log(colors.blue('====================\n'));

  if (dryRun) {
    console.log(colors.yellow('🔍 Running in DRY RUN mode - no changes will be made\n'));
  }

  if (!force && !dryRun) {
    console.log(colors.red('⚠️  WARNING: This will modify body tags in the database'));
    console.log(colors.red('   Run with --dry-run to preview changes first\n'));
    console.log(colors.red('   Or use --force to skip this warning\n'));
    process.exit(1);
  }

  console.log(colors.cyan('Starting migration...'));
  
  try {
    const result = await migrateAllBodyTags(dryRun);
    
    console.log('\n' + colors.green('Migration Complete ✓'));
    console.log(colors.blue('\nResults:'));
    console.log('--------');
    console.log(`Total tags processed: ${colors.cyan(result.total.toString())}`);
    console.log(`Successfully migrated: ${colors.green(result.migrated.toString())}`);
    console.log(`Skipped (already valid): ${colors.yellow(result.skipped.toString())}`);
    console.log(`Failed: ${colors.red(result.failed.toString())}`);

    if (result.errors.length > 0) {
      console.log(colors.red('\nErrors:'));
      result.errors.forEach(({ id, tag, error }) => {
        console.log(colors.red(`- ${id} (${tag}): ${error}`));
      });
    }

    // Provide next steps
    if (dryRun) {
      console.log(colors.cyan('\nTo apply these changes, run:'));
      console.log(colors.cyan('npm run migrate:body-tags -- --force'));
    }

  } catch (error) {
    console.error(colors.red('\nMigration failed:'));
    console.error(colors.red(error instanceof Error ? error.message : String(error)));
    process.exit(1);
  }
}

runMigration();
