#!/bin/bash

# <PERSON>ript to fix specific files with known issues

# Function to fix a file with 'use client' directive in wrong order
fix_client_directive() {
  local file=$1
  echo "Fixing 'use client' directive in: $file"

  # Create a temporary file
  local temp_file=$(mktemp)

  # Add 'use client' at the top
  echo "'use client';" > "$temp_file"
  echo "" >> "$temp_file"

  # Add dynamic export
  echo "// Force dynamic rendering" >> "$temp_file"
  echo "export const dynamic = \"force-dynamic\";" >> "$temp_file"
  echo "" >> "$temp_file"

  # Add the rest of the content, skipping the original directives
  cat "$file" | grep -v "^'use client';" | grep -v "^// Force dynamic rendering" | grep -v "^export const dynamic = " >> "$temp_file"

  # Replace the original file
  mv "$temp_file" "$file"
}

# Function to fix a file with duplicate dynamic imports
fix_duplicate_dynamic() {
  local file=$1
  echo "Fixing duplicate dynamic imports in: $file"

  # Replace dynamic import with dynamicImport
  sed -i 's/import dynamic from/import dynamicImport from/g' "$file"

  # Replace dynamic() calls with dynamicImport()
  sed -i 's/dynamic(/dynamicImport(/g' "$file"
}

# Function to fix a file with 'use server' directive in wrong order
fix_server_directive() {
  local file=$1
  echo "Fixing 'use server' directive in: $file"

  # Create a temporary file
  local temp_file=$(mktemp)

  # Add 'use server' at the top
  echo "'use server';" > "$temp_file"
  echo "" >> "$temp_file"

  # Add dynamic export
  echo "// Force dynamic rendering" >> "$temp_file"
  echo "export const dynamic = \"force-dynamic\";" >> "$temp_file"
  echo "" >> "$temp_file"

  # Add the rest of the content, skipping the original directives
  cat "$file" | grep -v "^'use server';" | grep -v "^// Force dynamic rendering" | grep -v "^export const dynamic = " >> "$temp_file"

  # Replace the original file
  mv "$temp_file" "$file"
}

# List of files with 'use client' directive in wrong order
client_directive_files=(
  "src/app/admin/mailer/page.tsx"
  "src/app/admin/rbac/page.tsx"
  "src/app/admin/tools/body-tags/page.tsx"
  "src/app/admin/tools/page.tsx"
  "src/app/dashboard/knowledge-base/imageEditor/page.tsx"
  "src/app/dashboard/planning/page.tsx"
  "src/app/dashboard/records/admissions/[id]/edit/page.tsx"
  "src/app/dashboard/records/bodies/[id]/edit/page.tsx"
  "src/app/dashboard/records/bodies/[id]/report/page.tsx"
  "src/app/dashboard/records/referrals/new/page.tsx"
  "src/app/dashboard/records/releases/[id]/edit/page.tsx"
  "src/app/dashboard/records/releases/new/page.tsx"
)

# List of files with duplicate dynamic imports
duplicate_dynamic_files=(
  "src/app/admin/users/all/page.tsx"
  "src/app/admin/users/page.tsx"
  "src/app/dashboard/body-collection/page.tsx"
  "src/app/dashboard/records/admissions/[id]/page.tsx"
  "src/app/dashboard/records/collections/[id]/page.tsx"
  "src/app/dashboard/reports/page.tsx"
)

# Fix files with 'use client' directive in wrong order
for file in "${client_directive_files[@]}"; do
  if [ -f "$file" ]; then
    fix_client_directive "$file"
  else
    echo "Warning: File not found: $file"
  fi
done

# Fix files with duplicate dynamic imports
for file in "${duplicate_dynamic_files[@]}"; do
  if [ -f "$file" ]; then
    fix_duplicate_dynamic "$file"
  else
    echo "Warning: File not found: $file"
  fi
done

# Fix files with 'use server' directive in wrong order
for file in "${server_directive_files[@]}"; do
  if [ -f "$file" ]; then
    fix_server_directive "$file"
  else
    echo "Warning: File not found: $file"
  fi
done

echo "Specific file fixes completed."
