import { PrismaClient } from '@prisma/client';
import CryptoJ<PERSON> from 'crypto-js';

const prisma = new PrismaClient();
const PASSWORD_SALT_ROUNDS = 10;

// Copy of the hashPassword function from src/auth/utils/password.ts
async function hashPassword(password: string): Promise<string> {
  // Generate a random salt
  const salt = CryptoJS.lib.WordArray.random(16).toString();

  // Hash the password with the salt
  let hashedPassword = password;

  // Apply multiple rounds of hashing
  for (let i = 0; i < PASSWORD_SALT_ROUNDS; i++) {
    hashedPassword = CryptoJS.SHA256(hashedPassword + salt).toString();
  }

  // Return the salt and hashed password
  return `${salt}:${hashedPassword}`;
}

// Function to verify a password (for testing)
async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  // Split the hashed password into salt and hash
  const [salt, hash] = hashedPassword.split(':');

  if (!salt || !hash) {
    console.error('Invalid hashed password format:', { hashedPassword });
    return false;
  }

  // Hash the password with the salt
  let passwordToVerify = password;

  // Apply multiple rounds of hashing
  for (let i = 0; i < PASSWORD_SALT_ROUNDS; i++) {
    passwordToVerify = CryptoJS.SHA256(passwordToVerify + salt).toString();
  }

  // Compare the hashes
  const isMatch = passwordToVerify === hash;
  
  // Debug info
  console.log('Password verification:', {
    isMatch,
    passwordLength: password.length,
    saltLength: salt.length,
    hashLength: hash.length,
    rounds: PASSWORD_SALT_ROUNDS
  });
  
  return isMatch;
}

async function main() {
  try {
    // Email of the user to reset
    const email = '<EMAIL>';
    
    // New password
    const newPassword = 'Password123!';
    
    // Find the user
    const user = await prisma.user.findUnique({
      where: { email },
    });
    
    if (!user) {
      console.error(`User with email ${email} not found`);
      return;
    }
    
    console.log(`Found user: ${user.email}`);
    
    // Hash the new password
    const hashedPassword = await hashPassword(newPassword);
    
    // Update the user's password
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedPassword },
    });
    
    console.log(`Password updated for ${user.email}`);
    
    // Test verification
    const isValid = await verifyPassword(newPassword, hashedPassword);
    console.log(`Verification test: ${isValid ? 'PASSED' : 'FAILED'}`);
    
    console.log(`New login credentials: Email: ${email}, Password: ${newPassword}`);
    
  } catch (error) {
    console.error('Error resetting password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
