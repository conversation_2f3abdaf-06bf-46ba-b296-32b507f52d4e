import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkSeededData() {
  try {
    console.log('🔍 Checking seeded data...\n');

    // Check facilities
    const facilities = await prisma.facility.findMany({
      select: {
        id: true,
        name: true,
        type: true,
        status: true,
        _count: {
          select: {
            users: true,
            fridges: true
          }
        }
      }
    });

    console.log(`📍 Facilities (${facilities.length}):`);
    facilities.forEach(facility => {
      console.log(`  - ${facility.name} (${facility.type}) - ${facility._count.users} users, ${facility._count.fridges} fridges`);
    });

    // Check users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        facility: {
          select: {
            name: true
          }
        }
      }
    });

    console.log(`\n👥 Users (${users.length}):`);
    users.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - ${user.role} at ${user.facility?.name || 'No facility'}`);
    });

    // Check roles
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            users: true
          }
        }
      }
    });

    console.log(`\n🔐 Roles (${roles.length}):`);
    roles.forEach(role => {
      console.log(`  - ${role.name} (${role._count.users} users)`);
    });

    // Check permissions
    const permissions = await prisma.permission.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        category: true
      }
    });

    console.log(`\n🛡️ Permissions (${permissions.length}):`);
    permissions.slice(0, 10).forEach(permission => {
      console.log(`  - ${permission.name} (${permission.category}): ${permission.description || 'No description'}`);
    });
    if (permissions.length > 10) {
      console.log(`  ... and ${permissions.length - 10} more`);
    }

    console.log('\n✅ Data check completed successfully!');

  } catch (error) {
    console.error('❌ Error checking seeded data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSeededData();
