#!/bin/bash

# <PERSON>ript to fix the dynamic configuration in files that were incorrectly modified

# Function to fix a file with syntax errors
fix_file() {
  local file=$1
  
  # Check if the file has the problematic \n sequence
  if grep -q '\\n// Force dynamic rendering' "$file"; then
    echo "Fixing file: $file"
    
    # Remove the problematic line and add the correct dynamic export
    sed -i '/\\n\/\/ Force dynamic rendering/d' "$file"
    sed -i '/export { dynamic, fetchCache, revalidate }/d' "$file"
    
    # Add the correct dynamic export after 'use client'
    if grep -q '"use client"' "$file"; then
      sed -i '/"use client"/a\
// Force dynamic rendering\
export const dynamic = "force-dynamic";' "$file"
    else
      # If no 'use client', add at the beginning of the file
      sed -i '1i\// Force dynamic rendering\
export const dynamic = "force-dynamic";' "$file"
    fi
  fi
}

# Fix all page files in admin directory
find src/app/admin -name "*.tsx" | while read -r file; do
  fix_file "$file"
done

# Fix all page files in dashboard directory
find src/app/dashboard -name "*.tsx" | while read -r file; do
  fix_file "$file"
done

# Fix all API route files
find src/app/api -name "route.ts" | while read -r file; do
  # Check if the file has the problematic export
  if grep -q 'export { dynamic, fetchCache, revalidate, runtime }' "$file"; then
    echo "Fixing API route: $file"
    
    # Remove the problematic line
    sed -i '/export { dynamic, fetchCache, revalidate, runtime }/d' "$file"
    
    # Add the correct dynamic export at the beginning of the file
    sed -i '1i\// Force dynamic rendering\
export const dynamic = "force-dynamic";' "$file"
  fi
done

# Fix the root layout file
if grep -q 'export { dynamic, fetchCache, revalidate, dynamicParams, runtime }' "src/app/layout.tsx"; then
  echo "Fixing root layout file"
  sed -i '/export { dynamic, fetchCache, revalidate, dynamicParams, runtime }/d' "src/app/layout.tsx"
  sed -i '/import dynamic from/a\
// Force dynamic rendering for the entire application\
export const dynamic = "force-dynamic";' "src/app/layout.tsx"
fi

# Fix the admin layout file
if grep -q 'export { dynamic, fetchCache, revalidate, runtime }' "src/app/admin/layout.tsx"; then
  echo "Fixing admin layout file"
  sed -i '/export { dynamic, fetchCache, revalidate, runtime }/d' "src/app/admin/layout.tsx"
  sed -i '/"use client";/a\
// Force dynamic rendering for all admin pages\
export const dynamic = "force-dynamic";' "src/app/admin/layout.tsx"
fi

# Fix the dashboard layout file
if grep -q 'export { dynamic, fetchCache, revalidate, runtime }' "src/app/dashboard/layout.tsx"; then
  echo "Fixing dashboard layout file"
  sed -i '/export { dynamic, fetchCache, revalidate, runtime }/d' "src/app/dashboard/layout.tsx"
  sed -i '/"use client";/a\
// Force dynamic rendering for all dashboard pages\
export const dynamic = "force-dynamic";' "src/app/dashboard/layout.tsx"
fi

echo "Fixed dynamic configuration in all files"
