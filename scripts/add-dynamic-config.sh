#!/bin/bash

# Script to add dynamic configuration to all API route files

# Find all route.ts files in the API directory
find src/app/api -name "route.ts" | while read -r file; do
  # Check if the file already has dynamic export
  if ! grep -q "export const dynamic" "$file"; then
    # Add the dynamic export at the beginning of the file
    sed -i '1i// Force dynamic rendering\nexport { dynamic, fetchCache, revalidate, runtime } from "../../config";\n' "$file"
    echo "Added dynamic config to $file"
  fi
done

# Find all page.tsx files in the dashboard directory
find src/app/dashboard -name "page.tsx" | while read -r file; do
  # Check if the file already has dynamic export
  if ! grep -q "export const dynamic" "$file"; then
    # Add the dynamic export at the beginning of the file after 'use client'
    sed -i '/^.use client.;/a\\\n// Force dynamic rendering\nexport { dynamic, fetchCache, revalidate } from "../../config";\n' "$file"
    echo "Added dynamic config to $file"
  fi
done

# Find all page.tsx files in the admin directory
find src/app/admin -name "page.tsx" | while read -r file; do
  # Check if the file already has dynamic export
  if ! grep -q "export const dynamic" "$file"; then
    # Add the dynamic export at the beginning of the file after 'use client'
    sed -i '/^.use client.;/a\\\n// Force dynamic rendering\nexport { dynamic, fetchCache, revalidate } from "../../config";\n' "$file"
    echo "Added dynamic config to $file"
  fi
done

echo "Dynamic configuration added to all routes"
