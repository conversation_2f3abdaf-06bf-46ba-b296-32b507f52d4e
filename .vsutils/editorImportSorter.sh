#!/bin/bash

DIR="./src"
BACKUP_DIR="./import_sort_backup_$(date +%Y%m%d_%H%M%S)"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for required commands
for cmd in git sed awk; do
    if ! command_exists "$cmd"; then
        echo "Error: $cmd is not installed. Please install it and try again."
        exit 1
    fi
done

# Create a backup of the current state
mkdir -p "$BACKUP_DIR"
cp -R "$DIR" "$BACKUP_DIR"

# Commit current state if git is available
if git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
    git add .
    git commit -m "Auto-commit before import sorting [$(date)]"
    echo "Current state committed to git."
else
    echo "Not a git repository. Skipping git commit."
fi

process_file() {
    local file="$1"
    local temp_file=$(mktemp)

    # Group imports
    awk '
    BEGIN { print "// React imports" }
    /^import.*from.*react/ { print; next }
    /^$/ && !react_done { print "\n// MUI imports"; react_done=1 }
    /^import.*from.*@mui/ { print; next }
    /^$/ && !mui_done { print "\n// Third-party imports"; mui_done=1 }
    /^import.*from.*[^\.]/ && !/^import.*from.*@\// { print; next }
    /^$/ && !third_party_done { print "\n// Local imports"; third_party_done=1 }
    { print }
    ' "$file" > "$temp_file"

    # Convert relative imports to absolute
    sed -i 's|from "\.\./|from "@/|g' "$temp_file"
    sed -i 's|from "\./|from "@/|g' "$temp_file"

    # Use dynamic import for ApexCharts
    sed -i 's|import ReactApexChart from "react-apexcharts"|const ReactApexChart = dynamic(() => import("react-apexcharts"), { ssr: false })|g' "$temp_file"

    # Separate type imports
    sed -i 's|import \(.*\) from \(.*\)|import type \1 from \2|g' "$temp_file"

    # Use named imports for MUI components
    sed -i 's|import \(.*\) from "@mui/joy"|import { \1 } from "@mui/joy"|g' "$temp_file"

    # Remove unused imports
    sed -i '/^import.*from.*/ { /[^{}]*;$/d; }' "$temp_file"

    # Sort imports alphabetically within each group
    awk '
    /^\/\/ / { if (NR!=1) { asorti(a); for (i in a) print a[i]; delete a }; print; next }
    { a[NR] = $0 }
    END { asorti(a); for (i in a) print a[i] }
    ' "$temp_file" > "${temp_file}.sorted"

    mv "${temp_file}.sorted" "$file"
    rm -f "$temp_file"
}

find "$DIR" -type f \( -name "*.ts" -o -name "*.tsx" \) | while read -r file; do
    echo "Processing $file"
    process_file "$file"
done

# Generate a report of changes
diff -r "$BACKUP_DIR" "$DIR" > import_changes_report.txt

echo "Import sorting and optimization complete!"
echo "A backup of the original files is stored in $BACKUP_DIR"
echo "A report of changes has been generated in import_changes_report.txt"

# Offer to revert changes
read -p "Would you like to review the changes before committing? (y/n) " answer
if [[ $answer == [Yy]* ]]; then
    less import_changes_report.txt
    read -p "Do you want to keep these changes? (y/n) " keep_changes
    if [[ $keep_changes != [Yy]* ]]; then
        echo "Reverting changes..."
        cp -R "$BACKUP_DIR"/* "$DIR"
        echo "Changes reverted."
        exit 0
    fi
fi

# Commit changes if in a git repository
if git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
    git add .
    git commit -m "Auto-sort imports [$(date)]"
    echo "Changes committed to git."
else
    echo "Not a git repository. Changes are saved but not committed."
fi